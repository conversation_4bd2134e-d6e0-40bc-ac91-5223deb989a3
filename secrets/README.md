## Development

### Setup
- See [Kubernetes README](../docs/kube-setup.md).

### Local Secrets Deployment
1. Run `make decrypt-local-secrets` and use the 1Password `Local Env Vault` password when prompted.

### K8s Secrets Deployment
1. Make sure you're connected to our VPN.
2. Switch to <env> profile using `awsp`
3. Make sure you're using the correct kubernetes config using [eksctl aliases](../docs/kube-setup.md).
4. Run `make deploy-k8s-secrets-dev` and use the 1Password `k8s Env Vault {Dev, Prod}` password when prompted.

#### Examples

`dev`
```shell
awsp dev
kd-creds
make deploy-k8s-secrets-dev
```
`prod`
```shell
awsp prod
kp-creds
make deploy-k8s-secrets-prod
```

### Encrypting Secret
1. `ansible-vault create {assetName}` in `assets` directory.

### Editing Existing Encrypted Secret
1. `ansible-vault edit {assetName}` in `assets` directory.

### Canonical way to add secrets (local, k8s)
1. Decrypt secrets (`make decrypt-secrets`) 
2. Edit secrets in ~/.secrets/unblocked
3. Run `make encrypt-secrets` (note: there are separate targets for k8s and local if you need)
4. Commit to repo
