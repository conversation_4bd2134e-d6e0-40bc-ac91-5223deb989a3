type: object
properties:
  query:
    type: string
  sortType:
    $ref: "./InsightSortType.yml"
  repoIds:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
  teamMemberIds:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
  topicIds:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
  providers:
    type: array
    items:
      $ref: "./Provider.yml"
  commitHashes:
    type: array
    items:
      $ref: "./SHA.yml"
  threadIds:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
required:
  - sortType
