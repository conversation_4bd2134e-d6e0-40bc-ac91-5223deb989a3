type: object

description: Bot.

properties:
  botIdentityId:
    $ref: "./ApiResourceId.yml"
  botIdentity:
    $ref: "./Identity.yml"
  isConnected:
    type: boolean
    description: |
      False if not yet connected, revoked, or expired.
  oauthUrl:
    type: string
    description: |
      URL to initiate OAuth flow for user to connect bot.
      Null when bot is connected.

required:
  - botIdentityId
  - botIdentity
  - isConnected
