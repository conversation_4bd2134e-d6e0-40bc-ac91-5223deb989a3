type: object
properties:
  member:
    $ref: "./TeamMember.yml"
  connectedMembers:
    description: Accounts that the team member has explicitly connected to.
    type: array
    items:
      $ref: "./TeamMember.yml"
  associatedMembers:
    description: Additional accounts that Unblocked believes the team member is likely related to.
    type: array
    items:
      $ref: "./TeamMember.yml"
required:
  - member
  - connectedMembers
  - associatedMembers
