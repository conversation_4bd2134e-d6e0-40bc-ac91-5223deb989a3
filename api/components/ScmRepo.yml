type: object
properties:
  externalId:
    type: string
    description: |
      External ID of the repo, used for uniquely identifying a repo in the SCM.
  fullName:
    type: string
    description: |
      Full name of the repo, like `ownerName/repoName`.
  ownerName:
    type: string
    description: |
      Name of the repo owner.
  repoName:
    type: string
    description: |
      Name of the repo.
  webUrl:
    type: string
    description: |
      Web HTML url of the repo in the SCM, like `https://github.com/org/repo`.
  httpUrl:
    type: string
    description: |
      Canonical HTTP url of the repo in the SCM, like `https://github.com/org/repo.git`.
  sshUrl:
    type: string
    description: |
      Canonical SSH url of the repo in the SCM, like `ssh://github.com/org/repo.git`.
  scpUrl:
    type: string
    description: |
      Canonical SCP url of the repo in the SCM, like `**************:org/repo.git`.
      Optional, because SCP urls can only be constructed for urls with default ports.
  createdAt:
    type: string
    format: date-time
    description: |
      Original SCM repo creation timestamp, not date added to Unblocked.
  lastActiveAt:
    type: string
    format: date-time
    description: |
      Typically timestamp of latest commit pushed to the repo.
  isFork:
    type: boolean
    description: |
      Whether the repo is a fork.
  isEmpty:
    type: boolean
    description: |
      Whether the repo is empty, meaning it has zero Git commits.
required:
  - fullName
  - ownerName
  - repoName
  - webUrl
  - httpUrl
  - sshUrl
