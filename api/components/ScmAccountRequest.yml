type: object
description: |
  Represents a Source Control Management (SCM) Account details needed to create a new SCM Instance.
properties:
  enterpriseProviderId:
    $ref: "./ApiResourceId.yml"
  externalId:
    type: string
    description: |
      External SCM account ID.
  externalInstallationId:
    type: string
    description: |
      External SCM installation ID.
  provider:
    $ref: "./Provider.yml"
required:
  - externalId
  - externalInstallationId
  - provider
