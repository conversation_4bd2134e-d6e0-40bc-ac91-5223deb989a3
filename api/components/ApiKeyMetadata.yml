description: |
  API key metadata
type: object
properties:
  id:
    $ref: "./ApiResourceId.yml"
  name:
    description: |
      A human-readable name for the API key.
      The name is unique within the org.
    type: string
  createdAt:
    description: |
      The time the API key was created.
    type: string
    format: date-time
  creatorId: # The ID of the team member who created the API key
    $ref: "./ApiResourceId.yml"
  lastUsedAt:
    description: |
      The last time the API key was used.
      If null, the API key has never been used.
    type: string
    format: date-time
required:
  - id
  - name
  - createdAt
  - creatorId
