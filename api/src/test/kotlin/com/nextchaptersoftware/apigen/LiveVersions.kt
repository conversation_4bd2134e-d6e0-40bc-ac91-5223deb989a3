package com.nextchaptersoftware.apigen

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.okhttp.OkHttp
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.get
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.serialization.Serializable

class LiveVersions {

    private val apiClient = HttpClient(OkHttp.create()) {
        defaultRequest {
            url("https://getunblocked.com/api/")
            contentType(ContentType.Application.Json)
        }
        install(ContentNegotiation) {
            installJsonSerializer()
        }
        install(HttpRequestRetry)
        expectSuccess = true
    }

    private suspend fun getStableVersions(): List<VersionInfo> {
        return apiClient.get("versionInfo").body()
    }

    suspend fun getLiveVersions(): List<Int> {
        return getStableVersions()
            .map { it.productNumber.toInt() }
            .distinct()
            .sorted()
    }
}

@Serializable
private data class VersionInfo(
    val productNumber: String,
)
