syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.nextchaptersoftware.common.transcription";
option java_outer_classname = "TranscriptionProto";

package transcription;

message TranscriptionSegment {
  int32 start = 1;
  int32 end = 2;
  string content = 3;
}

message TranscriptionBody {
  string version = 1;
  string id = 2;
  repeated TranscriptionSegment segments = 3;
}
