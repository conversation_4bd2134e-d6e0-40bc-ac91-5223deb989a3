replicaCount: 0

# https://cloud.google.com/kubernetes-engine/docs/concepts/autopilot-resource-requests
resources:
  tgi:
    requests:
      cpu: "4"
      memory: "6Gi"
      gpu: "2"
    limits:
      cpu: "4"
      memory: "6Gi"
      gpu: "2"
    gpu:
      type: nvidia-tesla-a100
    shm:
      size: 2Gi

startupProbe:
  httpGet:
    path: /
    port: 8080
  failureThreshold: 30
  periodSeconds: 10
  initialDelaySeconds: 120

env:
  - name: NVIDIA_DRIVER_CAPABILITIES
    value: "compute,utility"
  - name: DISABLE_CUSTOM_KERNELS
    value: "true"
  - name: NVIDIA_REQUIRE_CUDA
    value: "cuda>=8.0"
  - name: HF_HUB_CACHE
    value: "/hf-snapshots"
  - name: HUGGINGFACE_HUB_CACHE
    value: "/hf-snapshots"
  - name: MODEL_ID
    value: "NousResearch/Hermes-2-Pro-Llama-3-8B"
  - name: NUM_SHARD
    value: "2"
  - name: MAX_INPUT_LENGTH
    value: "22000"
  - name: MAX_BATCH_PREFILL_TOKENS
    value: "22000"
  - name: MAX_BATCH_TOTAL_TOKENS
    value: "50000"
  - name: MAX_CONCURRENT_REQUESTS
    value: "50"
  - name: MAX_TOTAL_TOKENS
    value: "23000"
  - name: PORT
    value: "8080"
  - name: ROPE_SCALING
    value: "dynamic"
  - name: ROPE_FACTOR
    value: "2"
  - name: METRICS_ENABLED
    value: "true"
  - name: METRICS_PORT
    value: "8081"

pvc:
  storage: 50Gi
  accessModes: ["ReadWriteMany"]
  storageClassName: csi-filestore-multishare-128-rwx

service:
  touch: "yes_touch_it9"


hpa:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  scaleUpStabilizationWindowSeconds: 15
  targetCPUUtilizationPercentage: 80
  targetGPUUtilizationPercentage: 90
  targetRequestCount: 60

