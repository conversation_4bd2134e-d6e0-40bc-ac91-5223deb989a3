name: network
runtime:
  name: nodejs
  options:
    packagemanager: npm
description: Deploy VPCs and subnets to GCP projects
config:
  pulumi:tags:
    value:
      pulumi:template: gcp-typescript
  core-vpc-primary-cidr:
    description: "CIDR range for the core VPC primary address range"
    type: string
  core-bpg-asn:
    description: "BGP asn number for Core vpc"
    type: string
  aws-core-vpc-primary-cidr:
    description: "CIDR range for the core VPC primary address range on AWS"
    type: string
  kube-vpc-primary-cidr:
    description: "CIDR range for kubernetes VPC primary address range"
    type: string
  network:kube-vpc-proxy-only-cidr:
    description: "CIDR range for internal l7 loadbalancer in kube vpc"
    type: string
  kube-bpg-asn:
    description: "BGP asn number for Kube vpc"
    type: string
  aws-kube-vpc-primary-cidr:
    description: "CIDR range for kubernetes VPC primary address range on AWS"
    type: string
