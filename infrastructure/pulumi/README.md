# Overview
This directory containts various projects used to configure our GCP accounts. Each project contains a stack per-environment.
For example Pulumi.dev.yaml stack contains environment specific configuration for a given project.

Each stack exports various values which can then be imported by other stacks.

Projects names:
- `development-431217` for Dev environment
- `production-431217` for prod environment

## List of projects
- **identity**: configures IAM roles, users and service accounts
- **network**: configures all the networking features including, VPC, Subnet, Firewall Rules, Routers, Routes, VPN gateways
- **dns**: Creates a hosted zone for the target environment as `${env}.gcp.getunblocked.com`
- **k8s-gke-cluster**: provisions an Autopilot managed GKE cluster.
- **k8s-gke-apps**: provisions all third-party resources such as Cert-Manager, Storage classes etc and deploys Huggingface models or raw Kubernetes manifests

## Deployment order
It is crucial that projects are deployed in the correct order. It would ensure that latest and most up-to-date output values are made availabel to consumer projects.

Here's the current recommended deployment order:
1. identity
2. dns
3. network
4. k8s-gke-cluster
5. k8s-gke-apps

## Manual deployment procedure

To deploy your infrastructure, follow the below steps.

### Prerequisites

1. [Get Started with Kubernetes on Pulumi](https://www.pulumi.com/docs/get-started/kubernetes/)

### Steps

After cloning this repo, from this working directory, run these commands:

1. Install the required Node.js packages:

    ```bash
    $ npm install
    ```

1.  Deploy to target environment

    ```bash
    # Switch to correct project for target env
    # Use project ID "production-431217" for production
    $  gcloud config set project development-431217

    # Stack names can be either "dev" or "prod" depending on target environemnt
    $  pulumi up --stack dev
    ```
1. Inspecting project outputs for a stack
    ```bash
        # Switch to correct project for target env
        # Use project ID "production-431217" for production
        $  gcloud config set project development-431217

        # Stack names can be either "dev" or "prod" depending on target environemnt
        $  pulumi stack output  --stack dev

        # Retrieve value for a project secret output
        # pulumi stack output {OUTPUT_NAME_HERE}  --show-secrets  --stack {STACK_NAME_HERE}
        $  pulumi stack output certManagerServiceAccountSecret  --show-secrets  --stack dev
    ```

## CI/CD
- GH Actions workflow [ci-gcp-infra.yml](../../../.github/workflows/ci-gcp-infra.yml)
- GH Actions re-usable deploy action: [ci-pulumi-deploy](./../../.github/actions/ci-pulumi-deploy/)

Required secrets and environment variables:
- `GCP_CIINFRADEPLOYER_CREDENTIALS` GH action environment specific secret containing auth.json for target GCP project
- `HUGGING_FACE_HUB_TOKEN` Huggingface token to be added as a kube secret
- `PULUMI_ACCESS_TOKEN` Pulumi API access token
