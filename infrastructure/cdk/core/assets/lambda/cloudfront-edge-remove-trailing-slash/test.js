const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');
const existingTestEventTemplate = require('./existing-app-event.json');
const lambdaLocal = require('lambda-local');
const path = require('path');

const expectedResp = {
    status: '301',
    statusDescription: 'Permanently moved',
    headers: {
        location: [
            {
                key: 'Location',
                value: '/home',
            },
        ],
    },
};

describe('Test', function () {
    it('Removes slash from uri', async function () {
        let testEvent = clone(existingTestEventTemplate);
        testEvent.Records[0].cf.request.uri = '/home/';

        assert.deepEqual(await runLambda(testEvent), expectedResp);
    });

    it('Does not redirect if slash does not exists', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/home';
        assert.notDeepEqual(await run<PERSON>ambda(testEvent), expectedResp);
    });
});

async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
