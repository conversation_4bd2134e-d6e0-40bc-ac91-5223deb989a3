'use strict';
/*
 - This function is called on Origin Request events.
 - It's resolves `/teamID/assetID to an S3 signed URL,
  override necessary headers and finally pass it to CF
  for object retrieval

 - Things to keep in mind:
   We need to ensure objects are cached for path `/teamID/assetID`.
   That might require another Lambda for Origin Response to revert
   `host` header and `uri` in request body
 */
// config.json provides streaming related configs
const config = require('./config.json');
const http = require('http');
const querystring = require('querystring');
const https = require('https');
const url = require('url');
const lambdaName = 'assets-lookup';

// Declare constants required for the signature process
const { GetObjectCommand, S3Client } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const client = new S3Client();

const parseUnblockedHeaders = (headers, filter) => {
    const parsedHeaders = {};
    for (const [k, v] of Object.entries(headers)) {
        if (Array.isArray(v)) {
            const cfHeader = v[0];
            if (cfHeader['key'] && cfHeader['value']) {
                const cfHeaderKey = decodeURIComponent(cfHeader['key']).trim();
                const cfHeaderValue = decodeURIComponent(cfHeader['value']).trim();
                if (cfHeaderKey.toLowerCase().includes('x-unblocked')) {
                    parsedHeaders[cfHeaderKey] = decodeURIComponent(cfHeaderValue);
                }
            }
        }
    }
    return parsedHeaders;
};

exports.handler = async (event, context, callback) => {
    const request = event.Records[0].cf.request;
    const uri = request.uri;

    // This is a fallback logic. Auth Lambda should handle this
    // by returning a custom placeholder error image
    const qStrings = querystring.parse(request.querystring);
    if (typeof qStrings.authorization == 'undefined') {
        const req = customErrorImage(event, 401);
        callback(null, req);
        return;
    }

    // Filter out all empty elements from split
    let pathComponents = uri.split('/').filter(Boolean);
    if (!validateRequestUri(pathComponents, uri)) {
        let response = createErrorResponse(400, `invalid team or asset uuid: ${uri}`, request, lambdaName);
        callback(null, response);
        return;
    }

    try {
        // Special case until we figure out asset registration for Agora stuff
        // TODO: This should be removed in the future
        if (pathComponents[1].endsWith('.m3u8')) {
            // It's a legacy streaming asset
            const bucket = config[qStrings.api]['video_streaming_bucket'];
            const ttl = config[qStrings.api]['signed_url_ttl'];
            const response = await generateM3u8Playlist(bucket, pathComponents[1], ttl, pathComponents[0]);
            response.headers = createCorsResponseHeaders(response.headers);
            callback(null, response);
        } else {
            // It's a standard asset
            var req = await lookupObjectUsingAssetService(pathComponents, qStrings, event);
            req.headers = createCorsResponseHeaders(req.headers);
            callback(null, req);
        }
    } catch (error) {
        let response = createErrorResponse(
            500,
            `the server encountered an unexpected condition that prevented it from fulfilling the request: ${error}`,
            request,
            lambdaName
        );
        callback(null, response);
    }
};

async function generateM3u8Playlist(bucket, key, ttl, teamId) {
    // It's a streaming asset
    //const bucket = "agora-streaming-assets-dev-us-west-2"

    // Read playlist file content
    var m3u8File = await getS3Object(bucket, key);

    // Replace each part file name with an S3 signed url
    for (const line of m3u8File.split('\n')) {
        if (line.endsWith('.ts')) {
            const getObjectCommand = new GetObjectCommand({
                Bucket: bucket,
                Key: `${teamId}/${line.replace('\n', '')}`,
            });
            const url = await getSignedUrl(client, getObjectCommand, { expiresIn: 21600 });
            m3u8File = m3u8File.replace(line, `${url}`);
        }
    }

    // return a response with playlist file content in the body
    return {
        status: '200',
        headers: {
            'content-type': [{ value: 'application/x-mpegurl' }],
        },
        body: m3u8File,
    };
}

function getS3Object(Bucket, Key) {
    return new Promise(async (resolve, reject) => {
        const getObjectCommand = new GetObjectCommand({ Bucket, Key });

        try {
            const response = await client.send(getObjectCommand);

            // Store all of data chunks returned from the response data stream
            // into an array then use Array#join() to use the returned contents as a String
            let responseDataChunks = [];

            // Handle an error while streaming the response body
            response.Body.once('error', (err) => reject(err));

            // Attach a 'data' listener to add the chunks of data to our array
            // Each chunk is a Buffer instance
            response.Body.on('data', (chunk) => responseDataChunks.push(chunk));

            // Once the stream has no more data, join the chunks into a string and return the string
            response.Body.once('end', () => resolve(responseDataChunks.join('')));
        } catch (err) {
            // Handle the error or throw
            return reject(err);
        }
    });
}

/*
 * Validates request path, teamID and assetID formats
 */
function validateRequestUri(pathComponents, uri) {
    if (
        uri.startsWith('/assets') || // Auth Lambda@Edge should remove any instances of this prefix
        pathComponents.length !== 2 || // Expected => "" + teamID + assetID
        !isUUID(pathComponents[0]) || // Team ID is always a UUID
        (!isUUID(pathComponents[1]) && !pathComponents[1].endsWith('.m3u8')) // Non-uuid asset names are only allowed for streaming playlists
    ) {
        return false;
    }
    return true;
}

/*
 * Gets S3 signed url for asset from Asset Service
 * and passes it to CF as a custom origin request
 */
async function lookupObjectUsingAssetService(pathComponents, qStrings, event) {
    let request = event.Records[0].cf.request;

    const unblockedHeaders = parseUnblockedHeaders(request.headers);
    if (qStrings.unblockedProductAgent) {
        unblockedHeaders['X-Unblocked-Product-Agent'] = qStrings.unblockedProductAgent;
    }

    const path = `/api/assets/teams/${pathComponents[0]}/${pathComponents[1]}?amznCloudFrontId=${event.Records[0].cf.config.requestId}`;
    const assetApiRequest = {
        hostname: qStrings.api,
        path: path,
        headers: {
            ...unblockedHeaders,
            Accept: 'application/json',
            Authorization: qStrings.authorization,
        },
        port: qStrings.api.includes('localhost') ? 8080 : 443, // Ugly hack to get local testing working
        timeout: 1000,
    };

    const assetApiResponse = await httpGet(assetApiRequest);
    console.log(
        `For request headers: ${JSON.stringify(request.headers)}, path: ${path}, and authorization: ${
            qStrings.authorization
        }
        , qStrings: ${JSON.stringify(qStrings)},
        , raw asset request: ${JSON.stringify(assetApiRequest)}
        , raw asset response: ${JSON.stringify(assetApiResponse)}`
    );
    const respBody = JSON.parse(assetApiResponse['body']);

    if (assetApiResponse.status == 404) {
        return customErrorImage(event, 404);
    } else if (assetApiResponse.status == 200) {
        // Special handling for streaming assets
        const bucket = config[qStrings.api]['video_streaming_bucket'];
        const ttl = config[qStrings.api]['signed_url_ttl'];
        if (respBody['asset']['name'].endsWith('.m3u8') && respBody['downloadUrl']['url'].includes(bucket)) {
            // It's a streaming asset
            const response = await generateM3u8Playlist(bucket, pathComponents[1], ttl, pathComponents[0]);
            return response;
        }

        // If you get to here then this is a non-streaming asset
        const parsedS3Url = url.parse(respBody['downloadUrl']['url']);
        // Create custom origin object
        request['origin'] = createOriginRequestObject(parsedS3Url.host);

        // Note 1: This field caused us a lot of pain. When setting `uri` field at request level
        // there's no need to set `path` in custom origin object.
        // Note 2: Viewer request object removes `/assets/` prefix to ensure request paths match
        // we get a cache hits on subsequent requests
        request.uri = parsedS3Url.pathname;

        // Extract query strings from signed url and pass it to request object
        request['querystring'] = querystring.stringify(querystring.parse(parsedS3Url.query));

        // Override host header => S3 bucket endpoint
        request.headers['host'] = [{ key: 'host', value: parsedS3Url.host }];

        // Do not send back the request authorization header, Cloudfront will return a 400 complaining about:
        // Only one auth mechanism allowed; only the X-Amz-Algorithm query parameter, Signature query string parameter or the Authorization header should be specified
        delete request.headers.authorization;

        console.log(
            `For path: ${path}, and authorization: ${qStrings.authorization}, response is: ${JSON.stringify(request)}`
        );

        return request;
    }

    return createErrorResponse(
        assetApiResponse.status,
        `failed to retrieve info from assets api: ${assetApiResponse}`,
        request,
        lambdaName
    );
}

function customErrorImage(event, errorCode) {
    let request = event.Records[0].cf.request;
    const requestStr = JSON.stringify(request);
    console.log(`Request caused error code: ${errorCode} for request : ${requestStr}`);

    request.headers = createCorsResponseHeaders(request.headers);
    request.headers['Cache-Control'] = [{ value: 'no-cache, cf-no-cache' }];

    // Create custom origin object
    request['origin'] = createOriginRequestObject(event.Records[0].cf.request.headers.host[0].value);
    request.uri = `/error-assets/error-${errorCode}.png`;
    request['querystring'] = '';

    if (request.headers.authorization) delete request.headers.authorization;
    return request;
}

function createErrorResponse(code, description, request, lambdaName) {
    const requestStr = JSON.stringify(request);

    // Debug headers
    const responseHeaders = createCorsResponseHeaders({});
    responseHeaders['Cache-Control'] = [{ value: 'no-cache, cf-no-cache' }];
    if (code >= 500) {
        responseHeaders['x-lambda-request-uri'] = [{ value: request.uri }];
        responseHeaders['x-lambda-name'] = [{ value: lambdaName }];
    }

    console.log(
        `Returning an error response: ${JSON.stringify(
            responseHeaders
        )} to request : ${requestStr} ${code}  ${description}`
    );

    return {
        status: code.toString(),
        headers: responseHeaders,
        statusDescription: description,
    };
}

function createOriginRequestObject(domainName) {
    return {
        custom: {
            customHeaders: {},
            domainName: domainName,
            keepaliveTimeout: 5,
            path: '',
            port: 443,
            protocol: 'https',
            readTimeout: 4,
            sslProtocols: ['TLSv1', 'TLSv1.1', 'TLSv1.2'],
        },
    };
}

function httpGet(params) {
    return new Promise((resolve, reject) => {
        // Ugly hack to get local testing working
        const client = params['hostname'].includes('localhost') ? http : https;
        client
            .get(params, (resp) => {
                let result = {
                    status: resp.statusCode,
                    headers: resp.headers,
                    body: '',
                };
                resp.on('data', (chunk) => {
                    result.body += chunk;
                });
                resp.on('end', () => {
                    resolve(result);
                });
            })
            .on('error', (err) => {
                console.log(`Couldn't fetch ${params.hostname}${params.path} : ${err.message}`);
                reject(err, null);
            });
    });
}

function isUUID(uuid) {
    let s = '' + uuid;

    s = s.match('^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$');
    if (s === null) {
        return false;
    }
    return true;
}

function createCorsResponseHeaders(headers) {
    headers = headers || {};
    headers['access-control-allow-origin'] = [{ key: 'Access-Control-Allow-Origin', value: '*' }];
    headers['access-control-allow-headers'] = [{ key: 'Access-Control-Allow-Headers', value: '*' }];
    return headers;
}
