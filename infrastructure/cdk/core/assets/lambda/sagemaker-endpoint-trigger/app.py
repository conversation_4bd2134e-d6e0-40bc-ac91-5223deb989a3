# Documentation
# https://docs.aws.amazon.com/elasticloadbalancing/latest/application/lambda-functions.html
# https://github.com/aws/elastic-load-balancing-tools/tree/master/application-load-balancer-serverless-app/helloworld
import os
import boto3
import json

client = boto3.client("sagemaker-runtime")

ENDPOINT_NAME = os.environ.get("ENDPOINT_NAME", None)


def handler(event, context):
    print("event", event)

    if ENDPOINT_NAME is None:
        return {"error": "Environment variable `ENDPOINT_NAME` not defined"}
    try:
        invokeResponse = client.invoke_endpoint(
            EndpointName=ENDPOINT_NAME,
            ContentType="application/json",
            Accept="application/json",
            Body=event["body"],
        )
        print("invokeResponse", invokeResponse)
        response = {
            "statusCode": 200,
            "headers": {
                "Content-Type": "application/json",
            },
            "body": invokeResponse["Body"].read().decode(),
            # required for lambda functions invoked by alb
            "isBase64Encoded": False,
        }
        print("response", response)
        return response
    except Exception as e:
        print(repr(e))
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
            },
            "body": json.dumps({"error": repr(e)}),
            # required for lambda functions invoked by alb
            "isBase64Encoded": False,
        }
    # return {
    #     'statusCode': 200,
    #     'body': json.loads(response['Body'].read())
    # }
