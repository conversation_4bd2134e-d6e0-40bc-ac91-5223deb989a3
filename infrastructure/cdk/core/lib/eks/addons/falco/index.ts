import * as blueprints from '@aws-quickstart/eks-blueprints';
import { HelmAddOn, ClusterInfo, HelmAddOnUserProps, HelmAddOnProps, Values } from '@aws-quickstart/eks-blueprints';
import { createNamespace, readYamlDocument, setPath, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { Construct } from 'constructs';
import { Duration } from 'aws-cdk-lib';
import * as path from 'path';
import { merge } from 'ts-deepmerge';

/**
 * User provided options for the Helm Chart
 */
export interface FalcoAddOnProps extends HelmAddOnUserProps {
    environment: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & FalcoAddOnProps = {
    name: 'blueprints-falco-addon',
    namespace: 'falco',
    chart: 'falco',
    version: '4.20.0',
    release: 'falco',
    repository: 'https://falcosecurity.github.io/charts',
    values: {},
    environment: 'dev',
};

@supportsALL
export class FalcoAddon extends HelmAddOn {
    readonly options: FalcoAddOnProps;
    readonly grafanaWebhookUrl: string;
    readonly slackWebhookUrl: string;

    constructor(version: string, props?: FalcoAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as FalcoAddOnProps;

        this.options.version = version;

        // Load values file for target environment
        const valuesDir = path.join(__dirname, 'values/');
        const valuesFile = readYamlDocument(path.join(valuesDir, `${this.options.environment}.yaml`));
        this.options.values = blueprints.utils.loadYaml(valuesFile);

        // Get Grafana webhook url from env var (GH secrets)
        if (!process.env.GRAFANA_WEBHOOK_URL_INFRA_ALARMS) {
            throw Error('GRAFANA_WEBHOOK_URL_INFRA_ALARMS environment variable is not defined');
        }
        this.grafanaWebhookUrl = process.env.GRAFANA_WEBHOOK_URL_INFRA_ALARMS;

        // Get Slack webhook url from env var (GH secrets)
        if (!process.env.SLACK_WEBHOOK_URL_INFRA_ALARMS) {
            throw Error('SLACK_WEBHOOK_URL_INFRA_ALARMS environment variable is not defined');
        }
        this.slackWebhookUrl = process.env.SLACK_WEBHOOK_URL_INFRA_ALARMS;
    }

    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        // Merge and render values
        let values: Values = populateValues(this.options, this.slackWebhookUrl, this.grafanaWebhookUrl);
        values = merge(values, this.props.values ?? {});
        const cluster = clusterInfo.cluster;

        // Create falco namespace
        const namespace = createNamespace(this.options.namespace!, cluster, true, true);

        // Deploy chart
        const chart = this.addHelmChart(clusterInfo, values, undefined, true, Duration.minutes(5));

        chart.node.addDependency(namespace);
        return Promise.resolve(chart);
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: FalcoAddOnProps, slackWebhookUrl: string, grafanaWebhookUrl: string): Values {
    const values = helmOptions.values ?? {};
    setPath(values, 'falcosidekick.enabled', true);
    setPath(values, 'falcosidekick.webui.enabled', true);
    setPath(values, 'falcosidekick.config.slack.username', 'Guard-zilla');
    setPath(values, 'falcosidekick.config.slack.minimumpriority', 'notice');
    setPath(values, 'falcosidekick.config.slack.webhookurl', slackWebhookUrl);
    setPath(values, 'falcosidekick.config.webhook.address', grafanaWebhookUrl);
    setPath(values, 'falcosidekick.config.webhook.minimumpriority', 'notice');
    return values;
}
