# Default values for refinery.
## Schema
## https://github.com/honeycombio/refinery/blob/36b6fc77acfb7e5a8fd144652c9a0768fe06c6d9/config_complete.yaml

## Scaling Refinery ##
#
# Refinery is a stateful service and is not optimized for dynamic auto-scaling.
# Changes in cluster membership can result in temporary inconsistent sampling
# decisions and dropped traces. As such, we recommend provisioning refinery for
# your anticipated peak load
#

# Use replicaCount and resource limits to set the size of your Refinery cluster
# per your anticipated peak load.
# replicaCount is ignored if autoscaling is enabled
replicaCount: 2

# Changing memory limits from the default 2Gi, requires updates to the
# config.InMemCollector.MaxAlloc property.
resources:
    limits:
        cpu: 800m
        # Changing memory limits requires updating config.InMemCollector.MaxAlloc
        memory: 1Gi
    requests:
        cpu: 500m
        memory: 512Mi

image:
    repository: honeycombio/refinery
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ''

imagePullSecrets: []
nameOverride: ''
fullnameOverride: ''

# Use to pass in additional environment variables into Refinery
# Refinery supports environment variables for some configuration options such as:
# - Honeycomb API Key used by Logger and Metrics: REFINERY_HONEYCOMB_API_KEY
# - gRPC listen address: REFINERY_GRPC_LISTEN_ADDRESS
# - Redis Host: REFINERY_REDIS_HOST
# - Redis Username: REFINERY_REDIS_USERNAME
# - Redis Password: REFINERY_REDIS_PASSWORD
environment:
    - name: REFINERY_HONEYCOMB_API_KEY
      valueFrom:
          secretKeyRef:
              name: refinery-secrets-env
              key: api-key
    - name: REFINERY_OTEL_METRICS_API_KEY
      valueFrom:
          secretKeyRef:
              name: refinery-secrets-env
              key: api-key
    - name: REFINERY_HONEYCOMB_METRICS_API_KEY
      valueFrom:
          secretKeyRef:
              name: refinery-secrets-env
              key: api-key

# Use to map additional volumes into the Refinery pods
# Useful for volume-based secrets
extraVolumeMounts:
    - name: refinery-secrets-env
      mountPath: '/mnt/secrets-store'
      readOnly: true

# Use to map additional volumes into the Refinery pods
extraVolumes:
    - name: refinery-secrets-env
      secret:
          secretName: refinery-secrets-env
          defaultMode: 0400

config:
    BufferSizes:
        PeerBufferSize: 10000
        UpstreamBufferSize: 10000
    Collection:
        MaxAlloc: 512M
        DisableRedistribution: false
        RedistributionDelay: 30s
        ShutdownDelay: 15s
        TraceLocalityMode: concentrated
    Debugging:
        AdditionalErrorFields:
            - trace.span_id
    GRPCServerParameters:
        Enabled: true
        ListenAddr: 0.0.0.0:4317
    General:
        ConfigurationVersion: 2
        MinRefineryVersion: v2.0
    PeerManagement:
        IdentifierInterfaceName: eth0
        Type: redis
    PrometheusMetrics:
        Enabled: true
        ListenAddr: 0.0.0.0:9090
    RedisPeerManagement:
        Host: '{{include "refinery.redis.fullname" .}}:6379'
    RefineryTelemetry:
        AddRuleReasonToTrace: true
        AddSpanCountToRoot: true
    StressRelief:
        ActivationLevel: 90
        DeactivationLevel: 25
        Mode: monitor
    Traces:
        BatchTimeout: 1s
        TraceTimeout: 120s
        MaxBatchSize: 750
        MaxExpiredTraces: 2000
        SpanLimit: 1000
    OTelMetrics:
        Enabled: true

rules:
    RulesVersion: 2
    Samplers:
        __default__:
            RulesBasedSampler:
                Rules:
                    - Name: drop incomplete traces
                      Drop: true
                      Conditions:
                          - Operator: has-root-span
                            Value: false
                    - Name: drop deepcheck
                      Drop: true
                      Conditions:
                          - Field: service.call.attribute.operationId
                            Operator: '='
                            Value: getDeepCheck
                    - Name: drop shallowcheck
                      Drop: true
                      Conditions:
                          - Field: service.call.attribute.operationId
                            Operator: '='
                            Value: getShallowCheck
                    - Name: drop everything but essential services
                      Drop: true
                      Conditions:
                          - Field: service.name
                            Operator: '!='
                            Value: apiservice
                          - Field: service.name
                            Operator: '!='
                            Value: authservice
                          - Field: service.name
                            Operator: '!='
                            Value: ciservice
                          - Field: service.name
                            Operator: '!='
                            Value: proxy-provider
                          - Field: service.name
                            Operator: '!='
                            Value: slackservice
                          - Field: service.name
                            Operator: '!='
                            Value: searchservice
                    # Keep all bot questions
                    - Name: keep BotQuestion
                      SampleRate: 1
                      Conditions:
                          - Field: service.name
                            Operator: '='
                            Value: 'searchservice'
                            Datatype: string
                          - Field: service.message.type
                            Operator: '='
                            Value: 'BotQuestion'
                            Datatype: string
                    # Try to keep all rpc methods
                    - Name: Keep RPCMethod
                      SampleRate: 1
                      Conditions:
                          - Field: rpc.method
                            Operator: 'exists'
                    # Try to keep most errors, balanced by exception diversity.
                    - Name: Errors
                      Conditions:
                          - Field: error
                            Operator: 'exists'
                      Sampler:
                          EMAThroughputSampler:
                              GoalThroughputPerSec: 20
                              Weight: 0.7
                              FieldList:
                                  - exception.type
                    - Name: CatchAll
                      Sampler:
                          EMAThroughputSampler:
                              GoalThroughputPerSec: 1
                              Weight: 0.7
                              FieldList:
                                  - service.name
redis:
    # To install a simple single pod Redis deployment set this to true.
    # If false, you must specify a value for existingHost
    # For production, it is recommended to set this to false and provide
    # a highly available Redis configuration using redis.existingHost
    enabled: true

    # If redis.enabled is false this needs to be specified.
    # This needs to be the name:port of a Redis configuration
    # existingHost:

    # If redis.enabled is true, this the image that will be used to create
    # the Redis deployment
    image:
        repository: redis
        tag: 7.2
        pullPolicy: IfNotPresent

    # Node selector specific to installed Redis configuration. Requires redis.enabled to be true
    nodeSelector: {}

    # Tolerations specific to installed Redis configuration. Requires redis.enabled to be true
    tolerations: []

    # Affinity specific to installed Redis configuration. Requires redis.enabled to be true
    affinity: {}
    annotations: {}
serviceAccount:
    # Specifies whether a service account should be created
    create: true
    labels: {}
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: ''

podLabels: {}

podAnnotations: {}

deploymentAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext:
    {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
# runAsUser: 1000

service:
    type: ClusterIP
    port: 80
    grpcPort: 4317
    labels: {}
    annotations: {}

ingress:
    enabled: false
    labels: {}
    annotations:
        {}
        # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
        - host: refinery.local
          path: /
    tls: []
    #  - secretName: refinery-tls
    #    hosts:
    #      - refinery.local

grpcIngress:
    enabled: false
    labels: {}
    annotations:
        {}
        # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    hosts:
        - host: refinery.local
          path: /
    tls: []
    #  - secretName: refinery-tls
    #    hosts:
    #      - refinery.local

# Setup autoscaling for refinery
# When autoscaling events occur, trace sharding will be recomputed. This will result in traces with missing spans being
# sent to Honeycomb, for a small period of time (approximately config.TraceTimeout * 2).
# Because of this, scaleDown is disabled by default to avoid unnecessary broken traces should traffic go up and down rapidly.
autoscaling:
    enabled: false
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 75
    # targetMemoryUtilizationPercentage: 80
    behavior:
        scaleDown:
            selectPolicy: Disabled

nodeSelector: {}

tolerations: []

affinity: {}

# If you need to create a secret provider, such as for using AWS SSM, you can do so here.
# secretProvider functionality requires the Secret Store CSI Driver:
# https://secrets-store-csi-driver.sigs.k8s.io/
#
# secretProvider:
#   create: true
#   spec:
#     provider: aws
#     secretObjects:
#     - secretName: refinery
#       type: Opaque
#       data:
#         - key: yourenvironment.refinery_honeycomb_api_key
#           objectName: youenvironment.refinery_honeycomb_api_key
#     parameters:
#       objects: |
#           - objectName: yourenvironment.refinery_honeycomb_api_key
#             objectType: "ssmparameter"
#   name: "refinery"

secretProvider:
    create: false
    name: refinery
    spec: {}
