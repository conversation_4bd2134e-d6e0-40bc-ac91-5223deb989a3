namespaceOverride: prefect
worker:
  priorityClassName: high-priority
  # https://github.com/PrefectHQ/prefect-helm/blob/main/charts/prefect-worker/values.yaml
  apiConfig: selfHostedServer

  selfHostedServerApiConfig:
    # If the prefect server is located external to this cluster, set a fully qualified domain name as the apiUrl
    # If the prefect server pod is deployed to this cluster, use the cluster DNS endpoint: http://<prefect-server-service-name>.<namespace>.svc.cluster.local:<prefect-server-port>/api
    # -- prefect API url (PREFECT_API_URL)
    apiUrl: http://prefect-server.prefect.svc.cluster.local:4200/api

    # ref: https://docs.prefect.io/v3/develop/settings-and-profiles#security-settings
    basicAuth:
      # -- enable basic auth for the worker, for an administrator/password combination. must be enabled on the server as well
      enabled: true
      # -- basic auth credentials in the format admin:<your-password> (no brackets)
      authString: ""
      # -- name of existing secret containing basic auth credentials. takes precedence over authString. must contain a key `auth-string` with the value of the auth string
      existingSecret: "prefect-server-umbrella-basicauth"

  config:
    workPool: k8s-work-pool

  tolerations:
    - key: "node-class"
      operator: "Equal"
      value: "worker"
      effect: "NoSchedule"

  autoscaling:
    # -- enable autoscaling for the worker
    enabled: true
    # -- minimum number of replicas to scale down to
    minReplicas: 1
    # -- maximum number of replicas to scale up to
    maxReplicas: 10
    # -- target CPU utilization percentage for scaling the worker
    targetCPUUtilizationPercentage: 80
    # -- target memory utilization percentage for scaling the worker
    targetMemoryUtilizationPercentage: 80
