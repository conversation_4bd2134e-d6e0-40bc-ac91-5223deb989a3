import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { NetworkStack } from '../vpc/network-stack';
import { BuildConfig, CdkAppInfo } from '../build-config';
import { EFSFileSystemConstruct } from '../common/efs/efs-filesystem-construct';

export interface EFSStackProps extends StackProps {
    buildConfig: BuildConfig;
    networkStack: NetworkStack;
    cdkAppInfo: CdkAppInfo;
}

export class EFSStack extends Stack {
    constructor(scope: Construct, id: string, props: EFSStackProps) {
        super(scope, id, props);

        if (props.buildConfig.efsFileSystems.length > 0) {
            const vpc = props.networkStack.coreVpc;
            const vpcSubnets = vpc.selectSubnets({
                subnetType: ec2.SubnetType.PUBLIC,
            });

            props.buildConfig.efsFileSystems.forEach(
                (efsFileSystem) =>
                    new EFSFileSystemConstruct(this, efsFileSystem.name, {
                        efsFileSystem: efsFileSystem,
                        vpc: vpc,
                        vpcSubnets: vpcSubnets,
                    })
            );
        }
    }
}
