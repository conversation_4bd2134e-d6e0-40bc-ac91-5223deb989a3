import { Stack, StackProps, RemovalPolicy } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { S3BucketConstruct } from '../common/s3-bucket-construct';
import { BucketEncryption } from 'aws-cdk-lib/aws-s3';
import { EnumHelpers } from '../common/utils/enum-helpers';
import { CustomerAssets } from './config';

interface CustomerAssetsStackConfig {
    awsEnvAccount: AwsEnvAccount;
    cdkAppInfo: CdkAppInfo;
    customerAssets: CustomerAssets;
}

export interface CustomerAssetsStackProps extends StackProps {
    buildConfig: CustomerAssetsStackConfig;
}

export class CustomerAssetsStack extends Stack {
    constructor(scope: Construct, id: string, props: CustomerAssetsStackProps) {
        super(scope, id, props);

        const customerAssets = [props.buildConfig.customerAssets.standardS3Bucket];

        // CORS policies are required to do any sort of browser-based puts/posts/gets
        const corsRules: s3.CorsRule[] = [
            {
                allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.POST, s3.HttpMethods.PUT],
                allowedOrigins: ['*'],
                allowedHeaders: ['*'],
            },
        ];

        for (const customerAsset of customerAssets) {
            const customerAssetBucket = new S3BucketConstruct(this, `${customerAsset.name}Construct`, {
                id: customerAsset.id,
                awsEnvAccount: props.buildConfig.awsEnvAccount,
                cdkAppInfo: props.buildConfig.cdkAppInfo,
                addAccessLogPolicy: false,
                name: customerAsset.name,
                cors: corsRules,
                enableVersioning: customerAsset.enableVersioning,
                bucketKeyEnabled: customerAsset.bucketKeyEnabled,
                bucketEncryption: EnumHelpers.findEnumType(BucketEncryption, customerAsset.bucketEncryptionType),
                publicReadAccess: customerAsset.publicReadAccess,
                transferAcceleration: customerAsset.transferAcceleration,
                bucketTier: customerAsset.bucketTier,
                removalPolicy: RemovalPolicy.RETAIN,
                enableUserdataRetentionRule: customerAsset.enableUserdataRetentionRule,
                autoDeleteObjects: customerAsset.autoDeleteObjects,
                replicationTargetRegion: customerAsset.replicationTargetRegion,
                replicationTargetKMSKeyArn: customerAsset.replicationTargetKMSKeyArn,
            });

            // This is a hack https://www.endoflineblog.com/cdk-tips-03-how-to-unblock-cross-stack-references
            this.exportValue(customerAssetBucket.s3Bucket.bucketArn);
        }
    }
}
