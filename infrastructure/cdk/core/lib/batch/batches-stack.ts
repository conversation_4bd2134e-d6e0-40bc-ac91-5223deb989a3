import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { BatchConstruct } from '../common/batch/batch-construct';
import { NetworkStack } from '../vpc/network-stack';
import { BuildConfig, CdkAppInfo } from '../build-config';

export interface BatchStackProps extends StackProps {
    buildConfig: BuildConfig;
    networkStack: NetworkStack;
    cdkAppInfo: CdkAppInfo;
}

export class BatchesStack extends Stack {
    public readonly batches: Array<BatchConstruct>;

    constructor(scope: Construct, id: string, props: BatchStackProps) {
        super(scope, id, props);

        if (props.buildConfig.batches.length > 0) {
            const vpc = props.networkStack.coreVpc;
            const vpcSubnets = vpc.selectSubnets({
                subnetType: ec2.SubnetType.PUBLIC,
            });

            this.batches = props.buildConfig.batches.map(
                (batch) =>
                    new BatchConstruct(this, `${batch.name}`, {
                        batch: batch,
                        vpc: vpc,
                        vpcSubnets: vpcSubnets,
                    })
            );
        }
    }
}
