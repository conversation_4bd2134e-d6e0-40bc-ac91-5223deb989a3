{{- if ne .Values.global.unblockedPrefectSecretsB64 "" }}
apiVersion: v1
kind: Secret
metadata:
  name: prefect-secrets-env
type: Opaque
stringData:
  {{- range $line := .Values.global.unblockedPrefectSecretsB64 | b64dec | replace "\r" "" | replace "\t" "" | splitList "\n" }}
  {{- $line := trim $line }}
  {{- if and $line (contains ":" $line) }}
  {{- $kv := splitList ":" $line }}
  {{- if eq (len $kv) 2 }}
  {{ trim (index $kv 0) }}: {{ trim (index $kv 1)  }}
  {{- end }}
  {{- end }}
  {{- end }}
{{- end }}
