global:
  unblockedHFSecretsB64: "dummy"
  replicated:
    dockerconfig<PERSON><PERSON>: 'dummy'
  #ingress:
  #  className: ""
  #  hosts:
  #    - "app.mydomain.com"
  #  annotations:
  #    kubernetes.io/ingress.class: alb
  #    alb.ingress.kubernetes.io/scheme: internal
  #    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
  #    alb.ingress.kubernetes.io/group.name: "unblocked-internal"
  #    alb.ingress.kubernetes.io/healthcheck-path: "/api/__shallowcheck"
  #    alb.ingress.kubernetes.io/target-type: instance
  #    service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-016571197ec54d35c"
  #    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:881490090405:certificate/4bdba59b-f9aa-4ddb-aa2a-e432d5c89a83

replicaCount: 1

image:
  nginx:
    repository: nginx
    tag: latest
    pullPolicy: IfNotPresent
  tgi:
    #imageRepoOverride: ecr-repo-name
    pullPolicy: IfNotPresent

resources:
  nginx:
    requests:
      cpu: "1"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "1Gi"
  tgi:
    requests:
      cpu: "6"
      memory: "26Gi"
      gpu: "1"
    limits:
      cpu: "6"
      memory: "26Gi"
      gpu: "1"
    gpu:
      type: nvidia-l4
    shm:
      size: 512Mi

service:
  type: NodePort
  port: 80
  targetPort: 80

ingress:
  enabled: false
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

deploymentStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 30%
    maxSurge: 50%

readinessProbe:
  httpGet:
    path: /            # The endpoint to check
    port: 8080          # The port to make the request to
    scheme: HTTP        # Use HTTP (can change to HTTPS if required)
  initialDelaySeconds: 120
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 20
  successThreshold: 1

pvc:
  enable: false
  storage: 20Gi
  accessModes: ["ReadWriteMany"]
  storageClassName: csi-filestore-multishare-128-rwx

#tolerations:
#  - key: "nvidia.com/gpu"
#    operator: "Equal"
#    value: "true"
#    effect: "NoSchedule"

#nodeSelector:
#  accelerator: "something"

hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  scaleUpStabilizationWindowSeconds: 60
  targetCPUUtilizationPercentage: 80
  #targetGPUUtilizationPercentage: 80
  targetRequestCount: 30

nginxConf: |
  events {
    worker_connections 768;
  }

  http {
    gzip on;
    gzip_proxied any;
    gzip_types text/plain application/json;
    gzip_min_length 1000;

    # Define a custom log format to include the request time
    log_format custom_format '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $body_bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             'Request time: $request_time seconds';

    # Apply the custom log format to the access log
    access_log /var/log/nginx/access.log custom_format;

    server {
      listen 80;
      listen [::]:80;

      client_max_body_size 20M;  # Increase to 20MB

      server_name localhost;

      location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
      }
      location = /healthz {
        return 200 'OK';
        add_header Content-Type text/plain;
      }
      location = /metrics {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }

      location / {
        if ($request_method !~ ^(GET|POST|HEAD|OPTIONS|PUT|DELETE)$) {
          return 405;
        }

        rewrite ^/(.*)$ / break;

        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }

      location ~ ^(.*/)?v1/(.*) {
        if ($request_method !~ ^(GET|POST|HEAD|OPTIONS|PUT|DELETE)$) {
          return 405;
        }

        rewrite ^(.*/)?v1/(.*) /v1/$2 break;

        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }
    }
  }
