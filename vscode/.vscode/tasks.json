{"version": "2.0.0", "tasks": [{"type": "shell", "command": "npm", "args": ["run", "build:dev", "--", "--env", "env=dev"], "group": "build", "problemMatcher": [], "label": "build:dev", "detail": "webpack"}, {"type": "shell", "command": "npm", "args": ["run", "build:dev", "--", "--env", "env=prod"], "group": "build", "problemMatcher": [], "label": "build:prod", "detail": "webpack"}, {"type": "shell", "command": "npm", "args": ["run", "build:local"], "group": "build", "problemMatcher": [], "label": "build:local", "detail": "webpack"}, {"type": "shell", "command": "npm", "args": ["run", "watch:dev", "--", "--env", "env=dev"], "group": "build", "label": "watch:dev", "detail": "webpack", "isBackground": true, "problemMatcher": [{"pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": {"regexp": "[Cc]ompiling.*?|[Cc]ompil(ation|er) .*?starting"}, "endsPattern": {"regexp": "[Cc]ompiled (.*?successfully|with .*?(error|warning))|[Cc]ompil(ation|er) .*?finished"}}}]}, {"type": "shell", "command": "npm", "args": ["run", "watch:dev", "--", "--env", "env=prod"], "group": "build", "label": "watch:prod", "detail": "webpack", "isBackground": true, "problemMatcher": [{"pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": {"regexp": "[Cc]ompiling.*?|[Cc]ompil(ation|er) .*?starting"}, "endsPattern": {"regexp": "[Cc]ompiled (.*?successfully|with .*?(error|warning))|[Cc]ompil(ation|er) .*?finished"}}}]}, {"type": "shell", "command": "npm", "args": ["run", "watch:local"], "group": "build", "label": "watch:local", "detail": "webpack", "isBackground": true, "problemMatcher": [{"pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": {"regexp": "[Cc]ompiling.*?|[Cc]ompil(ation|er) .*?starting"}, "endsPattern": {"regexp": "[Cc]ompiled (.*?successfully|with .*?(error|warning))|[Cc]ompil(ation|er) .*?finished"}}}]}]}