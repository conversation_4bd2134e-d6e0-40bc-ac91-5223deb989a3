import fs from 'fs';
import { Uri, window } from 'vscode';

import { DownloadStore } from '@shared/stores/DownloadStore';
import { LazyValue } from '@shared/webUtils';
import { logger } from '@shared/webUtils/log';
const log = logger('VSCodeDownloadStore');
export class VSCodeDownloadStore implements DownloadStore {
    static instance = LazyValue(() => new VSCodeDownloadStore());
    async downloadSvg(options: { svg: string; filename: string }): Promise<void> {
        this.downloadFile(options.svg, options.filename, { 'SVG Files': ['svg'] });
    }

    async downloadFile(content: string, filename: string, filters?: { [name: string]: string[] }): Promise<void> {
        // No way of 100% getting downloads diretory...
        const result = await window.showSaveDialog({
            defaultUri: Uri.parse(filename),
            filters,
        });

        if (!result) {
            return;
        }

        const filePath = result.fsPath;
        try {
            await fs.promises.writeFile(filePath, content, 'utf8');
            window.showInformationMessage('Downloaded');
            return;
        } catch (error) {
            log.error('Download failed:', error);
            return;
        }
    }
}
