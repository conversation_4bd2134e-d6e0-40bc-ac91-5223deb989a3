import { GitProvider } from '@shared-node-git';
import { logger } from '@shared-web-utils';

import { CustomGitProvider } from './CustomGitProvider';

const log = logger('GitProvider');

async function createGitProvider(): Promise<GitProvider> {
    try {
        const provider = await CustomGitProvider.create();
        return provider;
    } catch (error) {
        log.error('Unable to use Custom git provider', error);
        throw error;
    }
}

let provider: Promise<GitProvider> | undefined;

export async function getGitProvider(): Promise<GitProvider> {
    if (!provider) {
        provider = createGitProvider();
    }
    return provider;
}
