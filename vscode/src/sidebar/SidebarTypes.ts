import { ViewColumn } from 'vscode';

import { OnboardingStatus, ThreadInfoAggregate } from '@models';

import { SidebarThreadProps } from '@shared/ide/sidebar/SidebarTypes';
import { InsightAggregate } from '@shared/stores/InsightStore';

// ViewColumn copied from vscode for use in webview code
export type ViewColumnType = ViewColumn;

export enum SidebarViewTypes {
    // This is the "main" sidebar view.  No I don't know why it's called 'new-sidebar;
    // but the ID can't be renamed without breaking people's IDE layouts
    NewSidebar = 'unblocked-vscode.new-sidebar',

    ExplorerInsights = 'unblocked-vscode.explorer-insights',

    // show unreads
    SidebarShowUnreadOnly = 'unblocked-vscode.filter-unreads',
    SidebarShowAll = 'unblocked-vscode.filter-all',
    SidebarMarkAllAsRead = 'unblocked-vscode.mark-all-read',
}

export enum SidebarCommandNames {
    ExplorerInsightsFocus = 'unblocked-vscode.explorer-insights-focus',
}

export enum MainSidebarCommandNames {
    AskQuestion = 'unblocked-vscode.ask-question-focus',
    MyQuestions = 'unblocked-vscode.my-questions-focus',
}

export interface SidebarSectionState {
    isExpanded: boolean;
    height: number;
}

export type SidebarViewState =
    | { $case: 'loading' }
    | { $case: 'searchLoading' }
    | { $case: 'loaded'; loadedProps: SidebarLoaded }
    | { $case: 'searchLoaded'; loadedProps: SidebarSearchLoaded };

export interface SidebarViewProps {
    state: SidebarViewState;
    selectedThreadId?: string;
    selectedPrId?: string;
    inputValue?: string;
}

interface SidebarLoaded {
    recommendedThreads: ThreadInfoAggregate[];
    teamIds: string[];
    onboardingStatus?: OnboardingStatus;
}

interface SidebarSearchLoaded {
    insights: InsightAggregate[];
    recommendedThreads: ThreadInfoAggregate[];
    selectedThreadId?: string;
    selectedPrId?: string;
    teamIds: string[];
    onboardingStatus?: OnboardingStatus;
}

interface SidebarSearchThread {
    command: 'searchThread';
    query: string | undefined;
}

export type SidebarExtensionProps = SidebarThreadProps | SidebarSearchThread;

export type PullRequestSort = 'ChangeCount' | 'DiscussionCount' | 'MergedDate';
