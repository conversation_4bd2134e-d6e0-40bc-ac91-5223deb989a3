@use '../styles/layout' as *;
@use '../styles/layout-mixin' as *;
@use '../styles/fonts' as *;
@use '../styles/flex' as *;
@use '../styles/misc' as *;
@use '../styles/animations' as *;
@use '../styles/fonts' as *;

.explorer_insights {
    .tabs {
        border-top: $border-width $border-style var(--vscode-terminal-border);
        .tabs__headers {
            .tab_header {
                font-size: $font-size-12;

                .explorer_insights__tab__badge {
                    font-size: $font-size-11;
                    color: var(--vscode-tab-inactiveForeground);
                    background-color: var(--vscode-badge-background);
                    border-radius: $border-radius-20;
                    padding: $spacer-1 $spacer-4;
                    margin-left: $spacer-4;
                }

                &.tab_header__selected {
                    background-color: var(--vscode-sideBar-background);

                    .explorer_insights__tab__badge {
                        color: var(--vscode-tab-activeForeground);
                    }
                }
            }
        }
    }

    .explorer_insights__header {
        background-color: var(--vscode-sideBar-background);
    }

    .explorer_insights__ask_button {
        padding: $spacer-8;
        border-bottom: $border-width $border-style var(--vscode-panel-border);

        .button {
            span {
                opacity: 0.8;
            }
        }
    }
}
