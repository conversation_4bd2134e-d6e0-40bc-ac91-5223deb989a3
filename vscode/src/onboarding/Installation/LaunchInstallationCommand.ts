import { commands, Uri, ViewColumn } from 'vscode';

import { EditorViewRouterStream } from '@shared/ide/ViewRouter/EditorViewRouterStream';
import { EditorViewRouterCommand } from '@vscode/editorViewRouter/EditorViewRouterCommand';

import { InstallationCommandNames } from './InstallationCommandNames';

export async function LaunchInstallationCommand(uri: Uri) {
    const showOptions = {
        viewColumn: ViewColumn.Beside,
        preserveFocus: true,
    };
    const panelTitle = 'Installation';
    EditorViewRouterStream.instance().select({ $case: 'install' });
    await EditorViewRouterCommand(uri, { $case: 'install' }, panelTitle, showOptions);
}

export function LaunchInstallation() {
    commands.executeCommand(InstallationCommandNames.LaunchInstallation);
}
