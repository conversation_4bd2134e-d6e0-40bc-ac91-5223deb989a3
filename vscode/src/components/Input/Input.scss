@use 'layout' as *;
@use 'misc' as *;

.input {
    padding: $spacer-6 $spacer-4;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: $border-width $border-style var(--vscode-settings-textInputBorder, transparent);

    &.input__full_width {
        width: -webkit-fill-available;
        width: -moz-available;
    }

    &:focus {
        outline: none;
        border: $border-width $border-style var(--vscode-focusBorder);
    }
}
