@use 'layout' as *;

.pull_request_tag {
    color: var(--vscode-button-foreground);
    border-radius: $size-10;
    padding: $spacer-1 $spacer-6;
    background-color: transparent;
    position: relative;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        border-radius: $size-10;
        z-index: -1;
        opacity: 0.7;
        background-color: var(--vscode-terminal-ansiBrightMagenta);
    }
}
