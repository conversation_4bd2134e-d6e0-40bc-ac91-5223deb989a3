## Creating Kotlin Services

- `make create-service` generates boilerplate code for a new service
- `make create-service-helm-charts` generates helm charts for a new service

## Updating Kotlin Services

- `make update-service-helm-charts` updates all service helm charts if baseservice helm charts are changed [helm](../helm/baseservice)

## Building Kotlin Services

- `make lint` runs **detekt** and **ktlintCheck** on all kotlin subprojects
- `make clean` cleans builds directory in all kotlin subprojects
- `make build` runs lint, drops then restarts db and finally executes `build` gradle task (we run this as part of CI/CD)
- `make check` runs lint, drops then restarts db and finally executes `check` gradle task (we run this as part of CI/CD)

## Running Local Stack (Docker)

This is mainly intended for those who need a fully functional local stack similar to Dev environment on their local machine (e.g front-end development)

### Setup Environment

Follow instructions provided below to setup Java and local secrets:
- See also [Backend README](./backend-setup.md).
- See also [secrets README](../secrets/README.md).

### Launch Services

- `make full-build-local-stack` (with tests) drops db then rebuilds and packages all Kotlin services into docker images.
- `make fast-build-local-stack` (no tests) retains DB but rebuilds and packages all Kotlin services into docker images.
- `make run-local-stack` Launches all Kotlin services using docker compose fronted by Nginx load balancer to perform correct path mappings.
- `make run-local-intellij-stack` Launches dependencies using docker compose fronted by Nginx load balancer to perform correct path mappings. Requires services to be run via Intellij.

#### Example workflows

- Local Full-Stack Engineering:
    - build and test: `make full-build-local-stack`
    - then run local stack(optional): `make run-local-stack`
- Front-End Development:
    - fast build: `make fast-build-local-stack`
    - run local stack: `make run-local-stack`

### Validate

```bash
curl http://localhost:8080/api/__deepcheck
Ok! Dependencies ok too!         <-- Expected output
```

### Common Errors:
- ` Cannot start service SOME_SERVICE: Ports are not available: listen tcp 0.0.0.0:SOME_PORT: bind: address already in use`
    - Make sure you are not running any local Kotlin services for example via IDE
    - `sudo lsof -i -P | grep LISTEN | grep :PORT_HERE` and then kill the process on that port `kill -15 PID`
