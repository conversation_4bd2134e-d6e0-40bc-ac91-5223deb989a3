ignoreFileOverride: .openapi-generator-ignore
skipOperationExample: true
additionalProperties:
  dateLibrary: java8
  serializationLibrary: kotlinx_serialization
  featureLocations: true
  library: ktor
files:
  libraries/ktor/apiDelegateInterface.mustache:
    destinationFilename: DelegateInterface.kt
    templateType: API
  libraries/ktor/Resources.kt.mustache:
    destinationFilename: Resources.kt
    folder: src/main/kotlin/org/openapitools/server
    templateType: SupportingFiles
  libraries/ktor/infrastructure/Serializer.kt.mustache:
    destinationFilename: Serializer.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/SerializerHelper.kt.mustache:
    destinationFilename: SerializerHelper.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/StringBuilderAdapter.kt.mustache:
    destinationFilename: StringBuilderAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/OffsetDateTimeAdapter.kt.mustache:
    destinationFilename: OffsetDateTimeAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/LocalDateTimeAdapter.kt.mustache:
    destinationFilename: LocalDateTimeAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/LocalDateAdapter.kt.mustache:
    destinationFilename: LocalDateAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/URIAdapter.kt.mustache:
    destinationFilename: URIAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/URLAdapter.kt.mustache:
    destinationFilename: URLAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/UUIDAdapter.kt.mustache:
    destinationFilename: UUIDAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/KtorUrlAdapter.kt.mustache:
    destinationFilename: KtorUrlAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/DateAdapter.kt.mustache:
    destinationFilename: DateAdapter.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
  libraries/ktor/infrastructure/CallAttributes.kt.mustache:
    destinationFilename: CallAttributes.kt
    folder: src/main/kotlin/org/openapitools/server/infrastructure
    templateType: SupportingFiles
