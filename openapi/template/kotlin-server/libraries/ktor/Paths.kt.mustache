package {{packageName}}

import io.ktor.server.locations.*
import {{modelPackage}}.*
{{#imports}}import {{import}}
{{/imports}}

{{#apiInfo}}
@KtorExperimentalLocationsAPI
object Paths {
{{#apis}}
{{#operations}}
    {{#operation}}
    /**{{#summary}}
     * {{.}}{{/summary}}
     * {{unescapedNotes}}
     {{#allParams}}* @param {{paramName}} {{description}} {{^required}}(optional{{#defaultValue}}, default to {{{.}}}{{/defaultValue}}){{/required}}
     {{/allParams}}*/
    {{#hasParams}}
        @Location("{{path}}") class {{operationId}}({{#allParams}}{{^isBodyParam}}{{^isHeaderParam}}val {{paramName}}: {{{dataType}}}{{^required}}? = null{{/required}}{{#required}}{{#isNullable}}?{{/isNullable}}{{/required}}{{^-last}}, {{/-last}}{{/isHeaderParam}}{{/isBodyParam}}{{/allParams}})
    {{/hasParams}}
    {{^hasParams}}
    @Location("{{path}}") object {{operationId}}
    {{/hasParams}}

    {{/operation}}
{{/operations}}
{{/apis}}
}
{{/apiInfo}}
