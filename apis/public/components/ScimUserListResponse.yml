type: object

description: |
  A SCIM user list.

properties:

  schemas:
    type: array
    items:
      type: string
    description: |
      An array of Strings containing URIs that are used to indicate the namespaces of the SCIM schemas that define the attributes present
      in the current structure. Each schema URI MUST be unique, and any schema that defines attributes in the current structure MUST be
      included in the array. The schemas array is REQUIRED and MUST be non-empty.

  totalResults:
    type: integer
    description: |
      The total number of results returned by the list or query operation. The value may be larger than the number of resources returned,
      such as when returning a single page of results where multiple pages are available.

  Resources:
    type: array
    items:
      $ref: './ScimUserResponse.yml'
    description: |
      An array of SCIM user objects as defined in [RFC 7643](https://tools.ietf.org/html/rfc7643#section-4.1).
      This object is used to represent a user in the SCIM API.

  startIndex:
    type: integer
    description: |
      The 1-based index of the first result in the current set of list results.
      REQUIRED when partial results are returned due to pagination.

  itemsPerPage:
    type: integer
    description: |
      The number of resources returned in a list response page.
      REQUIRED when partial results are returned due to pagination.

required:
  - schemas
  - totalResults
  - Resources
  - startIndex
  - itemsPerPage
