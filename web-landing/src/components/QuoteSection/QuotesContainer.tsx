import classNames from 'classnames';

import globeBackground from '@clientAssets/landing/globe.png';

import { HomeSection } from '../Section/HomeSection';

import './QuotesContainer.scss';

interface Props {
    children: React.ReactNode;
    header: React.ReactNode;
    className?: string;
    id?: string;
}

export const QuotesContainer = ({ header, children, className, id }: Props) => {
    return (
        <HomeSection
            className={classNames('home__quotes', className ?? '')}
            id={id}
            header={<h2>{header}</h2>}
            background={globeBackground.src}
        >
            {children}
        </HomeSection>
    );
};
