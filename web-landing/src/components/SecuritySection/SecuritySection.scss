@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use '../../app/landing-mixin.scss' as *;
@use '../../app/landing-colors.scss' as *;

.security_section {
    .home_section__header h2 {
        color: $white-100;
        .checkmark_highlight {
            color: themed($success);
        }
    }
    .home_section__subheader {
        color: $white-60;
        max-width: 740px;

        a,
        button.button__as_link {
            color: themed($link-tertiary);
            text-decoration: underline;
            font-weight: 300;
            letter-spacing: -0.015em;
        }
    }
}

.security__grid {
    display: grid;
    grid-template:
        'soc2'
        'casa'
        'data_isolation';
    gap: $spacer-12;
    margin-top: $spacer-48;

    @include breakpoint(md) {
        grid-template:
            'soc2 casa'
            'data_isolation data_isolation';
        gap: $spacer-24;
    }

    .soc2 {
        grid-area: soc2;
    }

    .casa {
        grid-area: casa;
    }

    .security_feature.data_isolation {
        grid-area: data_isolation;
        flex-direction: column;

        @include breakpoint(md) {
            flex-direction: row;
        }

        &.security_feature__large {
            @include breakpoint(md) {
                padding: $spacer-16 $spacer-80;
            }

            .security_feature__icon {
                height: 112px;
                width: auto;

                @include breakpoint(md) {
                    height: 160px;
                }
            }
        }

        .security_feature__content {
            @include breakpoint(md) {
                align-items: flex-start;
                text-align: left;
            }
        }
    }
}
