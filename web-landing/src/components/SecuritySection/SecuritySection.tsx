import { Button } from '@shared/webComponents/Button/Button';
import { launchIntercom, useIntercom } from '@shared/webComponents/Intercom/Intercom';

import casa from '@clientAssets/landing/casa.svg';
import layersLock from '@clientAssets/landing/layers-lock.svg';
import soc2 from '@clientAssets/landing/soc2.svg';

import { SecurityFeature } from '../HomeBlocks/SecurityFeatureBlock';
import { HomeSection } from '../Section/HomeSection';

import './SecuritySection.scss';

interface Props {
    header: React.ReactNode;
    id?: string;
}

export const SecuritySection = ({ header, id }: Props) => {
    const intercomApi = useIntercom();

    return (
        <HomeSection
            dark
            className="security_section"
            id={id}
            header={<h2>{header}</h2>}
            subheader={
                <div>
                    Unblocked has been designed with security in mind from day one. Read more about our{' '}
                    <a href="/security">security posture</a> or{' '}
                    <Button as="link" onClick={() => launchIntercom(intercomApi)}>
                        contact us
                    </Button>{' '}
                    with questions.
                </div>
            }
        >
            <div className="security__grid">
                <SecurityFeature header={'Soc 2 Type II Certified'} icon={soc2.src} className="soc2">
                    Unblocked is AICPA SOC 2 Type II certified, underscoring our commitment to maintaining top-tier
                    security standards with regular third-party penetration testing.
                </SecurityFeature>
                <SecurityFeature header={'CASA Tier II Certified'} icon={casa.src} className="casa">
                    Unblocked has achieved CASA Tier II verification, highlighting our adherence to the core security
                    principles for cloud services.
                </SecurityFeature>
                <SecurityFeature
                    header={'Team-based data access and isolation'}
                    icon={layersLock.src}
                    className="data_isolation"
                >
                    Access to data across network layers is secured with encryption in transit and at rest using
                    team-specific keys. Customer data is never be used to train shared models.
                </SecurityFeature>
            </div>
        </HomeSection>
    );
};
