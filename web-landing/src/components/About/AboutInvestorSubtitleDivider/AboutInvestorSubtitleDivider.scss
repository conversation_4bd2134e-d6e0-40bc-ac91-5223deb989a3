@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;

.about_investor_subtitle_divider {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .about_investor_subtitle_divider__line {
        width: 40px;
        height: 1px;
        background-color: themed($border);

        @include breakpoint(md) {
            width: 60px;
        }
    }

    .about_investor_subtitle_divider__subtitle {
        font-size: 16px;
        line-height: 22px;
        font-weight: 600;
        letter-spacing: -0.0245em;
        text-transform: uppercase;
        color: $heliotrope;

        // min-width: 190px;

        @include breakpoint(md) {
            font-size: 19px;
            line-height: 25px;
        }

        @include breakpoint(lg) {
            font-size: 22px;
            line-height: 28px;
        }
    }
}
