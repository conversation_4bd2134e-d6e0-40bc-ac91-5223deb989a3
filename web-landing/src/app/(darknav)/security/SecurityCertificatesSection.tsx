import { HomeSection } from '@landing/components/Section/HomeSection';
import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';

import casa from '@clientAssets/landing/casa.svg';
import soc2 from '@clientAssets/landing/soc2.svg';

import './SecurityCertificatesSection.scss';

export const SecurityCertificatesSection = () => {
    return (
        <HomeSection className="security_certificates_section">
            <h2 className="security_certificates_section__title">Security Certificates</h2>
            <div>
                <SecurityCertificate
                    icon={soc2.src}
                    title="AICPA SOC 2"
                    description="Unblocked is AICPA SOC 2 Type II certified, underscoring our commitment to maintaining top-tier security standards with regular third-party penetration testing."
                />
                <SecurityCertificate
                    icon={casa.src}
                    title="Cloud Application Security Assessment"
                    description="Unblocked has achieved CASA Tier II verification, highlighting our adherence to the core security principles for cloud services."
                />
            </div>
        </HomeSection>
    );
};

interface Props {
    icon: IconSrc;
    title: string;
    description: string;
}
const SecurityCertificate = ({ icon, title, description }: Props) => {
    return (
        <div className="security_certificate">
            <Icon className="security_certificate__icon" icon={icon} />
            <h3>{title}</h3>
            <p>{description}</p>
        </div>
    );
};
