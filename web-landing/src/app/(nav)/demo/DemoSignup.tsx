import { ButtonHTMLAttributes, useCallback, useState } from 'react';

import { API } from '@shared/api';

import { daveKnellQuote3 } from '@landing/components/CustomerQuotes';
import { EmphasisText } from '@landing/components/EmphasisText/EmphasisText';
import { QuoteBlock } from '@landing/components/QuoteSection/QuoteBlock';
import { useLandingDocumentTitle } from '@landing/components/useLandingDocumentTitle';
import { Button } from '@shared/webComponents/Button/Button';
import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';
import { EmailInput } from '@shared/webComponents/TextInput/EmailInput';
import { TextInput } from '@web/components/TextInput/TextInput';

import appDirect from '@clientAssets/landing/logo-app-direct-mono.svg';
import bigCartel from '@clientAssets/landing/logo-big-cartel-mono.svg';
import clio from '@clientAssets/landing/logo-clio-mono.svg';
import cribl from '@clientAssets/landing/logo-cribl-mono.svg';
import drata from '@clientAssets/landing/logo-drata-mono.svg';
import expo from '@clientAssets/landing/logo-expo-mono.svg';
import forto from '@clientAssets/landing/logo-forto-mono.svg';
import humanInterest from '@clientAssets/landing/logo-human-interest-mono.svg';
import measurabl from '@clientAssets/landing/logo-measurabl-mono.svg';
import userTesting from '@clientAssets/landing/logo-user-testing-mono.svg';
import checkIcon from '@clientAssets/success-checkmark.png';

import { faShieldHalved } from '@fortawesome/pro-duotone-svg-icons/faShieldHalved';
import { faVideo } from '@fortawesome/pro-duotone-svg-icons/faVideo';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons/faChevronRight';

import PeopleWithStars from './PeopleWithStars.svg';

import './DemoSignup.scss';

const DemoButton = ({
    icon,
    children,
    url,
    ...props
}: { icon: IconSrc; url: string } & ButtonHTMLAttributes<HTMLAnchorElement>) => (
    <a className="demo_button" {...props} href={url} target="_blank" rel="noreferrer">
        <Icon className="demo_button__icon" icon={icon} size="large" />
        <p className="demo_button__content">{children}</p>
        <Icon className="demo_button__arrow" icon={faChevronRight} size="medium" />
    </a>
);

const DemoSignupForm = ({
    submitDemoRequest,
}: {
    submitDemoRequest: (info: { name: string; email: string; company: string; discovery?: string }) => Promise<void>;
}) => {
    const [name, setName] = useState<string>('');
    const [email, setEmail] = useState<string>('');
    const [isEmailValid, setIsEmailValid] = useState<boolean>();
    const [company, setCompany] = useState<string>('');
    const [discovery, setDiscovery] = useState<string>('');
    const [requested, setRequested] = useState<boolean>(false);

    const onSubmit = useCallback(async () => {
        await submitDemoRequest({ name, email, company, discovery });
        setRequested(true);
    }, [name, email, company, discovery, submitDemoRequest]);

    if (requested) {
        return (
            <div className="demo_signup_form demo_signup_form__requested">
                <Icon icon={checkIcon.src} size={{ height: 120, width: 140 }} />
                <div>
                    <h3>Thanks for booking!</h3>
                    <p className="demo_signup_form__requested__description">
                        A member of the Unblocked team will contact you shortly to schedule a demo.
                    </p>

                    <h5>Want to get a head start? Check out:</h5>

                    <DemoButton icon={faVideo} url="https://docs.getunblocked.com/what-is-unblocked#video-overview">
                        A quick video overview of Unblocked and its features
                    </DemoButton>

                    <DemoButton
                        icon={PeopleWithStars.src}
                        url="https://getunblocked.com/blog/?categories=Customer+stories"
                    >
                        Stories about how our customers use Unblocked
                    </DemoButton>

                    <DemoButton icon={faShieldHalved} url="https://getunblocked.com/security/">
                        More details about our approach to security at Unblocked
                    </DemoButton>
                </div>
            </div>
        );
    }

    return (
        <form className="demo_signup_form">
            <div>
                <p className="demo_signup_form__label">
                    Name:<span>*</span>
                </p>
                <TextInput placeholder="Your full name" value={name} onValueChange={setName} />
            </div>

            <div>
                <p className="demo_signup_form__label">
                    Work email:<span>*</span>
                </p>
                <EmailInput
                    placeholder="<EMAIL>"
                    value={email}
                    onValueChange={(value, isValid) => {
                        setEmail(value);
                        setIsEmailValid(isValid);
                    }}
                />
            </div>

            <div>
                <p className="demo_signup_form__label">
                    Company name:<span>*</span>
                </p>
                <TextInput placeholder="Your company name" value={company} onValueChange={setCompany} />
            </div>

            <div>
                <p className="demo_signup_form__label">How did you hear about Unblocked?</p>
                <TextInput
                    value={discovery}
                    onValueChange={setDiscovery}
                    placeholder="LinkedIn, blog post, recommendation, etc"
                />
            </div>

            <Button disabled={!name || !email || !isEmailValid || !company} type="submit" onClickPromise={onSubmit}>
                Book a Demo
            </Button>
        </form>
    );
};

export const DemoSignup = () => {
    useLandingDocumentTitle(() => 'Book a Demo', []);

    return (
        <section className="demo_signup_container">
            <div className="demo_signup_gradient">
                <div className="demo_signup">
                    <h2 className="demo_signup__header">
                        Save every engineer on your team <EmphasisText>1-2 hours a day</EmphasisText>.
                    </h2>
                    <DemoSignupForm
                        submitDemoRequest={({ name, email, company, discovery }) =>
                            API.demo.postDemoSignup({
                                demoSignupRequest: { name, email, companyName: company, discoveryMethod: discovery },
                            })
                        }
                    />
                    <div className="demo_signup__content">
                        <QuoteBlock quote={daveKnellQuote3} variant="secondary" withUserIcon />
                    </div>
                </div>
            </div>
            <div className="demo_signup__footer">
                <h2>
                    Used by developer teams of <EmphasisText>all sizes</EmphasisText>.
                </h2>
                <div className="demo_signup__logos">
                    <img src={expo.src} />
                    <img src={bigCartel.src} />
                    <img src={clio.src} />
                    <img src={cribl.src} />
                    <img src={appDirect.src} />
                    <img src={forto.src} />
                    <img src={humanInterest.src} />
                    <img src={measurabl.src} />
                    <img src={userTesting.src} />
                    <img src={drata.src} />
                </div>
            </div>
        </section>
    );
};
