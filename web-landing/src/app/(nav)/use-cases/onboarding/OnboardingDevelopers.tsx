import { nirajJayantQuote, steveToutonghiQuote } from '@landing/components/CustomerQuotes';
import { EmphasisText } from '@landing/components/EmphasisText/EmphasisText';
import { UseCase } from '@landing/components/UseCases/UseCaseContainer';
import { UseCaseFeaturesSection } from '@landing/components/UseCases/UseCaseFeaturesSection';
import { UseCaseHeroSection } from '@landing/components/UseCases/UseCaseHeroSection';
import { UseCaseProblemsSection } from '@landing/components/UseCases/UseCaseProblemsSection';

import onboardingHeroLarge from '@clientAssets/usecases/usecase-onboarding-hero-L.png';
import onboardingHeroSmall from '@clientAssets/usecases/usecase-onboarding-hero-S.png';

import { faBrainCircuit } from '@fortawesome/pro-duotone-svg-icons/faBrainCircuit';
import { faEyeSlash } from '@fortawesome/pro-duotone-svg-icons/faEyeSlash';
import { faFaceExplode } from '@fortawesome/pro-duotone-svg-icons/faFaceExplode';
import { faGaugeMin } from '@fortawesome/pro-duotone-svg-icons/faGaugeMin';
import { faHammerCrash } from '@fortawesome/pro-duotone-svg-icons/faHammerCrash';
import { faLightbulb } from '@fortawesome/pro-duotone-svg-icons/faLightbulb';
import { faUserCheck } from '@fortawesome/pro-duotone-svg-icons/faUserCheck';

import './OnboardingDevelopers.scss';

export const OnboardingDevelopers = () => {
    return (
        <UseCase className="use_case__onboarding_developers" quotes={[steveToutonghiQuote, nirajJayantQuote]}>
            <UseCaseHeroSection
                header={
                    <span>
                        <EmphasisText>Onboarding</EmphasisText>
                        <br />
                        new developers
                    </span>
                }
                description={
                    <span>
                        Unblocked helps new team members find the answers they need to so they can start contributing
                        right away.
                    </span>
                }
                imgDesktop={onboardingHeroLarge}
                imgMobile={onboardingHeroSmall}
            />

            <UseCaseProblemsSection
                header={
                    <span>
                        Being new is <b>overwhelming.</b>
                    </span>
                }
                blocks={[
                    {
                        icon: faFaceExplode,
                        header: <>There is an overload of information</>,
                        description: (
                            <>
                                Mastering a complete tech stack is time-consuming, especially when context is scattered
                                across many tools.
                            </>
                        ),
                    },
                    {
                        icon: faGaugeMin,
                        header: <>Answers don&rsquo;t come fast or easily</>,
                        description: (
                            <>
                                Busy mentors, gaps in documentation, and not knowing what to ask can all lead to
                                confusion.
                            </>
                        ),
                    },
                    {
                        icon: faHammerCrash,
                        header: <>It&rsquo;s hard to move fast and not break things</>,
                        description: (
                            <>
                                Everyone wants to start contributing quickly, but costly mistakes are more likely when
                                onboarding is rushed.
                            </>
                        ),
                    },
                ]}
            />

            <UseCaseFeaturesSection
                header={
                    <span>
                        <EmphasisText variant="green">Reduce the time</EmphasisText> to first commit
                    </span>
                }
                blocks={[
                    {
                        icon: faUserCheck,
                        header: <>Provide tailored answers in seconds</>,
                        description: (
                            <>
                                New hires need to know how <em>your</em> codebase works on a deep level. Unblocked
                                augments your source code with existing knowledge, so it can explain detailed concepts
                                as well as your most skilled engineers.
                            </>
                        ),
                    },
                    {
                        icon: faLightbulb,
                        header: <>Put your existing knowledge to work</>,
                        description: (
                            <>
                                Even when code documentation exists, it can be hard to find. Unblocked links to the
                                sources it uses to come up with answers, so new hires can explore important context
                                without having to search endlessly.
                            </>
                        ),
                    },
                    {
                        icon: faEyeSlash,
                        header: <>Make it safe to ask</>,
                        description: (
                            <>
                                No one wants to look silly in front of an experienced team. With incognito mode, new
                                hires can keep their questions private, and ask as many questions as they need without
                                feeling overexposed.
                            </>
                        ),
                    },
                    {
                        icon: faBrainCircuit,
                        header: <>Turn new hires into experts</>,
                        description: (
                            <>
                                Your codebase is a compilation of thousands of past decisions and discussions. Unblocked
                                surfaces this history next to your code, so new teammates get the same context as the
                                original code authors.
                            </>
                        ),
                    },
                ]}
            />
        </UseCase>
    );
};
