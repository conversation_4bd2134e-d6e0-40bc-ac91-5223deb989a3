import { AutoPlayVideo } from '@landing/components/AutoPlayVideo';
import { Button } from '@shared/webComponents/Button/Button';
import { useIntercom } from '@shared/webComponents/Intercom/Intercom';

import articleHero from '@clientAssets/blog/article-008/article-008-preview.jpg';
import macAppShortcutPoster from '@clientAssets/blog/article-008/mac-app-shortcut-poster.png';
import macAppShortcutmp4 from '@clientAssets/blog/article-008/product-update-mac-app-shortcut.mp4';
import macAppShortcutwebm from '@clientAssets/blog/article-008/product-update-mac-app-shortcut.webm';
import getAvatarDennis from '@clientAssets/landing/avatar-dennis-128.jpg';

import { Post, PostTitle } from '../Post';

import '../Article.scss';

export const Post008 = () => {
    const intercom = useIntercom();
    // FIXME
    // useScrollToHash();

    return (
        <Post>
            <PostTitle
                header="Answers, right at your fingertips, with the new Unblocked macOS app"
                author={{ name: '<PERSON>', avatarUrl: getAvatarDennis.src }}
                createdAt="July 2, 2024"
            />
            <div className="articleContent">
                <div className="media">
                    <img src={articleHero.src}></img>
                </div>

                <p>
                    Today we&rsquo;re releasing a brand new mac app for Unblocked - you can{' '}
                    <a href="https://www.getunblocked.com/download">download</a> and try it from our website!
                </p>
                <p>
                    Beta testers have commented on its speed, and that it offers a focused and centralized place to ask
                    questions, get answers, and view your past queries.
                </p>
                <p>
                    If you&rsquo;re an existing user of our macOS menu-bar application, you&rsquo;ll notice that the new
                    app is completely self-contained - questions you ask will no longer result in a new web page being
                    launched.
                </p>
                <p>
                    Just like Unblocked on the <a href="https://www.getunblocked.com/dashboard/login">web</a>, you can
                    use the mac app to ask a wide range of questions about your codebase, from how specific parts of
                    your application work to why particular code was written the way it was. Unblocked augments your
                    codebase with relevant data and discussions from Slack, Jira, Confluence, and others, to give you
                    accurate and useful answers that keep you moving with your work.
                </p>

                <p>
                    The new Unblocked mac app is accessible in your top menu bar and through a keyboard shortcut
                    (Command + Shift + U).
                </p>

                <p>
                    It also has incognito mode, which enables you to ask questions privately so they don&rsquo;t appear
                    in your team&rsquo;s question feed. This feature is especially useful if you&rsquo;re new to a team,
                    or when you&rsquo;re working on an unfamiliar part of the codebase and have lots of questions.
                </p>

                <AutoPlayVideo autoPlay loop poster={macAppShortcutPoster.src}>
                    <source src={macAppShortcutmp4} type='video/mp4; codecs="hvc1"' />
                    <source src={macAppShortcutwebm} type="video/webm" />
                </AutoPlayVideo>

                <p className="add-top-margin">
                    If you&rsquo;ve been using our previous mac hub app, you will be automatically updated to this new
                    version. If you haven&rsquo;t yet tried Unblocked on your mac, you can{' '}
                    <a href="https://www.getunblocked.com/download">download the new app from our website</a>.
                </p>
                <p>
                    {' '}
                    We&rsquo;d love to hear your feedback as you use the new mac app, and are available to answer any
                    questions that may come up.
                </p>
                <p>
                    As always, if you haven&rsquo;t yet received an Unblocked t-shirt, we&rsquo;d love to send you one.{' '}
                    <Button as="link" onClick={() => intercom?.showNewMessage()}>
                        Message us
                    </Button>{' '}
                    with your t-shirt size and mailing address, and phone number, and we&rsquo;ll put a package in the
                    mail ASAP.
                </p>
                <p>
                    If you and your team have feedback on Unblocked, including requests for additional features, please
                    let us know.
                </p>
                <p>Dennis - Founder & CEO</p>
            </div>
        </Post>
    );
};
