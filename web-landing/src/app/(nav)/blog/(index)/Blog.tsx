import { BlogLandingPreview } from '@landing/components/BlogLandingPreview';
import { useLandingDocumentTitle } from '@landing/components/useLandingDocumentTitle';

import article001Hero from '@clientAssets/blog/article-001/article-001-preview.jpg';
import article002Hero from '@clientAssets/blog/article-002/article-002-preview.jpg';
import article003Hero from '@clientAssets/blog/article-003/article-003-preview.jpg';
import article004Hero from '@clientAssets/blog/article-004/article-004-preview.jpg';
import article005Hero from '@clientAssets/blog/article-005/article-005-preview.jpg';
import article006Hero from '@clientAssets/blog/article-006/article-006-preview.jpg';
import article007Hero from '@clientAssets/blog/article-007/article-007-preview.jpg';
import article008Hero from '@clientAssets/blog/article-008/article-008-preview.jpg';
import article009Hero from '@clientAssets/blog/article-009/article-009-preview.jpg';
import article010Hero from '@clientAssets/blog/article-010/article-010-preview.jpg';
import article011Hero from '@clientAssets/blog/article-011/article-011-preview.jpg';
import article012Hero from '@clientAssets/blog/article-012/article-012-preview.jpg';
import article013Hero from '@clientAssets/blog/article-013/article-013-preview.jpg';
import article014Hero from '@clientAssets/blog/article-014/article-014-preview.jpg';
import article015Hero from '@clientAssets/blog/article-015/article-015-preview.jpg';

import './Blog.scss';

export const Blog = () => {
    // useScrollToHash();

    useLandingDocumentTitle(() => 'The Unblocked Blog', []);

    return (
        <>
            <div className="hero landing__hero blog__hero">
                <div className="section__text">
                    <h1>The Unblocked Blog</h1>
                    <p>
                        A creative space for the Unblocked team to share technical knowledge and the latest product
                        announcements.
                    </p>
                </div>
            </div>
            <div className="divider"></div>
            <section className="articleRows">
                <BlogLandingPreview
                    pictureUrl={article015Hero.src}
                    title="Introducing new features that make Unblocked even more helpful in Slack"
                    articleUrl="/blog/slack-dms-and-private-channels"
                    description="We’re expanding the ways teams can get fast, accurate, and relevant answers to their questions in Slack with auto responses, support for private channels, bot DMs, and channel summarizations."
                />
                <BlogLandingPreview
                    pictureUrl={article014Hero.src}
                    title="AppDirect: “Unblocked has been a game-changer for efficiency and focus.”"
                    articleUrl="/blog/customer-story-app-direct"
                    description="For the team at AppDirect, Unblocked checks all the boxes and more, tying together knowledge dispersed across internal platforms and giving developers context right where they’re working. The gains in productivity have more than justified the investment."
                />
                <BlogLandingPreview
                    pictureUrl={article013Hero.src}
                    title="A Year of Unblocking Engineering Teams"
                    articleUrl="/blog/beta-learnings"
                    description="Unblocked launched a year ago to help developers get helpful and accurate answers about their codebase. Since then, we’ve added many new features, saved our customers hours of time every day, and reaffirmed our conviction that we’re solving a real and expensive problem for software development teams."
                />
                <BlogLandingPreview
                    pictureUrl={article012Hero.src}
                    title="Cribl: “Unblocked is our number one tool for finding the information we should know but don’t.”"
                    articleUrl="/blog/customer-story-cribl"
                    description="Cribl’s remote-first culture expands its talent pool and empowers its employees with flexibility, but with more than 100 engineers working across different time zones on a large, shared codebase, keeping everyone on the same page can be a real challenge. That’s why the team turned to Unblocked."
                />
                <BlogLandingPreview
                    pictureUrl={article011Hero.src}
                    title="Connecting your code no matter where it lives"
                    articleUrl="/blog/unblocked-updates-2024-sep-20"
                    description="Unblocked now supports connecting to multiple source code systems at the same time - meaning you and your team can get answers from any repository no matter where it lives (including GitHub, Bitbucket, and GitLab)."
                />
                <BlogLandingPreview
                    pictureUrl={article010Hero.src}
                    title="Drata: “Our team saves 1 to 2 hours per engineer each day with Unblocked”"
                    articleUrl="/blog/customer-story-drata"
                    description="Drata’s globally distributed engineering team relies on Unblocked to get the answers they need about their codebase. With Unblocked, issues get resolved faster, tasks are completed sooner, and projects go more smoothly."
                />
                <BlogLandingPreview
                    pictureUrl={article009Hero.src}
                    title="Your new developer feels like a fraud. It's your onboarding process."
                    articleUrl="/blog/no-imposter-onboarding"
                    description="New engineers face a tough learning curve, navigating a new role and a complex codebase. It’s no wonder they feel like imposters. We need to reimagine onboarding to better support them."
                />
                <BlogLandingPreview
                    pictureUrl={article008Hero.src}
                    title="Answers, right at your fingertips, with the new Unblocked macOS app"
                    articleUrl="/blog/unblocked-mac-app-2024-jul"
                    description="The new Unblocked mac app provides a centralized place to ask questions, get answers, and view your past queries."
                />
                <BlogLandingPreview
                    pictureUrl={article007Hero.src}
                    title="HTTPS Reverse Tunnel, now without the pain and cost"
                    articleUrl="/blog/chissl"
                    description="Built to enable the work of Unblocked's engineering team, chiSSL is a new, lightweight version of chisel that allow you to expose any local server running on your development machine to the internet with a valid SSL certificate all via a single command."
                />
                <BlogLandingPreview
                    pictureUrl={article006Hero.src}
                    title="Unblocked updates: faster responses, better answers about your projects and issues, role-based permissions, and more"
                    articleUrl="/blog/unblocked-updates-2024-may-22"
                    description="A number of new features are now available in Unblocked, helping you and your team get more of the answers you need about your codebase faster. "
                />
                <BlogLandingPreview
                    pictureUrl={article005Hero.src}
                    title="Unblocked IDE plugins now in the marketplace"
                    articleUrl="/blog/unblocked-updates-2024-apr-02"
                    description="Download the Unblocked plugin for Visual Studio Code and JetBrains IDE’s such as IntelliJ, WebStorm, PyCharm and more."
                />

                <BlogLandingPreview
                    pictureUrl={article004Hero.src}
                    title="Unblocked updates: Support for Google Drive, better answers for PRs and more"
                    articleUrl="/blog/unblocked-updates-2024-feb-28"
                    description="The latest updates to Unblocked include features like Google Drive support, enhanced Pull Request question handling, team member usage overview, support for Open Source projects, and persistent suggested follow-ups. Users can now benefit from improved interaction and tailored responses, aiming to provide better answers and a more seamless experience within Unblocked."
                />

                <BlogLandingPreview
                    pictureUrl={article003Hero.src}
                    title="Unblocked updates: IDE improvements and support for all sized teams"
                    articleUrl="/blog/unblocked-updates-2024-feb"
                    description="Today we’re rolling out several new features to Unblocked that we think you’ll enjoy. Unblocked is able to generate helpful answers because it is not limited to the source code you’re browsing - it’s aware of your entire codebase and all the documents from the integrations you’ve connected. Today we’re improving how you ask questions, discover relevant documents / threads / bugs.... and making this available to everyone."
                />
                <BlogLandingPreview
                    pictureUrl={article002Hero.src}
                    title="How we built it: Saving time and money with self-hosted runners on EC2 on-demand / spot instances"
                    articleUrl="/blog/ec2-self-hosted-runners"
                    description="In Unblocked's early days, our dependence on GitHub's hosted runners became a bottleneck for both cost and performance. In response, we created a custom GitHub Action that automatically deploys self-hosted ephemeral runners to EC2 using Spot or On-Demand instances. In this article, we share why and how we've done this — and why it might help you too."
                />
                <BlogLandingPreview
                    pictureUrl={article001Hero.src}
                    title="&ldquo;Get Unblocked&rdquo;"
                    articleUrl="/blog/get-unblocked"
                    description="Today we're publicly launching Unblocked, a new developer tool that increases your productivity by providing helpful and accurate answers to questions about your codebase. We're also announcing our funding: a $8.3M seed round led by Amplify Partners with First Round Capital and XYZ Capital participating alongside angel investors including Stewart Butterfield, John Lilly, and Mike Vernal. But first, let's start with some context. Despite decades of developer tools, development teams are still very inefficient. In 2016, we started buddybuild — a cloud-hosted, continuous integration platform for mobile apps, which was loved and used by tens of thousands of developers. Three years later, buddybuild was acquired by Apple and now lives on as Xcode Cloud."
                />
            </section>
        </>
    );
};
