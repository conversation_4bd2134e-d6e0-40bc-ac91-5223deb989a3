@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

.pricing_faq {
    @include flex-column-start;
    max-width: 1258px;
    padding: 60px 0;

    h2 {
        text-align: left;
        margin-bottom: 46px;
    }

    .faq_item {
        align-items: center;
        padding: 24px 0;
        column-gap: 4px;
        border-top-color: $indigo-40a;

        &.faq_item__selected {
            row-gap: 12px;
        }

        h4 {
            font-weight: $font-weight-bold;
        }

        .icon {
            color: themed($link);
            @include icon-size(large);
        }

        b {
            color: themed($text);
        }
    }

    @include breakpoint(lg) {
        @include flex-start-between;
        flex-direction: row;
        padding: 80px 0;
        gap: 12px;

        h2 {
            flex: 1;
            text-align: start;
        }

        .faq_section__list {
            flex: 2;

            .faq_item {
                h4 {
                    font-weight: $font-weight-bold;
                }

                .icon {
                    @include icon-size(large);
                }
            }
        }
    }
}
