{"name": "web-landing", "source": "public/index.html", "scripts": {"generate-api": "make -C ../api/. generate-api", "fast-deps": "make -C ../api/. fast-client-dependencies", "open-local-browser": "sleep 5 && open http://localhost:9001", "start": "npm run fast-deps && npm run open-local-browser & next dev -p 9001", "build": "next build", "postbuild": "next-sitemap", "check-lint": "npm run check-pretty && npm run check-eslint && npm run check-stylelint", "fix-lint": "npm run fix-eslint && npm run fix-stylelint && npm run fix-pretty", "check-pretty": "prettier --check .", "fix-pretty": "prettier --write .", "check-eslint": "eslint '**/*.{js,jsx,ts,tsx}'", "fix-eslint": "eslint '**/*.{js,jsx,ts,tsx}' --fix", "check-stylelint": "stylelint \"**/*.scss\"", "fix-stylelint": "stylelint \"**/*.scss\" --fix", "test": "npm run check-pretty", "test-only": "jest", "build:dev": "npm run generate-api && next build && next-sitemap", "build:prod": "npm run generate-api && next build && next-sitemap", "analyze": "ANALYZE=true npm run build"}}