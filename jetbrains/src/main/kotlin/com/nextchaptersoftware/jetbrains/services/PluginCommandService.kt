package com.nextchaptersoftware.jetbrains.services

import com.intellij.ide.BrowserUtil
import com.intellij.ide.plugins.PluginManagerConfigurable
import com.intellij.notification.Notification
import com.intellij.notification.NotificationAction
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.nextchaptersoftware.common.ideagent.PluginCommandRequest
import com.nextchaptersoftware.common.ideagent.pluginCommandResponse
import com.nextchaptersoftware.jetbrains.sidebar.LeftSidebarToolWindowService
import com.nextchaptersoftware.jetbrains.sidebar.RightSidebarToolWindowService
import com.nextchaptersoftware.jetbrains.util.DownloadUtils
import com.nextchaptersoftware.jetbrains.util.ProjectCoroutineScope
import com.nextchaptersoftware.jetbrains.util.serviceOrThrow
import kotlinx.coroutines.Job

private val LOG = logger<PluginCommandService>()

@Service(Service.Level.PROJECT)
class PluginCommandService(override val project: Project) : DumbAware, Disposable, ProjectCoroutineScope {

    init {
        LOG.info("PluginCommandService initialized")
    }

    private var notification: Notification? = null

    private val ideAgentJob: Job? = launchWithUpdatingIDEAgent { agentAPI ->
        agentAPI.runPluginCommand(pluginCommandResponse { }).collect { request ->

            when (request.commandCase) {
                PluginCommandRequest.CommandCase.OPENBROWSER -> openUrl(request.openBrowser.url)

                PluginCommandRequest.CommandCase.NOTIFYPLUGINUPDATE -> notifyPluginUpdate()

                PluginCommandRequest.CommandCase.RESTARTIDE -> restartIDE()

                PluginCommandRequest.CommandCase.SHOWMARKETPLACEUI -> showMarketplaceUI()

                PluginCommandRequest.CommandCase.DOWNLOADSVG -> downloadSvg(request.downloadSvg.svg, request.downloadSvg.fileName)

                PluginCommandRequest.CommandCase.OPENRIGHTSIDEBAR ->
                    project.serviceOrThrow<RightSidebarToolWindowService>().openToolbar()

                PluginCommandRequest.CommandCase.OPENLEFTSIDEBAR ->
                    project.serviceOrThrow<LeftSidebarToolWindowService>().openToolbar()

                null, PluginCommandRequest.CommandCase.COMMAND_NOT_SET -> {}
            }
        }
    }

    private fun openUrl(url: String) {
        BrowserUtil.browse(url)
    }

    private fun downloadSvg(content: String, fileName: String) {
        DownloadUtils.downloadSvg(content, fileName)
    }

    private fun notifyPluginUpdate() {
        notification?.let {
            // Expire the current notification if there is one
            if (!it.isExpired) {
                it.expire() // This might not be idempotent depending on the version of IntelliJ running
            }
        }

        notification = Notification(
            "Unblocked",
            "Unblocked has been updated.",
            "Reload to use the latest version!",
            NotificationType.IDE_UPDATE,
        )

        notification?.let { thisNotification ->
            thisNotification.isImportant = true

            thisNotification.whenExpired {
                if (this.notification === thisNotification) {
                    this.notification = null
                }
            }

            thisNotification.addAction(
                object : NotificationAction("Reload") {
                    override fun actionPerformed(e: AnActionEvent, notification: Notification) {
                        restartIDE()
                    }
                },
            )

            Notifications.Bus.notify(thisNotification, project)
        }
    }

    private fun restartIDE() {
        ApplicationManager.getApplication().restart()
    }

    private fun showMarketplaceUI() {
        ApplicationManager.getApplication().invokeLater {
            val configurable = PluginManagerConfigurable()
            configurable.openMarketplaceTab("Unblocked")
            ShowSettingsUtil.getInstance().editConfigurable(project, configurable)
        }
    }

    override fun dispose() {
        LOG.info("PluginCommandService disposed")
        ideAgentJob?.cancel()
    }
}
