<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>${plugin.id}</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>${plugin.name}</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor email="<EMAIL>" url="https://getunblocked.com">Unblocked</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Simple HTML elements (text formatting, paragraphs, and lists) can be added inside of <![CDATA[ ]]> tag.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
<p>Trained on the systems you use, Unblocked provides the answers you need so you can minimize disruptions and spend more time writing code.</p>
<p>With the Unblocked IDE Plugin, you can ask Unblocked questions and view contextual discussions without leaving your IDE.</p>
<h3>Ask Questions</h3>
<p>When you ask questions from the Unblocked extension in your IDE, Unblocked takes into account the context of your active and recently viewed files, along with systems you’ve connected, such as GitHub, Slack, Confluence, Notion, and more.</p>
<h3>View Detailed Discussions</h3>
<p>The Discussions tab shows all the historical conversations, across all your connected systems, for the file you currently have open. You can view discussions for the entire file or scoped to the range of the code you’re currently viewing.</p>
<h3>Line-level Context</h3>
<p>Unblocked extends the gutter of the IDE to highlight helpful merged Pull Request discussions tied to specific lines of code.</p>
]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>
    <depends>Git4Idea</depends>

    <resource-bundle>messages.Unblocked</resource-bundle>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <toolWindow id="UnblockedSidebarToolWindow" icon="/icons/ToolWindowInsights.svg" anchor="left" secondary="true"
                    factoryClass="com.nextchaptersoftware.jetbrains.sidebar.LeftSidebarToolWindowFactory"/>

        <postStartupActivity implementation="com.nextchaptersoftware.jetbrains.Startup"/>

        <codeInsight.linkHandler prefix="#unblocked/"
                                 handlerClass="com.nextchaptersoftware.jetbrains.util.UnblockedTooltipLinkHandler"/>

        <notificationGroup id="Unblocked"
                           displayType="STICKY_BALLOON"/>

        <iconMapper mappingFile="UnblockedIconMappings.json"/>

        <fileEditorProvider implementation="com.nextchaptersoftware.jetbrains.webview.WebviewEditorProvider"/>

    </extensions>

    <actions>
        <group id="Unblocked.Menu" text="Unblocked">
            <action id="Unblocked.Logout" class="com.nextchaptersoftware.jetbrains.util.LogoutAction"
                    text="Unblocked: Logout" description="Unblocked: Logout"/>
            <action id="Unblocked.AskQuestion" class="com.nextchaptersoftware.jetbrains.util.AskQuestionAction"
                    text="Unblocked: Ask a Question" description="Unblocked: Ask a Question">
                <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt u"/>
            </action>
        </group>
    </actions>
</idea-plugin>
