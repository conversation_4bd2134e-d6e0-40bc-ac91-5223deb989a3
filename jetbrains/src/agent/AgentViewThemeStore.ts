import { ViewThemeStore } from '@shared/webComponents/View/ViewThemeStore';
import { ViewTheme } from '@shared/webComponents/View/ViewThemeTypes';
import { LazyValue } from '@shared/webUtils';

import { ThemeMode } from '../../build/generated/source/proto/main/ts_proto/IDEAgent';
import { IDEAgent } from './IDEAgentService';

function ThemeModeToViewTheme(theme: ThemeMode): ViewTheme {
    switch (theme) {
        case ThemeMode.Dark:
            return 'dark';
        case ThemeMode.Light:
            return 'light';

        case ThemeMode.UNRECOGNIZED:
            return 'light';
    }
}

export class AgentViewThemeSource extends ViewThemeStore {
    static get = LazyValue(() => new AgentViewThemeSource());
    constructor() {
        // Initialize with current theme
        const viewTheme = ThemeModeToViewTheme(IDEAgent.getThemeMode());
        super(viewTheme);
    }
}
