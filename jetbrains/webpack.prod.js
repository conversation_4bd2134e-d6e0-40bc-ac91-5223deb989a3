const { merge } = require('webpack-merge');

const baseFn = require('./webpack.base.js');

module.exports = (env, args) => {
    if (!env.PRODUCT_NUMBER || !env.PRODUCT_VERSION) {
        throw new Error(
            'Product number and version are required for production builds.  If you want to make a developer build against the prod environment, run "npm run build:prod --product_number=1 --product_version=development"'
        );
    }

    const base = baseFn(env, args);
    const mergedExtensionConfig = merge(base[0], {
        mode: 'production',
    });

    const mergedWebpackConfig = merge(base[1], {
        mode: 'production',
    });

    return [mergedExtensionConfig, mergedWebpackConfig];
};
