import type { Configuration } from 'webpack';

import { resolve as _resolve } from 'path';
import { rules } from './webpack.rules';
import { plugins } from './webpack.plugins';
import CopyPlugin from 'copy-webpack-plugin';

import path from 'path';
import webpack from 'webpack';

export const baseConfig: Configuration = {
    context: _resolve(__dirname, '.'),
    entry: './src/index.ts',
    resolve: {
        alias: {
            '@desktop': path.resolve(__dirname, 'src'),
            '@shared': _resolve(__dirname, '../shared'),
            '@api': path.resolve(__dirname, 'src/api'),
            '@ide': _resolve(__dirname, '../shared/ide'),
            '@node': _resolve(__dirname, '../shared/node'),
            '@config': _resolve(__dirname, '../shared/config'),
            '@clientAssets': _resolve(__dirname, '../shared/clientAssets'),
            '@shared-api': _resolve(__dirname, '../shared/api'),
            '@shared-hooks': _resolve(__dirname, '../shared/hooks'),
            '@shared-metrics': _resolve(__dirname, '../shared/metrics'),
            '@shared-ide-utils': _resolve(__dirname, '../shared/ide/utils'),
            '@shared-ide-sourcemark': _resolve(__dirname, '../shared/ide/sourcemark'),
            '@shared-ide-sidebar': _resolve(__dirname, '../shared/ide/sidebar'),
            '@shared-ide-sidebar/': _resolve(__dirname, '../shared/ide/sidebar/'),
            '@shared-mocks': _resolve(__dirname, '../shared/mocks'),
            '@shared-node-git': _resolve(__dirname, '../shared/node/git'),
            '@shared-node-process': _resolve(__dirname, '../shared/node/process'),
            '@shared-node-sourcemark': _resolve(__dirname, '../shared/node/sourcemark'),
            '@shared-styles': _resolve(__dirname, '../shared/webComponents/styles'),
            '@shared-video': _resolve(__dirname, '../shared/video'),
            '@shared-web-utils': _resolve(__dirname, '../shared/webUtils'),
            '@shared-health': _resolve(__dirname, '../shared/health'),
            '@shared-ide-components': _resolve(__dirname, '../shared/ide/components'),
            '@shared-ide-components/': _resolve(__dirname, '../shared/ide/components/'),
            '@shared-ide-webview': _resolve(__dirname, '../shared/ide/webview'),
        },
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        fallback: {
            os: false,
            zlib: false,
            http: false,
            https: false,
        },
    },

    stats: {
        children: true,
    },

    module: {
        rules,
    },
    plugins: [
        ...plugins(),

        // Copy tray icons separately.  We don't import these icons because we need them to all get copied,
        // without file name mangling or referencing, so that the different sized icons are used
        new CopyPlugin({
            patterns: [{ from: '**/*', to: 'tray', context: 'src/assets/tray/' }],
        }),
    ],
};

export const webviewConfig: Configuration = {
    entry: './src/index.ts',
    resolve: {
        alias: {
            '@desktop': path.resolve(__dirname, 'src'),
            '@shared': _resolve(__dirname, '../shared'),
            '@ide': _resolve(__dirname, '../shared/ide'),
            '@node': _resolve(__dirname, '../shared/node'),
            '@config': _resolve(__dirname, '../shared/config'),
            '@clientAssets': _resolve(__dirname, '../shared/clientAssets'),
            '@shared-api': _resolve(__dirname, '../shared/api'),
            '@shared-hooks': _resolve(__dirname, '../shared/hooks'),
            '@shared-metrics': _resolve(__dirname, '../shared/metrics'),
            '@shared-ide-utils': _resolve(__dirname, '../shared/ide/utils'),
            '@shared-ide-sourcemark': _resolve(__dirname, '../shared/ide/sourcemark'),
            '@shared-ide-sidebar': _resolve(__dirname, '../shared/ide/sidebar'),
            '@shared-ide-sidebar/': _resolve(__dirname, '../shared/ide/sidebar/'),
            '@shared-mocks': _resolve(__dirname, '../shared/mocks'),
            '@shared-node-git': _resolve(__dirname, '../shared/node/git'),
            '@shared-node-process': _resolve(__dirname, '../shared/node/process'),
            '@shared-node-sourcemark': _resolve(__dirname, '../shared/node/sourcemark'),
            '@shared-styles': _resolve(__dirname, '../shared/webComponents/styles'),
            '@shared-video': _resolve(__dirname, '../shared/video'),
            '@shared-web-utils': _resolve(__dirname, '../shared/webUtils'),
            '@shared-health': _resolve(__dirname, '../shared/health'),
            '@shared-ide-components': _resolve(__dirname, '../shared/ide/components'),
            '@shared-ide-components/': _resolve(__dirname, '../shared/ide/components/'),
            '@shared-ide-webview': _resolve(__dirname, '../shared/ide/webview'),
        },
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        fallback: {
            os: false,
            zlib: false,
            http: false,
            https: false,
            path: false,
            fs: false,
            process: false,
            child_process: false,
        },
    },

    stats: {
        children: true,
    },

    module: {
        rules,
    },
    plugins: [
        ...plugins(),
        new webpack.ProvidePlugin({
            process: 'process/browser',
            Buffer: ['buffer', 'Buffer'],
            setImmediate: ['timers-browserify', 'setImmediate'],
        }),
    ],
};
