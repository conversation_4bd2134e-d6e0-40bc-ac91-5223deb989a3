import { useCallback, useRef } from 'react';

import { NotificationSetupStreamTraits } from '@desktop/components/ClientWorkspace/NotificationSetupStreamTraits';
import enableNotificationsImgLight from '@shared/clientAssets/onboarding/enable-notifications.png';
import enableNotificationsImgDark from '@shared/clientAssets/onboarding/enable-notifications-dark.png';
import { useStream } from '@shared/stores/DataCacheStream';
import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { useViewThemeContext } from '@shared/webComponents/View/ViewThemeContext';

import './NotificationSetup.scss';

interface Props {
    isProcessing: boolean;
}

export function NotificationSetup({ isProcessing }: Props) {
    const store = useRef(
        ClientWorkspace.instance().getControlledStream(NotificationSetupStreamTraits, { $case: 'notificationSetup' })
    );

    // We don't really need the result, this just ensures the stream is initialized
    useStream(() => store.current.stream, []);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const openNotifications = useCallback(() => {
        store.current.sendCommand({ $case: 'openNotificationPreferences' });
    }, []);

    const skipNotificationSetup = useCallback(() => {
        store.current.sendCommand({ $case: 'skipNotificationSetup' });
    }, []);

    const title = isProcessing ? 'Get notified when processing is complete' : 'Get started with Unblocked for Mac';

    const subtitle = isProcessing
        ? "Unblocked is processing the systems you've connected so you can ask questions. Allow notifications so you can be notified when it's complete."
        : "Allow notifications so you can participate in discussions when you're mentioned by anyone on your development team.";

    const { theme } = useViewThemeContext();
    const img = theme === 'light' ? enableNotificationsImgLight : enableNotificationsImgDark;

    return (
        <div className="notification_setup">
            <div className="notification_setup__body">
                <h2>{title}</h2>
                <p>{subtitle}</p>
                <Button size="large" onClick={openNotifications}>
                    Allow Notifications
                </Button>
                <Button as="link" onClick={skipNotificationSetup}>
                    Skip this step
                </Button>
            </div>

            <img src={img} className="notification_setup__image" />
        </div>
    );
}
