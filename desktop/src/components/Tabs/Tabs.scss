@use 'layout' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'theme' as *;
@use 'misc' as *;
@use 'layout-mixin' as *;

$selected-tab-line-size: 0.5px;

.tabs {
    &.tabs__primary,
    &.tabs__settings {
        .tabs__headers {
            position: relative;
            border-radius: $border-radius-6;
            background: themed($tabs-primary-bg);
            border-color: themed($tabs-primary-bg);
            display: grid;
            grid-auto-flow: column;
            grid-auto-columns: 1fr;

            .tab_header {
                flex: none;
                cursor: default;
                font-size: $font-size-12;
                line-height: $line-height-13;
                font-weight: $font-weight-semibold;
                text-align: center;
                color: themed($tabs-primary-fg);
                position: relative;
                padding: $spacer-6 $spacer-12 $spacer-6;
                z-index: 1;

                &::after {
                    pointer-events: none;
                    content: '';
                    width: 1px;
                    height: 60%;
                    right: 0;
                    background: themed($border);
                    position: absolute;
                    top: 50%;
                    transform: translate(0, -50%);
                }

                &::before {
                    pointer-events: none;
                    box-sizing: border-box;
                    content: '';
                    width: 100%;
                    height: 100%;
                    top: 0;
                    right: 0;
                    left: 0;
                    bottom: 0;
                    background: themed($tabs-primary-selected-bg);
                    position: absolute;
                    border-radius: $border-radius-6;

                    border: $border-width $border-style themed($tabs-primary-outline);
                    box-shadow: 0 0.5px 1px 0 themed($tabs-primary-outline);
                    display: none;
                    z-index: -1;
                }

                &.tab_header__selected {
                    * {
                        color: themed($tabs-primary-selected-fg);
                    }
                    &::before {
                        display: inherit;
                    }
                }

                &:last-of-type,
                & + .tab_header__selected {
                    &::after {
                        opacity: 0;
                    }
                }
                &:first-of-type {
                    margin-left: 0;
                }
            }
        }
    }
}
