import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { Team } from '@shared/api/generatedApi';

import { HomeStateStream } from '@desktop/home/<USER>';
import { MainWindowManager } from '@desktop/MainWindow';
import { WebviewBrowserWindow } from '@desktop/webview/WebviewBrowserWindow';
import { PlanStore } from '@shared/stores/PlanStore';
import { LazyValue } from '@shared/webUtils';
import { isExpiredTrialPlan } from '@shared/webUtils/PlanHelpers';

// This allows TypeScript to pick up the magic constants that's auto-generated by <PERSON>ge's Webpack
// plugin that tells the Electron app where to look for the Webpack-bundled app code (depending on
// whether you're running in development or production).
declare const PLAN_EXPIRY_DIALOG_WEBPACK_ENTRY: string;
declare const PLAN_EXPIRY_DIALOG_PRELOAD_WEBPACK_ENTRY: string;

type PlanExpiryStreamType = {
    team: Team;
    teams: Team[];
};

const ShouldShowPlanExpiryStream = LazyValue(
    (): Stream<PlanExpiryStreamType | undefined> =>
        HomeStateStream.instance.stream
            .map((state) => {
                if (state.$case === 'loading' || state.$case === 'missingTeam') {
                    return Stream.of<PlanExpiryStreamType | undefined>(undefined);
                }

                const team = state.currentTeam;
                const teams = state.teams;

                return PlanStore.get(team.id).stream.map((planState) => {
                    const isExpired = planState.$case === 'ready' && isExpiredTrialPlan(planState.plan);
                    return isExpired ? { team, teams } : undefined;
                });
            })
            .flatten()
            .compose(
                dropRepeats((lhs, rhs) => {
                    return lhs?.team.id === rhs?.team.id;
                })
            )
);

const CreatePlanExpiryDialog = () => {
    const window = new WebviewBrowserWindow({
        url: PLAN_EXPIRY_DIALOG_WEBPACK_ENTRY,
        preloadUrl: PLAN_EXPIRY_DIALOG_PRELOAD_WEBPACK_ENTRY,
        vibrancy: 'window',

        resizable: false,
        minimizable: false,
        maximizable: false,

        width: 682,
        height: 408,

        modal: true,
        parent: MainWindowManager.instance.mainWindow,

        // Ensure app is always in the foreground, above other windows
        type: 'panel',
        titleBarStyle: 'hidden',
    });

    return window;
};

export function InitPlanExpiryDialog() {
    let dialog: WebviewBrowserWindow | undefined;

    ShouldShowPlanExpiryStream().subscribe({
        next: (state) => {
            if (dialog) {
                dialog.destroy();
                dialog = undefined;
            }

            if (state) {
                dialog = CreatePlanExpiryDialog();
            }
        },
    });
}
