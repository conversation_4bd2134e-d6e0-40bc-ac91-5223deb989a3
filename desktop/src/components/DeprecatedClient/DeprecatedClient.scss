@use 'layout' as *;
@use 'flex' as *;
@use 'theme' as *;
@use 'fonts' as *;

.deprecated_client {
    height: 100%;
    @include flex-center-center;
    padding: $spacer-20;
    box-sizing: border-box;
    margin-top: -$spacer-20;

    .deprecated_client__content {
        max-width: 400px;
        @include flex-column-center;
        text-align: center;
    }

    h1 {
        font-size: $font-size-28;
        font-weight: $font-weight-bold;
        line-height: $line-height-32;
        margin-bottom: $spacer-12;
    }
    p {
        color: themed($text-secondary);
        font-size: $font-size-14;
        line-height: $line-height-20;
        margin-bottom: $spacer-20;
    }
}
