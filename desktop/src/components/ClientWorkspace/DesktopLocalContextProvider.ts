import dayjs from 'dayjs';

import { HubAPIService } from '@shared/HubAPIServer/HubAPIService';
import { LocalContext, LocalContextProvider } from '@shared/webComponents/ClientWorkspace/LocalContextProvider';
import { logger } from '@shared/webUtils/log';

import { ThreadContextResponse } from '../../../../common/build/generated/source/proto/main/ts_proto/HubIPC';
const log = logger('DesktopLocalContextProvider');
export class DesktopLocalContextProvider implements LocalContextProvider {
    async getContext(teamId: string): Promise<LocalContext | undefined> {
        try {
            const contexts = await HubAPIService.requestThreadContext();
            const matchingContext = contexts.find((v) => isValidContext(teamId, v));
            const threadContext = matchingContext?.context;
            if (!threadContext) {
                return undefined;
            }
            return {
                threadContext,
            };
        } catch (e) {
            log.info('Failed to get local context', e);
            return undefined;
        }
    }
}

const isValidContext = (currentTeamId: string, context: ThreadContextResponse): boolean => {
    if (!context.teamId || currentTeamId.toLocaleLowerCase() !== context.teamId.toLocaleLowerCase()) {
        return false;
    }

    const minutesUntilInvalid = 10;
    return dayjs().subtract(minutesUntilInvalid, 'minutes').isBefore(dayjs());
};
