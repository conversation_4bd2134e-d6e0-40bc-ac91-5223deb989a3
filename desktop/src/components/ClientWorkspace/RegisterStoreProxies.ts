import { DiscussionThreadStore } from '@shared/ide/insight/DiscussionThread/DiscussionThreadStore';
import { DiscussionThreadStoreTraits } from '@shared/ide/insight/DiscussionThread/DiscussionThreadTypes';
import { StoreProxyRegistry } from '@shared/proxy/StoreProxy/StoreProxyRegistry';
import { DownloadStoreTraits } from '@shared/stores/DownloadStoreTraits';
import { RegisterStoreProxies as SharedRegisteredStoreProxies } from '@shared/stores/RegisterStoreProxies';
import { DefaultLoginStore } from '@shared/webComponents/Login/LoginStore';
import { LoginStoreTraits } from '@shared/webComponents/Login/LoginStoreTraits';
import { ViewThemeStoreTraits } from '@shared/webComponents/View/ViewThemeTypes';

import { ElectronDownloadStore } from '../Utils/ElectronDownloadStore';
import { ElectronViewThemeStore } from '../Utils/ElectronViewThemeStore';

export function RegisterStoreProxies() {
    SharedRegisteredStoreProxies();

    const registry = StoreProxyRegistry.instance();
    registry.register(LoginStoreTraits, () => DefaultLoginStore.instance());
    registry.register(DownloadStoreTraits, () => ElectronDownloadStore.instance());
    registry.register(DiscussionThreadStoreTraits, (key) => DiscussionThreadStore.get(key));
    registry.register(ViewThemeStoreTraits, () => ElectronViewThemeStore.get());
}
