@use 'layout' as *;
@use 'misc' as *;
@use 'fonts' as *;
@use 'theme' as *;

$dialog-max-width: 372px; // 420px max-width - (24 * 2)

.modal_dialog {
    .modal_dialog__overlay {
        background: themed($dialog-overlay-bg);
    }

    .modal_dialog__header {
        margin-top: 0;
        color: themed($text);
        font-size: $font-size-14;
        line-height: $line-height-17;
        font-weight: $font-weight-bold;
        margin-bottom: $spacer-2;

        .dialog__close_header {
            & > button {
                justify-self: end;
                top: $spacer-4;
                right: $spacer-8;
                height: max-content;
            }
        }
    }

    .modal_dialog__close_button {
        display: none;
    }

    .modal_dialog__body {
        padding: $spacer-18;
        border-radius: $border-radius-10;
        background: themed($dialog-body-bg);
        width: $dialog-max-width;
        max-height: 70%;
        @include shadow-border-dialog-desktop;

        .modal_dialog__description {
            font-size: $font-size-13;
            line-height: $line-height-17;
            color: themed($text-secondary);
        }

        .modal_dialog__content {
            color: themed($text);
            margin: $spacer-16 0;
        }

        .modal_dialog__buttons {
            display: flex;
            justify-content: flex-end;
        }
    }
}
