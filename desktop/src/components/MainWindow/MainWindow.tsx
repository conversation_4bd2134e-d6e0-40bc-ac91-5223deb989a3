import { Transition } from '@headlessui/react';
import classNames from 'classnames';
import { Fragment, ReactNode, RefObject, useCallback, useEffect, useRef, useState } from 'react';

import { ShortcutToString, ToggleSidebarShortcut } from '@desktop/ShortCutTypes';
import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { FocusManager, FocusManagerForwardedRef } from '@shared/webComponents/FocusManager/FocusManager';
import { ShadowScrollContainer } from '@shared/webComponents/ShadowScrollContainer/ShadowScrollContainer';

import { faSidebar } from '@clientAssets/fa-custom-icons/faSidebar';
import { faSidebarNotification } from '@clientAssets/fa-custom-icons/faSidebarNotification';

import { ToolbarButton } from '../Button/ToolbarButton';
import { SidebarState, SidebarStateStreamTraits } from '../ClientWorkspace/HomeStreamTraits';
import { MainWindowLayoutContextProvider } from './MainWindowLayoutContext';
import { SidebarResizer } from './SidebarResizer';

import './MainWindow.scss';

interface Props {
    toolbar?: ReactNode;
    sidebar?: ReactNode;
    sidebarToolbarRight?: ReactNode;
    sidebarNotification?: boolean;
    toolbarRight?: ReactNode;
    teamSelector?: ReactNode;
    children: ReactNode;
}

type FocusState = 'main' | 'sidebar';

function setToWidth(ref: RefObject<HTMLDivElement>, width: string) {
    if (ref.current) {
        ref.current.style.width = width;
    }
}

function setToCurrentWidth(ref: RefObject<HTMLDivElement>) {
    if (ref.current) {
        ref.current.style.width = ref.current.clientWidth + 'px';
    }
}

export function MainWindow({
    toolbar,
    sidebar,
    sidebarToolbarRight,
    sidebarNotification,
    toolbarRight,
    teamSelector,
    children,
}: Props) {
    const sidebarOpenStream = useRef(
        ClientWorkspace.instance().getControlledStream(SidebarStateStreamTraits, {
            $case: 'sidebarState',
        })
    );

    const [sidebarState, setSidebarState] = useState<SidebarState>({
        size: 0,
        minSize: 0,
        maxSize: 0,
        isExpanded: false,
    });
    const lastSidebarState = useRef<SidebarState>();

    const toggleSidebar = useCallback(() => {
        sidebarOpenStream.current.sendCommand({ $case: 'setIsExpanded', isExpanded: !sidebarState.isExpanded });
    }, [sidebarState]);

    const hasRightToolbarSection = !!teamSelector || !!toolbarRight;

    const hasToolbar = !!toolbar || hasRightToolbarSection || !!sidebar;
    const hasSidebar = !!sidebar;

    const sidebarDivRef = useRef<HTMLDivElement>(null);
    const sidebarWrapperRef = useRef<HTMLDivElement>(null);
    const onResizeSidebar = useCallback((size: number, isDone: boolean) => {
        if (isDone) {
            sidebarOpenStream.current.sendCommand({ $case: 'setSize', size });
        }

        if (sidebarWrapperRef.current) {
            sidebarWrapperRef.current.style.width = `${size}px`;
        }
    }, []);

    const contentRef = useRef<HTMLDivElement>(null);

    const updateSidebarState = useCallback((newState: SidebarState) => {
        // If sidebar div currently does not exist, do not attempt to update size + cache state
        if (!sidebarDivRef.current) {
            return;
        }

        setToWidth(sidebarWrapperRef, newState.size + 'px');

        // If this is the first state update (we are loading from storage), or if we aren't changing
        // the expansion state, simply set the new values
        if (!lastSidebarState.current || lastSidebarState.current.isExpanded === newState.isExpanded) {
            setToWidth(sidebarDivRef, newState.isExpanded ? `auto` : `0`);
        }

        // For all subsequent updates, we try to match the window animation
        else {
            // Animate the sidebar from the current size to the desired size
            setToCurrentWidth(sidebarDivRef);
            requestAnimationFrame(() => setToWidth(sidebarDivRef, newState.isExpanded ? newState.size + 'px' : `0`));

            // Lock the content size while animating
            setToCurrentWidth(contentRef);

            // Revert the above once animation is complete
            setTimeout(() => {
                setToWidth(sidebarDivRef, newState.isExpanded ? `auto` : `0`);
                setToWidth(contentRef, 'auto');
            }, 375);
        }

        lastSidebarState.current = newState;
    }, []);

    const onSidebarStateChanged = useCallback(
        (newState: SidebarState) => {
            // Keep reference to state for when sidebar is loaded.
            setSidebarState(newState);
            updateSidebarState(newState);
        },
        [updateSidebarState]
    );

    // Used to setup initial sidebar state when sidebar is added for the first time.
    useEffect(() => {
        // We are updating a sidebar for the first time -- set state directly wtihout animation
        if (!lastSidebarState.current && hasSidebar) {
            updateSidebarState(sidebarState);
        }

        // We don't have a sidebar -- clear the state so we set the state correctly next time
        if (!hasSidebar) {
            lastSidebarState.current = undefined;
        }
    }, [hasSidebar, sidebarState, updateSidebarState]);
    useStreamEffect(() => sidebarOpenStream.current.stream, [], onSidebarStateChanged);

    const focusSidebarRef = useRef<FocusManagerForwardedRef>(null);
    const focusMainRef = useRef<FocusManagerForwardedRef>(null);

    const toolbarContentClasses = classNames({
        main_window__toolbar_content: true,
        main_window__toolbar_content__toggle_space: sidebar && !sidebarState.isExpanded,
    });

    const resizerClasses = classNames({
        main_window__sidebar__resizer: true,
        main_window__sidebar__resizer__hidden: !sidebarState.isExpanded,
    });

    const getCurrentFocus = useCallback((): FocusState | undefined => {
        const activeElement = document.activeElement;
        const inSidebar = focusSidebarRef.current && focusSidebarRef.current.contains(activeElement);
        const inMain = focusMainRef.current && focusMainRef.current.contains(activeElement);

        if (inSidebar) {
            return 'sidebar';
        }

        if (inMain) {
            return 'main';
        }

        return undefined;
    }, []);

    const toggleFocus = useCallback(() => {
        const currentFocus = getCurrentFocus();

        // If we're currently focused to main, move focus to sidebar
        if (currentFocus === 'main') {
            if (!sidebarState.isExpanded) {
                sidebarOpenStream.current.sendCommand({ $case: 'setIsExpanded', isExpanded: true });
            }
            // setCurrentFocus('sidebar');
            focusSidebarRef.current?.focus();
            return;
        }

        // else default to focusing on main
        focusMainRef.current?.focus();
    }, [getCurrentFocus, sidebarState]);

    return (
        <MainWindowLayoutContextProvider>
            {({ sidebarHeader }) => (
                <div className="main_window">
                    {/*
                Draggable toolbar area -- this is a separate div below all other content so that the draggable
                window region is set correctly
            */}
                    {hasToolbar && <div className="main_window__toolbar_drag" />}

                    {sidebar && (
                        <ShadowScrollContainer hideShadow={!!sidebarHeader}>
                            {({ scrollingRef: sidebarScrollingRef, shadowClassName: sidebarShadowClassName }) => (
                                <>
                                    {/* The collapsible sidebar area */}
                                    <div className="main_window__sidebar" ref={sidebarDivRef}>
                                        <div
                                            className={classNames({
                                                main_window__sidebar__wrapper: true,
                                                'main_window__sidebar__wrapper--header': !!sidebarHeader,
                                            })}
                                            ref={sidebarWrapperRef}
                                        >
                                            {/* The scrolling drop-shadow */}
                                            <div
                                                className={classNames(
                                                    'main_window__sidebar__shadow',
                                                    sidebarShadowClassName
                                                )}
                                            />
                                            {sidebarHeader && (
                                                <div className="main_window__sidebar__header">{sidebarHeader}</div>
                                            )}
                                            {/* The actual sidebar content */}
                                            <div className="main_window__sidebar__content" ref={sidebarScrollingRef}>
                                                <FocusManager cycleParentFocus={toggleFocus} ref={focusSidebarRef}>
                                                    {sidebar}
                                                </FocusManager>
                                            </div>
                                        </div>
                                    </div>

                                    {/* The sidebar resizer */}
                                    <SidebarResizer
                                        className={resizerClasses}
                                        min={sidebarState.minSize}
                                        max={sidebarState.maxSize}
                                        value={sidebarState.size}
                                        onValueChange={onResizeSidebar}
                                    />

                                    {/* The floating sidebar toggle button */}
                                    <div className="main_window__sidebar__toggle">
                                        <ToolbarButton
                                            onClick={toggleSidebar}
                                            icon={sidebarNotification ? faSidebarNotification : faSidebar}
                                            hoverTooltip={ShortcutToString(ToggleSidebarShortcut)}
                                        />
                                    </div>
                                    {sidebarToolbarRight && (
                                        <Transition
                                            show={sidebarState.isExpanded}
                                            as={Fragment}
                                            enter="main_window__sidebar__toolbar_right--transition-enter"
                                            enterFrom="main_window__sidebar__toolbar_right--enter-from"
                                            enterTo="main_window__sidebar__toolbar_right--enter-to"
                                        >
                                            <div className="main_window__sidebar__toolbar_right">
                                                {sidebarToolbarRight}
                                            </div>
                                        </Transition>
                                    )}
                                </>
                            )}
                        </ShadowScrollContainer>
                    )}

                    {/* Toolbar content */}
                    {hasToolbar && (
                        <div className="main_window__toolbar">
                            {toolbar && <div className={toolbarContentClasses}>{toolbar}</div>}
                            {hasRightToolbarSection && (
                                <div className="main_window__toolbar_right">
                                    {toolbarRight && (
                                        <div className="main_window__toolbar_right__content">{toolbarRight}</div>
                                    )}
                                    {teamSelector && (
                                        <div className="main_window__toolbar_team_selector">{teamSelector}</div>
                                    )}
                                </div>
                            )}
                        </div>
                    )}

                    {/* Main ontent */}
                    <div className="main_window__content" ref={contentRef} tabIndex={0}>
                        <FocusManager cycleParentFocus={toggleFocus} ref={focusMainRef}>
                            {children}
                        </FocusManager>
                    </div>
                </div>
            )}
        </MainWindowLayoutContextProvider>
    );
}
