import { createContext, DependencyList, useContext, useEffect, useState } from 'react';

import { EmptyFn } from '@shared/webUtils/TypeUtils';

const MainWindowLayoutContext = createContext<{
    sidebarHeader: React.ReactNode;
    setSidebarHeader: (header: React.ReactNode) => void;
}>({
    sidebarHeader: null,
    setSidebarHeader: EmptyFn,
});

export const MainWindowLayoutContextProvider = ({
    children,
}: {
    children: (args: { sidebarHeader: React.ReactNode }) => React.ReactNode;
}) => {
    const [sidebarHeader, setSidebarHeader] = useState<React.ReactNode>(null);
    return (
        <MainWindowLayoutContext.Provider
            value={{
                sidebarHeader,
                setSidebarHeader,
            }}
        >
            {children({ sidebarHeader })}
        </MainWindowLayoutContext.Provider>
    );
};

export function useMainWindowLayoutContext() {
    return useContext(MainWindowLayoutContext);
}

export const useSidebarHeaderLayout = (content: () => React.ReactNode, dependencies: DependencyList) => {
    const { setSidebarHeader } = useMainWindowLayoutContext();

    useEffect(() => {
        setSidebarHeader(content());

        return () => {
            setSidebarHeader(null);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, dependencies);
};
