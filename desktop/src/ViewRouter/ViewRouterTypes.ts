import { ViewRouterViewState } from '@shared/ide/ViewRouter/ViewRouterTypes';

export type ViewRouterSelectableType = { $case: 'empty' };

export type ViewRouterAllType = ViewRouterSelectableType | 'auth';

export const didViewRouterStateChange = (
    prevState: ViewRouterViewState | null,
    newState: ViewRouterViewState
): boolean => {
    if (!prevState) {
        return true;
    }

    if (prevState.$case !== newState.$case) {
        return true;
    }

    return false;
};
