import { Stream } from 'xstream';

import { createValueStream } from '@shared/stores/ValueStream';
import { LazyValue } from '@shared/webUtils';

export class HasCustoverStream {
    static instance = LazyValue<HasCustoverStream>(() => new HasCustoverStream());

    private valueStream = createValueStream<boolean>(false);
    stream: Stream<boolean> = this.valueStream.stream;

    public update(value: boolean) {
        this.valueStream.updateValue(value);
    }
}
