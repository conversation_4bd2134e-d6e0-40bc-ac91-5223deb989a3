@use 'theme' as *;

pre {
    .hljs-doctag,
    .hljs-keyword,
    .hljs-meta .hljs-keyword,
    .hljs-template-tag,
    .hljs-template-variable,
    .hljs-type,
    .hljs-variable.language_ {
        color: themed($code-keyword);
    }
    .hljs-title,
    .hljs-title.class_,
    .hljs-title.class_.inherited__,
    .hljs-title.function_ {
        color: themed($code-title);
    }
    .hljs-attr,
    .hljs-attribute,
    .hljs-meta,
    .hljs-operator,
    .hljs-selector-attr,
    .hljs-selector-class,
    .hljs-selector-id,
    .hljs-variable {
        color: themed($code-attr);
    }
    .hljs-meta .hljs-string,
    .hljs-regexp,
    .hljs-string {
        color: themed($code-string);
    }
    .hljs-built_in,
    .hljs-symbol,
    .hljs-name,
    .hljs-quote,
    .hljs-literal,
    .hljs-number,
    .hljs-selector-pseudo,
    .hljs-selector-tag {
        color: themed($code-const);
    }
    .hljs-code,
    .hljs-comment,
    .hljs-formula {
        color: themed($code-comment);
    }
    .hljs-subst {
        color: themed($code-substring);
    }
    // .hljs-section {
    //     color: #005cc5;
    //     font-weight: 700;
    // }
    // .hljs-bullet {
    //     color: #735c0f;
    // }
    .hljs-emphasis {
        font-style: italic;
    }
    .hljs-strong {
        font-weight: 700;
    }
}
