import fs from 'fs/promises';
import os from 'os';
import path from 'path';
import { z } from 'zod';

import { fileExists } from '@shared/ide/utils/FileUtils';
import { logger } from '@shared/webUtils/log';

import { ClaudeDesktopInfo, IDELocationResolver } from './IDEInfo';
import { McpServerName } from './McpTypes';
import { NodeInstaller } from './NodeInstaller';

const log = logger('ClaudeDesktopMcpInstaller');

const SettingsSchema = z
    .object({
        mcpServers: z.record(z.string(), z.object({}).passthrough().optional()).optional(),
    })
    .passthrough();

type SettingsType = z.infer<typeof SettingsSchema>;

export class ClaudeDesktopMcpInstaller {
    static async install(mcpScript: string) {
        // If cursor is not installed, we're done
        const claudeInstallation = await IDELocationResolver.instance().findLocation(ClaudeDesktopInfo);

        // <PERSON> desktop isn't installed
        if (!fileExists(this.getSettingsJsonFolder()) && !claudeInstallation) {
            return;
        }

        // Ensure node is installed
        const nodeLocation = await NodeInstaller.instance().ensureInstalled();

        await fs.mkdir(this.getSettingsJsonFolder(), { recursive: true });

        const settingsObject = await this.getSettingsJson();
        settingsObject.mcpServers = settingsObject?.mcpServers ?? {};
        settingsObject.mcpServers[McpServerName] = {
            command: nodeLocation.binPath,
            args: [mcpScript, '--client', 'mcpClaudeDesktop'],
        };

        await fs.writeFile(this.getSettingsJsonPath(), JSON.stringify(settingsObject, null, 2), 'utf-8');
    }

    private static getSettingsJsonFolder() {
        switch (os.platform()) {
            case 'darwin':
                return path.join(os.homedir(), 'Library', 'Application Support', 'Claude');

            case 'win32':
                return path.join(os.homedir(), 'AppData', 'Claude');

            default:
                /* FIXME */
                return path.join(os.homedir(), '.claude');
        }
    }

    private static getSettingsJsonPath() {
        return path.join(this.getSettingsJsonFolder(), 'claude_desktop_config.json');
    }

    private static async getSettingsJson(): Promise<SettingsType> {
        let mcp: string;
        try {
            mcp = await fs.readFile(this.getSettingsJsonPath(), 'utf-8');
        } catch (error) {
            log.warn('Could not read Claude user settings', error);
            // No user settings file -- start one from scratch
            return {};
        }

        try {
            const jsonContent = JSON.parse(mcp);
            return SettingsSchema.parse(jsonContent);
        } catch (error) {
            // If we can't parse the user settings file, we will let exceptions bubble,
            // because we don't want to accidentally edit the user settings in a way that
            // is destructive -- this file is highly customized and important to users.
            // This means MCP installation will fail.
            log.error('Could not read Claude user settings', error);
            throw error;
        }
    }
}
