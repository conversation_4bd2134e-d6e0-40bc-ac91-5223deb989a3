import fs from 'fs/promises';
import * as jsonc from 'jsonc-parser';
import os from 'os';
import path from 'path';

import { fileExists } from '@shared/ide/utils/FileUtils';
import { FileSystemUtils } from '@shared/webUtils/FileSystemUtils';

import { IDELocationResolver, VSCodeIDEInfos } from './IDEInfo';
import { NodeInstaller } from './NodeInstaller';

export class VSCodeMcpInstaller {
    static async install(mcpScript: string) {
        // If cursor is not installed, we're done
        const vscodeInstallations = await IDELocationResolver.instance().findLocations([
            VSCodeIDEInfos.vscode,
            VSCodeIDEInfos.vscodeInsiders,
        ]);

        if (!fileExists(this.getSettingsJsonFolder()) && vscodeInstallations.length === 0) {
            return;
        }

        // Ensure node is installed
        const nodeLocation = await NodeInstaller.instance().ensureInstalled();

        await fs.mkdir(this.getSettingsJsonFolder(), { recursive: true });

        const unblockedMcpServer = {
            unblocked: {
                type: 'stdio',
                command: nodeLocation.binPath,
                args: [mcpScript, '--client', 'mcpVscode'],
            },
        };

        const settingsJsonPath = this.getSettingsJsonPath();

        // If the file doesn't yet exists, write out a new file with the MCP server settings
        if (!FileSystemUtils.exists(settingsJsonPath)) {
            const fullSettingsObj = {
                mcp: {
                    servers: unblockedMcpServer,
                },
            };

            await fs.writeFile(settingsJsonPath, JSON.stringify(fullSettingsObj), 'utf-8');
            return;
        }

        // Otherwise modify the existing file.
        const fileContent = await fs.readFile(settingsJsonPath, 'utf-8');
        const newContent = this.addMcpServer(fileContent, unblockedMcpServer);
        await fs.writeFile(settingsJsonPath, newContent, 'utf-8');
    }

    private static addMcpServer(jsoncContent: string, unblockedMcpServer: object): string {
        // Update an existing VSCode settings file to add the given MCP server definition.
        // Note that VSCode settings files often contain comments -- so we can't use plain
        // JSON operations, use the jsonc library instead
        const jsoncParsed = jsonc.parse(jsoncContent);

        const existingMcpServersRef = jsoncParsed?.mcp?.servers;
        const existingMcpServers: object = typeof existingMcpServersRef === 'object' ? existingMcpServersRef : {};

        const newMcpServers = Object.assign({}, existingMcpServers, unblockedMcpServer);

        const edits = jsonc.modify(jsoncContent, ['mcp', 'servers'], newMcpServers, {});
        return jsonc.applyEdits(jsoncContent, edits);
    }

    private static getSettingsJsonFolder() {
        switch (os.platform()) {
            case 'darwin':
                return path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User');

            case 'win32':
                return path.join(os.homedir(), 'AppData', 'Code', 'User');

            default:
                return path.join(os.homedir(), '.config', 'Code', 'User');
        }
    }

    private static getSettingsJsonPath() {
        return path.join(this.getSettingsJsonFolder(), 'settings.json');
    }
}
