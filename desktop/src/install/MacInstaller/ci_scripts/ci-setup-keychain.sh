#!/bin/sh
set -v
set -e

echo "$MACOS_CERTIFICATE" | base64 --decode > developerId.p12

security create-keychain -p github-actions build.keychain
security set-keychain-settings -lut 21600 build.keychain # Set keychain lock timeout to 6 hours
security unlock-keychain -p github-actions build.keychain
security import developerId.p12 -k build.keychain -f pkcs12 -A -T /usr/bin/codesign -T /usr/bin/security -P "$MACOS_CERTIFICATE_PWD"
security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k github-actions build.keychain
security list-keychains -d user -s build.keychain login.keychain
security default-keychain -s build.keychain
security find-identity -v
