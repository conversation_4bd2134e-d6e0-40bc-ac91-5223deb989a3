{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Desktop: watch:dev", "runtimeExecutable": "${workspaceFolder}/node_modules/@electron-forge/cli/script/vscode.sh", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/@electron-forge/cli/script/vscode.cmd"}, "preLaunchTask": "fast-deps", "env": {"ENVIRONMENT": "dev"}, "cwd": "${workspaceFolder}", "console": "integratedTerminal", "presentation": {"group": "Desktop"}}, {"type": "node", "request": "launch", "name": "Desktop: watch:prod", "runtimeExecutable": "${workspaceFolder}/node_modules/@electron-forge/cli/script/vscode.sh", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/@electron-forge/cli/script/vscode.cmd"}, "preLaunchTask": "fast-deps", "env": {"ENVIRONMENT": "prod"}, "cwd": "${workspaceFolder}", "console": "integratedTerminal", "presentation": {"group": "Desktop"}}]}