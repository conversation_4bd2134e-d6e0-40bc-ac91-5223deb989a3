{"display_information": {"name": "Unblocked [LOCAL]", "description": "Unblocked  - A developer tool used to analyze slack channels"}, "features": {"bot_user": {"display_name": "Unblocked [LOCAL]", "always_online": false}}, "oauth_config": {"redirect_urls": ["https://9nqaftua.ngrok.io/api/auth/slack/exchange"], "scopes": {"user": ["channels:history", "channels:read", "team:read", "usergroups:read", "users.profile:read", "users:read", "users:read.email", "reactions:read"], "bot": ["app_mentions:read", "channels:history", "channels:join", "channels:read", "chat:write", "chat:write.public", "commands", "groups:history", "groups:read", "im:history", "reactions:read", "team:read", "usergroups:read", "users.profile:read", "users:read", "users:read.email"]}}, "settings": {"event_subscriptions": {"request_url": "https://9nqaftua.ngrok.io/api/hooks/slack", "user_events": ["channel_created", "channel_deleted", "channel_rename", "message.app_home", "message.channels", "reaction_added", "reaction_removed", "team_join", "team_rename", "user_change"], "bot_events": ["app_home_opened", "app_mention", "app_uninstalled", "channel_created", "channel_deleted", "channel_rename", "group_deleted", "group_rename", "message.channels", "message.groups", "reaction_added", "reaction_removed", "team_join", "team_rename", "tokens_revoked", "user_change"]}, "org_deploy_enabled": false, "socket_mode_enabled": false, "token_rotation_enabled": false}}