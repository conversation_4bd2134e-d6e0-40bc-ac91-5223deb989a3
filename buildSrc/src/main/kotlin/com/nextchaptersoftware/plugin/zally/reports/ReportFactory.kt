package com.nextchaptersoftware.plugin.zally.reports

import com.nextchaptersoftware.plugin.zally.internal.ZallyViolationRules

object ReportFactory {

    fun getReporter(rules: ZallyViolationRules, config: ZallyReportConfig): Reporter {
        return when (config) {
            is ZallyConsoleReportConfig -> Consol<PERSON><PERSON><PERSON><PERSON><PERSON>(rules = rules, config = config)
            is ZallyJsonReportConfig -> JsonReporter(rules = rules, config = config)
            else -> throw IllegalArgumentException("invalid report config provided")
        }
    }
}
