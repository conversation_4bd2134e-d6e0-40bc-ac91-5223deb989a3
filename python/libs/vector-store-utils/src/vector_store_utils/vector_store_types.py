"""
vector_types.py  –  document-centric VectorStore API (keyword-only)

Key differences from the previous revision
------------------------------------------
* Every public method now begins with a lone '*' so **all** arguments must
  be passed by name.  Positional calls will raise `TypeError`.
"""

from __future__ import annotations
from dataclasses import dataclass
from enum import Enum
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Protocol,
    Sequence,
    Union,
    runtime_checkable,
)

from embedding_utils.embedding_models import SafeDenseVector, SafeSparseVector

# --------------------------------------------------------------------------- #
#                           EmbeddingGenerator stub                           #
# --------------------------------------------------------------------------- #
try:
    from embedding_utils.embedding_generator import EmbeddingGenerator  # type: ignore
except ImportError:  # fallback for type-checking
    class EmbeddingGenerator(Protocol):
        def get_dense_embeddings(self, *, embedding_type: Any, docs: Sequence[str]) -> Any: ...

        def safely_get_sparse_embeddings(self, *, embedding_type: Any, docs: Sequence[str]) -> Any: ...


# --------------------------------------------------------------------------- #
#                               Core documents                                #
# --------------------------------------------------------------------------- #

@dataclass(frozen=True, slots=True)
class BaseDocument:
    id: str
    namespace: str
    metadata: Dict[str, Any]
    embedding: SafeDenseVector = None  # dense
    sparse_embedding: SafeSparseVector = None


# --------------------------------------------------------------------------- #
#                              Filter primitives                              #
# --------------------------------------------------------------------------- #

class FilterOperator(str, Enum):
    EQ = "=="  # equal
    NE = "!="  # not equal
    GT = ">"
    LT = "<"
    GTE = ">="
    LTE = "<="
    IN = "in"
    NIN = "nin"


class FilterCondition(str, Enum):
    AND = "and"
    OR = "or"
    NOT = "not"


@dataclass(slots=True)
class MetadataFilter:
    key: str
    value: Union[str, int, float, List[Union[str, int, float]]]
    operator: FilterOperator = FilterOperator.EQ


@dataclass(slots=True)
class MetadataFilters:
    filters: List[Union["MetadataFilters", MetadataFilter]]
    condition: FilterCondition = FilterCondition.AND


# --------------------------------------------------------------------------- #
#                                 Query model                                 #
# --------------------------------------------------------------------------- #

class QueryMode(str, Enum):
    DENSE = "dense"
    SPARSE = "sparse"
    HYBRID = "hybrid"


@dataclass(slots=True)
class VectorQuery:
    embedding: SafeDenseVector = None
    query_text: Optional[str] = None
    embedder: Optional[EmbeddingGenerator] = None

    top_k: int = 10
    mode: QueryMode = QueryMode.DENSE
    alpha: float = 0.5
    filters: Optional[MetadataFilters] = None


# --------------------------------------------------------------------------- #
#                               Query result row                              #
# --------------------------------------------------------------------------- #

@dataclass(slots=True)
class ScoredDocument:
    id: str
    score: float
    namespace: str
    metadata: Dict[str, Any]


# --------------------------------------------------------------------------- #
#                               VectorStore API                               #
# --------------------------------------------------------------------------- #

@runtime_checkable
class VectorStore(Protocol):
    # — introspection —
    @property
    def client(self) -> Any: ...

    # — CRUD —
    def add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]: ...

    async def async_add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]: ...

    def remove(self, *, document_ids: List[str], namespace: str) -> None: ...

    async def async_remove(self, *, document_ids: List[str], namespace: str) -> None: ...

    def list_by_id_prefix(
            self, *, prefix: str, namespace: str, limit: Optional[int] = None
    ) -> List[str]: ...

    async def async_list_by_id_prefix(
            self, *, prefix: str, namespace: str, limit: Optional[int] = None
    ) -> List[str]: ...

    def delete_by_id_prefix(self, *, prefix: str, namespace: str) -> None: ...

    async def async_delete_by_id_prefix(self, *, prefix: str, namespace: str) -> None: ...

    # — search —
    def query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]: ...

    async def async_query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]: ...


# --------------------------------------------------------------------------- #
#                    Pydantic-friendly abstract base class                    #
# --------------------------------------------------------------------------- #

from pydantic import BaseModel, ConfigDict


class BasePydanticVectorStore(BaseModel):  # noqa: D101
    model_config = ConfigDict(arbitrary_types_allowed=True)

    # — introspection —
    @property
    def client(self) -> Any: ...  # subclasses implement

    # — CRUD (keyword-only) —
    def add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]: ...

    async def async_add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]: ...

    def remove(self, *, document_ids: List[str], namespace: str) -> None: ...

    async def async_remove(self, *, document_ids: List[str], namespace: str) -> None: ...

    def list_by_id_prefix(
            self, *, prefix: str, namespace: str, limit: Optional[int] = None
    ) -> List[str]: ...

    async def async_list_by_id_prefix(
            self, *, prefix: str, namespace: str, limit: Optional[int] = None
    ) -> List[str]: ...

    def delete_by_id_prefix(self, *, prefix: str, namespace: str) -> None: ...

    async def async_delete_by_id_prefix(
            self, *, prefix: str, namespace: str
    ) -> None: ...

    # — query —
    def query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]: ...

    async def async_query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]: ...
