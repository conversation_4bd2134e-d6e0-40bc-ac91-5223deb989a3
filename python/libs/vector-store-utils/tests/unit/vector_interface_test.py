"""
tests/test_vector_interface.py
------------------------------
Basic smoke–tests for the VectorInterface protocol.

• Ensures that a VectorDocument can be serialised.
• Ensures that any *duck-typed* class satisfying the protocol passes
  `isinstance(obj, VectorInterface)`.
"""

from __future__ import annotations
import unittest

from vector_store_utils.vector_interface import (
    VectorInterface,
    VectorDocument,
)

DENSE = [0.1, 0.2, 0.3]
SPARSE = {"indices": [1, 3], "values": [0.9, 0.8]}


class CustomVector:  # deliberately *not* inheriting
    def __init__(self):
        self._dense = DENSE
        self._sparse = SPARSE

    # --- required by the protocol --- #
    def get_dense_vector(self):
        return self._dense

    def get_sparse_vector(self):
        return self._sparse

    def to_dict(self):
        return {
            "id": "custom",
            "values": self._dense,
            "sparse_values": self._sparse,
            "metadata": {"source": "custom"},
        }


class VectorInterfaceTest(unittest.TestCase):
    def test_vector_document_to_dict(self):
        doc = VectorDocument(id="v1", dense=DENSE, sparse=SPARSE, metadata={"foo": "bar"})
        d = doc.to_dict()
        self.assertEqual(d["id"], "v1")
        self.assertEqual(d["values"], DENSE)
        self.assertEqual(d["sparse_values"], SPARSE)
        self.assertEqual(d["metadata"]["foo"], "bar")

    def test_duck_typing_with_protocol(self):
        vec = CustomVector()
        # The Protocol allows runtime isinstance checks
        self.assertIsInstance(vec, VectorInterface)
        self.assertEqual(vec.get_dense_vector(), DENSE)
        self.assertEqual(vec.get_sparse_vector(), SPARSE)


if __name__ == "__main__":  # pragma: no cover
    unittest.main()
