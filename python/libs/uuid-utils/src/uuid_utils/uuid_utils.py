import uuid


class NULL_NAMESPACE:
    bytes = b""


class UuidUtils:
    @staticmethod
    def get_uuid_from_string(string: str) -> uuid.UUID:
        """Java compatible UUID from string"""
        return uuid.uuid3(NULL_NAMESPACE, string)

    @staticmethod
    def get_uuid_from_file(repo_id: str, file_path: str) -> uuid.UUID:
        """Stable UUID representation of a file path in a repo"""
        return UuidUtils.get_uuid_from_string(f"{repo_id}/{file_path}")
