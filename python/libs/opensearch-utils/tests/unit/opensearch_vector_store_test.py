from __future__ import annotations

import os
from typing import List

from embedding_utils.embedding_generator import EmbeddingGenerator

"""
tests/test_opensearch_vector_store.py
-------------------------------------
Integration checks for OpenSearchVectorStore.

Env vars or AWS secret `opensearch-vector-store-it` must supply:
    OPENSEARCH_HOST, OPENSEARCH_USER, OPENSEARCH_PASSWORD

All calls follow the keyword‑only, namespace‑explicit API introduced in
`vector_types.py`.
"""

import asyncio
import logging
import time
import unittest
from uuid import uuid4

from embedding_utils.embedding_models import Embedding<PERSON>ype, SparseVector, DenseVector

from opensearch_utils.opensearch_index_config import (
    IndexConfig,
    FieldConfig,
    FieldType,
    Analyzer,
)
from opensearch_utils.opensearch_vector_store import OpenSearchVectorStore
from vector_store_utils.vector_store_types import (
    BaseDocument,
    VectorQuery,
    QueryMode,
    MetadataFilter,
    MetadataFilters,
    FilterOperator,
)

_LOG = logging.getLogger(__name__)

class DummyEmbeddingGenerator(EmbeddingGenerator):
    def get_embeddings(self, embedding_type: EmbeddingType, docs: List[str]) -> List[DenseVector]:
        return self.get_dense_embeddings(embedding_type, docs)

    def get_dense_embeddings(self, embedding_type: EmbeddingType, docs: List[str]) -> List[DenseVector]:
        return [self._hash_to_dense_vector(doc, dim=768) for doc in docs]

    def get_sparse_embeddings(self, embedding_type: EmbeddingType, docs: List[str]) -> List[SparseVector]:
        return [self._hash_to_sparse_vector(doc, dim=768, num_active=3) for doc in docs]

    def _hash_to_dense_vector(self, doc: str, dim: int) -> DenseVector:
        values = []
        for i in range(dim):
            acc = 0
            for c in doc:
                acc += (ord(c) * (i + 1)) % 97
            values.append((acc % 1000) / 1000.0)  # Normalize to [0, 1]
        return values

    def _hash_to_sparse_vector(self, doc: str, dim: int, num_active: int) -> SparseVector:
        indices = []
        values = []
        for i in range(num_active):
            char = doc[i % len(doc)] if doc else chr(i + 65)
            index = (ord(char) + i * 7) % dim
            val = ((ord(char) * (i + 3)) % 1000) / 1000.0
            indices.append(index)
            values.append(val)
        return {"indices": sorted(indices), "values": values}

_EMB = DummyEmbeddingGenerator()

# ─────────── helper: build a BaseDocument ─────────────────────────── #

def _build_doc(text: str, ns: str, **meta) -> BaseDocument:
    dense = _EMB.get_dense_embeddings(EmbeddingType.DOCUMENT, [text])[0]
    sparse = {"indices": [0, 1, 2], "values": [0.9, 0.8, 0.7]}
    metadata = {"content": text, **meta}
    return BaseDocument(
        id=str(uuid4()),
        namespace=ns,
        metadata=metadata,
        embedding=dense,
        sparse_embedding=sparse,
    )


# ─────────────────────── test case class ──────────────────────────── #

@unittest.skipIf(os.environ.get("CI") == "true", "Disabled in CI environment")
class OpenSearchVectorStoreIT(unittest.TestCase):
    ns = "itest-ns"
    index_name = "itest-os-kws"

    # ---------- class-level setup ----------------------------------- #
    @classmethod
    def setUpClass(cls):
        host = "http://localhost:9200/"
        user = "admin"
        pwd = "Unblocked1!"
        if not all([host, user, pwd]):
            raise unittest.SkipTest("OpenSearch credentials missing")

        cfg = IndexConfig(
            name=cls.index_name,
            shards=1,
            replicas=0,
            fields=[
                FieldConfig("id", FieldType.KEYWORD),
                FieldConfig("embedding", FieldType.VECTOR, dimension=768),
                FieldConfig("rank", FieldType.RANK_FEATURES),
                FieldConfig("metadata.content", FieldType.TEXT, analyzer=Analyzer.STANDARD),
                FieldConfig("metadata.tag", FieldType.KEYWORD),
            ],
        )

        cls.store = OpenSearchVectorStore(
            hosts=host,
            index_name=cls.index_name,
            auth=(user, pwd),
            index_config=cfg,
        )

    # ---------- per-test setup / teardown --------------------------- #
    def _wipe_namespace(self):
        ids = self.store.list_by_id_prefix(prefix="", namespace=self.ns)
        if ids:
            self.store.remove(document_ids=ids, namespace=self.ns)
            time.sleep(2)

    def setUp(self):
        self._wipe_namespace()

    def tearDown(self):
        self._wipe_namespace()

    # ---------- helper to add one doc ------------------------------- #
    def _add_one(self, text: str, **meta) -> BaseDocument:
        doc = _build_doc(text, self.ns, **meta)
        self.store.add(documents=[doc], namespace=self.ns)
        time.sleep(4)
        return doc

    # ---------- tests ---------------------------------------------- #
    def test_add_and_dense_query(self):
        doc = self._add_one("OpenSearch dense query.")
        hits = self.store.query(
            q=VectorQuery(embedding=doc.embedding, top_k=3, mode=QueryMode.DENSE),
            namespace=self.ns,
        )
        self.assertTrue(any(h.id == doc.id for h in hits))

    def test_hybrid_query(self):
        doc = self._add_one("Hybrid rank features search.")
        hits = self.store.query(
            q=VectorQuery(query_text="rank search", embedder=_EMB, mode=QueryMode.HYBRID),
            namespace=self.ns,
        )
        self.assertTrue(any(h.id == doc.id for h in hits))

    def test_filter_by_tag(self):
        keep = self._add_one("Keep me", tag="keep")
        self._add_one("Skip me", tag="skip")

        tag_filter = MetadataFilters(
            filters=[
                MetadataFilter(
                    key="metadata.tag",
                    value="keep",
                    operator=FilterOperator.EQ,
                )
            ]
        )

        hits = self.store.query(
            q=VectorQuery(
                embedding=keep.embedding,
                top_k=5,
                mode=QueryMode.DENSE,
                filters=tag_filter,
            ),
            namespace=self.ns,
        )
        ids = {h.id for h in hits}
        self.assertIn(keep.id, ids)
        self.assertEqual(len(ids), 1)

    def test_list_and_delete_prefix(self):
        doc = self._add_one("Prefix list delete test.")
        pref = doc.id[:8]
        self.assertIn(doc.id, self.store.list_by_id_prefix(prefix=pref, namespace=self.ns))

        self.store.delete_by_id_prefix(prefix=pref, namespace=self.ns)
        time.sleep(2)
        self.assertNotIn(doc.id, self.store.list_by_id_prefix(prefix=pref, namespace=self.ns))

    def test_remove_ids(self):
        d1 = self._add_one("Remove A.")
        d2 = self._add_one("Remove B.")
        self.store.remove(document_ids=[d1.id, d2.id], namespace=self.ns)
        time.sleep(2)

        for d in (d1, d2):
            hits = self.store.query(
                q=VectorQuery(embedding=d.embedding, top_k=1, mode=QueryMode.DENSE),
                namespace=self.ns,
            )
            self.assertFalse(any(h.id == d.id for h in hits))

    def test_async_roundtrip(self):
        doc = _build_doc("Async test.", ns=self.ns)

        async def _task():
            await self.store.async_add(documents=[doc], namespace=self.ns)
            await asyncio.sleep(2)
            hits = await self.store.async_query(
                q=VectorQuery(embedding=doc.embedding, top_k=3),
                namespace=self.ns,
            )
            self.assertTrue(any(h.id == doc.id for h in hits))

        asyncio.run(_task())

    # ---------- class-level teardown ------------------------------- #
    @classmethod
    def tearDownClass(cls):
        try:
            ids = cls.store.list_by_id_prefix(prefix="", namespace=cls.ns)
            if ids:
                cls.store.remove(document_ids=ids, namespace=cls.ns)
                time.sleep(2)
        except Exception as exc:  # pragma: no cover
            _LOG.warning("Final cleanup failed: %s", exc, exc_info=False)
