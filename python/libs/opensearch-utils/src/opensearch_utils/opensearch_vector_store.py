from __future__ import annotations

"""
opensearch_vector_store.py
--------------------------
OpenSearch ≥2.4 adapter

• dense / sparse / hybrid search
• wildcard prefix list & delete on `id`
• AND / OR / NOT metadata filters
• vectors from BaseDocument.embedding / sparse_embedding
• vector & rank_features field names taken from IndexConfig

All public methods are **keyword-only** and require an explicit
`namespace` argument to conform to the latest `vector_types.py` API.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Sequence, Tuple

from opensearchpy import OpenSearch, helpers
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception,
    before_sleep_log,
)

from embedding_utils.embedding_models import EmbeddingType
from pinecone_text.hybrid import hybrid_convex_scale

from logging_utils.unblocked_logger import UnblockedLogger
from opensearch_utils.opensearch_index_config import IndexConfig, FieldType
from vector_store_utils.vector_store_types import (
    BaseDocument,
    FilterCondition,
    FilterOperator,
    MetadataFilter,
    MetadataFilters,
    QueryMode,
    ScoredDocument,
    VectorQuery,
    VectorStore,
)

logger = UnblockedLogger(name=__name__)
_RETRYABLE = (IOError, TimeoutError)


# ───────────────────────── retry helper ───────────────────────────── #

def _retry():
    return retry(
        reraise=True,
        stop=stop_after_attempt(5),
        wait=wait_exponential(min=1, max=60),
        retry=retry_if_exception(lambda e: isinstance(e, _RETRYABLE)),
        before_sleep=before_sleep_log(logger, logging.WARNING),
    )


# ───────────────────── filter → bool DSL ──────────────────────────── #

def _filters_to_bool(filt: Optional[MetadataFilters | MetadataFilter]) -> Dict[str, Any]:
    """Recursively map MetadataFilters → OpenSearch bool structure."""
    if filt is None:
        return {}

    if isinstance(filt, MetadataFilter):
        op = filt.operator
        if op == FilterOperator.EQ:
            return {"term": {filt.key: filt.value}}
        if op == FilterOperator.NE:
            return {"bool": {"must_not": {"term": {filt.key: filt.value}}}}
        if op in {FilterOperator.GT, FilterOperator.LT, FilterOperator.GTE, FilterOperator.LTE}:
            return {
                "range": {
                    filt.key: {
                        {
                            FilterOperator.GT: "gt",
                            FilterOperator.LT: "lt",
                            FilterOperator.GTE: "gte",
                            FilterOperator.LTE: "lte",
                        }[op]: filt.value
                    }
                }
            }
        if op == FilterOperator.IN:
            return {"terms": {filt.key: filt.value}}
        if op == FilterOperator.NIN:
            return {"bool": {"must_not": {"terms": {filt.key: filt.value}}}}
        raise ValueError(op)

    kids = [_filters_to_bool(c) for c in filt.filters]
    if filt.condition == FilterCondition.AND:
        return {"bool": {"must": kids}}
    if filt.condition == FilterCondition.OR:
        return {"bool": {"should": kids, "minimum_should_match": 1}}
    if filt.condition == FilterCondition.NOT:
        return {"bool": {"must_not": kids}}
    raise ValueError(filt.condition)


# ─────────── rank-feature helpers (index + query) ─────────────────── #

def _rank_values(sparse: Optional[Dict[str, Any]], *, top_n: int = 20) -> Optional[Dict[str, float]]:
    """For indexing: sparse → {feature: value}."""
    if not sparse:
        return None
    if "indices" in sparse and "values" in sparse:
        mapping = {str(i): float(v) for i, v in zip(sparse["indices"], sparse["values"])}
    else:
        mapping = {str(k): float(v) for k, v in sparse.items()}
    if not mapping:
        return None
    return dict(sorted(mapping.items(), key=lambda kv: kv[1], reverse=True)[:top_n])


def _rank_feature_queries(
        sparse: Optional[Dict[str, Any]],
        *,
        rank_field: str,
        top_n: int = 20,
) -> List[Dict[str, Any]]:
    """For querying: sparse → list of rank_feature clauses (boost)."""
    vals = _rank_values(sparse, top_n=top_n)
    if not vals:
        return []
    return [
        {
            "rank_feature": {
                "field": f"${rank_field}.{idx}",
                "boost": value,
            }
        }
        for idx, value in vals.items()
    ]


# ─────────────────── OpenSearchVectorStore ────────────────────────── #


class OpenSearchVectorStore(VectorStore):
    """Concrete vector store backed by OpenSearch k-NN & rank_features."""

    # ── init ──────────────────────────────────────────────────────── #

    def __init__(
            self,
            *,
            hosts: str | List[str],
            index_name: str,
            auth: Optional[Tuple[str, str]] = None,
            index_config: IndexConfig,
    ) -> None:
        self._client = OpenSearch(hosts=hosts, http_auth=auth)
        self._index_name = index_name

        vec = [f.name for f in index_config.fields if f.type is FieldType.VECTOR]
        rank = [f.name for f in index_config.fields if f.type is FieldType.RANK_FEATURES]
        assert vec and rank, "IndexConfig must declare VECTOR and RANK_FEATURES"
        self._vec_field, self._rank_field = vec[0], rank[0]

        if not self._client.indices.exists(index_name):
            self.create_index(cfg=index_config)

    # ── index creation helpers ────────────────────────────────────── #

    @_retry()
    def create_index(self, *, cfg: IndexConfig) -> None:
        self._client.indices.create(index=cfg.name, body=cfg.to_index_body())
        logger.info("Created index '%s'", cfg.name)

    async def async_create_index(self, *, cfg: IndexConfig) -> None:
        await asyncio.to_thread(self.create_index, cfg=cfg)

    # ── property ──────────────────────────────────────────────────── #

    @property
    def client(self) -> Any:  # noqa: D401 – simple passthrough
        return self._client

    # ── CRUD operations ──────────────────────────────────────────── #

    @_retry()
    def add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]:
        if not documents:
            return []

        actions = []
        for doc in documents:
            # namespace consistency check & embedding presence
            if doc.namespace and doc.namespace != namespace:
                raise ValueError(
                    f"Document {doc.id} belongs to namespace '{doc.namespace}',"
                    f" but add() was called with '{namespace}'.",
                )
            if doc.embedding is None:
                raise ValueError(f"{doc.id}: dense embedding missing")

            rv = _rank_values(doc.sparse_embedding)
            if rv is None:
                raise ValueError(f"{doc.id}: sparse embedding empty")

            actions.append(
                {
                    "_op_type": "index",
                    "_index": self._index_name,
                    "_id": doc.id,
                    "_source": {
                        "namespace": doc.namespace,
                        "id": doc.id,
                        self._vec_field: doc.embedding,
                        self._rank_field: rv,
                        "metadata": doc.metadata,
                    },
                }
            )
        helpers.bulk(self._client, actions)
        return [d.id for d in documents]

    async def async_add(self, *, documents: Sequence[BaseDocument], namespace: str) -> List[str]:
        return await asyncio.to_thread(self.add, documents=documents, namespace=namespace)

    # ── remove / list / delete prefix ────────────────────────────── #

    @_retry()
    def remove(self, *, document_ids: List[str], namespace: str) -> None:  # noqa: D401 – explicit namespace
        if document_ids:
            helpers.bulk(
                self._client,
                [{"_op_type": "delete", "_index": self._index_name, "_id": i} for i in document_ids],
            )

    async def async_remove(self, *, document_ids: List[str], namespace: str) -> None:
        await asyncio.to_thread(self.remove, document_ids=document_ids, namespace=namespace)

    @_retry()
    def list_by_id_prefix(
            self,
            *,
            prefix: str,
            namespace: str,
            limit: Optional[int] = None,
    ) -> List[str]:
        body = {
            "size": limit or 1000,
            "_source": ["id"],
            "query": {
                "bool": {
                    "must": [
                        {"wildcard": {"id": f"{prefix}*"}},
                        {"term": {"namespace": namespace}},
                    ]
                }
            },
        }
        hits = self._client.search(index=self._index_name, body=body)["hits"]["hits"]
        return [h["_source"]["id"] for h in hits]

    async def async_list_by_id_prefix(
            self,
            *,
            prefix: str,
            namespace: str,
            limit: Optional[int] = None,
    ) -> List[str]:
        return await asyncio.to_thread(self.list_by_id_prefix, prefix=prefix, namespace=namespace, limit=limit)

    @_retry()
    def delete_by_id_prefix(self, *, prefix: str, namespace: str) -> None:
        self._client.delete_by_query(
            index=self._index_name,
            body={
                "query": {
                    "bool": {
                        "must": [
                            {"wildcard": {"id": f"{prefix}*"}},
                            {"term": {"namespace": namespace}},
                        ]
                    }
                }
            },
            refresh=True,
        )

    async def async_delete_by_id_prefix(self, *, prefix: str, namespace: str) -> None:
        await asyncio.to_thread(self.delete_by_id_prefix, prefix=prefix, namespace=namespace)

    # ── query ─────────────────────────────────────────────────────── #

    @_retry()
    def query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]:
        dense = q.embedding
        sparse = None

        # On-the-fly embedding generation
        if dense is None and q.embedder and q.query_text:
            dense = q.embedder.get_dense_embeddings(EmbeddingType.QUERY, [q.query_text])[0]
            safe = q.embedder.safely_get_sparse_embeddings(EmbeddingType.QUERY, [q.query_text])[0]
            if safe:
                sparse = {"indices": safe["indices"], "values": safe["values"]}

        if q.mode in {QueryMode.DENSE, QueryMode.HYBRID} and dense is None:
            raise ValueError("Dense embedding (or embedder) required")

        if q.mode is QueryMode.HYBRID and sparse:
            dense, sparse = hybrid_convex_scale(query_dense=dense, query_sparse=sparse, alpha=q.alpha)

        sparse_boosts = _rank_feature_queries(sparse, rank_field=self._rank_field) if sparse else []

        # filter for namespace + user filters
        filter_bool = {"bool": {"must": [{"term": {"namespace": namespace}}]}}
        meta_bool = _filters_to_bool(q.filters)
        if meta_bool:
            filter_bool["bool"]["must"].append(meta_bool)

        # k-NN clause (skip for SPARSE-only)
        knn_clause = None
        if q.mode in {QueryMode.DENSE, QueryMode.HYBRID}:
            knn_clause = {
                "knn": {
                    self._vec_field: {"vector": dense, "k": q.top_k, "filter": filter_bool}
                }
            }

        final_bool: Dict[str, Any] = {}
        if knn_clause:
            final_bool["must"] = [knn_clause]
        if sparse_boosts:
            final_bool.setdefault("should", []).extend(sparse_boosts)
        if not final_bool:
            raise ValueError("No query clauses generated")

        body = {"size": q.top_k, "query": {"bool": final_bool}}
        hits = self._client.search(index=self._index_name, body=body)["hits"]["hits"]
        return [
            ScoredDocument(
                id=h["_source"]["id"],
                score=h["_score"],
                metadata=h["_source"].get("metadata", {}),
                namespace=h["_source"].get("namespace", namespace),
            )
            for h in hits
        ]

    async def async_query(self, *, q: VectorQuery, namespace: str) -> List[ScoredDocument]:
        return await asyncio.to_thread(self.query, q=q, namespace=namespace)
