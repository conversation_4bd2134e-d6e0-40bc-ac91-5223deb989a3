import hashlib
import os
import httpx
import magic
import asyncio
import logging
from markitdown import MarkItDown

logging.basicConfig(
    format="[%(asctime)s] %(levelname)s (%(module)s) | %(message)s", level=logging.INFO, datefmt="%Y-%m-%d %H:%M:%S"
)


class BinaryExtractor:

    @staticmethod
    async def async_pdf_extractor(file_path: str, llm_url: str) -> str:
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(None, BinaryExtractor.sync_pdf_extractor, file_path)
        return result

    @staticmethod
    def sync_pdf_extractor(file_path: str) -> str:
        converter = MarkItDown()
        try:
            result = converter.convert(file_path)
            return result.text_content
        except Exception as e:
            logging.exception(f"Failed to convert file {file_path}: {e}")

    @staticmethod
    async def async_extractor(file_path: str, llm_url: str) -> str:
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(None, BinaryExtractor.sync_extractor, file_path, llm_url)
        return result

    @staticmethod
    def sync_extractor(file_path: str, llm_url: str) -> str:
        timeout = httpx.Timeout(connect=5.0, read=120.0, write=120.0, pool=None)
        with httpx.Client(timeout=timeout) as client:
            try:
                with open(file_path, "rb") as file:
                    mime = magic.Magic(mime=True)
                    mime_type = mime.from_file(file_path)
                    files = {"file": (os.path.basename(file_path), file, mime_type)}
                    response = client.post(llm_url, files=files)
                    response.raise_for_status()
                    return response.text
            except httpx.TimeoutException as e:
                logging.exception(f"{e}")
                raise

    @staticmethod
    def get_extractor(ext: str):
        map = {
            ".pdf": BinaryExtractor.async_pdf_extractor,
            ".png": BinaryExtractor.async_extractor,
            ".jpg": BinaryExtractor.async_extractor,
            ".jpeg": BinaryExtractor.async_extractor,
        }
        return map.get(ext.lower())

    @staticmethod
    def is_supported(file_path: str) -> bool:
        ext = os.path.splitext(file_path)[1].lower()
        return BinaryExtractor.get_extractor(ext) is not None

    @staticmethod
    async def extract(file_path: str, llm_url: str) -> dict[str, str]:
        ext = os.path.splitext(file_path)[1].lower()
        extractor = BinaryExtractor.get_extractor(ext)
        if extractor:
            content = await extractor(file_path, llm_url)
            header = f"blob {len(content)}\0"
            data = header.encode() + content.encode()
            hash_val = hashlib.sha1()
            hash_val.update(data)

            return {
                "content": content,
                "hash": hash_val.hexdigest(),
            }
        else:
            return {}
