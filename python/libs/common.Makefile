## --------------------------------------------------------
## Common Makefile used by each of the libs.
## --------------------------------------------------------
PYPY_REPO="unblocked-pypi"

warning := \033[33mWarning:\033[0m

# Default target runs code formatting and unit tests
check: format test

format:
	poetry run black .

test:
	poetry run pytest .

clean:
	rm -rf dist/

build: clean
	poetry build

version:
	poetry version minor

setup:
	jf poetry-config --repo-resolve=$(PYPY_REPO)

release: build
	@echo "$(warning) Did you remember to increment the version yet (eg: using 'make version')? [y/N] " && read ans && [ $${ans:-N} = y ]
	jf rt upload dist/ $(PYPY_REPO)/libs/


# These are not file targets
PHONY_TARGETS := check setup format test clean build version release target1

.PHONY: $(PHONY_TARGETS)
