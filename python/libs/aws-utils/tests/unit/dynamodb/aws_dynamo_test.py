import unittest
import uuid

from aws_utils.dynamodb.aws_dynamo import DynamoDbClient


class TestDynamoDbClient(unittest.TestCase):
    def setUp(self) -> None:
        repo_id = uuid.uuid4()
        self.client = DynamoDbClient(table_name="documentPartitions", repo_id=repo_id)

    @unittest.skip("Test is disabled because it requires a MFA token to run")
    def test_get_put_delete_item(self):
        file_path = "file/path"

        self.assertIsNone(self.client.get_item(file_path=file_path))

        self.client.put_item(
            file_path=file_path,
            file_hash="f8400b5653f6721145f2e64dfda0ea140d915a4f",
            partitions=[uuid.uuid4(), uuid.uuid4(), uuid.uuid4()],
        )

        item = self.client.get_item(file_path=file_path)
        self.assertEqual(item["hash"], "f8400b5653f6721145f2e64dfda0ea140d915a4f")
        self.assertEqual(len(item["partitions"]), 3)

        self.client.delete_item(file_path="file/path")

        self.assertIsNone(self.client.get_item(file_path=file_path))


if __name__ == "__main__":
    unittest.main()
