[build-system]
build-backend = 'poetry.core.masonry.api'
requires = ['poetry-core']

[tool]
[tool.black]
extend-exclude = '__pycache__'
include = '\.pyi?$'
line-length = 120

[tool.poetry]
authors = []
description = ''
name = 'unblocked-amq-utils'
version = "0.11.0"

[tool.poetry.dependencies]
pydantic = ">=2.9,<3.0"
python = ">=3.10.12,<3.14"
stomp-py = "^8"
tenacity = "^9"
unblocked-logging-utils = "^0"

[tool.poetry.group]
[tool.poetry.group.dev]
[tool.poetry.group.dev.dependencies]
black = '^24'
pytest = '^8'
pytest-custom-exit-code = "^0"

[[tool.poetry.packages]]
from = 'src'
include = 'amq_utils'

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = "supplemental"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"
