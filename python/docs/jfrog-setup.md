## Prerequisites
### Dependencies
```bash
brew install poetry
brew install jfrog-cli
```

## Warning

> Avoid using most of the `jf poetry` wrappers as they will modify your poetry project files with destructive changes.
Feel free to use any of the standard `poetry` commands.

## Setup

### JFrog
```bash
jf c add
```
For arguments:
* server identifier: unblocked
* JFrog Platform URL: https://getunblocked.jfrog.io/
* Choose Username/Password (1Password7)
  * Username: <EMAIL>
  * Password: ****
* Is the Artifactory reverse proxy configured to accept a client certificate?: n

#### Validate connection
```bash
jf rt ping
```

### Poetry

```bash
# The JFROG_PASSWORD can be found in 1Password
# Notice that the JFrog alias is 'jfrog-server'
poetry config  http-basic.jfrog-server "<EMAIL>" "<JFROG_PASSWORD>"
```

## Exporting Config Tokens
```bash
jf c export
```
