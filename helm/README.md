## Setup
- See Also [Kubernetes README](../docs/kube-setup.md).
- Install Ansible: `brew install ansible`

## Development
The helm structure is as following:
* `helm/baseservice` is the base helm charts for a service. It will be used as a subchart.
* `helm/scaffold` is the scaffolding for a new service.

## Create New Service Helm Charts
1. `make create-service-helm-charts`

## Update Base Service Helm Charts
1. Update any files under helm/baseservice
2. Make sure to update all subcharts using `make update-service-helm-charts` and submit changes.

## Verify your changes locally 
You can render Dev apiservice charts with some dummy cli args by running: `make render-apiservice-helm-charts`