{{- if .Values.secretProviderClass.create }}
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: {{ include "service.fullname" . }}
  labels:
    {{- include "service.labels" . | nindent 4 }}
  annotations:
    {{- include "service.annotations" . | nindent 4 }}
spec:
  provider: aws
  parameters:
    objects: |
{{ .Values.secretProviderClass.objects | indent 8 }}

{{- end }}
