<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Asset" type="JetRunConfigurationType" folderName="web-facing">
    <option name="MAIN_CLASS_NAME" value="com.nextchaptersoftware.assetservice.ApplicationKt" />
    <module name="unblocked.projects.services.assetservice.main" />
    <shortenClasspath name="NONE" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="true" />
      <option name="IS_SUBST" value="true" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="true" />
      <option name="IS_IGNORE_MISSING_FILES" value="true" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH=".run/global.env" />
        <ENTRY IS_ENABLED="true" PARSER="env" IS_EXECUTABLE="false" PATH=".run/AssetService.env" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>