export const SetUtils = {
    // Calculate the difference between set A and B
    difference<T>(a: Iterable<T>, b: Set<T>): T[] {
        const diff = new Array<T>();

        for (const value of a) {
            if (!b.has(value)) {
                diff.push(value);
            }
        }

        return diff;
    },

    // Return the first element, or undefined if the set is empty
    first<T>(set: Set<T>): T | undefined {
        if (set.size === 0) {
            return undefined;
        }

        return set.values().next().value;
    },

    toggle<T>(value: T, set: Set<T>): Set<T> {
        const copy = new Set(set);
        if (!copy.has(value)) {
            copy.add(value);
        } else {
            copy.delete(value);
        }
        return copy;
    },
};
