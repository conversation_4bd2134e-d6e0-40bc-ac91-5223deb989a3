/**
 * Maps a key to potentially multiple values.
 */
export class MultiMap<K, V> {
    private map: Map<K, V[]>;

    constructor(entries?: [K, V][] | undefined) {
        this.map = new Map<K, V[]>();

        if (entries) {
            entries.forEach(([key, value]) => this.add(key, value));
        }
    }

    add(key: K, value: V) {
        const array = this.map.get(key);
        if (!array) {
            this.map.set(key, [value]);
        } else {
            array.push(value);
        }
    }

    remove(key: K, value: V) {
        const array = this.map.get(key);
        if (!array) {
            return;
        }

        const idx = array.indexOf(value);
        if (idx >= 0) {
            array.splice(idx, 1);
        }

        if (array.length === 0) {
            this.map.delete(key);
        }
    }

    get(key: K): V[] {
        return this.map.get(key) ?? [];
    }

    hasKey(key: K): boolean {
        return this.map.has(key);
    }

    has(key: K, value: V): boolean {
        return this.map.get(key)?.includes(value) ?? false;
    }

    clear() {
        this.map.clear();
    }

    get isNotEmpty(): boolean {
        return this.map.size > 0;
    }

    get isEmpty(): boolean {
        return this.map.size === 0;
    }
}
