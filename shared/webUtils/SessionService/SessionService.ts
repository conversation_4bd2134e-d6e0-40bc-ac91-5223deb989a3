import { v1 as uuid, V1Options } from 'uuid';

import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';

import { AsyncLazyValue } from '../AsyncLazyValue';

const SESSION_KEY = 'sessionIdentifier';

export function createSessionIdentifier(options?: V1Options) {
    return uuid(options);
}

export async function initSessionService(): Promise<string> {
    const sessionIdentifier = await getSessionIdentifier();
    if (sessionIdentifier) {
        return sessionIdentifier;
    }
    const newSession = createSessionIdentifier();
    ClientWorkspace.instance().setLocalStorage(SESSION_KEY, newSession);
    return newSession;
}

export async function getSessionIdentifier(): Promise<string | undefined> {
    try {
        return await ClientWorkspace.instance().getLocalStorage(SESSION_KEY);
    } catch {
        return undefined;
    }
}

async function getOrCreateSessionIdentifier(): Promise<string> {
    try {
        const existingKey = await ClientWorkspace.instance().getLocalStorage(SESSION_KEY);
        if (existingKey) {
            return existingKey;
        }

        return initSessionService();
    } catch {
        return initSessionService();
    }
}

export const SessionIdentifier = AsyncLazyValue(() => getOrCreateSessionIdentifier());
