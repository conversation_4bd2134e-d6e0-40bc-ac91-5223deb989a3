import { Block, InlineElement, TeamMember } from '@shared/api/models';

import { ArrayUtils } from './collection';

const createTextBlock = (text: string): Block => {
    return {
        content: {
            $case: 'paragraph',
            paragraph: {
                elements: [
                    {
                        content: {
                            $case: 'text',
                            text: {
                                text,
                            },
                        },
                    },
                ],
            },
        },
    };
};

const createTextBlockWithMention = (text: string, mention: TeamMember): Block => {
    return {
        content: {
            $case: 'paragraph',
            paragraph: {
                elements: [
                    createMentionElement(mention),
                    createSpaceElement(),
                    {
                        content: {
                            $case: 'text',
                            text: {
                                text,
                            },
                        },
                    },
                ],
            },
        },
    };
};

const createTextBlocks = (text: string): Block[] => text.split(/\r?\n/).map(createTextBlock);

const createSpaceElement = (): InlineElement => {
    return {
        content: {
            $case: 'text',
            text: {
                text: ' ',
            },
        },
    };
};

const createMentionElement = (mention: TeamMember): InlineElement => {
    return {
        content: {
            $case: 'mention',
            mention: {
                teamMemberId: mention.id,
                username: mention.identity.username ?? mention.identity.displayName,
                displayName: mention.identity.displayName,
            },
        },
    };
};

const createMentionBlock = (mention: TeamMember, withTrailingSpace = true): Block => {
    return {
        content: {
            $case: 'paragraph',
            paragraph: {
                elements: ArrayUtils.compact([
                    createMentionElement(mention),
                    withTrailingSpace ? createSpaceElement() : undefined,
                ]),
            },
        },
    };
};

const getAllInlineElements = (blocks: Block[]): InlineElement[] => {
    return blocks.flatMap((block) => getAllInlineElementsForSingle(block));
};

const getAllInlineElementsForSingle = (block: Block): InlineElement[] => {
    if (!block.content) {
        return [];
    }
    switch (block.content.$case) {
        case 'paragraph':
            return block.content.paragraph.elements;
        case 'list':
            return block.content.list.items.flatMap((item) => getAllInlineElements(item.blocks));
        case 'quote':
            return getAllInlineElements(block.content.quote.blocks);
        default:
            return [];
    }
};

const getTitleText = (blocks: Block[]): string => {
    for (const block of blocks) {
        if (block.content?.$case === 'paragraph') {
            const text = ArrayUtils.compactMap(block.content.paragraph.elements, (element) =>
                element.content?.$case === 'text' ? element.content.text.text : undefined
            ).join(' ');

            if (text.length > 0) {
                return text;
            }
        }
    }

    return '';
};

const getAllTeamMembers = (blocks: Block[]): string[] => {
    const inlineElements = getAllInlineElements(blocks);
    return ArrayUtils.compactMap(inlineElements, (element) => {
        if (element.content?.$case !== 'mention') {
            return undefined;
        }
        return element.content.mention.teamMemberId;
    });
};

const appendMentionBlock = (
    blocks: Block[],
    mention: TeamMember,
    withTrailingSpace = true,
    dedupeMentions = true
): Block[] => {
    const lastBlock = ArrayUtils.lastOrUndefined(blocks);
    if (!lastBlock || !lastBlock.content) {
        return [createMentionBlock(mention, withTrailingSpace)];
    }

    if (dedupeMentions) {
        const allTeamMembers = getAllTeamMembers(blocks);
        if (allTeamMembers.includes(mention.id)) {
            return blocks;
        }
    }

    if (lastBlock.content.$case !== 'paragraph') {
        return [...blocks, createMentionBlock(mention, withTrailingSpace)];
    }
    const paragraphBlock = lastBlock.content.paragraph;
    const lastElementInParagraph = ArrayUtils.lastOrUndefined(paragraphBlock.elements);

    const isLastElementInParagraphASpace = () => {
        if (lastElementInParagraph?.content?.$case !== 'text') {
            return false;
        }
        const text = lastElementInParagraph.content.text.text;
        const lastChar = text.charAt(text.length - 1);
        return lastChar === ' ';
    };

    if (!isLastElementInParagraphASpace()) {
        paragraphBlock.elements.push(createSpaceElement());
    }
    paragraphBlock.elements.push(createMentionElement(mention));
    if (withTrailingSpace) {
        paragraphBlock.elements.push(createSpaceElement());
    }

    return blocks;
};

export const BlockUtils = {
    createTextBlock,
    createTextBlockWithMention,
    createTextBlocks,
    createMentionBlock,
    appendMentionBlock,
    createMentionElement,
    getAllInlineElements,
    getTitleText,
};
