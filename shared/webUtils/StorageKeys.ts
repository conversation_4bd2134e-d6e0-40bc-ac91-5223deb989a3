export const StorageKeys = {
    loginCompletionUrl: 'LOGIN_COMPLETION_URL',

    isExtensionInstalled: 'UB_EXTENSION_INSTALLED',
    ignoreHubFallback: 'IGNORE_HUB_FALLBACK',
    selectedTeamId: 'SELECTED_TEAM_ID',
    demoModeOriginUrl: 'DEMO_MODE_ORIGIN_URL',
    demoModeOriginProvider: 'DEMO_MODE_ORIGIN_PROVIDER',
    demoModeTitleLabel: 'DEMO_MODE_TITLE_LABEL',
    demoModeButtonLabel: 'DEMO_MODE_BUTTON_LABEL',
    showUnreads: 'SHOW_UNREADS_ONLY',
    discussionsFilterType: 'DISCUSSIONS_FILTER_TYPE',
    showExtensionUnreads: 'SHOW_EXT_UNREADS_ONLY',
    relatedPullRequestsState: 'RELATED_PULL_REQUEST_STATE',
    currentFileThreadsState: 'CURRENT_FILE_THREADS_STATE',
    selectedSettingsTeamId: 'SELECTED_SETTINGS_TEAM_ID',
    showOnlyA<PERSON>untMembers: 'SHOW_ONLY_ACCOUNT_MEMBERS',

    showExtensionBanner: 'SHOW_EXTENSION_BANNER',
    showInviteLinkToast: 'SHOW_INVITE_LINK_TOAST',
    showSlackStateBumperInQa: 'SHOW_SLACK_STATE_BUMPER',
    showSlackLinkBanner: 'SHOW_SLACK_LINK_BANNER',

    explorerInsightsSort: 'EXPLORER_INSIGHTS_SORT',
    explorerInsightFilter: 'EXPLORER_INSIGHTS_FILTER',
    usageTableSort: 'USAGE_TABLE_SORT',

    // Set in session storage when onboarding, to specify that we onboarded from the desktop app
    onboardingFromDesktopKey: 'ONBOARDING_FROM_DESKTOP',
};
