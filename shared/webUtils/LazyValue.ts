interface LazyResolvedValue<T> {
    (): T;
}

/*
    Create a lazy value -- this is a value that is only created on demand, when
    the code using this value uses it as a function: someLazyValue()

    This is used to improve startup performance and ensure that tests can run
    without bringing up the entire system.
*/
export function LazyValue<T>(generator: () => T): LazyResolvedValue<T> {
    let value: T | undefined;

    return (): T => {
        if (!value) {
            value = generator();
        }
        return value;
    };
}
