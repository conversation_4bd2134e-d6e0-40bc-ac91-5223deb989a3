import { StringPluralCounts } from '@shared/webUtils/StringsHelper/StringsHelper';

export interface CIProviderTraits {
    readonly hasProjects: boolean;
    readonly supportsProjectSlugs: boolean;
    readonly projectSlugPlaceholder?: string;
    readonly projectsLabelCounts?: StringPluralCounts;
    readonly docsUrl: string;
    readonly requiresPats: boolean;
    readonly requiresWebhookSetup: boolean;
}
