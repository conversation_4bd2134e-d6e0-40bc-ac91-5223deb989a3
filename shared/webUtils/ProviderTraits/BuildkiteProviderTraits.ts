import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class BuildkiteProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Buildkite';
    readonly shortenedDisplayName: string = this.displayName;
    readonly adminNode: string = 'Project Admin';
    readonly determinateAdminNodedminNode: string = 'a Project Admin';

    projectLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Buildkite project', plural: 'Buildkite projects' }
            : { singular: 'project', plural: 'projects' };
    }

    workspaceLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Buildkite project', plural: 'Buildkite projects' }
            : { singular: 'project', plural: 'projects' };
    }

    orgNode(short?: boolean): string {
        return short ? 'Project' : 'Buildkite Project';
    }

    iconSrc(): IconSrc {
        return BrandIcons.buildkite;
    }

    isCiProvider(): boolean {
        return true;
    }
}
