import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class MicrosoftEntraProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Microsoft Entra SSO';
    readonly shortenedDisplayName: string = 'Microsoft Entra';

    projectLabelCounts(): StringPluralCounts {
        return { singular: 'content', plural: 'content' }; // Default value from ProviderUtils for MicrosoftEntra
    }

    workspaceLabelCounts(): StringPluralCounts {
        return { singular: 'team', plural: 'teams' }; // Default value from ProviderUtils for MicrosoftEntra
    }

    iconSrc(): IconSrc {
        return BrandIcons.microsoftEntra;
    }
}
