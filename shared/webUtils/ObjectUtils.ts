/**
 * Lodash Get alternative
 * https://dev.to/tipsy_dev/advanced-typescript-reinventing-lodash-get-4fhe
 *
 * Useful for parsing data from JSON
 */
type GetFieldType<Obj, Path> = Path extends `${infer Left}.${string}`
    ? Left extends keyof Obj
        ? Obj[Left]
        : undefined
    : Path extends keyof Obj
      ? Obj[Path]
      : undefined;

function getValue<TData, TPath extends string, TDefault = GetFieldType<TData, TPath>>(
    data: TData,
    path: TPath,
    defaultValue?: TDefault
): GetFieldType<TData, TPath> | TDefault {
    const value = path
        .split(/[.[\]]/)
        .filter(Boolean)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .reduce<GetFieldType<TData, TPath>>((value, key) => (value as any)?.[key], data as any);

    return value !== undefined ? value : (defaultValue as TDefault);
}

/**
 * Returns an object only containing the properties that differ between lhs and rhs.
 * Specifically, for proerties in lhs, any properties that are different in rhs will be returned.
 * If no properties changed, returns undefined
 */
function getChangedPropertiesOrUndefined<T extends Record<string, unknown>>(lhs: T, rhs: T): Partial<T> | undefined {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const entries = Object.entries(lhs) as [keyof T, any][];
    const changedEntries = entries.filter(([key, value]) => rhs[key] !== value);

    if (!changedEntries.length) {
        return undefined;
    }

    const changes: Partial<T> = {};
    changedEntries.forEach(([key]) => (changes[key] = rhs[key]));
    return changes;
}

/**
 * Returns an object only containing the properties that differ between lhs and rhs.
 * Specifically, for proerties in lhs, any properties that are different in rhs will be returned.
 */
function getChangedProperties<T extends Record<string, unknown>>(lhs: T, rhs: T): Partial<T> {
    return getChangedPropertiesOrUndefined(lhs, rhs) ?? {};
}

/**
 * For all properties in lhs, returns whether rhs has the same property values or not.
 */
function arePropertiesEqual<T extends Record<string, unknown>>(lhs: T, rhs: T): boolean {
    for (const [key, value] of Object.entries(lhs)) {
        if (rhs[key] !== value) {
            return false;
        }
    }

    return true;
}

export const ObjectUtils = {
    getValue,
    getChangedProperties,
    getChangedPropertiesOrUndefined,
    arePropertiesEqual,
};
