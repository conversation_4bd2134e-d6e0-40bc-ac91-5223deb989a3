export const UserAgentParser = {
    browser: (value?: string) => {
        const string = value ?? window.navigator.userAgent;

        /**
         * parsing logic taken from:
         * https://developer.mozilla.org/en-US/docs/Web/HTTP/Browser_detection_using_the_user_agent#browser_name
         */
        if (string.includes('Chrome/') && string.indexOf('Chromium/') === -1) {
            return 'Chrome';
        }
        if (string.includes('Safari/') && string.indexOf('Chromium/') === -1 && string.indexOf('Chrome/') === -1) {
            return 'Safari';
        }
        if (string.includes('Firefox/') && string.indexOf('Seamonkey/') === -1) {
            return 'Firefox';
        }
        if (string.includes('; MSIE xyz;') || string.includes('Trident/7.0; .*rv:xyz')) {
            return 'Internet Explorer';
        }
        if (string.includes('OPR/') || string.includes('Opera/')) {
            return 'Opera';
        }

        // default to Chrome
        return 'Chrome';
    },

    platform: (value?: string) => {
        const string = value ?? window.navigator.userAgent;

        /**
         * values taken from:
         * https://www.useragents.me
         */
        if (string.includes('(Windows')) {
            return 'windows';
        }

        if (string.includes('(Macintosh;')) {
            return 'mac';
        }

        if (string.includes('(X11;')) {
            return 'linux';
        }

        if (string.includes('(Linux;') && string.includes('Android')) {
            return 'android';
        }

        if (
            string.includes('(iPhone;') ||
            string.includes('(iPad;') ||
            string.includes('(iPod;') ||
            string.includes('(iPod touch;')
        ) {
            return 'ios';
        }
    },
};
