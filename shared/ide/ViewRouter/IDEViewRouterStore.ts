import { Stream } from 'xstream';

import { ValueStream } from '@shared/stores/NewValueStream';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';

import { SidebarType, SidebarTypeStream } from '../sidebar/SidebarTypeStream';
import { IDEViewRouterKey } from './IDEViewRouterStoreTypes';
import { ViewRouterSelectableType, ViewRouterViewState } from './ViewRouterTypes';

export abstract class IDEViewRouterStore {
    private userSelectableStateStream: ValueStream<ViewRouterSelectableType>;
    get selectedStateStream(): Stream<ViewRouterSelectableType> {
        return this.userSelectableStateStream;
    }

    // The resulting stream
    readonly stream: Stream<ViewRouterViewState>;

    constructor(defaultSelectableState: ViewRouterSelectableType) {
        this.userSelectableStateStream = new ValueStream<ViewRouterSelectableType>(defaultSelectableState);
        this.stream = Stream.combine(SidebarTypeStream(), this.userSelectableStateStream)
            .map(([sidebarType, selectableState]) => {
                const sidebarState = this.sidebarToRouter(sidebarType) ?? this.selectableToRouter(selectableState);
                return this.determineState(sidebarState);
            })
            .remember();
    }

    select(newState: ViewRouterSelectableType) {
        this.userSelectableStateStream.value = newState;
    }

    protected determineState(state: ViewRouterViewState): ViewRouterViewState {
        return state;
    }

    private sidebarToRouter(sidebarType: SidebarType): ViewRouterViewState | undefined {
        switch (sidebarType) {
            case SidebarType.RepoSelector:
                return { $case: 'missingRepos' };
            case SidebarType.Auth:
                return { $case: 'auth' };
            case SidebarType.Deprecated:
                return { $case: 'deprecated' };
            case SidebarType.Pending:
                return { $case: 'pending' };
            case SidebarType.RequiresConnection:
                return { $case: 'install' };
            case SidebarType.RequiresSSO:
                return { $case: 'requireSSO' };
            case SidebarType.Offline:
                return { $case: 'offline' };
            case SidebarType.Loading:
                return { $case: 'loading' };

            // FIXME TODO explorer sidebar
        }
    }

    private selectableToRouter(state: ViewRouterSelectableType): ViewRouterViewState {
        switch (state.$case) {
            case 'ask':
            case 'myQuestions':
            case 'install':
            case 'explorerInsights':
            case 'empty':
                return state;

            case 'discussion':
                return {
                    $case: 'discussion',
                    discussionThreadState: {
                        key: {
                            teamId: state.teamId,
                            repoId: state.repoId,
                            threadId: state.threadId,
                        },
                    },
                };

            case 'pr':
                return {
                    $case: 'pr',
                    teamId: state.teamId,
                    repoId: state.repoId,
                    pullRequestId: state.prId,
                    threadId: state.threadId,
                };
        }
    }
}

// VSCode explorer sidebar store
export class ExplorerSidebarStore extends IDEViewRouterStore {
    constructor() {
        super({ $case: 'explorerInsights', tab: 'all' });
    }
    determineState(state: ViewRouterViewState): ViewRouterViewState {
        switch (state.$case) {
            case 'auth':
            case 'requireSSO':
            case 'deprecated':
            case 'install':
                return { ...state, mini: true };
            case 'explorerInsights':
            case 'loading':
            case 'pending':
                return state;
            default:
                return { $case: 'empty' };
        }
    }
}

// VSCode content sidebar store
export class MainSidebarStore extends IDEViewRouterStore {
    constructor() {
        super({ $case: 'ask' });
    }
}

// VSCode editor store
export class EditorStore extends IDEViewRouterStore {
    constructor() {
        super({ $case: 'empty' });
    }
}

export class LeftSidebarStore extends IDEViewRouterStore {
    constructor() {
        super({ $case: 'explorerInsights', tab: 'all' });
    }

    determineState(state: ViewRouterViewState): ViewRouterViewState {
        switch (state.$case) {
            case 'missingRepos':
                return { ...state, mini: true };
            default:
                return state;
        }
    }
}

export class RightSidebarStore extends IDEViewRouterStore {
    constructor() {
        super({ $case: 'ask' });
    }

    determineState(state: ViewRouterViewState): ViewRouterViewState {
        switch (state.$case) {
            case 'ask':
            case 'discussion':
            case 'myQuestions':
            case 'pr':
                return state;

            default:
                return { $case: 'empty' };
        }
    }
}

export const IDEViewRouterStores = {
    get: LazyValueCache((key: IDEViewRouterKey) => {
        switch (key) {
            case 'explorer':
                return new ExplorerSidebarStore();
            case 'main':
                return new MainSidebarStore();
            case 'editor':
                return new EditorStore();
            case 'left':
                return new LeftSidebarStore();
            case 'right':
                return new RightSidebarStore();
        }
    }),
};
