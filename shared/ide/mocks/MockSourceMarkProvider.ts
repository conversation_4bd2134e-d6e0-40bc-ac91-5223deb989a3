/* eslint-disable @typescript-eslint/no-unused-vars */
import { Stream } from 'xstream';

import { UnsavedChangeTracker } from '@shared/node/sourcemark/engine/UnsavedChangeTracker';
import { createValueStream, ValueStream } from '@shared/stores/ValueStream';

import { Mark } from '../../api/models';
import { ISourceMarkProvider, RepoInfo, SourcePointResolution } from '../../node/sourcemark';
import { FilePath } from '../../webUtils';

export class MockSourceMarkProvider implements ISourceMarkProvider {
    hybridSourcemarkEngine: boolean = true;

    private fileMap = new Map<string, Mark[]>();
    private streams = new Array<{ filePath: string; stream: ValueStream<Mark[]> }>();

    getSourceMarkLatestPoint(): Promise<SourcePointResolution> {
        throw new Error('Method not implemented.');
    }

    recalculateRepo(): Promise<void> {
        return Promise.resolve(undefined);
    }

    getSourceMarkStreamForFile(
        repo: RepoInfo,
        file: FilePath,
        unsavedChangeTracker: UnsavedChangeTracker,
        refreshStream: Stream<void>
    ): Stream<Mark[]> {
        const stream = createValueStream<Mark[]>(this.fileMap.get(file.value) ?? []);

        this.streams.push({
            filePath: file.value,
            stream,
        });

        refreshStream.subscribe({
            next: () => stream.updateValue(this.fileMap.get(file.value) ?? []),
        });

        return stream.stream;
    }

    // Not a part of the SourceaMarkProvider API, used by tests to fake updating the marks
    updateMarksForFile(repoId: string, filePath: string, marks: Mark[]) {
        this.fileMap.set(filePath, marks);

        this.streams
            .filter((stream) => stream.filePath === filePath)
            .forEach((stream) => stream.stream.updateValue(marks));
    }

    addActiveFiles(/* repo: RepoInfo, files: FilePath[] */) {
        // FIXME IMPL
    }
}
