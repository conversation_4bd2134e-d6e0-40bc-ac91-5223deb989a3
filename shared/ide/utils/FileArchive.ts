import fs from 'fs/promises';
import StreamZip from 'node-stream-zip';
import { extract } from 'tar';

import { logger } from '@shared/webUtils/log';

import { generateTempFilePath } from './FileUtils';

type PackageType = 'targz' | 'zip' | 'unknown';

const log = logger('FileArchive');

/**
 * Handles uncompressiong of zip and tar.gz files
 */
export class FileArchive {
    static async uncompressAndRun(sourcePath: string, action: (path: string) => Promise<void>): Promise<void> {
        const extractPath = await generateTempFilePath({ create: true });
        try {
            await this.uncompress(sourcePath, extractPath);
            await action(extractPath);
        } finally {
            try {
                await fs.rm(extractPath, { recursive: true });
            } catch {}
        }
    }

    static async uncompress(sourcePath: string, destPath: string) {
        switch (this.getType(sourcePath)) {
            case 'targz':
                return await this.uncompressTargz(sourcePath, destPath);

            case 'zip':
                return await this.uncompressZip(sourcePath, destPath);

            case 'unknown':
                log.error('Archive type could not be determined', { sourcePath });
                throw new Error('Archive type could not be determined');
        }
    }

    private static async uncompressTargz(sourcePath: string, destPath: string) {
        await extract({ file: sourcePath, cwd: destPath });
    }

    private static async uncompressZip(sourcePath: string, destPath: string) {
        const zip = new StreamZip.async({ file: sourcePath });
        await zip.extract(null, destPath);
        await zip.close();
    }

    private static getType(path: string): PackageType {
        if (path.endsWith('.tar.gz') || path.endsWith('.tgz')) {
            return 'targz';
        }

        if (path.endsWith('zip')) {
            return 'zip';
        }

        return 'unknown';
    }
}
