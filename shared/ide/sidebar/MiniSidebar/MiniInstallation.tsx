import { useStore } from '@shared/stores/useStore';
import { Button } from '@shared/webComponents/Button/Button';

import { MiniSidebar } from './MiniSidebar';
import { MiniSidebarStoreTraits } from './MiniSidebarStoreTypes';

export const MiniInstallation = () => {
    const store = useStore(MiniSidebarStoreTraits, {});
    return (
        <MiniSidebar>
            <Button as="link" onClick={() => store.install()}>
                Connect Unblocked
            </Button>
            &nbsp;<span>to view current file insights for this project.</span>
        </MiniSidebar>
    );
};
