import { ReactNode, useCallback, useMemo, useState } from 'react';

import { useVisibility } from '@shared/ide/components/VisibilityContext';
import { useStream } from '@shared/stores/DataCacheStream';
import { useStore } from '@shared/stores/useStore';
import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { Tab, Tabs } from '@shared/webComponents/Tabs/Tabs';
import { DashboardUrls } from '@shared/webUtils';
import { BrandIcons } from '@shared/webUtils/BrandIcons';

import ConnectTo from '@clientAssets/connect-to.svg';
import Unblocked from '@clientAssets/unblocked.svg';

import { faGears } from '@fortawesome/pro-duotone-svg-icons/faGears';
import { faComputerMouseScrollwheel } from '@fortawesome/pro-light-svg-icons/faComputerMouseScrollwheel';
import { faMessages } from '@fortawesome/pro-regular-svg-icons/faMessages';
import { faSquareCode } from '@fortawesome/pro-solid-svg-icons/faSquareCode';

import { DiscussionsTab } from './DiscussionsTab';
import { ExplorerInsightsStoreTraits, ExplorerInsightsTab } from './ExplorerInsightTypes';

import './ExplorerInsights.scss';

function TabHeader({ children, count }: { children: ReactNode; count?: number }) {
    return (
        <>
            {children}
            {!!count && <span className="explorer_insights__tab__badge">{count}</span>}
        </>
    );
}

function Status({ children, images }: { children: ReactNode; images: ReactNode }) {
    return (
        <div className="explorer_insights__content__status">
            <div className="explorer_insights__content__status__images">{images}</div>
            {children}
        </div>
    );
}

interface Props {
    tab: ExplorerInsightsTab;
}

export function ExplorerInsights({ tab }: Props) {
    const { isVisible } = useVisibility();
    const store = useStore(ExplorerInsightsStoreTraits, { tab });
    const stateValue = useStream(() => (isVisible ? store.stateStream : undefined), [store, isVisible]);
    const state = stateValue?.currentFileState;
    const parentSelectedTab = stateValue?.selectedTab;

    const [selectedTab, setSelectedTab] = useState(0);

    const tabToDisplay = useMemo(() => {
        if (parentSelectedTab && parentSelectedTab !== 'all') {
            return parentSelectedTab;
        }

        switch (selectedTab) {
            case 0:
                return 'pullRequests';
            case 1:
                return 'docs';
        }
    }, [selectedTab, parentSelectedTab]);

    const openAskQuestion = useCallback(
        () => ClientWorkspace.instance().handleAction({ $case: 'openAskQuestion' }),
        []
    );

    let body: ReactNode;

    if (!state || state.$case === 'uninitialized') {
        body = null;
    }

    // No-file-selected state
    else if (state.$case === 'missingFile') {
        body = (
            <div className="explorer_insights__content__no_file">
                <p>Select a file to view related content from integrations you&apos;ve connected to Unblocked.</p>
            </div>
        );
    }

    // Requires connection state
    else if (state.$case === 'requiresConnection') {
        body = (
            <Status
                images={
                    <div className="explorer_insights__content__connect_img">
                        <Icon icon={faSquareCode} size={30} />
                        <Icon
                            className="explorer_insights__content__arrow_img"
                            icon={ConnectTo}
                            size={{ height: 24, width: 20 }}
                        />
                        <Icon icon={Unblocked} size={30} />
                    </div>
                }
            >
                <p>
                    <Button
                        as="link"
                        onClick={() =>
                            ClientWorkspace.instance().handleAction({
                                $case: 'launchInstallation',
                            })
                        }
                    >
                        Connect your repositories
                    </Button>
                    &nbsp;with Unblocked so you can ask it questions.
                </p>
            </Status>
        );
    }

    // Processing state
    else if (state.$case === 'processing') {
        body = (
            <Status images={<Icon icon={faGears} size="xxLarge" />}>
                <p>Unblocked is processing the source code and related discussions for this repository.</p>
                <p>You&apos;ll be able to ask questions when this is complete.</p>
            </Status>
        );
    }

    // Loading state
    else if (state.$case === 'loading') {
        body = <Loading size="medium" />;
    }

    // Empty no-PR state
    else if (state.prInsights.length === 0 && tabToDisplay === 'pullRequests') {
        // No PRs in the visible area, but some do exist in the file
        if (state.hasFileInsights) {
            body = (
                <Status images={<Icon icon={faComputerMouseScrollwheel} size="xLarge" />}>
                    <p>
                        There are no related Pull Requests discussions for the visible code in &quot;{state.fileName}
                        &quot;
                    </p>
                    <p>Try scrolling to a different section of the file.</p>
                </Status>
            );
        }

        // No PRs in the file at all
        else {
            body = (
                <Status images={<Icon icon={faMessages} size="xLarge" />}>
                    <p>There are no related Pull Request discussions in this file.</p>
                </Status>
            );
        }
    }

    // Empty no-docs state
    else if (state.docInsights.length === 0 && tabToDisplay === 'docs') {
        // Team has not installed any document integrations -- special UI
        if (state.needsLongFormDocInstallation) {
            body = (
                <Status
                    images={
                        <>
                            <Icon icon={BrandIcons.notion} size={30} />
                            <Icon icon={BrandIcons.googleDrive} size={30} />
                            <Icon icon={BrandIcons.confluence} size={30} />
                        </>
                    }
                >
                    <p>
                        <a href={DashboardUrls.integrationSettings(state.teamId)}>Connect Unblocked to systems</a> like
                        Notion, Google Docs, and Confluence to discover documents related to the file you have open.
                    </p>
                    <p>
                        <a href="https://vimeo.com/getunblocked/ides">Watch this video</a> to see it in action.
                    </p>
                </Status>
            );
        }

        // Team has installed document integrations
        else {
            body = (
                <Status images={<Icon icon={faComputerMouseScrollwheel} size="xLarge" />}>
                    <p>
                        There are no related questions or documents for the visible code in &quot;{state.fileName}
                        &quot;.
                    </p>
                    <p>
                        Try scrolling to a different section of the file, or{' '}
                        <a href={DashboardUrls.integrationSettings(state.teamId)}>add additional integrations</a>.
                    </p>
                </Status>
            );
        }
    }

    // Thread content state
    else {
        switch (tabToDisplay) {
            case 'docs':
                body = (
                    <DiscussionsTab
                        {...state}
                        insights={state.docInsights}
                        state={state}
                        source="qaExplorer"
                        store={store}
                    />
                );
                break;
            case 'pullRequests':
                body = (
                    <DiscussionsTab
                        {...state}
                        insights={state.prInsights}
                        state={state}
                        source="prExplorer"
                        store={store}
                    />
                );
                break;
        }
    }

    let header: ReactNode;

    // This is absurdly complicated for what we're doing -- clean up when we remove the old UI
    const hasContent =
        state &&
        state.$case === 'ready' &&
        ((tabToDisplay === 'docs' && !!state.docInsights.length) ||
            (tabToDisplay === 'pullRequests' && !!state.prInsights.length));
    const askButton = (
        <>
            <div className="explorer_insights__ask_button">
                <Button variant="secondary" onClick={openAskQuestion}>
                    <span>Ask a New Question</span>
                </Button>
            </div>
            {hasContent && <div className="explorer_insights__label">Related to visible code</div>}
        </>
    );

    if (parentSelectedTab === 'docs') {
        header = askButton;
    } else if (parentSelectedTab === 'pullRequests') {
        header = askButton;
    } else if (parentSelectedTab === 'all') {
        const docCount = state && state.$case === 'ready' ? state.docInsights.length : undefined;
        const prCount = state && state.$case === 'ready' ? state.prInsights.length : undefined;
        header = (
            <Tabs selectedIndex={selectedTab} onSetSelectedIndex={setSelectedTab} size="small">
                <Tab header={<TabHeader count={prCount}>PR Discussions</TabHeader>}>{askButton}</Tab>
                <Tab header={<TabHeader count={docCount}>Questions & Docs</TabHeader>}>{askButton}</Tab>
            </Tabs>
        );
    }

    return (
        <div className="explorer_insights">
            <div className="explorer_insights__header">{header}</div>
            <div className="explorer_insights__body">{body}</div>
        </div>
    );
}
