import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { PullRequest } from '@shared/api/generatedApi';
import { ThreadInfoAggregate } from '@shared/api/generatedExtraApi';

import {
    CurrentFileInsights,
    ExplorerInsightsTab,
    ExplorerInsightsViewState,
} from '@shared/ide/sidebar/ExplorerInsights/ExplorerInsightTypes';
import { ActiveFileResolvedTeamStream } from '@shared/ide/stores/ActiveFileResolvedTeamStream';
import { IDEWorkspace } from '@shared/ide/utils';
import {
    IsInvalidScmState,
    PendingTeamStatusCase,
    PendingTeamStatusStore,
} from '@shared/stores/PendingTeamStatusStore';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { ThreadViewSource } from '@shared/webUtils/ThreadUtils';

import { LazyValue, SortInsightFn } from '../../../webUtils';
import { ActiveFileInstallationStateStream } from './ActiveFileInstallationStateStream';
import { ViewScopedReferenceStream } from './FileReferenceStreams';
import { ViewScopedInsightStream } from './ViewScopedInsightStream';

export class ExplorerInsightsStore {
    static get = LazyValueCache((tab: ExplorerInsightsTab) => new ExplorerInsightsStore(tab));

    public stateStream: Stream<ExplorerInsightsViewState>;
    constructor(private tab: ExplorerInsightsTab) {
        const workspaceName = IDEWorkspace.instance().name;
        this.stateStream = FileInsightStream().map((state) => ({
            currentFileState: state,
            workspaceName,
            selectedTab: this.tab,
        }));
    }

    async openPullRequest(props: {
        source: ThreadViewSource;
        teamId: string;
        pullRequest: PullRequest;
        threadInfo?: ThreadInfoAggregate;
    }): Promise<void> {
        ClientWorkspace.instance().handleAction({
            $case: 'openPr',
            source: props.source,
            teamId: props.teamId,
            repoId: props.pullRequest.repoId,
            prId: props.pullRequest.id,
        });
    }

    async selectThread(props: {
        source: ThreadViewSource;
        threadInfo: ThreadInfoAggregate;
        currentFilePath?: string;
    }): Promise<void> {
        const basePullRequest = props.threadInfo.pullRequest;
        if (basePullRequest) {
            ClientWorkspace.instance().handleAction({
                $case: 'openPr',
                source: props.source,
                teamId: props.threadInfo.thread.teamId,
                repoId: basePullRequest.repoId,
                prId: basePullRequest.id,
                threadId: props.threadInfo.thread.id,
            });
        } else {
            ClientWorkspace.instance().handleAction({
                $case: 'openThread',
                source: props.source,
                teamId: props.threadInfo.thread.teamId,
                repoId: props.threadInfo.repoId,
                threadId: props.threadInfo.thread.id,
            });
        }
    }

    async updateThreadUnread(props: { threadInfo: ThreadInfoAggregate }): Promise<void> {
        const threadInfo = props.threadInfo;
        const isUnread = threadInfo.unread?.latestMessage !== threadInfo.unread?.latestReadMessage;

        const updateBody = { latestReadMessage: isUnread ? threadInfo.unread?.latestMessage : undefined };
        ClientWorkspace.instance().handleAction({
            $case: 'updateThreadUnread',
            teamId: threadInfo.thread.teamId,
            threadId: threadInfo.thread.id,
            updateThreadRequest: updateBody,
        });
    }
}

const FileInsightStream = LazyValue((): Stream<CurrentFileInsights> => {
    // PR-scoped insight streams
    const prInsightStream = ViewScopedInsightStream().map((state) => {
        if (state.$case !== 'ready') {
            return state;
        }

        // Filter out non-PR threads
        const filteredInsights = state.insights.filter(
            (insight) => !(insight.$case === 'thread' && !insight.threadInfo.pullRequest)
        );

        return {
            ...state,
            insights: filteredInsights,
        };
    });

    // Semantic search relevance streams
    const semanticSearchStream = ViewScopedReferenceStream();

    const activeFileResolvedTeamStream = ActiveFileResolvedTeamStream();

    // A stream that returns the TeamStatus for the team that is relevant to the current file
    const teamStatusStream: Stream<PendingTeamStatusCase> = activeFileResolvedTeamStream
        .map((state) => (state.$case === 'ready' ? state.teamId : undefined))
        .compose(dropRepeats())
        .map<Stream<PendingTeamStatusCase>>((teamId) =>
            teamId
                ? PendingTeamStatusStore.get(teamId).stream.map((state) => state.status)
                : Stream.of<PendingTeamStatusCase>('ok')
        )
        .flatten();

    return Stream.combine(
        activeFileResolvedTeamStream,
        prInsightStream,
        semanticSearchStream,
        ActiveFileInstallationStateStream(),
        teamStatusStream
    )
        .map<CurrentFileInsights>(
            ([currentFileRepoState, prInsightState, semanticSearchInsightState, installationState, teamStatus]) => {
                if (currentFileRepoState.$case === 'missingRepo') {
                    return { $case: 'requiresConnection' };
                }
                if (currentFileRepoState.$case === 'loading') {
                    return { $case: 'loading' };
                }
                if (prInsightState.$case !== 'ready') {
                    return prInsightState;
                }
                if (semanticSearchInsightState && semanticSearchInsightState.$case !== 'ready') {
                    return semanticSearchInsightState;
                }

                if (semanticSearchInsightState && semanticSearchInsightState.filePath !== prInsightState.filePath) {
                    return { $case: 'loading' };
                }

                if (teamStatus === 'processing') {
                    return { $case: 'processing' };
                }

                if (IsInvalidScmState(teamStatus)) {
                    return { $case: 'requiresConnection' };
                }

                const prInsights = prInsightState.insights.sort(SortInsightFn());

                return {
                    ...prInsightState,
                    prInsights,
                    docInsights: semanticSearchInsightState?.insights ?? [],
                    needsLongFormDocInstallation:
                        installationState.$case === 'ready' && !installationState.hasLongFormDocInstallation,
                };
            }
        )
        .startWith({ $case: 'loading' });
});
