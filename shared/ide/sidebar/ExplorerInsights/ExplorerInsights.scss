@use 'layout' as *;
@use 'misc' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'animations' as *;

.explorer_insights {
    width: 100%;
    height: 100%;
    display: grid;
    grid: auto 1fr / 100%;
    position: relative; // So that Loading component centers

    .explorer_insights__header {
        position: sticky;
        top: 0;
        @include flex-column;
        z-index: 1; // Ensure appearing above all scrolled content
    }

    .explorer_insights__content__status {
        margin: $spacer-8;
        flex: 1;

        @include flex-column-center;
        justify-content: center;

        p {
            line-height: $line-height-16;
            margin-block-start: 0;
            text-align: center;
        }

        .explorer_insights__content__status__images {
            align-self: center;
            display: flex;
            gap: $spacer-8;
            margin-bottom: $spacer-12;
        }
    }

    .explorer_insights__content__no_file {
        margin: $spacer-8 $spacer-16;
        opacity: 0.5;
    }

    .explorer_insights__content__connect_img {
        @include flex-center-center;
        .explorer_insights__content__arrow_img {
            margin-top: $spacer-6;
        }
    }

    .explorer_insights__ask_button {
        .button {
            width: 100%;
        }
    }

    .explorer_insights__label {
        font-size: $font-size-11;
        padding: $spacer-8 $spacer-8 $spacer-4 $spacer-8;
        opacity: 0.6;
    }

    .explorer_insights__body {
        height: 100%;
        overflow: overlay;
        @include flex-column;
    }
}
