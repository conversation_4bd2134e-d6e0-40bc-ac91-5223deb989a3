@use 'flex' as *;
@use 'layout' as *;
@use 'fonts' as *;

.missing_repos_sidebar {
    height: 100%;

    .missing_repos_sidebar__empty {
        @include flex-center-center;
        padding: $spacer-20;
        text-align: center;
        height: 100%;
    }

    & > p {
        margin-top: $spacer-12;
        margin-bottom: $spacer-24;
    }

    .onboarding_row {
        display: flex;
        align-items: center;
        overflow: hidden;

        .icon,
        .button {
            flex: 0;
        }

        .icon {
            padding-right: $spacer-12;
        }

        p {
            flex: 1;
            margin: 0;
            font-size: $font-size-13;
            padding-right: $spacer-4;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }
}
