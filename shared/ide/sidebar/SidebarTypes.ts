import { PullRequest, ThreadInfoAggregate } from '@shared/api/models';

import { PullRequestAggregate } from '@shared/webUtils/Insights/InsightUtils';
import { ThreadViewSource } from '@shared/webUtils/ThreadUtils';

export type CurrentFilePullRequestsInfo =
    | { $case: 'loading'; fileName?: string }
    | { $case: 'uninitialized' }
    | {
          $case: 'ready';
          teamId: string;
          filePath: string;
          fileName: string;
          pullRequests: PullRequestAggregate[];
          isLoadingDiffStats: boolean;
          potentiallyLoadingPrs: boolean;
      }
    | { $case: 'missingFile' /* No file selected yet */ };

type SidebarSelectThread = {
    command: 'selectThread';
    threadInfo: ThreadInfoAggregate;
    currentFilePath?: string;
    viewColumn?: number; // FIXME
    source: ThreadViewSource;
};

type SidebarUpdateThreadUnread = {
    command: 'updateThreadUnread';
    threadInfo: ThreadInfoAggregate;
};

export type SidebarOpenPullRequestInfo = {
    command: 'openPullRequestInfo';
    teamId: string;
    currentFilePath: string;
    pullRequest: PullRequest;
    threadInfo?: ThreadInfoAggregate;
    viewColumn?: number; // FIXME
    source: ThreadViewSource;
};

// Operations on threads -- shared between full sidebar and explorer current file sidebar
export type SidebarThreadProps = SidebarSelectThread | SidebarUpdateThreadUnread | SidebarOpenPullRequestInfo;
