import classNames from 'classnames';
import { ReactNode, useCallback, useMemo, useRef, useState } from 'react';
import { Stream } from 'xstream';

import { useLocalStorageStateBool } from '@shared/hooks/useLocalStorageState';
import { useStream } from '@shared/stores/DataCacheStream';
import { FeatureSettingsStoreTraits } from '@shared/stores/FeatureSettingsStoreType';
import { TeamStoreTraits } from '@shared/stores/TeamStoreTypes';
import { UpsellState } from '@shared/stores/UpsellTypes';
import { useStore } from '@shared/stores/useStore';
import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { useUserSettingState } from '@shared/stores/useUserSettingState';
import { AnimateContent } from '@shared/webComponents/Animation/AnimateContent';
import { QaInputBannerRenderer } from '@shared/webComponents/Banner/QaInputBannerRenderer';
import { But<PERSON> } from '@shared/webComponents/Button/Button';
import { RemoveButton } from '@shared/webComponents/Button/RemoveButton';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import {
    FocusActionStreamTraits,
    UpsellStreamTraits,
} from '@shared/webComponents/ClientWorkspace/ClientWorkspaceStreamTraits';
import { CodeBlock } from '@shared/webComponents/CodeBlock/CodeBlock';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { IncognitoAlwaysOnDialog } from '@shared/webComponents/Incognito/IncognitoAlwaysOnDialog';
import { MessageEditor, MessageEditorForwardedRef } from '@shared/webComponents/MessageEditor';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { PlanExpiryBannerRenderer } from '@shared/webComponents/PlanBanners/PlanExpiryBanner';
import { TeamDialogBlockers } from '@shared/webComponents/TeamDialogBlockers/TeamDialogBlockers';
import { UpsellDialogIncognitoMode } from '@shared/webComponents/UpsellDialog/UpsellDialogIncognitoMode';
import { useViewThemeContext } from '@shared/webComponents/View/ViewThemeContext';

import darkHighlight from '@clientAssets/dark-ide-code-highlight.gif';
import lightHighlight from '@clientAssets/light-ide-code-highlight.gif';
import unblockedBotIcon from '@clientAssets/unblockedbot.svg';

import { Block } from '../../../../common/build/generated/source/proto/main/ts_proto/Message';
import { AskSidebarStoreTraits } from './AskSidebarStoreTypes';

import './AskSidebar.scss';

export function AskSidebar() {
    const store = useStore(AskSidebarStoreTraits, {});
    const state = useStream(() => store.stream, [store], {});

    const { workspaceName, requiresConnection, snippet } = state;
    const [isPrivate, setIsPrivate] = useUserSettingState('incognitoMode');
    const { openModal } = useModalContext();
    const featureSettingsStore = useStore(FeatureSettingsStoreTraits, { teamId: state.teamId ?? '' });
    const featureSettings = useStream(() => featureSettingsStore.stream, [featureSettingsStore]);

    const isDsacEnabled = useMemo(
        () => featureSettings?.$case === 'ready' && !!featureSettings.settings.enableDataSourceAccessControl,
        [featureSettings]
    );

    const teamStore = useStore(TeamStoreTraits, {});

    const team = useStream(
        () =>
            teamStore.teams.map((teamState) =>
                teamState.$case === 'ready' && state.teamId
                    ? teamState.value.find((team) => team.id === state.teamId)
                    : undefined
            ),
        [teamStore, state.teamId]
    );

    const privateable = useMemo(() => {
        if (!team) {
            return false;
        }

        return !!team.capabilities.showIncognito;
    }, [team]);

    const upsellState = useStream(
        () =>
            state.teamId
                ? ClientWorkspace.instance().getStream(UpsellStreamTraits, {
                      $case: 'upsell',
                      teamId: state.teamId,
                  })
                : Stream.of<UpsellState>({ $case: 'loading' }),
        [state.teamId],
        { $case: 'loading' }
    );

    const [didAskQuestion, setDidAskQuestion] = useState(false);
    const askQuestion = useCallback(
        async (blocks: Block[], mentions: string[]) => {
            setDidAskQuestion(true);
            await ClientWorkspace.instance().handleAction({
                $case: 'askQuestion',
                content: { $case: 'blocks', content: blocks, mentions },
                isPrivate,
            });
        },
        [isPrivate]
    );

    const togglePrivate = useCallback(
        (isPrivate: boolean) => {
            if (!state.teamId || !privateable) {
                return;
            }

            if (isDsacEnabled) {
                openModal(
                    <IncognitoAlwaysOnDialog
                        teamId={state.teamId}
                        canConfigure={featureSettings?.$case === 'ready' && featureSettings.isAllowedToUpdate}
                    />
                );
                return;
            }

            if (state.teamId && upsellState?.$case === 'ready' && upsellState.incognitoMode && isPrivate) {
                openModal(<UpsellDialogIncognitoMode teamId={state.teamId} template={upsellState.incognitoMode} />);
            } else {
                setIsPrivate(isPrivate);
            }
        },
        [openModal, setIsPrivate, upsellState, state.teamId, isDsacEnabled, featureSettings, privateable]
    );

    const [hideTutorial, setHideTutorial] = useLocalStorageStateBool('AskSidebarTutorial', false, true);
    const closeTutorial = useCallback(() => setHideTutorial(true), [setHideTutorial]);

    const snippetContent = useMemo(() => {
        let body: ReactNode = undefined;
        if (snippet) {
            body = <CodeBlock {...snippet} filePathDisplay="base" />;
        } else if (!hideTutorial) {
            body = <AskSidebarInstructions onClose={closeTutorial} />;
        }

        return (
            <>
                <div
                    className={classNames('ask_sidebar__snippet', {
                        ask_sidebar__snippet__has_snippet: !!snippet,
                        ask_sidebar__subcontent__has_content: body,
                    })}
                >
                    <AnimateContent axis="height">{body}</AnimateContent>
                </div>
            </>
        );
    }, [closeTutorial, hideTutorial, snippet]);

    const editorRef = useRef<MessageEditorForwardedRef>(null);

    useStreamEffect(
        () =>
            ClientWorkspace.instance()
                .getStream(FocusActionStreamTraits, { $case: 'focusAction' })
                .filter((action) => action.$case === 'askUnblockedInput'),
        [],
        () => editorRef.current?.focus()
    );

    const placeholder = workspaceName ? `Ask Unblocked about "${workspaceName}"` : 'Ask Unblocked about this project';

    const baseClass = classNames({
        sidebar__content: true,
        ask_sidebar: true,
        'ask_sidebar--fade-out': didAskQuestion,
        'ask_sidebar--missing_repos': requiresConnection,
    });

    return (
        <div className={baseClass}>
            <div>
                {team && (
                    <PlanExpiryBannerRenderer
                        team={team}
                        variant="info"
                        buttonVariant="primary"
                        showAction
                        floating
                        compressed
                    />
                )}
                {team && <TeamDialogBlockers teamId={team.id} />}
            </div>

            <div className="ask_sidebar__content">
                <Icon icon={unblockedBotIcon} size={80} />
                <h1>Ask Unblocked about your codebase</h1>

                <AnimateContent axis="height">
                    {snippet ? 'Ask about:' : 'Ask anything, or highlight specific code to ask about it directly.'}
                </AnimateContent>

                {snippetContent}
            </div>

            {requiresConnection && (
                <div className="ask_sidebar__unconnected_repos">
                    <div className="ask_sidebar__unconnected_repos__title">
                        Repositories in this project are unconnected
                    </div>
                    <div className="ask_sidebar__unconnected_repos__description">
                        <Button
                            as="link"
                            onClick={() => {
                                ClientWorkspace.instance().handleAction({
                                    $case: 'launchInstallation',
                                });
                            }}
                        >
                            Connect your Repositories
                        </Button>
                        &nbsp;so Unblocked can answer questions about this project.
                    </div>
                </div>
            )}

            {state.teamId && team ? (
                <QaInputBannerRenderer
                    teamId={state.teamId}
                    privateable={privateable}
                    incognitoClassName="ask_sidebar__incognito_banner"
                />
            ) : null}

            <MessageEditor
                teamId={state.teamId}
                autofocus
                placeholder={placeholder}
                onSubmit={askQuestion}
                isPrivate={privateable ? (isDsacEnabled ? true : isPrivate) : undefined}
                onSetPrivate={privateable ? togglePrivate : undefined}
                ref={editorRef}
            />
        </div>
    );
}

export function AskSidebarInstructions({ onClose }: { onClose: () => void }) {
    const { theme } = useViewThemeContext();
    const content = useMemo(() => {
        switch (theme) {
            case 'light':
                return lightHighlight;
            case 'dark':
                return darkHighlight;
            default:
                return null;
        }
    }, [theme]);

    if (!content) {
        return null;
    }

    return (
        <div className="ask_sidebar__instructions">
            <RemoveButton className="ask_sidebar__icon_close" iconSize="small" onClick={onClose} />
            <img src={content} />
        </div>
    );
}
