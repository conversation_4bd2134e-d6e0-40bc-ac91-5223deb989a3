import { CreateStoreProxyTraits, StoreProxyStream } from '@shared/proxy/StoreProxy/StoreProxyTypes';
import { ApiDataStreamState } from '@shared/stores/ApiDataStream';
import { ThreadInfoAggregateStreamData } from '@shared/stores/ThreadListStoreTypes';

// My Questions Sidebar

export type MyQuestionsSidebarState = ApiDataStreamState<ThreadInfoAggregateStreamData>;

export const MyQuestionsStoreTraits = CreateStoreProxyTraits({
    category: 'myQuestions',
    keyToString: () => '',
    actions: {},
    streams: {
        stream: StoreProxyStream<MyQuestionsSidebarState>(),
    },
});
