@use 'layout' as *;
@use 'misc' as *;
@use 'flex' as *;

$sidebar-section-max-height: calc(100vh - 106px); // height of tabs + support section + tree_item__root = 106px

.sidebar__container {
    overflow: hidden !important; // override the default container scroll (sections will scroll)
    border-top-width: $border-width;
    border-top-style: $border-style;

    .sidebar_frame {
        width: 100%;
        height: 100%;
    }
}

.sidebar {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: max-content 1fr max-content;
    grid-template-areas: '.';
    gap: 0px 0px;
    height: 100%;
    justify-content: start;
    align-content: start;
    justify-items: start;
    align-items: start;

    .tree_item .user_icon_stack {
        z-index: 0;
    }

    .loading {
        position: absolute;
    }

    .tabs {
        width: 100%;
        height: 100%;

        .tabs__headers {
            margin-bottom: $spacer-12;
        }

        .tabs__panels {
            height: 100%;

            .tab_panel {
                height: 100%;
            }
        }
    }

    .sidebar__tab__header {
        @include flex-center-center;

        gap: $spacer-6;
    }
}

.sidebar__loading {
    position: relative;
    width: 100%;
    height: 100%;
}
