import classNames from 'classnames';
import { useCallback } from 'react';

import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { ProviderIcon } from '@shared/webComponents/Icon/ProviderIcon';

import './DeprecatedClient.scss';

interface Props {
    size: 'large' | 'small';
}

export function DeprecatedClient({ size }: Props) {
    const onVisitMarketplace = useCallback(
        () => ClientWorkspace.instance().handleAction({ $case: 'showDownloadUI' }),
        []
    );

    const classnames = classNames('deprecated_client', `deprecated_client__${size}`);
    const iconSize = size === 'large' ? 75 : 32;

    const titleText = 'Download a new version of the Unblocked extension';
    const title = size === 'large' ? <h2>{titleText}</h2> : <h3>{titleText}</h3>;

    return (
        <div className={classnames}>
            <div className="deprecated_client__content">
                <ProviderIcon provider="unblocked" size={iconSize} />
                {title}
                <p>This version of the Unblocked extension is no longer supported.</p>
                <p>
                    Please download a new version from the{' '}
                    <Button as="link" onClick={onVisitMarketplace}>
                        Extensions Marketplace
                    </Button>
                    .
                </p>
            </div>
        </div>
    );
}
