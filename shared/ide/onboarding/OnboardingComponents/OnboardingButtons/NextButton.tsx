import { Button } from '@shared/webComponents/Button/Button';
import { Icon } from '@shared/webComponents/Icon/Icon';

import { faArrowRight } from '@fortawesome/pro-regular-svg-icons/faArrowRight';

import { OnboardingButtonProps } from './OnboardingButtonTypes';

import './OnboardingButtons.scss';

export const NextButton = (props: OnboardingButtonProps) => {
    const { postMessage, stepState, navigate = true, destination, withIcon = true, footerLink, ...buttonProps } = props;
    return (
        <Button
            {...buttonProps}
            className="continue_button"
            onClick={async (e) => {
                if (props.onClick) {
                    await props.onClick(e);
                }
                if (navigate) {
                    if (destination) {
                        postMessage({ command: 'navigate', destinationStep: destination });
                    } else {
                        postMessage({
                            command: 'navigate',
                            destinationStep: stepState.$case + 1,
                        });
                    }
                }
            }}
            variant={props.variant ?? 'primary'}
            size={props.size ?? 'large'}
        >
            <span className="continue_button__content">{props.children ?? 'Continue'}</span>
            <span className="continue_button__content__md">Next</span>
            {withIcon && <Icon className="continue_button__icon" size="small" icon={faArrowRight} />}
            {footerLink ? <div className="continue_button__footer_link">{footerLink}</div> : null}
        </Button>
    );
};
