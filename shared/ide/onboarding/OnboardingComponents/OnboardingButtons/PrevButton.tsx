import { Button } from '@shared/webComponents/Button/Button';
import { Icon } from '@shared/webComponents/Icon/Icon';

import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons/faArrowLeft';

import { OnboardingButtonProps } from './OnboardingButtonTypes';

import './OnboardingButtons.scss';

export const PreviousButton = (props: OnboardingButtonProps) => {
    const { postMessage, stepState, navigate = true, destination, withIcon = true, ...buttonProps } = props;

    return (
        <Button
            {...buttonProps}
            className="previous_button"
            onClick={async (e) => {
                if (props.onClick) {
                    await props.onClick(e);
                }
                if (navigate) {
                    if (destination) {
                        postMessage({ command: 'navigate', destinationStep: destination });
                    } else {
                        postMessage({
                            command: 'navigate',
                            destinationStep: stepState.$case - 1,
                        });
                    }
                }
            }}
            variant={props.variant ?? 'secondary'}
            size={props.size ?? 'large'}
        >
            {withIcon && <Icon className="previous_button__icon" size="small" icon={faArrowLeft} />}
            <span className="previous_button__content">{props.children ?? 'Previous'}</span>
            <span className="previous_button__content__sm">Prev</span>
        </Button>
    );
};
