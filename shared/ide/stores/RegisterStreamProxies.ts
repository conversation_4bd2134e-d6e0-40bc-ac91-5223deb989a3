import { StreamProxyRegistry } from '@shared/proxy/StreamProxy/StreamProxyRegistry';
import { RegisterStreamProxies as RegisterStreamProxiesShared } from '@shared/stores/RegisterStreamProxies';

import { SelectedInsightStateStreamTraits } from '../components/ClientWorkspace/IDEClientWorkspaceStreamTraits';
import { IDEWorkspace } from '../utils/IDEWorkspace';

export function RegisterStreamProxies() {
    RegisterStreamProxiesShared();

    StreamProxyRegistry.instance.register(
        'selectedInsightState',
        SelectedInsightStateStreamTraits,
        () => IDEWorkspace.instance().selectedInsightStateStream
    );
}
