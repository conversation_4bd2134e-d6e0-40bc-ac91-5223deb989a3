import { Stream } from 'xstream';

import { Team } from '@shared/api/generatedApi';

import { LoadableDataStreamState } from '@shared/stores/ApiDataStream';
import { mapCase } from '@shared/stores/StreamOperators';
import { ArrayUtils } from '@shared/webUtils';
import { LazyValue } from '@shared/webUtils/LazyValue';

import { RepoStore } from './RepoStore';

// A stream containing all of the teams relevant to the current project.
// We take the repositories that are mapped for this project, and get the teams that own
// these repositories.
export const IDETeamStream = LazyValue<Stream<LoadableDataStreamState<Team[]>>>(() =>
    RepoStore.instance.stream.compose(
        mapCase('ready', (repoState) => ({
            $case: 'ready',
            value: ArrayUtils.distinct(
                repoState.value.map((repo) => repo.team),
                (team) => team.id
            ),
        }))
    )
);
