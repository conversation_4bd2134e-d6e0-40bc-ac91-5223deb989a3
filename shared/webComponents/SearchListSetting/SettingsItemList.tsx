import classNames from 'classnames';
import { forwardRef, ReactNode, Ref, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { useStream } from '@shared/stores/DataCacheStream';
import { Identifiable } from '@shared/stores/LocalCache';
import { SettingsItemListStore, SettingsItemListStoreState } from '@shared/stores/SettingsItemListStore';
import { SearchInput } from '@shared/webComponents/TextInput/SearchInput';
import { ArrayUtils } from '@shared/webUtils';

import { Button } from '../Button/Button';
import { Loading } from '../Loading/Loading';
import { useModalContext } from '../Modal/ModalContext';
import { SearchListSettingSection } from './SearchListSettingSection';
import { SettingsItemListAddDialog } from './SettingsItemListAddDialog';

import './SettingsItemList.scss';

export interface SettingsItemListForwardRef {
    openModal: () => void;
}

interface Props<
    T extends Identifiable,
    GroupT extends string = '',
    StreamT extends SettingsItemListStoreState<T> = SettingsItemListStoreState<T>,
> {
    store: SettingsItemListStore<T, StreamT>;

    className?: string;

    title?: ReactNode;
    subtitle?: ReactNode;

    banner?: ReactNode;

    addButtonLabel: string;
    filterPlaceholder?: string;
    addPlaceholder?: string;
    addEmptyListPlaceholder: ReactNode;
    emptySearchResults: ReactNode;
    emptyListPlaceholder: ReactNode;

    listFooter?: ReactNode;

    dialogClassName?: string;
    dialogTitle?: ReactNode;
    dialogDescription?: ReactNode;

    synchronizingTitle?: ReactNode;
    synchronizingDescription?: ReactNode;

    itemTemplate: (item: T) => ReactNode;
    addItemTemplate?: (item: T) => ReactNode;

    itemGroupFn: (item: T) => GroupT;
    groupTitleTemplate?: (group: GroupT) => ReactNode;
    addGroupTitleTemplate?: (group: GroupT) => ReactNode;
    dialogAddGroupTitleTemplate?: (group: GroupT) => ReactNode;

    onRemoveItem?: (item: T) => void;

    autoOpenDialog?: boolean;

    ref?: Ref<SettingsItemListForwardRef>;
}

export function SettingsItemListComponent<
    T extends Identifiable,
    GroupT extends string = '',
    StreamT extends SettingsItemListStoreState<T> = SettingsItemListStoreState<T>,
>(
    {
        store,
        className,
        title,
        subtitle,
        banner,
        dialogClassName,
        dialogTitle,
        dialogDescription,
        synchronizingTitle,
        synchronizingDescription,
        addButtonLabel,
        filterPlaceholder,
        addPlaceholder,
        addEmptyListPlaceholder,
        emptySearchResults,
        emptyListPlaceholder,
        listFooter,
        itemTemplate,
        addItemTemplate,
        itemGroupFn,
        groupTitleTemplate,
        addGroupTitleTemplate,
        dialogAddGroupTitleTemplate,
        onRemoveItem,
        autoOpenDialog,
    }: Props<T, GroupT, StreamT>,
    ref: Ref<SettingsItemListForwardRef>
) {
    const [searchTerm, setSearchTerm] = useState('');
    const state = useStream(() => store.stream, [store], { $case: 'loading' });
    const { openModal } = useModalContext();

    const onChangeSearchValue = useCallback(
        (value: string) => {
            setSearchTerm(value);
            store.setFilter(value);
        },
        [store]
    );

    const onShowDialog = useCallback(() => {
        openModal(
            <SettingsItemListAddDialog
                store={store}
                className={dialogClassName}
                title={dialogTitle}
                description={dialogDescription}
                addEmptyListPlaceholder={addEmptyListPlaceholder}
                emptySearchResults={emptySearchResults}
                searchPlaceholder={addPlaceholder}
                itemGroupFn={itemGroupFn}
                addItemTemplate={addItemTemplate ?? itemTemplate}
                addGroupTitleTemplate={dialogAddGroupTitleTemplate ?? addGroupTitleTemplate ?? groupTitleTemplate}
                synchronizingTitle={synchronizingTitle}
                synchronizingDescription={synchronizingDescription}
            />
        );
    }, [
        dialogAddGroupTitleTemplate,
        addGroupTitleTemplate,
        addItemTemplate,
        addPlaceholder,
        dialogClassName,
        dialogDescription,
        dialogTitle,
        addEmptyListPlaceholder,
        emptySearchResults,
        groupTitleTemplate,
        itemGroupFn,
        itemTemplate,
        openModal,
        store,
        synchronizingDescription,
        synchronizingTitle,
    ]);

    useImperativeHandle(ref, () => ({ openModal: onShowDialog }), [onShowDialog]);

    // As soon as we complete loading, if our initial state is an empty list,
    // pop up the dialog
    const hasInitialized = useRef(false);
    useEffect(() => {
        if (hasInitialized.current || state.$case === 'loading') {
            return;
        }
        hasInitialized.current = true;
        if (!state.value.items.length || autoOpenDialog) {
            onShowDialog();
        }
    }, [state, onShowDialog, autoOpenDialog]);

    if (state.$case === 'loading') {
        store.setSuggestedItemsFilter('');
        return <Loading />;
    }

    // Group items
    const items = [...ArrayUtils.groupByMap(state.value.filteredItems, (item) => itemGroupFn(item)).entries()];
    const addableItems = [...ArrayUtils.groupByMap(state.value.addableItems, (item) => itemGroupFn(item)).entries()];

    const hasItems = items.length > 0 || addableItems.length > 0;

    return (
        <div className={classNames('settings_item_list', className)}>
            <div className="settings_item_list__header">
                {title && <div className="settings_item_list__title">{title}</div>}
                <Button className="settings_item_list__add_button" onClick={onShowDialog} size="xs">
                    {addButtonLabel}
                </Button>
            </div>
            {subtitle && <div className="settings_item_list__subtitle">{subtitle}</div>}

            <div className="search_list_setting">
                <div className="search_list_setting__search">
                    <SearchInput
                        searchType="filter"
                        value={searchTerm}
                        onValueChange={onChangeSearchValue}
                        placeholder={filterPlaceholder}
                        autoFocus
                    />
                </div>

                {banner}

                {!hasItems && (
                    <div className="settings_item_list__empty_suggestions">
                        {state.value.isFiltered ? emptySearchResults : emptyListPlaceholder}
                    </div>
                )}

                {hasItems && (
                    <div>
                        {items.map(([group, items]) => (
                            <SearchListSettingSection
                                key={group}
                                title={groupTitleTemplate ? groupTitleTemplate(group) : group}
                                items={items}
                                itemTemplate={itemTemplate}
                                onRemoveItem={onRemoveItem}
                            />
                        ))}

                        {addableItems.map(([group, items]) => (
                            <SearchListSettingSection
                                key={group}
                                title={
                                    addGroupTitleTemplate
                                        ? addGroupTitleTemplate(group)
                                        : groupTitleTemplate
                                          ? groupTitleTemplate(group)
                                          : group
                                }
                                items={items}
                                itemTemplate={addItemTemplate ?? itemTemplate}
                            />
                        ))}

                        {!!listFooter && <div className="settings_item_list__footer">{listFooter}</div>}
                    </div>
                )}
            </div>
        </div>
    );
}

export const SettingsItemList = forwardRef(SettingsItemListComponent);
