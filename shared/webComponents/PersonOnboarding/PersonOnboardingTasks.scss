@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'layout' as *;
@use 'flex' as *;
@use 'fonts' as *;

.person_onboarding_view {
    .person_onboarding_view__progressing {
        text-align: center;
        margin: $spacer-24 0 $spacer-16 0;
        @include ellipsis;
    }

    .person_onboarding_view__progress_bar {
        margin: $spacer-16 0 $spacer-24 0;
    }

    .person_onboarding_view__subtitle {
        text-align: center;
    }

    .person_onboarding_step {
        padding: $spacer-11 $spacer-8;
        min-height: 38px;
    }
}
