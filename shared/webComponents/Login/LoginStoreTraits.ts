import { Provider } from '@shared/api/generatedApi';

import {
    CreateStoreProxyTraits,
    StoreProxyAction0Args,
    StoreProxyAction1Args,
    StoreProxyStream,
} from '@shared/proxy/StoreProxy/StoreProxyTypes';
import { LoginOption, LoginOptionsMetadata } from '@shared/stores/LoginTypes';

import { LoginState } from './LoginStore';

export type LoginInvite = { teamId: string; inviteId: string };

type LoginStoreKey = {
    teamId?: string;
    inviteId?: string;
    scmOnly?: boolean;
    ignoreSSO?: boolean;
    ignoreCache?: boolean;
};

const KeyToString = (key: LoginStoreKey) => {
    return `login-${key.teamId}-${key.inviteId}-${!!key.scmOnly}-${!!key.ignoreSSO}-${!!key.ignoreCache}`;
};

export const LoginStoreTraits = CreateStoreProxyTraits({
    category: 'login',
    keyToString: (key: LoginStoreKey) => KeyToString(key),
    actions: {
        updateFocusedProvider: StoreProxyAction1Args<{ provider?: Provider }, void>(),
        loadFocusedEnterpriseProvider: StoreProxyAction1Args<{ enterpriseProviderId: string }, void>(),
        triggerLogin: StoreProxyAction1Args<
            { loginOption: LoginOption; metadata: LoginOptionsMetadata; completionUrl?: string },
            void
        >(),
        cancelLogin: StoreProxyAction0Args<void>(),
    },
    streams: {
        state: StoreProxyStream<LoginState>(),
    },
});
