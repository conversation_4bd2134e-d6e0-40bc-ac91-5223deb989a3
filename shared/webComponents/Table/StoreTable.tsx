import { useStream } from '@shared/stores/DataCacheStream';

import { Props as BaseProps, Table } from './Table';
import { TableDataStore, TableItem } from './TableTypes';

export interface Props<T extends TableItem, StoreT extends TableDataStore<T, SortT>, SortT = void>
    extends Omit<BaseProps<T, StoreT, SortT>, 'items' | 'sort' | 'state'> {
    store: StoreT;
}

export function StoreTable<T extends TableItem, StoreT extends TableDataStore<T, SortT>, SortT = void>({
    store,
    ...baseProps
}: Props<T, StoreT, SortT>) {
    const state = useStream(() => store.stream, [store], { $case: 'loading' });
    const isLoading = state.$case === 'loading';
    const items = state.$case === 'ready' ? state.value.items : [];
    const sort = state.$case === 'ready' ? state.value.sort : undefined;

    return <Table items={items} sort={sort} isLoading={isLoading} state={store} {...baseProps} />;
}
