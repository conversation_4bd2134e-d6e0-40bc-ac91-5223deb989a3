import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { But<PERSON> } from '../Button/Button';
import { ModalContextProvider, useModalContext } from './ModalContext';
import { ModalDialog, Props as ModalDialogProps } from './ModalDialog';

import './ModalDialogStories.scss';

export default {
    title: 'Shared/Modal Dialog',
    component: ModalDialog,
} as Meta<typeof ModalDialog>;

type Story = StoryObj<typeof ModalDialog>;

const ModalContainer = (args: ModalDialogProps) => {
    const { openModal } = useModalContext();

    return (
        <div>
            <Button onClick={() => openModal(<ModalDialog {...args} />)}>Open</Button>
        </div>
    );
};

const render = (args: ModalDialogProps) => (
    <ModalContextProvider>
        <ModalContainer {...args} />
    </ModalContextProvider>
);

export const Primary: Story = {
    render,
    args: {
        children: 'This is a modal!',
        deprecatedButtons: {
            primary: {
                children: 'Primary Action',
            },
        },
    },
};

export const WithTitle: Story = {
    render,
    args: {
        title: 'Modal Title',
        children: 'This is a modal!',
        deprecatedButtons: {
            primary: {
                children: 'Primary Action',
            },
        },
    },
};

export const SingleAction: Story = {
    render,
    args: {
        title: 'Modal Title',
        children: 'This is a modal!',
        deprecatedButtons: {
            primary: {
                children: 'Primary Action',
            },
            secondary: false,
        },
    },
};
