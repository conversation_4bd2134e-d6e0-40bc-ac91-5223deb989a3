import classNames from 'classnames';
import { useMemo } from 'react';

import { Icon, IconCustomSize, IconSrc } from '../Icon/Icon';
import { SuccessCheckIcon } from '../SuccessCheckIcon/SuccessCheckIcon';
import { ModalDialog, Props as ModalDialogProps } from './ModalDialog';

interface BaseProps {
    title: React.ReactNode;
    description?: React.ReactNode;
    children?: React.ReactNode;
}

type IconSrcProps = BaseProps & {
    src: true;
    icon: IconSrc;
    iconSize?: IconCustomSize;
};

type IconComponentProps = BaseProps & {
    src?: never;
    icon: React.ReactNode;
};

type Props = IconSrcProps | IconComponentProps;

type IconDialogProps = Props & Omit<ModalDialogProps, 'children'>;

export const IconDialog = ({ title, description, children, className, ...props }: IconDialogProps) => {
    const iconSection = useMemo(() => {
        if (props.src) {
            return <Icon icon={props.icon} size={props.iconSize} />;
        }
        return props.icon;
    }, [props]);

    return (
        <ModalDialog {...props} padding="separate" className={classNames(className, 'icon_dialog')}>
            {iconSection}
            <h1 className="icon_dialog__header">{title}</h1>
            {description}
            {children}
        </ModalDialog>
    );
};

export const SuccessDialog = ({
    iconSize,
    className,
    ...props
}: Omit<IconDialogProps, 'src' | 'icon' | 'iconSrc' | 'iconOverride' | 'variant'> & { iconSize?: IconCustomSize }) => {
    return (
        <IconDialog
            {...props}
            variant="success"
            className={classNames(className, 'success_icon_dialog')}
            icon={<SuccessCheckIcon size={iconSize ?? { width: 116, height: 100 }} />}
        />
    );
};
