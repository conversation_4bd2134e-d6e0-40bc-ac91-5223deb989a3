import { Description, Dialog as HeadlessDialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import classNames from 'classnames';
import React, { CSSProperties, forwardRef, Ref, useCallback, useEffect, useMemo, useRef } from 'react';

import { useClickOutside } from '@shared/hooks/useClickOutside';
import { EmptyFn } from '@shared/webUtils/TypeUtils';

import { faXmark } from '@fortawesome/pro-regular-svg-icons/faXmark';

import { Button, Props as ButtonProps } from '../Button/Button';
import { useDialogContext } from './ModalContext';
import { DialogButtons } from './ModalDialogButtons/DialogButtons';

import './ModalDialog.scss';

type ModalDialogPadding =
    | 'standard' // padding around the entire dialog
    | 'compact' // separate padding around header and footer - no padding around content
    | 'separate'; // separate padding around header, footer, and content

type ModalDialogWidth =
    | 'x-wide' // 680
    | 'wide' // 600
    | 'medium' // 460
    | 'standard'; // 400
type ModalDialogSize = ModalDialogWidth | number; // handles non-standard modal widths

export function isCustomModalSize(size: ModalDialogSize): size is number {
    return typeof size === 'number';
}

type ModalButtonProps = ButtonProps & { overrideClose?: boolean };

type ModalDialogVariant = 'emphasis' | 'success';

// Generic type for any DialogButtons component that extends BaseDialogButtonsProps
type DialogButtonsComponent = React.ReactElement<DialogButtons>;

// Simplified type that will render a single close primary button
type SimpleCloseButton = { close: true };

type ModalButtonsType = DialogButtonsComponent | SimpleCloseButton;

export function isSimpleCloseButton(type: ModalButtonsType): type is SimpleCloseButton {
    if (typeof type !== 'object') {
        return false;
    }
    return 'close' in type;
}

export interface Props {
    title?: React.ReactNode;
    description?: React.ReactNode;
    action?: React.ReactNode;
    footer?: React.ReactNode;

    /// Banner is full-width, generally will be a <Banner> element
    banner?: React.ReactNode;

    children: React.ReactNode;
    padding?: ModalDialogPadding;
    variant?: ModalDialogVariant;
    size?: ModalDialogSize;

    deprecatedButtons?: {
        primary: ModalButtonProps;
        secondary?: ModalButtonProps | false;
        tertiary?: ModalButtonProps | false;
        stacked?: boolean;
        separate?: boolean;
        endAlign?: boolean;
    };

    /**
     * Dialog buttons component (e.g., <SingleFullWidthButtons />, <TwoEqualButtons />, etc.)
     */
    buttons?: ModalButtonsType;

    className?: string;
    bodyClassName?: string;
    contentClassName?: string;
    closeable?: boolean;

    /**
     * Prevents all built-in close functionality.
     */
    preventAutoClose?: boolean;

    /** Prevents click outside to close, but keeps accessibility (i.e. ESC key to close). */
    preventClickOutside?: boolean;

    onClose?: () => void;
    onCancel?: () => void;
    darken?: boolean;

    /**
     * If true, the dialog will act as a form, and will auto-submit the primary button
     * when enter is pressed.
     */
    form?: boolean;

    animatedEntrance?: boolean;
}

const ModalDialogBase = (
    {
        title,
        description,
        action,
        banner,
        footer,
        children,
        deprecatedButtons: oldButtons,
        buttons: newButtons,
        className,
        bodyClassName,
        contentClassName,
        onClose,
        onCancel,
        closeable,
        preventAutoClose,
        preventClickOutside,
        padding = 'standard',
        size = 'standard',
        darken,
        form,
        variant,
        animatedEntrance,
    }: Props,
    ref: Ref<HTMLDivElement>
) => {
    const { closeModal, className: dialogClasses, setCloseCallback } = useDialogContext();
    const dialogBodyRef = useRef<HTMLDivElement>(null);
    const focusButtonRef = useRef<HTMLButtonElement>(null);

    // FIXME: I think we don't need this -- headlessui Dialog below already handles auto-closing with its
    // onClose handler.
    useClickOutside(dialogBodyRef, () => {
        if (preventAutoClose || preventClickOutside) {
            return;
        }
        closeModal();
        if (onCancel) {
            onCancel();
        }
    });

    const classes = classNames(
        'modal_dialog',
        `modal_dialog__${padding}`,
        variant && `modal_dialog__${variant}`,
        className,
        dialogClasses,
        {
            [`modal_dialog__${size}`]: !isCustomModalSize(size),
        }
    );
    const bodyClasses = classNames('modal_dialog__body', bodyClassName, {
        modal_dialog__body__animated: animatedEntrance,
    });

    useEffect(() => {
        if (onClose) {
            setCloseCallback(onClose);
        }
    }, [onClose, setCloseCallback]);

    // Handle legacy deprecatedButtons (backward compatibility)
    const renderButton = useCallback(
        (props: ModalButtonProps, focus?: boolean) => {
            const { onClick, overrideClose, ...buttonProps } = props;

            return (
                <Button
                    onClick={(e) => {
                        onClick?.(e);
                        if (!overrideClose) {
                            closeModal();
                        }
                    }}
                    {...buttonProps}
                    type={props.type ?? (focus ? 'submit' : undefined)}
                    ref={focus ? focusButtonRef : null}
                />
            );
        },
        [closeModal]
    );

    const secondaryButton = useMemo(() => {
        if (oldButtons?.secondary === false) {
            return null;
        }

        if (oldButtons?.secondary) {
            return renderButton({
                ...oldButtons?.secondary,
                className: classNames('dialog__secondary_button', oldButtons?.secondary.className),
                variant: oldButtons?.secondary.variant ?? 'secondary',
            });
        }
        return (
            // default secondary button
            <Button variant="secondary" onClick={closeModal} className="dialog__secondary_button">
                Cancel
            </Button>
        );
    }, [oldButtons?.secondary, closeModal, renderButton]);

    const tertiaryButton = useMemo(() => {
        if (!oldButtons?.tertiary) {
            return null;
        }

        return renderButton({
            ...oldButtons.tertiary,
            className: classNames('dialog__tertiary_button', oldButtons.tertiary.className),
            variant: oldButtons.tertiary.variant ?? 'outline',
        });
    }, [oldButtons?.tertiary, renderButton]);

    const buttonClasses = classNames({
        modal_dialog__buttons: true,
        modal_dialog__buttons__single_column: oldButtons?.stacked || oldButtons?.secondary === false,
        modal_dialog__buttons__separate: oldButtons?.separate,
        modal_dialog__buttons__end: oldButtons?.endAlign,
        modal_dialog__buttons__tertiary: !!oldButtons?.tertiary && !oldButtons.stacked && !footer,
        modal_dialog__buttons__with_footer: !!footer,
    });

    // Determine which buttons implementation to use
    const renderButtonsSection = () => {
        if (newButtons) {
            if (isSimpleCloseButton(newButtons)) {
                const closeButtonProps: ButtonProps = {
                    onClick: closeModal,
                    children: 'Close',
                    variant: 'primary',
                };
                return (
                    <div className="modal_dialog__new_buttons modal_dialog__new_buttons__simple_close">
                        <Button ref={focusButtonRef} {...closeButtonProps} />
                    </div>
                );
            }

            const buttonsWithProps = React.cloneElement(newButtons, {
                onClose: closeModal,
                focusRef: focusButtonRef,
                ...newButtons.props, // Preserve existing props
            });
            return <div className="modal_dialog__new_buttons">{buttonsWithProps}</div>;
        }

        // Otherwise fallback to deprecated buttons implementation
        if (oldButtons) {
            return (
                <div className={buttonClasses}>
                    {footer ? <div className="modal_dialog__footer_content">{footer}</div> : null}
                    {!footer && tertiaryButton}
                    {secondaryButton}
                    {renderButton(oldButtons.primary, true)}
                </div>
            );
        }

        return null;
    };

    const overlayClasses = classNames({
        modal_dialog__overlay: true,
        modal_dialog__overlay__darken: darken,
        modal_dialog__overlay__disable_click: preventClickOutside,
        modal_dialog__overlay__animated: animatedEntrance,
    });

    const modalContent = (
        <>
            {closeable ? (
                <Button
                    as="icon"
                    icon={faXmark}
                    onClick={closeModal}
                    iconSize={13}
                    className="modal_dialog__close_button"
                />
            ) : null}
            {(title || description) && (
                <div className="modal_dialog__header_section">
                    {title && <DialogTitle className="modal_dialog__header">{title}</DialogTitle>}
                    {description && <Description className="modal_dialog__description">{description}</Description>}
                    {action && <div className="modal_dialog__header_action">{action}</div>}
                </div>
            )}

            {banner && <div className="modal_dialog__banner">{banner}</div>}
            <div className={classNames('modal_dialog__content', contentClassName)}>{children}</div>
            {renderButtonsSection()}
        </>
    );

    const modalContentWrapper = form ? <form action="#">{modalContent}</form> : modalContent;

    let modalStyle: CSSProperties | undefined = undefined;

    if (isCustomModalSize(size)) {
        modalStyle = { width: `${size}px` };
    }

    return (
        <HeadlessDialog
            className={classes}
            open={true}
            onClose={preventAutoClose ? EmptyFn : closeModal}
            ref={ref}
            initialFocus={focusButtonRef}
        >
            <DialogBackdrop className={overlayClasses} />
            <DialogPanel ref={dialogBodyRef} className={bodyClasses} style={modalStyle}>
                {modalContentWrapper}
            </DialogPanel>
        </HeadlessDialog>
    );
};

export const ModalDialog = forwardRef(ModalDialogBase);
