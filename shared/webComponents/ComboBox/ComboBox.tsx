import { Combobox as HeadlessComboBox, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react';
import classNames from 'classnames';
import { ReactNode, useState } from 'react';

import './ComboBox.scss';

interface Props<T> {
    value: T;
    children:
        | React.ReactElement<typeof ComboboxOption | null | undefined>[]
        | React.ReactElement<typeof ComboboxOption | null | undefined>;
    setValue: (value: T) => void;
    displayToValue?: (value: string) => T;
    valueToDisplay?: (value: T) => string;

    fullWidth?: boolean;
    inputSize?: 'large' | 'regular';
}

export const ComboBox = <T,>({
    displayToValue,
    value,
    setValue,
    children,
    valueToDisplay,
    fullWidth,
    inputSize,
}: Props<T>) => {
    const [query, setQuery] = useState('');

    const inputClasses = classNames({
        text_input: true,
        text_input__full_width: fullWidth,
        [`text_input__${inputSize}`]: !!inputSize,
    });
    return (
        <div className="combo_box">
            <HeadlessComboBox value={value} onChange={setValue}>
                <ComboboxInput
                    className={inputClasses}
                    onChange={(event) => {
                        setQuery(event.target.value);
                        if (displayToValue) {
                            setValue(displayToValue(event.target.value));
                        }
                    }}
                    displayValue={valueToDisplay}
                />
                <ComboboxOptions className="combo_box__options">
                    {query.length > 0 && displayToValue && (
                        <ComboboxOption
                            className={(classProps) => {
                                return classNames({
                                    combo_box__query: true,
                                    combo_box__option: true,
                                    'combo_box__options--active': classProps.focus,
                                });
                            }}
                            value={displayToValue(query)}
                        >
                            {query}
                        </ComboboxOption>
                    )}
                    {children}
                </ComboboxOptions>
            </HeadlessComboBox>
        </div>
    );
};

interface OptionProps<T> {
    key: string;
    value: T;
    children: ReactNode;
}

export const ComboBoxOption = <T,>({ key, value, children }: OptionProps<T>) => {
    return (
        <ComboboxOption
            key={key}
            value={value}
            className={(classProps) => {
                return classNames({
                    combo_box__option: true,
                    'combo_box__options--active': classProps.focus,
                });
            }}
        >
            {children}
        </ComboboxOption>
    );
};
