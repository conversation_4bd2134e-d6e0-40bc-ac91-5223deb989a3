import classNames from 'classnames';

import { faEllipsis } from '@fortawesome/pro-solid-svg-icons/faEllipsis';

import { Button, Props as ButtonProps } from './Button';

export const OptionsButton = (props: ButtonProps) => {
    const classes = classNames({
        options_button: true,
        [`${props.className}`]: !!props.className,
    });

    return <Button as="icon" icon={faEllipsis} {...props} className={classes} />;
};
