import { forwardRef, Ref } from 'react';

import { faCircleXmark } from '@fortawesome/pro-duotone-svg-icons/faCircleXmark';

import { Props as ButtonProps } from './Button';
import { CircleButton } from './CircleButton';

const RemoveButtonBase = (props: Omit<ButtonProps, 'icon'>, ref: Ref<HTMLButtonElement>) => {
    return <CircleButton {...props} icon={faCircleXmark} ref={ref} />;
};

export const RemoveButton = forwardRef(RemoveButtonBase);
