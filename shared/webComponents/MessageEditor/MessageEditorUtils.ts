import { Editor, Element, Location } from 'slate';

import { BlockTraitsMap, CustomElementType } from './CustomElementTypes';
import { MarkKeys } from './MessageEditorTypes';

export function isMarkActive(editor: Editor, mark<PERSON>ey: <PERSON><PERSON><PERSON><PERSON>): boolean {
    const marks = Editor.marks(editor);

    // Something is up with the typing here.  It should be clear that bold is available now, but apparently it isn't.
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return marks ? marks[markKey] : false;
}

export function toggleMark(editor: Editor, mark<PERSON>ey: <PERSON><PERSON><PERSON><PERSON>) {
    if (isMarkActive(editor, markK<PERSON>)) {
        Editor.removeMark(editor, markKey);
    } else {
        Editor.addMark(editor, markKey, true);
    }
}

export function hasMark(editor: Editor): boolean {
    const mark = Editor.marks(editor);
    if (mark) {
        for (const mark in Editor.marks(editor)) {
            return true;
        }
    }

    return false;
}

export function clearMarks(editor: Editor) {
    for (const mark in Editor.marks(editor)) {
        Editor.removeMark(editor, mark);
    }
}

export function isBlockActive(editor: Editor, blockKey: CustomElementType): boolean {
    const { selection } = editor;
    if (!selection) {
        return false;
    }

    const [match] = Array.from(
        Editor.nodes(editor, {
            at: Editor.unhangRange(editor, selection),
            match: (n) => !Editor.isEditor(n) && Element.isElement(n) && n.type === blockKey,
        })
    );

    return !!match;
}

/**
 * Gets the closest Element to the given location
 */
export function getClosestElement(editor: Editor, at?: Location): Element | undefined {
    const match = Editor.above(editor, { at, match: (node) => Element.isElement(node), mode: 'lowest' });
    if (!match) {
        return undefined;
    }
    const [node] = match;
    if (Element.isElement(node)) {
        return node;
    }
}

/**
 * Returns true if the document allows inline formatted content at the given location
 */
export function allowsInlineFormattedContent(editor: Editor, at?: Location) {
    const element = getClosestElement(editor, at);
    return element && BlockTraitsMap.get(element.type)?.allowsInlineFormattedContent;
}
