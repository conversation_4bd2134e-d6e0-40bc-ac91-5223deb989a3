import { flip, FloatingPortal, offset, shift, useFloating, useInteractions } from '@floating-ui/react';
import { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import { Editor, Transforms } from 'slate';
import { ReactEditor } from 'slate-react';
import { Stream } from 'xstream';

import { TeamMember } from '@shared/api/generatedApi';

import { ControlledStream } from '@shared/proxy/StreamProxy/StreamProxyTypes';
import { useStream } from '@shared/stores/DataCacheStream';
import { TeamMembersSort } from '@shared/stores/TeamMembersListStoreTypes';
import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import {
    TeamMemberListStreamCommands,
    TeamMemberListStreamTraits,
} from '@shared/webComponents/ClientWorkspace/ClientWorkspaceStreamTraits';

import { TeamMemberDropdown } from '../../../../TeamMemberDropdown/TeamMemberDropdown';

import './MentionDropdown.scss';

interface Props {
    parent: RefObject<HTMLDivElement>;
    editor: Editor;
    teamId: string;
}

export const MentionDropdown = ({ parent, editor, teamId }: Props) => {
    const [isOpen, setIsOpen] = useState(false);
    const [teamMemberStream, setTeamMemberStream] = useState<Stream<TeamMember[]>>(Stream.of<TeamMember[]>([]));
    const controlledStream = useRef<ControlledStream<TeamMember[], TeamMemberListStreamCommands>>();

    const foundMentions = useStream(() => teamMemberStream, [teamMemberStream], []);

    const onSearchChange = useCallback(
        (searchString: string | undefined) => {
            const sort = searchString ? TeamMembersSort.closestSearchMatch : TeamMembersSort.nameAsc;
            const search = searchString ?? '';
            if (searchString !== undefined && !controlledStream.current) {
                controlledStream.current = ClientWorkspace.instance().getControlledStream(TeamMemberListStreamTraits, {
                    $case: 'teamMemberList',
                    teamId,
                    pageSize: 10,
                    sortOrder: sort,
                    search,
                    includeBot: true,
                    instanceId: crypto.randomUUID(),
                });
                setTeamMemberStream(controlledStream.current.stream);
            }

            controlledStream.current?.sendCommand({ $case: 'update', search, sort });
        },
        [teamId]
    );
    // As @-mention value updates, reset our search
    useStreamEffect(() => editor.mentionSearch, [editor, onSearchChange], onSearchChange);

    const mentionRange = useStream(() => editor.mentionRange, [editor.mentionRange]);

    const { x, y, refs, strategy } = useFloating({
        open: isOpen,
        onOpenChange: setIsOpen,
        middleware: [offset({ mainAxis: 5 }), flip(), shift()],
        placement: 'bottom-start',
    });
    const { getFloatingProps } = useInteractions();

    useEffect(() => {
        if (mentionRange && foundMentions.length > 0) {
            const domRange = ReactEditor.toDOMRange(editor, mentionRange);
            refs.setReference({
                getBoundingClientRect: () => {
                    return domRange.getBoundingClientRect();
                },
            });
            setIsOpen(true);
        } else {
            setIsOpen(false);
        }
    }, [foundMentions.length, editor, refs, mentionRange]);

    const selectMention = useCallback(
        (mention: TeamMember, close: () => void) => {
            editor.insertMentionNode(mention, { target: mentionRange });
            close();
            setIsOpen(false);
            Transforms.select(editor, Editor.end(editor, []));
            ReactEditor.focus(editor);
        },
        [editor, mentionRange]
    );

    const [selectedIdx, setSelectedIdx] = useState(0);

    return (
        <div className="mention_dropdown">
            {isOpen && (
                <FloatingPortal>
                    <TeamMemberDropdown
                        itemContainerProps={getFloatingProps({
                            ref: refs.setFloating,
                            style: {
                                position: strategy,
                                left: x ?? 0,
                                top: y ?? 0,
                                zIndex: 100,
                            },
                        })}
                        initOpen={!!(mentionRange && foundMentions.length > 0 && isOpen)}
                        onSelectTeamMember={selectMention}
                        teamMembers={foundMentions}
                        activeIndex={selectedIdx}
                        setIndex={setSelectedIdx}
                        onEscape={() => {
                            ReactEditor.focus(editor);
                            setIsOpen(false);
                        }}
                        onKeyDownDefault={() => {
                            ReactEditor.focus(editor);
                        }}
                        parent={parent}
                    />
                </FloatingPortal>
            )}
        </div>
    );
};
