import type { <PERSON>a, StoryObj } from '@storybook/react';

import { TeamMember } from '../../api/models';
import { TeamContext } from '../Team/TeamContext';
import { EditorContent, MessageEditor } from './MessageEditor';
import { EditorToBlockTranslator } from './MessageTranslators/EditorToBlockTranslator';

import './MessageEditorStories.scss';

export default {
    title: 'Shared/MessageEditor',
    component: MessageEditor,
    argTypes: {
        backgroundColor: { control: 'color' },
    },
    parameters: {
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof MessageEditor>;

type Story = StoryObj<typeof MessageEditor>;

const initialContent: EditorContent = [
    {
        type: 'paragraph',
        children: [
            { text: 'A line of text in a paragraph.  ' },
            { text: 'With styling ', bold: true },
            { text: ' and more styling.', italic: true },
        ],
    },
    { type: 'code', children: [{ text: 'A code block.\nThis can be multiline.' }] },
    {
        type: 'quote',
        children: [
            {
                type: 'paragraph',
                children: [
                    { text: 'A Quote Block ' },
                    { text: 'with bold ', bold: true },
                    { text: 'and italic ', italic: true },
                    {
                        type: 'link',
                        url: 'https://source.unsplash.com/kFrdX5IeQzI',
                        title: undefined,
                        children: [{ text: 'and link' }],
                    },
                ],
            },
            {
                type: 'quote',
                children: [
                    {
                        type: 'paragraph',
                        children: [{ text: 'Nested Quote' }],
                    },
                ],
            },
        ],
    },
    {
        type: 'paragraph',
        children: [{ text: 'More text in a paragraph.' }],
    },
];

const translatedBlockData = EditorToBlockTranslator.translateToBlock(initialContent);
const teamMembers: TeamMember[] = [
    {
        id: '2db87734-1411-4834-9b6c-baa59791d43c',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '18c7831e-9337-4fe8-b8f2-b9043c11cf52',
            provider: 'github',
            username: 'kaych',
            displayName: 'Kay Cheng',
            avatarUrl: 'https://avatars.githubusercontent.com/u/********?v=4',
            htmlUrl: 'https://github.com/kaych',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            'cd197a1851995136e9f1dd9bb6f2685d24b1b38a40053f0630fd4374a66bb752',
            '2a5d5891445cd55134e80fe29113c03d99fd72b99a7a8e517704f5f8f5301001',
        ],
    },
    {
        id: 'd5e8be11-9cc2-49c6-82a1-52fafa50a4d3',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'a4c23ca2-15a5-4649-a4a4-27f1c710d42d',
            provider: 'github',
            username: 'UnblockedDemo',
            displayName: 'UnblockedDemo',
            avatarUrl: 'https://avatars.githubusercontent.com/u/********?v=4',
            htmlUrl: 'https://github.com/UnblockedDemo',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            '6d834706083d684098ccf0770c6f14e41dd927ac5cc98dfa36e0b255d4369c61',
            '5e62f6c179e3b1e61c3d73420d7699ba3ee8c8445754811f4b975304172aebbb',
            '993d6d8e60f4e6377902a0c8d24759d4172ad87f59b562943aace5be9f896f4c',
        ],
    },
    {
        id: '624c613b-876f-4160-8744-04394c976a48',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '45adac91-29b9-4cde-a8a5-8370ceae1db1',
            provider: 'github',
            username: 'davidkwlam',
            displayName: 'davidkwlam',
            avatarUrl: 'https://avatars.githubusercontent.com/u/1924615?v=4',
            htmlUrl: 'https://github.com/davidkwlam',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            'dc89a102e93e14eca891c29d0c944c4a8817bda4a7abcbf07b5a07b390fb4641',
            '5f260c9ff6975bf3f25973fc5f59b749f23f52478bafad767be71bf48a4e36a6',
            '7a6a906ab73293c9c6dc33a60330baf27e73ba5900b7eb133c05e92401a01a93',
        ],
    },
    {
        id: 'df586e16-a346-4332-a4d5-8e036be5a929',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'd8ae770e-2a69-477c-a55f-c1d998728f6c',
            provider: 'github',
            username: 'pwerry',
            displayName: 'pwerry',
            avatarUrl: 'https://avatars.githubusercontent.com/u/858772?v=4',
            htmlUrl: 'https://github.com/pwerry',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            'c0112e58a3fd081454712b297402f736e4e88e9bbfd8468b6e6e3a110b85436b',
            'cee54be9110c70d369d3420ae8ef0f8e740944a83ac421d89a2ebe2f613d6d63',
            'ef7ff7de68afe1f8ce62a88abb5e8c71f1fbec4b6557c1f38c8ec08ee7b3831d',
        ],
    },
    {
        id: '8701af0b-422e-4a21-a4c0-fe84262568dd',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '040b4d1e-caa0-4093-befc-9db1d4133e67',
            provider: 'github',
            username: 'rasharab',
            displayName: 'Rashin Arab',
            avatarUrl: 'https://avatars.githubusercontent.com/u/3806658?v=4',
            htmlUrl: 'https://github.com/rasharab',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: ['03f08e9ad5ca835a5edc0db8ae2d9d14e12a0d19601aa5da45282aeaa1db649b'],
    },
    {
        id: 'c69451be-175b-418e-8733-1b0a47e71c22',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '0debd5c3-ae01-4786-81e6-c1320e643e1d',
            provider: 'github',
            username: 'richiebres',
            displayName: 'richiebres',
            avatarUrl: 'https://avatars.githubusercontent.com/u/1798345?v=4',
            htmlUrl: 'https://github.com/richiebres',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            'a804eb64ed7b3acb5ad4f96524ab1a8efea832551e30c139f39adac22f77ab66',
            'e8fe1ef45a7341b2232991b4d5bb1adba1a3e7d883af0a2503fa97fcf9afeef7',
            'acb5c2fa1c7f3cdee152a3f24ad1e4b14acd63bddf45770754efffe56b07f071',
            '6e128d85f0539dc2447bd33cac3f048e9ab485831b081b5d62c2b89c74f05ef0',
            'e2464040bb6467518da78d82c76369fb0ae5731d654faf4c14361bed6f1fee58',
            '69a7185b467103123c3f5a02c2eaafc064695d8e3c0d0be43f2135b65d9ab55b',
        ],
    },
    {
        id: '3683bed1-e09e-4202-b2f2-b1b4b47d7740',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '5fd3d7db-ecc4-46e7-8a91-a819f866141d',
            provider: 'github',
            username: 'benedict-jw',
            displayName: 'benedict-jw',
            avatarUrl: 'https://avatars.githubusercontent.com/u/********?v=4',
            htmlUrl: 'https://github.com/benedict-jw',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            'bc5b6a60d392feaaadc1dd4fd828e40ed0c9152b0173384fb996ea5c39b787b1',
            '61228812c1ab5f819eb05d3814e808fbf9c650630e34cd7df8a3696a43270677',
            'ee639eb1b506dac162397b46d0e8137d288c68d0e260bdf110cf2ddda187a1d9',
        ],
    },
    {
        id: '1fc08f12-e573-4a83-9831-e7eb5a62c747',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'c4a2c93f-14d2-438b-b522-378c2bfac36a',
            provider: 'github',
            username: 'dennispi',
            displayName: 'Dennis Pilarinos',
            avatarUrl: 'https://avatars.githubusercontent.com/u/387044?v=4',
            htmlUrl: 'https://github.com/dennispi',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: ['4d53b7d798b6f0a88dfd03aa35b5c7d763b586f90ca40a609513d7643fd42f94'],
    },
    {
        id: '2101019b-5065-493e-b8d5-677510400b31',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'c2432470-c7ea-467f-8d3f-55439a2cd73c',
            provider: 'github',
            username: 'jeffrey-ng',
            displayName: 'jeffrey-ng',
            avatarUrl: 'https://avatars.githubusercontent.com/u/1553313?v=4',
            htmlUrl: 'https://github.com/jeffrey-ng',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            '7d5265bee03382e67ff7b2613a023490fd0ec59c26a835ea9c7b31aa0619721d',
            '31e9a8dc3b4d9a3a845b7a7c6cb63022a0e394aa9fea4a1af05ca8798b7b581f',
        ],
    },
    {
        id: 'bbd3002b-7d22-4add-9695-15fe9c151a83',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'b373dc43-5b36-49d9-99fc-2553c918840f',
            provider: 'github',
            username: 'mahdi-torabi',
            displayName: 'mahdi-torabi',
            avatarUrl: 'https://avatars.githubusercontent.com/u/********?v=4',
            htmlUrl: 'https://github.com/mahdi-torabi',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            '90d02234f7de787cdebe55fdc994a21ea5c0a91ef0b2638198769648edc22cd8',
            '66053f181153536af64657ffdc1e8df2100029c6564ada87d57c1dc1fd993e7f',
            'bc71b38746783cceb8aedea82cbbe8eab33363fd950dbf96507ff5166f5289ba',
        ],
    },
    {
        id: '71317fff-6643-428b-9fa5-e9c3dd30be27',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: 'e567e483-cb16-40e1-b907-2ae1bf966dae',
            provider: 'github',
            username: 'matthewjamesadam',
            displayName: 'Matt Adam',
            avatarUrl: 'https://avatars.githubusercontent.com/u/2133518?v=4',
            htmlUrl: 'https://github.com/matthewjamesadam',
            isBot: false,
        },
        isCurrentMember: true,
        hasAccount: true,
        hashedEmails: [
            '6185df957e34ddd8c9b13708c513b89acfa5cf7b9d0f00c0ad563d102ea1cb1f',
            'bd64dd90b04fb73f5f23fde6b555912bd37f33046a683c28ec1ba8f524cf407e',
            '03c0c1bf8ba1fd20036a5a66c796047d9a640bfabb9513e86095eb1ec4bb41ba',
            'e54dc94911dbdc72d55edee58731e53d1759b54a68fb29f77a699e2811070775',
        ],
    },
    {
        id: 'ac8383b8-3df8-4bf1-a543-c8197b43d5dc',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '7bc0982b-83ab-4d59-b073-b8333fe1acd2',
            provider: 'github',
            username: 'unblocked-app[bot]',
            displayName: 'unblocked-app[bot]',
            avatarUrl: 'https://avatars.githubusercontent.com/in/166219?v=4',
            htmlUrl: 'https://github.com/apps/unblocked-app',
            isBot: false,
        },
        isCurrentMember: false,
        hasAccount: false,
    },
    {
        id: '7f0d61d5-89da-4c4c-a665-d4c12b49af58',
        teamId: 'f72bb2e8-61d0-4fb2-abb4-b406253e4e22',
        identity: {
            id: '9c2ba50e-327d-4bb3-97fd-d3e060238548',
            provider: 'github',
            username: 'PadraigK',
            displayName: 'PadraigK',
            avatarUrl: 'https://avatars.githubusercontent.com/u/466150?v=4',
            htmlUrl: 'https://github.com/PadraigK',
            isBot: false,
        },
        isCurrentMember: false,
        hasAccount: false,
    },
];

export const Gallery: Story = {
    render: (args) => (
        <TeamContext.Provider
            value={{
                teamMembers,
            }}
        >
            <MessageEditor {...args} initialContent={translatedBlockData} />
        </TeamContext.Provider>
    ),
};
