import { useEffect } from 'react';

import {
    ArchiveReasonType,
    FollowOnSuggestion,
    MessageAggregate,
    Provider,
    ThreadInfoAggregate,
    ThreadSlackInfo,
} from '@shared/api/models';

import { ArchiveReferenceId } from '@shared/stores/ArchiveReferenceUtilTypes';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { ExternalLink } from '../ExternalLink/ExternalLink';
import { MessageListView } from '../MessageListView/MessageListView';
import { MessageViewFeedbackRequest } from '../MessageView/MessageView';

import './ThreadView.scss';

interface ThreadViewProps {
    thread: ThreadInfoAggregate;
    messages: MessageAggregate[];
    provider: Provider | undefined;
    onConfirmDeleteMessage: (messageId: string, isAnchor?: boolean) => void;
    onUpdateFeedback?: (request: MessageViewFeedbackRequest) => void;
    onAskSuggestion?: (question: FollowOnSuggestion) => void;
    onArchiveReference?: (
        messageId: string,
        id: ArchiveReferenceId,
        reason?: ArchiveReasonType,
        comment?: string
    ) => void;
    onNavigateUser?: (teamMemberId: string) => void;
    slackInfo?: ThreadSlackInfo;
}

export const ThreadView = ({
    thread,
    messages,
    provider,
    onConfirmDeleteMessage,
    onUpdateFeedback,
    onAskSuggestion,
    onArchiveReference,
    onNavigateUser,
}: ThreadViewProps) => {
    // If this thread has unread messages, mark them as read now
    useEffect(
        () => {
            if (thread.unread && thread.unread.latestMessage !== thread.unread.latestReadMessage) {
                ClientWorkspace.instance().handleAction({
                    $case: 'updateThreadUnread',
                    teamId: thread.thread.teamId,
                    threadId: thread.thread.id,
                    updateThreadRequest: { latestReadMessage: thread.unread.latestMessage },
                });
            }
        },
        // We only trigger marking as read on first view (when thread/team IDs change)
        // thread.unread ought to be a dependency here, but we explicitly don't use it, so that if
        // the read state is manually changed while viewing, we don't immediately mark as viewed again
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [thread.thread.id, thread.thread.teamId]
    );

    return (
        <div className="thread_view">
            <MessageListView
                messages={messages}
                thread={thread}
                provider={provider}
                onDeleteMessage={onConfirmDeleteMessage}
                isAnchoredThread={true}
                onUpdateFeedback={onUpdateFeedback}
                onAskSuggestion={onAskSuggestion}
                onNavigateUser={onNavigateUser}
                onArchiveReference={onArchiveReference}
            />

            {thread.thread.provider === 'slack' ? (
                <div className="thread_view__slack_external">
                    <ExternalLink
                        href={thread.messages.at(-1)?.links.externalMessageUrl ?? thread.thread.links.externalUrl}
                    >
                        Continue discussion in Slack
                    </ExternalLink>
                </div>
            ) : null}
        </div>
    );
};
