import classNames from 'classnames';
import { ReactNode, useState } from 'react';

import { faChevronRight } from '@fortawesome/pro-regular-svg-icons/faChevronRight';

import { Button } from '../Button/Button';

import './CollapsibleSection.scss';

interface Props {
    children: ReactNode;
    title: string;
}

export const CollapsibleSection = ({ children, title }: Props) => {
    const [isOpen, setIsOpen] = useState(false);

    const className = classNames({
        collapsible_section: true,
        'collapsible_section--open': isOpen,
    });

    return (
        <section className={className}>
            <Button
                className="collapsible_section__button"
                as="link"
                onClick={() => setIsOpen((open) => !open)}
                icon={faChevronRight}
                iconSize="small"
            >
                {title}
            </Button>
            <div className="collapsible_section__child">{isOpen && children}</div>
        </section>
    );
};
