import { useViewThemeContext } from '../View/ViewThemeContext';
import { Icon, IconSrc, Props as IconProps } from './Icon';

interface Props extends Omit<IconProps, 'icon'> {
    lightIcon: IconSrc;
    darkIcon: IconSrc;
}

export function ThemeIcon({ lightIcon, darkIcon, ...props }: Props) {
    const { theme } = useViewThemeContext();

    return <Icon icon={theme === 'dark' ? darkIcon : lightIcon} {...props} />;
}
