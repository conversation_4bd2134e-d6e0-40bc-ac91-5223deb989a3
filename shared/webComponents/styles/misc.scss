// border radius
$border-radius-2: 2px !default;
$border-radius-3: 3px !default;
$border-radius-4: 4px !default;
$border-radius-6: 6px !default;
$border-radius-8: 8px !default;
$border-radius: $border-radius-8 !default;
$border-radius-10: 10px !default;
$border-radius-12: 12px !default;
$border-radius-15: 15px !default;
$border-radius-16: 16px !default;
$border-radius-20: 20px !default;
$border-radius-32: 32px !default;
$border-radius-circle: 50% !default;
$border-radius-100: 100px !default;

// border width
$border-width-1: 1px !default;
$border-width-2: 2px !default;
$border-width-3: 3px !default;
$border-width-4: 4px !default;
$border-width: $border-width-1 !default;

// border style
$border-style-solid: solid !default;
$border-style-dashed: dashed !default;
$border-style: $border-style-solid !default;

// outline
$outline-width-1: 1px !default;
$outline-width-2: 2px !default;
$outline-width-3: 3px !default;
$outline-width: $outline-width-2 !default;

$outline-style-solid: solid !default;
$outline-style-dashed: dashed !default;
$outline-style: $outline-style-solid !default;

$badge-border-radius: 80px;

@mixin dashed-bg($border, $stroke) {
    background-image: linear-gradient(to right, $border 50%, transparent 0%);
    background-position: top;
    background-size: $stroke 1px;
    background-repeat: repeat-x;
}

@mixin header-shadow {
    box-shadow: 0 1px 1px rgba(118, 111, 182, 0.03);
}

@mixin container-shadow($amount: 6%, $offsetX: 0, $offsetY: 20px, $blur: 60px) {
    box-shadow: $offsetX $offsetY $blur rgba(118, 111, 182, $amount);
}

@mixin small-container-shadow {
    box-shadow: 0 10px 30px rgb(31 26 36 / 40%);
}

@mixin icon-shadow {
    filter: drop-shadow(3px 5px 2px rgba(0 0 0 / 16%));
}

@mixin shadow-border {
    box-shadow: 0 2px 2px rgb(0 0 0 / 5%);
}

@mixin shadow-border-inverted {
    box-shadow: 0 -2px 2px rgb(0 0 0 / 5%);
}

@mixin shadow-border-medium {
    box-shadow: 0 2px 4px rgb(0 0 0 / 16%);
}

@mixin shadow-border-large {
    box-shadow: 0 1px 7px rgb(0 0 0 / 20%);
}

@mixin backdrop-filter($size: 10px) {
    /* stylelint-disable property-no-vendor-prefix */
    -webkit-backdrop-filter: blur($size);
    backdrop-filter: blur($size);
}
