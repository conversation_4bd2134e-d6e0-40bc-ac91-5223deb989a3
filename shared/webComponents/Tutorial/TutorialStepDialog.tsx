import classNames from 'classnames';
import { useMemo } from 'react';

import { faXmark } from '@fortawesome/pro-regular-svg-icons/faXmark';

import { BadgeIcon } from '../BadgeIcon/BadgeIcon';
import { Button } from '../Button/Button';
import { ActiveTutorialState } from './TutorialTypes';
import { TutorialViewRegistry } from './TutorialViewRegistry';

import './TutorialStepDialog.scss';

interface Props {
    state: ActiveTutorialState;
    onClose: () => void;
    onNext: () => void;
}

export const TutorialStepDialog = ({ state, onClose, onNext }: Props) => {
    const step = useMemo(() => {
        const views = TutorialViewRegistry.get(state.tutorialType);
        const steps = state.steps;
        const currentStepId = steps[state.currentIndex];
        return views[currentStepId];
    }, [state]);

    const dialogSize = step.dialogSize || 'standard';
    const classes = classNames({
        tutorial_step_dialog: true,
        [`tutorial_step_dialog--${dialogSize}`]: true,
    });

    return (
        <div className={classes}>
            <Button
                as="icon"
                icon={faXmark}
                iconSize={13}
                className="tutorial_step_dialog__close_button"
                onClick={onClose}
            />

            <div className="tutorial_step_dialog__body">
                <BadgeIcon size="medium" icon={step.icon} className="tutorial_step_dialog__icon" />
                <h1>{step.title}</h1>
                <div className="tutorial_step_dialog__content">{step.content}</div>
            </div>
            <div className="tutorial_step_dialog__buttons">
                <span>
                    {state.currentIndex + 1} of {state.stepCount}
                </span>
                <Button onClick={onNext} size="small">
                    {state.nextLabel}
                </Button>
            </div>
        </div>
    );
};
