import { useCallback, useMemo, useRef, useState } from 'react';

import { Block, EmailInvite, TeamMember } from '@shared/api/models';

import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { MessageTransformer, StreamAsyncFetcher } from '@shared/webUtils';
import { ArrayUtils } from '@shared/webUtils/collection/ArrayUtils';
import { StringsHelper } from '@shared/webUtils/StringsHelper/StringsHelper';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { FocusActionStreamTraits, TeamMemberStreamTraits } from '../ClientWorkspace/ClientWorkspaceStreamTraits';
import { GitContributor } from '../ContributorsList/GitContributor';
import { InviteContributorDialog } from '../ContributorsList/InviteContributorDialog';
import { InviteBanner } from '../Invite/InviteBanner';
import { MessageEditor, MessageEditorController, MessageEditorControllerRefAttributes } from '../MessageEditor';
import { MessageEditorProps } from '../MessageEditor/MessageEditor';
import { useModalContext } from '../Modal/ModalContext';
import { useMessageInputContext, useMessageInputContextHandler } from './MessageInputContext';

import './MessageInput.scss';

type MessageInputProps = {
    messageId?: string;
    teamId: string | undefined;
    onSubmit?: (blocks: Block[], mentions: string[], emails: string[] | undefined) => Promise<void>;
    onCancel?: () => void;
} & Omit<MessageEditorProps, 'onSubmit'>;

async function ResolveTeamMembers(teamId: string, teamMemberIds: string[]): Promise<Map<string, TeamMember>> {
    const teamMemberStream = ClientWorkspace.instance().getStream(TeamMemberStreamTraits, {
        $case: 'teamMembers',
        teamId,
        teamMemberIds,
        instanceId: crypto.randomUUID(),
    });

    const members = await StreamAsyncFetcher(teamMemberStream, (value) => value, 2000);
    return new Map(members.map((member) => [member.id, member]));
}

export const MessageInput = ({
    messageId,
    onSubmit,
    onCancel,
    autofocus = false,
    placeholder,
    ...props
}: MessageInputProps) => {
    const messageEditorRef = useRef<MessageEditorControllerRefAttributes>(null);
    const { openModal } = useModalContext();
    const {
        contributors,
        invitees,
        participants,
        onSendTeamEmails,
        onSubmit: contextOnSubmit,
        onMentionAdded,
        onMentionDeleted,
    } = useMessageInputContext();

    useStreamEffect(
        () =>
            ClientWorkspace.instance()
                .getStream(FocusActionStreamTraits, { $case: 'focusAction' })
                .filter((action) => action.$case === 'messageInput'),
        [],
        () => {
            messageEditorRef.current?.focus();
        }
    );

    const setBlocksFn = useRef<(blocks: Block[] | ((current: Block[]) => Block[])) => void>();
    const clearEditorFn = useRef<() => void>();

    const [hasSentInvite, setHasSentInvite] = useState<boolean>(false);

    const showInviteDialog = useCallback(
        (
            nonMembers: GitContributor[] | TeamMember[],
            resolveCb: ((emails?: EmailInvite[]) => void) | ((emails?: EmailInvite[]) => Promise<void>),
            mentionInvite?: boolean
        ) => {
            if (nonMembers.length > 0) {
                const header = mentionInvite
                    ? undefined
                    : nonMembers.length === 1
                      ? `Invite "${nonMembers[0].identity?.displayName ?? 'your teammate'}" to Unblocked.`
                      : 'Invite Team Members to Unblocked';

                const content = mentionInvite
                    ? undefined
                    : nonMembers.length === 1
                      ? `"${
                            nonMembers[0].identity?.displayName ?? 'Your teammate'
                        }" needs to be invited before they can participate in this discussion.`
                      : `Invite your teammates so they can participate in this discussion.`;

                const buttonText = mentionInvite
                    ? 'Invite and Post Comment'
                    : `Send ${StringsHelper.count({ singular: 'Invite' }, nonMembers.length)}`;

                openModal(
                    <InviteContributorDialog
                        nonMembers={nonMembers}
                        onInvite={async (invites) => {
                            await resolveCb(invites);
                        }}
                        header={header}
                        content={content}
                        buttonText={buttonText}
                        requireAllFields={!!mentionInvite}
                        stacked
                    />
                );
            } else {
                resolveCb();
            }
        },
        [openModal]
    );

    const checkForInvites = useCallback(
        async (mentions: string[], resolveCb: (emails?: EmailInvite[]) => void) => {
            const teamMembers = props.teamId
                ? await ResolveTeamMembers(props.teamId, mentions)
                : new Map<string, TeamMember>();

            const nonMembers = ArrayUtils.compact(
                mentions.map((id) => {
                    const matchingContributor = contributors?.find((contributor) => contributor.teamMemberId === id);
                    if (matchingContributor?.teamMember?.hasAccount === false) {
                        return matchingContributor;
                    }
                    const matchingTeamMember = teamMembers.get(id);
                    if (!matchingTeamMember?.hasAccount) {
                        return matchingTeamMember;
                    }
                })
            );

            showInviteDialog(nonMembers, resolveCb, true);
        },
        [contributors, showInviteDialog, props.teamId]
    );

    const inviteBanner = useMemo(() => {
        if (hasSentInvite || invitees?.$case === 'loading') {
            return null;
        }

        const teamMembersToInvite = participants.filter(
            (teamMember) => invitees?.value.map((v) => v.teamMemberId).includes(teamMember.id) ?? false
        );

        const contributorsWithoutAccount = teamMembersToInvite.map((teamMember) => {
            const matchingContributor = contributors?.find((contributor) => contributor.teamMemberId === teamMember.id);
            return matchingContributor ?? teamMember;
        });

        return (
            <InviteBanner
                membersToInvite={teamMembersToInvite}
                onInvite={() => {
                    showInviteDialog(contributorsWithoutAccount, (emails) => {
                        onSendTeamEmails(emails ?? []);
                        setHasSentInvite(true);
                    });
                }}
            />
        );
    }, [participants, contributors, showInviteDialog, hasSentInvite, onSendTeamEmails, invitees]);

    const onSubmitClicked = useCallback(
        (blocks: Block[], mentions: string[], clearEditor: () => void) => {
            checkForInvites(mentions, async (emails) => {
                const emailAddress = emails?.map((invite) => invite.email);
                const messageBytes = MessageTransformer.fromBlocksToBytes(blocks);
                await contextOnSubmit(messageId, messageBytes, mentions, emailAddress);
                await onSubmit?.(blocks, mentions, emailAddress);
                clearEditor();
            });
        },
        [checkForInvites, contextOnSubmit, messageId, onSubmit]
    );

    const onCancelClicked = useCallback(
        (clearEditor: () => void) => {
            clearEditor();
            onCancel?.();
        },
        [onCancel]
    );

    useMessageInputContextHandler(messageId, {
        setContent: (blocks) => {
            setBlocksFn.current?.(blocks);
        },
    });

    return (
        <div className="message_input">
            {inviteBanner}
            <MessageEditorController ref={messageEditorRef}>
                {({ ref, getBlocks, setBlocks, onClearEditor }) => {
                    clearEditorFn.current = onClearEditor;

                    setBlocksFn.current = (blocks: Block[] | ((current: Block[]) => Block[])) => {
                        if (typeof blocks === 'function') {
                            setBlocks(blocks(getBlocks()));
                        } else {
                            setBlocks(blocks);
                        }
                    };

                    return (
                        <div className="message_input__editor">
                            <MessageEditor
                                {...props}
                                ref={ref}
                                placeholder={placeholder ?? 'Continue the discussion'}
                                autofocus={autofocus}
                                onSubmit={(blocks, mentions) => onSubmitClicked(blocks, mentions, onClearEditor)}
                                onMentionAdded={onMentionAdded}
                                onMentionDeleted={onMentionDeleted}
                                onCancel={onCancel ? () => onCancelClicked(onClearEditor) : undefined}
                            />
                        </div>
                    );
                }}
            </MessageEditorController>
        </div>
    );
};
