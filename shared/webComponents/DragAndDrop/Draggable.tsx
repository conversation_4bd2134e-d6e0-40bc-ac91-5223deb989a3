import { ConnectDragSource, DragSourceMonitor, useDrag } from 'react-dnd';

interface ExportedProps {
    dragRef: ConnectDragSource;
    isDragging: boolean;
    canDrag: boolean;
}

interface Props<DraggableType, ItemType> {
    type: DraggableType;
    children: (props: ExportedProps) => React.ReactNode;
    onEndDrag?: ({ item, monitor }: { item: ItemType; monitor: DragSourceMonitor }) => void;
    item: ItemType;
}

export function Draggable<DraggableType, ItemType>({
    type,
    children,
    item,
    onEndDrag,
}: Props<DraggableType, ItemType>) {
    const [{ isDragging, canDrag }, dragRef] = useDrag(() => ({
        type: typeof type,
        item,
        end: (item, monitor) => {
            if (onEndDrag) {
                onEndDrag({ item, monitor });
            }
        },
        collect: (monitor: DragSourceMonitor) => ({
            isDragging: !!monitor.isDragging(),
            canDrag: !!monitor.canDrag,
        }),
    }));

    return <>{children({ dragRef, isDragging, canDrag })}</>;
}
