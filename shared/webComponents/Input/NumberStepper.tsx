import { useCallback, useEffect, useRef, useState } from 'react';

import { faMinus } from '@fortawesome/pro-regular-svg-icons/faMinus';
import { faPlus } from '@fortawesome/pro-regular-svg-icons/faPlus';

import { Icon } from '../Icon/Icon';
import { Input, Props as InputProps } from './Input';

interface Props {
    /** The current value */
    value: number;

    /** Raised when chosen value changes */
    onValueChange: (value: number, isBeyondMin?: boolean, isBeyondMax?: boolean) => void;

    /** Whether to include the min and max values in the input. */
    inclusiveMinMax?: boolean;

    /** The minimum value propagated up through onValueChange. */
    min?: number;

    /** The maximum value propagated up through onValueChange. */
    max?: number;
}

export const NumberStepper = ({
    value,
    onValueChange,
    inclusiveMinMax = true,
    min = 0,
    max,
    ...props
}: Props & InputProps) => {
    const inputRef = useRef(null);

    const [internalValue, setInternalValue] = useState<string>();

    // Update from external value prop
    useEffect(() => setInternalValue(value.toString()), [value]);

    const onTextChange = useCallback(
        (newValue: string) => {
            const numberValue = parseInt(newValue);
            if (Number.isNaN(numberValue)) {
                setInternalValue(newValue); // Use string value
            } else {
                let narrowedValue = numberValue;
                let isBeyondMin = false;
                let isBeyondMax = false;

                // Check if value is below min
                if (numberValue < min) {
                    isBeyondMin = true;
                    narrowedValue = min;
                }

                // Check if value is above max
                if (max !== undefined && numberValue > max) {
                    isBeyondMax = true;
                    narrowedValue = max;
                }

                onValueChange(narrowedValue, isBeyondMin, isBeyondMax);
            }
        },
        [max, min, onValueChange]
    );

    const onBlur = useCallback(
        (newValue: string) => {
            const numberValue = parseInt(newValue);
            if (Number.isNaN(numberValue)) {
                onValueChange(value);
                setInternalValue(value.toString());
            } else {
                let narrowedValue = numberValue;
                let isBeyondMin = false;
                let isBeyondMax = false;

                // Check if value is below min
                if (numberValue < min) {
                    isBeyondMin = true;
                    narrowedValue = min;
                }

                // Check if value is above max
                if (max !== undefined && numberValue > max) {
                    isBeyondMax = true;
                    narrowedValue = max;
                }

                onValueChange(narrowedValue, isBeyondMin, isBeyondMax);
            }
        },
        [max, min, onValueChange, value]
    );

    const onDecrease = useCallback(() => {
        const newValue = value - 1;

        if (newValue < min) {
            onValueChange(min, true, false);
            return min;
        }

        onValueChange(newValue);
        return newValue;
    }, [value, min, onValueChange]);

    const onIncrease = useCallback(() => {
        const newValue = value + 1;

        if (max !== undefined && newValue > max) {
            onValueChange(max, false, true);
            return max;
        }

        onValueChange(newValue);
        return newValue;
    }, [max, onValueChange, value]);

    return (
        <div className="number_stepper">
            <button
                className="number_stepper__down"
                onClick={onDecrease}
                disabled={inclusiveMinMax ? value <= min : value < min}
                type="button"
            >
                <Icon icon={faMinus} size="medium" />
            </button>
            <Input
                {...props}
                type="number"
                ref={inputRef}
                value={internalValue}
                onChange={(e) => onTextChange(e.currentTarget.value)}
                onFocus={(e) => e.target.select()}
                onBlur={(e) => onBlur(e.currentTarget.value)}
            />
            <button
                className="number_stepper__up"
                onClick={onIncrease}
                disabled={max !== undefined && (inclusiveMinMax ? value >= max : value > max)}
                type="button"
            >
                <Icon icon={faPlus} size="medium" />
            </button>
        </div>
    );
};
