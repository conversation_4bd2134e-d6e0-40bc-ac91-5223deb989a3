@use '../styles/layout' as *;
@use '../styles/layout-mixin' as *;
@use '../styles/misc' as *;
@use '../styles/fonts' as *;
@use '../styles/flex' as *;

.message_header {
    border-top-left-radius: $border-radius-3;
    border-top-right-radius: $border-radius-3;
    border-bottom: $border-width $border-style transparent;
    display: grid;
    grid-template: 'avatar author date actions' / auto auto 1fr auto;

    .message_header__author {
        grid-area: author;
        margin-right: $spacer-6;
        line-height: $size-16;
        align-self: center;
        font-weight: $font-weight-bold;
    }

    .message_actions {
        grid-area: actions;
        align-self: center;
        justify-self: end;
    }

    .message_header__icon {
        grid-area: avatar;
        margin-right: $spacer-8;
        border: $border-width-2 $border-style transparent;
    }

    .message_header__timestamp {
        grid-area: date;
        align-self: center;
        line-height: $size-16;
        opacity: 0.6;
        @include flex-center;

        .message_header__edited {
            margin-left: $spacer-4;
            opacity: 0.5;
        }
    }

    &.message_header__author_navigable {
        .message_header__author {
            cursor: pointer;
        }
    }
}
