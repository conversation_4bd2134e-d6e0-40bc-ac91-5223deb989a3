import { useCallback, useMemo } from 'react';

import { MessageContent, Provider } from '@shared/api/generatedExtraApi';

import { CopyToClipboard } from '@shared/webUtils/CopyUtils';
import { RenderMessageMarkdown } from '@shared/webUtils/MarkdownRenderer';
import { getViewInProvider } from '@shared/webUtils/ProviderUtils';

import { faEllipsisH } from '@fortawesome/pro-regular-svg-icons/faEllipsisH';

import { MessageLinks } from '../../api/models';
import { useSetString } from '../../hooks/useSetString';
import { ArrayUtils, MessageTransformer } from '../../webUtils';
import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { ContextMenuItem } from '../ContextMenuProvider/ContextMenuItem';
import { DropdownContextMenu } from '../ContextMenuProvider/DropdownContextMenu';
import { DropdownDivider, DropdownItem } from '../Dropdown/Dropdown';
import { Icon } from '../Icon/Icon';

interface BaseProps {
    links: MessageLinks;
    isAnchor: boolean;
    provider: Provider | undefined;
    isUnbotAuthor: boolean;
    messageContent?: MessageContent;
}

type ReadOnlyProps = BaseProps & {
    readOnly: true;
};

type MutableProps = BaseProps & {
    readOnly?: never;
    onEditMessage: () => void;
    onDeleteMessage: () => void;
    deletable?: boolean;
    isAuthor: boolean;
};

type Props = ReadOnlyProps | MutableProps;

export const MessageViewContextMenu = ({
    isAnchor,
    links,
    provider,
    isUnbotAuthor,
    messageContent,
    ...props
}: Props) => {
    const { dashboardUrl, externalMessageUrl } = links;

    const onCopyLinkCallback = useCallback(() => {
        if (dashboardUrl) {
            CopyToClipboard(dashboardUrl);
        }
    }, [dashboardUrl]);

    const { text: copyLinkText, onChange: onCopyLink } = useSetString(onCopyLinkCallback, {
        base: 'Copy Link',
        result: 'Copied!',
    });

    const onCopyMessageContentCallback = useCallback(() => {
        if (messageContent) {
            const content = MessageTransformer.fromBytesToMessage(messageContent.content);
            const markdown = RenderMessageMarkdown(content.blocks);
            CopyToClipboard(markdown);
        }
    }, [messageContent]);

    const { text: copyMessageText, onChange: onCopyMessageContent } = useSetString(onCopyMessageContentCallback, {
        base: 'Copy Message',
        result: 'Copied!',
    });

    const mutatingOptions = useMemo((): ContextMenuItem[] => {
        switch (props.readOnly) {
            case true:
                return [];
            default:
                const { isAuthor, onEditMessage, onDeleteMessage, deletable: canDelete } = props;

                const editable = isAuthor;
                const deletable = canDelete && (isAuthor || isUnbotAuthor);

                if (!editable && !deletable) {
                    return [];
                }

                const baseItems: ContextMenuItem[] = ArrayUtils.compact([
                    editable
                        ? {
                              item: {
                                  id: 'editMessage',
                                  title: 'Edit Message',
                              },
                              callback: onEditMessage,
                              dropdownItem: (
                                  <DropdownItem onClick={onEditMessage} key="editMessage">
                                      Edit Message
                                  </DropdownItem>
                              ),
                          }
                        : null,
                    deletable
                        ? {
                              item: {
                                  id: 'delete',
                                  title: isAnchor ? 'Archive Discussion' : 'Delete Message',
                              },
                              callback: onDeleteMessage,
                              dropdownItem: (
                                  <DropdownItem textStyle="danger" onClick={onDeleteMessage} key="delete">
                                      {isAnchor ? 'Archive Discussion' : 'Delete Message'}
                                  </DropdownItem>
                              ),
                          }
                        : null,
                ]);

                if (baseItems.length) {
                    baseItems.unshift({
                        item: {
                            id: 'separator',
                            title: '',
                            itemType: 'separator',
                        },
                        dropdownItem: <DropdownDivider key="separator" />,
                    });
                }

                return baseItems;
        }
    }, [isAnchor, isUnbotAuthor, props]);

    const baseItems: ContextMenuItem[] = ArrayUtils.compact([
        dashboardUrl
            ? {
                  item: {
                      id: 'copyDashboardUrl',
                      title: copyLinkText,
                  },
                  callback: onCopyLink,
                  dropdownItem: (
                      <DropdownItem key={`copy`} onClick={onCopyLink}>
                          {copyLinkText}
                      </DropdownItem>
                  ),
              }
            : null,
        messageContent
            ? {
                  item: {
                      id: 'copyDashboardMessage',
                      title: copyMessageText,
                  },
                  callback: onCopyMessageContent,
                  dropdownItem: (
                      <DropdownItem key={`copy`} onClick={onCopyMessageContent}>
                          {copyMessageText}
                      </DropdownItem>
                  ),
              }
            : null,
        externalMessageUrl
            ? {
                  item: {
                      id: 'externalLink',
                      title: getViewInProvider(provider),
                  },
                  callback: () => {
                      ClientWorkspace.instance().handleAction({
                          $case: 'openUrl',
                          url: externalMessageUrl,
                      });
                  },
                  dropdownItem: (
                      <DropdownItem
                          key={`externalLink`}
                          as="a"
                          href={externalMessageUrl}
                          target="_blank"
                          rel="noreferrer"
                      >
                          {getViewInProvider(provider)}
                      </DropdownItem>
                  ),
              }
            : null,
    ]);
    const items = ArrayUtils.compact([...baseItems, ...mutatingOptions]);
    return (
        <DropdownContextMenu
            header={<Icon size="small" icon={faEllipsisH} />}
            className="message_view__context_menu"
            withCaret={false}
            portal
            placement="bottom-end"
            items={items}
        />
    );
};
