import { VideoBlock } from '../../../api/models';
import { useAssetUrl } from '../../../webUtils/Assets/useAssetUrl';
import { Loading } from '../../Loading/Loading';

export const VideoBlockRenderer = ({ block }: { block: VideoBlock }) => {
    const videoSrc = useAssetUrl(block.url);

    return (
        <div className="video_block">
            {videoSrc && <video src={videoSrc} data-canonical-src={videoSrc} controls={true} muted={true} />}
            {!videoSrc && <Loading />}
        </div>
    );
};
