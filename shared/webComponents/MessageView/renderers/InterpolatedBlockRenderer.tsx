import { useEffect, useMemo } from 'react';

import { useStream } from '@shared/stores/DataCacheStream';

import { Block } from '../../../api/models';
import { BlockRenderer } from './BlockRenderer';
import { InterpolationManager } from './InterpolationManager';
import { MessageBlockRendererContext, MessageBlockRendererTraits } from './MessageBlockRendererContext';

interface Props {
    blocks: Block[];
    teamId: string | undefined;
    repoId: string | undefined;
    isStreaming: boolean;
    onNavigateLink?: (url: string) => void;
    textOnly?: boolean;
    stripParagraphs?: boolean;
    onDone: () => void;
}

export const InterpolatedBlockRenderer = ({
    blocks,
    teamId,
    repoId,
    isStreaming,
    onNavigateLink,
    textOnly,
    stripParagraphs,
    onDone,
}: Props) => {
    const interpolationMgr = useMemo(() => new InterpolationManager(onDone), [onDone]);

    const streamedBlocks = useStream(() => interpolationMgr.blocksStream, [interpolationMgr], []);

    useEffect(() => {
        interpolationMgr.updateBlocks(blocks, isStreaming);
    }, [blocks, isStreaming, interpolationMgr]);

    useEffect(() => {
        return () => interpolationMgr.uninitialize();
    }, [interpolationMgr]);

    const traits: MessageBlockRendererTraits = {
        teamId,
        repoId,
        onNavigateLink,
        textOnly,
        stripParagraphs,
    };

    return (
        <MessageBlockRendererContext.Provider value={traits}>
            {streamedBlocks.map((block, idx) => (
                <BlockRenderer key={idx} block={block} />
            ))}
        </MessageBlockRendererContext.Provider>
    );
};
