import { FloatingOverlay, FloatingPortal } from '@floating-ui/react';
import { Popover as HeadlessPopover, PopoverButton, PopoverPanel } from '@headlessui/react';
import classNames from 'classnames';
import { forwardRef, HTMLAttributes, MutableRefObject, Ref, useRef } from 'react';

import { useHoverEvents } from '@shared/hooks/useHoverEvents';
import { isReactChildFunction, ReactNodeOrFunction } from '@shared/webUtils/ReactUtils';

import './Popover.scss';

interface PopoverNodeArgs {
    open?: boolean;
    close?: (focusableElement?: HTMLElement | MutableRefObject<HTMLElement | null>) => void;
}

type PopoverNode = ReactNodeOrFunction<PopoverNodeArgs>;
export type PopoverEvent = 'click' | 'hover';

interface Props {
    header?: PopoverNode;
    children: PopoverNode;
    className?: string;
    itemContainerProps?: HTMLAttributes<HTMLDivElement>;
    triggerEvent?: PopoverEvent;
    modal?: boolean;
}

const PopoverBase = (
    { className, header, children, itemContainerProps, triggerEvent = 'click', modal }: Props,
    ref: Ref<HTMLDivElement>
) => {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const { onMouseEnter, onMouseLeave } = useHoverEvents(buttonRef);

    const classes = classNames({
        popover: true,
        [`${className}`]: !!className,
    });

    const overlayClasses = classNames({
        popover__floating_overlay: true,
        popover__floating_overlay__hover: triggerEvent === 'hover',
    });

    return (
        <HeadlessPopover as="div" className={classes} ref={ref}>
            {({ open, close }) => {
                const popoverContent = (
                    <PopoverPanel
                        static={!header}
                        as="section"
                        className="popover__canvas"
                        onMouseEnter={triggerEvent === 'hover' ? () => onMouseEnter(open) : undefined}
                        onMouseLeave={triggerEvent === 'hover' ? () => onMouseLeave(open, close) : undefined}
                        {...itemContainerProps}
                    >
                        {isReactChildFunction(children) ? children({ open, close }) : children}
                    </PopoverPanel>
                );

                const content =
                    modal && open ? (
                        <FloatingPortal>
                            <FloatingOverlay lockScroll className={overlayClasses}>
                                {popoverContent}
                            </FloatingOverlay>
                        </FloatingPortal>
                    ) : (
                        popoverContent
                    );

                return (
                    <>
                        {header ? (
                            <PopoverButton
                                ref={buttonRef}
                                as="div"
                                className="popover__header"
                                onMouseEnter={triggerEvent === 'hover' ? () => onMouseEnter(open) : undefined}
                                onMouseLeave={triggerEvent === 'hover' ? () => onMouseLeave(open, close) : undefined}
                            >
                                {isReactChildFunction(header) ? header({ open, close }) : header}
                            </PopoverButton>
                        ) : null}
                        {content}
                    </>
                );
            }}
        </HeadlessPopover>
    );
};

export const Popover = forwardRef(PopoverBase);
