import { Identity, TeamMember } from '../../api/models';

export type GitContributorProperties = {
    teamMemberId?: string;
    teamMember?: TeamMember;
    identity?: Identity;
    lastCommittedAt?: Date;
    totalCommits?: number;
    hasMostContributions?: boolean;
    hasLatestContribution?: boolean;
    email?: string;
    manuallyAdded?: boolean;
    mentioned?: boolean;
    recommended?: boolean;
    recommendedReason?: string;
    gitEmail?: string;
    gitName?: string;
};

export type GitContributor = {
    id: string;
} & GitContributorProperties;
