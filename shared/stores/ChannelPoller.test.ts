import { Stream } from 'xstream';

import { GetChannelsModifiedSinceRequest } from '@shared/api';
import { ChannelsModifiedSinceResponse, ResponseError } from '@shared/api/generatedApi';

import { MockPetApi } from '../mocks/MockPetApi';
import { ChannelPoller, ChannelPollFn } from './ChannelPoller';
import { createValueStream, ValueStream } from './ValueStream';

const { Response } = jest.requireActual('cross-fetch');

/*
    Note: Most of the basic ChannelPoller behaviour is well-tested by the
    DataCacheStream tests.  These tests are mostly testing corner cases that
    are harder to test there.
*/

const DEFAULT_POLL_INTERVAL = 50;
const DEFAULT_SHUTDOWN_INTERVAL = 0;
const SLOW_POLL_INTERVAL = 100;
const VERY_SLOW_POLL_INTERVAL = 1000;

let poller: ChannelPoller;
let focusStream: ValueStream<boolean>;
let mockApi: MockPetApi;
let pollCatsFn: ChannelPollFn;
let pollDogsFn: ChannelPollFn;
let pushApiFn: jest.Mock<Promise<ChannelsModifiedSinceResponse>, [GetChannelsModifiedSinceRequest]>;

beforeEach(() => {
    jest.useFakeTimers();

    mockApi = new MockPetApi();
    pollCatsFn = async () => {
        const lastModified = mockApi.cats.get().ifModifiedSince;
        return lastModified ? [['/cats', lastModified]] : [];
    };
    pollDogsFn = async () => {
        const lastModified = mockApi.dogs.get().ifModifiedSince;
        return lastModified ? [['/dogs', lastModified]] : [];
    };
    focusStream = createValueStream<boolean>();
    pushApiFn = jest.fn((request: GetChannelsModifiedSinceRequest) =>
        mockApi.pushApi.getChannelsModifiedSince(request)
    );

    poller = new ChannelPoller(
        'team',
        DEFAULT_POLL_INTERVAL,
        DEFAULT_SHUTDOWN_INTERVAL,
        (request) => pushApiFn(request),
        focusStream.stream,
        Stream.of(true)
    );
});

afterEach(() => {
    jest.useRealTimers();
});

test('Basic polling behaviour', async () => {
    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);

    // Expect initial GET call after a single poll interval
    await waitForPolls();

    expect(mockApi.cats.getMock).toHaveBeenCalledTimes(1);
    mockApi.clearAllMocks();

    // Wait for two polls, but expect no additional GET calls, until data changes
    await waitForPolls(2);

    expect(mockApi.cats.getMock).not.toHaveBeenCalled();
    expect(mockApi.pushApi.getMock).toHaveBeenCalledWith(['/cats']);
    mockApi.clearAllMocks();

    // Modify the data.  Expect a poll and a GET call
    mockApi.upsertCat({ id: 'catFluffy', name: 'fluffy' });
    await waitForPolls(2);

    expect(mockApi.cats.getMock).toHaveBeenCalledTimes(1);
    expect(mockApi.pushApi.getMock).toHaveBeenCalledTimes(2);
});

test('Supports polling at different speeds', async () => {
    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);
    poller.startPolling(pollDogsFn, SLOW_POLL_INTERVAL);

    // 50 -- first fetch, no polling check
    // 100 -- poll cats and dogs
    // 150 -- poll cats
    // 200 -- poll cats and dogs
    // 250 -- poll cats
    await waitForPolls(5);

    const catPolls = mockApi.pushApi.getMock.mock.calls.filter((call) => call[0].includes('/cats')).length;
    const dogPolls = mockApi.pushApi.getMock.mock.calls.filter((call) => call[0].includes('/dogs')).length;

    expect(catPolls).toEqual(4);
    expect(dogPolls).toEqual(2);
});

test("Doesn't call the polling function when there are no channels to poll", async () => {
    // Wait for a bunch of polling periods, and verify that we don't actually call the API,
    // because this channel is very low-rate and it's the only channel we're checking
    poller.startPolling(pollDogsFn, VERY_SLOW_POLL_INTERVAL);
    await waitForPolls(8);
    expect(mockApi.pushApi.getMock).toHaveBeenCalledTimes(1);
});

test('Stops polling immediately when focus lost', async () => {
    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);

    // Wait for some polls
    await waitForPolls(2);
    mockApi.clearAllMocks();

    // Stop polling when focus lost
    focusStream.updateValue(false);
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();

    // Start polling when focus re-gained
    focusStream.updateValue(true);
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).toHaveBeenCalled();
});

test('Polls active channels when focus is lost', async () => {
    let catsActive = true;
    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL, undefined, () => catsActive);
    poller.startPolling(pollDogsFn, DEFAULT_POLL_INTERVAL, undefined);

    // Wait for some polls
    await waitForPolls(2);
    mockApi.clearAllMocks();

    // When focus is lost, only continue polling on the active channel -- cats, but not dogs
    focusStream.updateValue(false);
    await waitForPolls();
    expect(mockApi.pushApi.getMock).toHaveBeenCalled();
    expect(mockApi.pushApi.getMock).toHaveBeenNthCalledWith(1, ['/cats']);
    mockApi.clearAllMocks();

    catsActive = false;
    await waitForPolls(2);
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();
});

test('Stops polling some time after focus has been lost', async () => {
    const shutdownInterval = DEFAULT_POLL_INTERVAL * 3;
    poller = new ChannelPoller(
        'team',
        DEFAULT_POLL_INTERVAL,
        shutdownInterval,
        (request) => {
            return mockApi.pushApi.getChannelsModifiedSince(request);
        },
        focusStream.stream,
        Stream.of(true)
    );

    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);

    // Wait for some polls
    await waitForPolls(2);
    mockApi.clearAllMocks();

    // Continue polling during the shutdown interval
    focusStream.updateValue(false);
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).toHaveBeenCalled();

    // Stop polling after the shutdown interval
    mockApi.clearAllMocks();
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();

    // Start polling when focus re-gained
    focusStream.updateValue(true);
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).toHaveBeenCalled();
});

test('Fetches requests immediately without waiting for poll', async () => {
    // Mock a blocked pusher API
    const blockedPoller = new ChannelPoller(
        'team',
        DEFAULT_POLL_INTERVAL,
        DEFAULT_SHUTDOWN_INTERVAL,
        async () =>
            new Promise(() => {
                /* Block forever */
            }),
        focusStream.stream
    );

    blockedPoller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);

    // Expect initial GET call after a single poll interval
    await waitForPolls();

    expect(mockApi.cats.getMock).toHaveBeenCalledTimes(1);
});

test('Correct stops polling when immediately detached', async () => {
    // Tests that a race condition on startup works as expected: if we cancel a channel
    // before the first poll has completed.
    const cancel = poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);
    cancel();

    // Expect initial GET call after a single poll interval
    await waitForPolls(3);

    expect(mockApi.cats.getMock).toHaveBeenCalledTimes(1);
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();
    mockApi.clearAllMocks();
});

test('Skips polling when service returns status code 429', async () => {
    const skipSeconds = 5;

    poller.startPolling(pollCatsFn, DEFAULT_POLL_INTERVAL);

    // Wait for some polls and steady state
    await waitForPolls(2);
    expect(mockApi.pushApi.getMock).toHaveBeenCalledTimes(1);
    mockApi.clearAllMocks();

    // Inject a 429 result
    pushApiFn.mockImplementationOnce(() => {
        throw new ResponseError(
            new Response(null, {
                status: 429,
                headers: [['Retry-After', `${skipSeconds}`]],
            })
        );
    });

    // Wait for the next poll -- this will trigger the throw
    await waitForPolls();
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();

    // We should skip the next series of polls, for the duration of the Retry-After header
    await waitForPolls((skipSeconds * 1000) / DEFAULT_POLL_INTERVAL - 1);
    expect(mockApi.pushApi.getMock).not.toHaveBeenCalled();

    // Start polling again after the Retry-after header duration
    await waitForPolls(3);
    expect(mockApi.pushApi.getMock).toHaveBeenCalled();
});

async function waitForPolls(count = 1) {
    for (const {} of [...Array(count).keys()]) {
        jest.advanceTimersByTime(DEFAULT_POLL_INTERVAL);
        await flushPromises();
    }
}

// https://github.com/facebook/jest/issues/2157#issuecomment-897935688
function flushPromises(): Promise<void> {
    return new Promise(jest.requireActual('timers').setImmediate);
}
