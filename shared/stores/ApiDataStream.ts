import { DependencyList } from 'react';
import xstream, { Listener, Stream } from 'xstream';

import { debugLog, DebugLogConfig } from '../webUtils/log';
import { ApiDataStreamTraits, ChannelIds } from './ApiDataStreamTraits';
import { ChannelLastModified, ChannelPoller } from './ChannelPoller';
import { useStream } from './DataCacheStream';
import { LoadableState } from './LoadableState';

export type LoadableDataStreamState<T> = LoadableState<T>;

export type ApiDataStreamState<T> = LoadableState<T>;

function getChannelIds(ids: ChannelIds): string[] {
    if (typeof ids === 'function') {
        return ids();
    } else if (Array.isArray(ids)) {
        return ids;
    }

    return [ids];
}

export interface ApiDataStream<T> {
    stream: Stream<ApiDataStreamState<T>>;
    trigger: () => void;
}

// Make a stream for a given data cache
export const createApiDataStream = <T>(
    traits: ApiDataStreamTraits<T>,
    debugLogConfig?: DebugLogConfig<ApiDataStreamState<T>>
): ApiDataStream<T> => {
    const poller = traits.poller || ChannelPoller.getInstance(traits.teamId);
    let cancelPollFn: (() => void) | undefined;

    let listener: Listener<ApiDataStreamState<T>> | undefined;

    const fetchFn = async (): Promise<ChannelLastModified> => {
        const { ifModifiedSince, value, valuesValid } = await traits.fetchFn();

        if (valuesValid === undefined || valuesValid) {
            listener?.next({ $case: 'ready', value: value });
        }
        return ifModifiedSince ? getChannelIds(traits.channelIds).map((channelId) => [channelId, ifModifiedSince]) : [];
    };

    const startStream = async (newListener: Listener<ApiDataStreamState<T>>): Promise<void> => {
        newListener.next({ $case: 'loading' });
        listener = newListener;
        cancelPollFn = poller.startPolling(fetchFn, traits.pollFrequencyMS, undefined, traits.isActive);
    };

    const stopStream = () => {
        cancelPollFn?.();
        listener = undefined;
        cancelPollFn = undefined;
    };

    return {
        stream: xstream.create({ start: startStream, stop: stopStream }).compose(debugLog(debugLogConfig)),
        trigger: fetchFn,
    };
};

// Ingest a stream and provide a default state
export function useApiDataStream<T>(stream: () => Stream<T>, dependencies: DependencyList): T;
export function useApiDataStream<T>(
    stream: () => Stream<ApiDataStreamState<T>>,
    dependencies: DependencyList
): ApiDataStreamState<T> {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useStream(stream, dependencies, { $case: 'loading' });
}
