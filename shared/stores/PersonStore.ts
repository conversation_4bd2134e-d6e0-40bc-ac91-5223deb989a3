import equal from 'fast-deep-equal';
import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { findPersonTeamMember } from '@shared/webUtils/TeamMemberUtils';

import { API } from '../api';
import { OnboardingStatusUpdate, Person, TeamMember } from '../api/models';
import { logger } from '../webUtils/log';
import { AuthStore } from './AuthStore';
import { PersonState } from './PersonStoreTypes';
import { createValueStream, ValueStream } from './ValueStream';

const log = logger('PersonStore');

class BasePersonStore {
    private base: ValueStream<PersonState>;
    person: Stream<Person | undefined>;

    constructor() {
        this.base = createValueStream<PersonState>(
            { $case: 'loading' },
            { debugLogConfig: { logger: log, description: PersonStateDescription } }
        );
        this.person = this.base.stream.map((state) => {
            if (state.$case !== 'loaded') {
                return;
            }
            return state.person;
        });

        AuthStore.get().stream.subscribe({
            next: (authState) => {
                switch (authState.$case) {
                    case 'authenticated':
                        this.refreshPerson();
                        break;
                    case 'unauthenticated':
                        this.base.updateValue({ $case: 'unauthenticated' });
                        break;
                    case 'loading':
                        this.base.updateValue({ $case: 'loading' });
                        break;
                    default:
                        break;
                }
            },
        });
    }

    get stream(): Stream<PersonState> {
        return this.base.stream.compose(dropRepeats(equal)).remember();
    }

    get currentPerson(): Person | undefined {
        const currentValue = this.base.getCurrentValue();

        if (!currentValue) {
            return undefined;
        }
        switch (currentValue.$case) {
            case 'loading':
            case 'unauthenticated':
            case 'error':
                return undefined;
            case 'loaded':
                return currentValue.person;
        }
    }

    async refreshPerson(): Promise<void> {
        try {
            const person = await API.persons.getPersonV2();
            this.base.updateValue({ $case: 'loaded', person });
        } catch (e) {
            log.error(e);
            this.base.updateValue({ $case: 'error' });
            // throw e;
        }
    }

    async updateOnboardingStatus(status: OnboardingStatusUpdate): Promise<void> {
        try {
            await API.persons.updateOnboardingStatus({
                onboardingStatusUpdate: status,
            });
            await this.refreshPerson();
        } catch (e) {
            log.error('error updating onboarding status', e);
        }
    }
}

const PersonStateDescription = (state: PersonState) => {
    switch (state.$case) {
        case 'loading':
        case 'unauthenticated':
            return state.$case;
        case 'error':
            return `error`;
        case 'loaded':
            return `loaded: ${state.person.id}`;
    }
};

export const PersonStore = new BasePersonStore();

export const getCurrentTeamMember = (teamId: string): TeamMember | undefined => {
    if (!PersonStore.currentPerson) {
        return undefined;
    }
    return findPersonTeamMember(PersonStore.currentPerson, teamId);
};
