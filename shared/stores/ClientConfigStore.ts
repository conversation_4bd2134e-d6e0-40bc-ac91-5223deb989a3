import dayjs, { Dayjs } from 'dayjs';
import { Stream, Subscription } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { API } from '../api';
import { ClientConfig } from '../api/models';
import { ClientConfigState } from './ClientConfigStoreTypes';
import { PersonStore } from './PersonStore';
import { PersonState } from './PersonStoreTypes';
import { createValueStream } from './ValueStream';

// Query for configuration updates once every 10 minute
const CONFIG_QUERY_SEC = 60 * 10;

// Default configuration
const DEFAULT_CONFIG: ClientConfig = {
    capabilities: {},
    quantities: {},
};

export const ClientConfigKeys = {
    DebugClient: 'DebugClient',
    DebugSourcemarkEngine: 'DebugSourcemarkEngine',
    SlackSettingsChannelPatterns: 'SlackSettingsChannelPatterns',
    SlackUserAcceptanceTesting: 'SlackUserAcceptanceTesting',
    FeatureConfluenceDataCenter: 'FeatureConfluenceDataCenter',
    FeatureJiraDataCenter: 'FeatureJiraDataCenter',
    FeatureShowNonQADashboard: 'FeatureShowNonQADashboard',
    FeatureConfigureCI: 'FeatureConfigureCI',
    FeatureDataShieldPerDataSource: 'FeatureDataShieldPerDataSource',
    FeatureGoogleDriveWorkspace: 'FeatureGoogleDriveWorkspace',
    FeatureMcp: 'FeatureMcp',
    FeatureAnswersTutorial: 'FeatureAnswersTutorial',
    FeatureCIPricing: 'FeatureCIPricing',
};

// Class dependencies
interface Deps {
    // API to fetch client config values
    fetchConfigFn: () => Promise<ClientConfig>;

    // Auth stream -- we use the person stream to tell when the user logs in/out
    // and when tokens are refreshed
    authUpdateStream: Stream<PersonState>;
}

// Default dependencies
const DefaultDeps = (): Deps => ({
    fetchConfigFn: () => API.config.getGlobalConfig(),
    authUpdateStream: PersonStore.stream,
});

/*
    ClientConfigStore -- monitors and publishes ClientConfig instances from the API service.

    This store will fetch updated ClientConfigs whenever the user logs in, is authed as a different
    person, or once an hour (while logged in).

    ClientConfig values can be queried and are published via streams.
*/
export class ClientConfigStore {
    // Singleton instance -- uses default dependencies
    private static instance_: ClientConfigStore;

    static get instance(): ClientConfigStore {
        if (!this.instance_) {
            this.instance_ = new ClientConfigStore();
        }
        return this.instance_;
    }

    private personSubscription: Subscription;
    private lastPersonId: string | undefined;
    private nextUpdate: Dayjs = dayjs();
    private config: ClientConfig = DEFAULT_CONFIG;

    private valueStream = createValueStream<ClientConfigState>({
        ...this.config,
        isFromServer: false,
    });

    constructor(private deps = DefaultDeps()) {
        // Use the Person stream to determine when our auth changes (token is refreshed),
        // and when auth status changes.
        this.personSubscription = this.deps.authUpdateStream.subscribe({
            next: (state) => {
                if (state.$case === 'loaded') {
                    this.updatePerson(state.person.id);
                } else {
                    this.updatePerson(undefined);
                }
            },
        });
    }

    private async updatePerson(personId: string | undefined) {
        // Only query when authed state changes, or when an hour expires
        if (this.lastPersonId === personId && dayjs().isBefore(this.nextUpdate)) {
            return;
        }

        this.nextUpdate = dayjs().add(CONFIG_QUERY_SEC, 'second');
        this.lastPersonId = personId;

        this.config = await this.fetchCfg();

        this.valueStream.updateValue({ ...this.config, isFromServer: !!personId });
    }

    private async fetchCfg(): Promise<ClientConfig> {
        if (this.lastPersonId) {
            try {
                return await this.deps.fetchConfigFn();
            } catch (error) {
                // Log and ignore error -- a failure here shouldn't break other areas
                console.error('Could not fetch client config', error);

                // FIXME figure out why logging doesn't work here
                // log.error('Could not fetch client config', error);
            }
        }

        // If logged out use default config
        return DEFAULT_CONFIG;
    }

    get stream() {
        return this.valueStream.stream;
    }

    // Synchronously get a single capability
    // Most scenarios will likely want to use the stream (getCapabilityStream)
    getCapability(key: string, defaultValue = false): boolean {
        return this.config.capabilities[key] ?? defaultValue;
    }

    // Get a stream of updates for a single capability
    // The stream will immediately send the default value, and will update with resolved
    // user values as the user logs in and out of unblocked.
    getCapabilityStream(key: string, defaultValue = false): Stream<boolean> {
        return this.stream
            .map((config) => config.capabilities[key] ?? defaultValue)
            .compose(dropRepeats())
            .remember();
    }

    // Get a stream of updates for a single capability
    // This only returns values after login, once service values are resolved.
    getServerCapabilityStream(key: string, defaultValue = false): Stream<boolean> {
        return this.stream
            .filter((config) => config.isFromServer)
            .map((config) => config.capabilities[key] ?? defaultValue)
            .compose(dropRepeats())
            .remember();
    }

    // Get a stream of all capabilities set to true
    getAllCapabilitiesStream(): Stream<Set<string>> {
        return this.stream
            .map(
                (config) =>
                    new Set(
                        Array.from(Object.entries(config.capabilities))
                            .filter(([, value]) => !!value)
                            .map(([key]) => key)
                    )
            )
            .remember();
    }

    // Synchronously get a single quantity value
    // Most scenarios will likely want to use the stream (getQuantityStream)
    getQuantity(key: string, defaultValue: number): number {
        return this.config.quantities[key] ?? defaultValue;
    }

    // Get a stream of updates for a single quantity
    getQuantityStream(key: string, defaultValue: number): Stream<number> {
        return this.stream
            .map((config) => config.quantities[key] ?? defaultValue)
            .compose(dropRepeats())
            .remember();
    }
}
