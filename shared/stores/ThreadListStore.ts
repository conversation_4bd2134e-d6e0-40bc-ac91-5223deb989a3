import { Stream } from 'xstream';

import { ThreadInfo } from '../api/models';
import { ApiDataStreamState } from './ApiDataStream';
import { ArchivedThreadStore } from './ArchivedThreadStore';
import { MyDiscussionThreadStore } from './MyDiscussionThreadStore';
import { ObjectMap } from './ObjectMap';
import { StreamOverlay } from './StreamOverlay';
import { TeamQaThreadStore } from './TeamQaThreadStore';
import { TeamThreadsKey, TeamThreadsValue, ThreadInfoAggregateStreamData } from './ThreadListStoreTypes';

export function concatenateThreadInfoApiDataStreams(
    stream: Stream<ApiDataStreamState<ThreadInfoAggregateStreamData>[]>
): Stream<ApiDataStreamState<ThreadInfoAggregateStreamData>> {
    return stream.map((states) => {
        return states.reduce(
            (previousState, state) => {
                // If still initializing, do not take current state into account in reducing
                if (state.$case === 'loading') {
                    return previousState;
                }

                if (previousState.$case === 'loading') {
                    // Previous state was not initialized so this is the first initialized state. Use this as base
                    return state;
                }

                const previousValues = previousState.value;
                const currentValues = state.value;
                return {
                    $case: 'ready',
                    value: {
                        ...previousValues,
                        threads: previousValues.threads.concat(currentValues.threads),
                    },
                };
            },
            { $case: 'loading' }
        );
    });
}

// A store that returns a thread list for a set of repos for a single team
class TeamThreadInfoStore extends ObjectMap<TeamThreadsKey, TeamThreadsValue> {
    protected toString(key: TeamThreadsKey): string {
        switch (key.type) {
            case 'mine':
                return `${key.type}.${key.teamId}.${key.sourceType}.${key.pollForUnreadsWhenUnfocused ?? false}}`;
            case 'teamQa':
                return `${key.type}.${key.teamId}`;
            case 'archived':
                return `${key.type}.${key.teamId}.${key.repoIds?.sort().join('.')}`;
        }
    }

    protected toObject(key: TeamThreadsKey): TeamThreadsValue {
        switch (key.type) {
            case 'mine':
                return new MyDiscussionThreadStore(key);
            case 'teamQa':
                return new TeamQaThreadStore(key);
            case 'archived':
                return new ArchivedThreadStore(key);
        }
    }

    getOverlays(keyFn: (key: TeamThreadsKey) => boolean): StreamOverlay<ApiDataStreamState<ThreadInfo[]>>[] {
        return this.values.filter((store) => keyFn(store.key)).map((store) => store.overlay);
    }

    // Force a local refresh on some set of stores
    // Useful if the store does not have a polling channel
    refresh(keyFn: (key: TeamThreadsKey) => boolean) {
        this.values.filter((store) => keyFn(store.key)).forEach((store) => store.refresh());
    }
}

export const TeamThreadInfoStreams = new TeamThreadInfoStore();
