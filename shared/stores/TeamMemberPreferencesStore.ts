import dayjs from 'dayjs';
import equal from 'fast-deep-equal';
import { Stream } from 'xstream';

import { API } from '@shared/api';
import { MemberPreferencesResponse } from '@shared/api/generatedApi';

import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { MS } from '@shared/webUtils/TimeUtils';

import { DataSourcePresetSummaryStore } from './DataSourcePresetSummaryStore';
import { ResolvedPreset, ResolvedPresets } from './DataSourcePresetTypes';
import { LoadableState } from './LoadableState';
import { PollingDataStream } from './PollingDataStream';
import { OverlayInstance, StreamOverlay, withOverlay } from './StreamOverlay';
import { TeamMemberPreferences, TeamMemberPreferencesRequest } from './TeamMemberPreferences';
import { WindowFocusStream } from './WindowFocusStream';

export class TeamMemberPreferencesStore {
    static get = LazyValueCache((teamId: string) => new TeamMemberPreferencesStore(teamId));

    constructor(private teamId: string) {}

    private overlayStream: StreamOverlay<LoadableState<TeamMemberPreferences>> = new StreamOverlay<
        LoadableState<TeamMemberPreferences>
    >();
    private pollingStream: PollingDataStream<LoadableState<TeamMemberPreferences>> = new PollingDataStream({
        teamId: this.teamId,
        pollFn: async (): Promise<LoadableState<TeamMemberPreferences>> => {
            const memberPreferences = await API.teamMembers.getMemberPreferences({ teamId: this.teamId });
            return { $case: 'ready', value: memberPreferences };
        },
        period: MS.hours(1),
    });

    readonly stream: Stream<LoadableState<TeamMemberPreferences>> = this.pollingStream
        .compose(this.overlayStream.overlayOnStream)
        .remember();

    readonly presetStream: Stream<LoadableState<ResolvedPresets>> = Stream.combine(
        this.stream,
        DataSourcePresetSummaryStore.get(this.teamId).resolvedPresetStream
    )
        .map(([preferencesState, presetState]) => this.joinPreferences(preferencesState, presetState))
        .remember();

    private joinPreferences(
        preferencesState: LoadableState<MemberPreferencesResponse>,
        presetState: LoadableState<ResolvedPreset[]>
    ): LoadableState<ResolvedPresets> {
        if (preferencesState.$case === 'loading' || presetState.$case === 'loading') {
            return { $case: 'loading' };
        }

        const available: ResolvedPreset[] = presetState.value;
        const selected = preferencesState.value.datasourcePresetId
            ? available.find((preset) => preset.id === preferencesState.value.datasourcePresetId)
            : undefined;

        return {
            $case: 'ready',
            value: { available, selected },
        };
    }

    async refresh(): Promise<void> {
        await Promise.all([DataSourcePresetSummaryStore.get(this.teamId).refresh(), this.pollingStream.trigger()]);
    }

    async queueRefreshOnFocus(): Promise<void> {
        WindowFocusStream.queueFocusAction({
            key: 'teamMemberPreferences',
            action: () => this.refresh(),
            until: dayjs().add(5, 'minute'),
        });
    }

    async updatePreferences(preferences: Partial<TeamMemberPreferencesRequest>): Promise<void> {
        const { datasourcePresetPreference, ...prefsOverlay } = preferences;
        const overlayValue: Partial<TeamMemberPreferences> = {
            ...prefsOverlay,
            ...(datasourcePresetPreference && {
                datasourcePresetId: datasourcePresetPreference.id,
            }),
        };

        const overlay: OverlayInstance<LoadableState<TeamMemberPreferences>> = {
            overlayMutationFn: (state) => {
                if (state.$case !== 'ready') {
                    return state;
                }

                return {
                    $case: 'ready',
                    value: { ...state.value, ...overlayValue },
                };
            },
            shouldCancel: (state) => {
                if (state.$case !== 'ready') {
                    return false;
                }
                // @ts-expect-error using any value -- kinda goofy but OK
                return Object.entries(overlayValue).every(([key, value]) => equal(value, state.value[key]));
            },
        };

        await withOverlay([this.overlayStream], overlay, async () => {
            await API.teamMembers.patchMemberPreferences({
                teamId: this.teamId,
                memberPreferencesRequest: preferences,
            });
            await this.pollingStream.trigger();
        });
    }
}
