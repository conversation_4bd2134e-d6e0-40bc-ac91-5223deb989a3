import { API } from '@shared/api';
import { LoginOptionsResponseV2, Provider } from '@shared/api/generatedApi';

import { LoadableState } from '@shared/stores/LoadableState';
import { PollingDataStream } from '@shared/stores/PollingDataStream';
import { ArrayUtils } from '@shared/webUtils';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { ProviderListSorter } from '@shared/webUtils/ProviderUtils';
import { MS } from '@shared/webUtils/TimeUtils';

export type TeamLoginProviderStoreState = LoadableState<{
    loginOptions: LoginOptionsResponseV2;
    providers: Provider[];
    signInProviders: Provider[];
}>;

export class TeamLoginProviderStore {
    static get = LazyValueCache((teamId: string) => new TeamLoginProviderStore(teamId));

    readonly stream: PollingDataStream<TeamLoginProviderStoreState>;

    constructor(private readonly teamId: string) {
        this.stream = new PollingDataStream({
            teamId,
            pollFn: async (): Promise<TeamLoginProviderStoreState> => {
                const loginOptions = await API.auth.loginOptionsV3({
                    teamId,
                });

                // map all provider types to a single provider list
                const { publicProviders, enterpriseProviders, ssoProviders } = loginOptions;
                const _publicProviders = publicProviders.map(({ provider }) => provider);
                const _enterpriseProviders = enterpriseProviders.map(({ provider }) => provider);
                const _ssoProviders = ssoProviders.map(({ provider }) => provider);
                const sortedProviders = ArrayUtils.distinct([
                    ..._ssoProviders,
                    ...[..._publicProviders, ..._enterpriseProviders].sort(ProviderListSorter),
                ]);

                // signInProviders are unique to the shortened provider display name
                const signInProviders = ArrayUtils.distinct(
                    sortedProviders,
                    (provider) => ProviderTraitsUtil.get(provider).shortenedDisplayName
                );

                return {
                    $case: 'ready',
                    value: {
                        loginOptions,
                        providers: sortedProviders,
                        signInProviders,
                    },
                };
            },
            period: MS.minutes(60),
        });
    }

    public async refresh() {
        await this.stream.trigger();
    }
}
