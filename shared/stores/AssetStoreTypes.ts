import { CreateStoreProxyTraits, StoreProxyAction1Args } from '@shared/proxy/StoreProxy/StoreProxyTypes';

export type UploadAssetRequest = {
    teamId: string;
    name: string;
    type: string;

    // the content to upload, Base64-encoded
    contentEncoded: string;
};

export type UploadAssetResponse = {
    downloadUrl: string;
};

export const AssetStoreTraits = CreateStoreProxyTraits({
    category: 'asset',
    keyToString: ({}) => '',
    actions: {
        getAssetUrl: StoreProxyAction1Args<string, string>(),
        uploadAsset: StoreProxyAction1Args<UploadAssetRequest, UploadAssetResponse>(),
    },
    streams: {},
});
