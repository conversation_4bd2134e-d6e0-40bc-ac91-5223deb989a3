import {
    MemberOnboardingStatus,
    MemberOnboardingStatusUpdate,
    PersonOnboardingStatus,
    PersonOnboardingStatusUpdate,
} from '@shared/api/generatedApi';

import {
    CreateStoreProxyTraits,
    StoreProxyAction1Args,
    StoreProxyStream,
} from '@shared/proxy/StoreProxy/StoreProxyTypes';

import { LoadableState } from './LoadableState';

export type PersonOnboarding = {
    person: PersonOnboardingStatus;
    member: MemberOnboardingStatus;
};

export type PersonOnboardingState = LoadableState<PersonOnboarding>;

export type PersonOnboardingTasks = {
    // These are individual tasks that are completed by taking an action
    hasInstalledIntegrations: boolean;
    hasInstalledDesktop: boolean;
    hasSeenDocs: boolean;
    hasInvitedTeamMembers: boolean;

    // If true, the person has completed the onboarding tasks, either by
    // completing each one individually, or by closing the UI
    hasCompletedAllTasks: boolean;

    // Tutorial states which requires no action to complete
    hasSeenAnswersTutorial: boolean;
};

export type PersonOnboardingTasksState = LoadableState<PersonOnboardingTasks>;

export const PersonOnboardingStoreTraits = CreateStoreProxyTraits({
    category: 'PersonOnboarding',
    keyToString: (key: { teamId: string }) => key.teamId,
    actions: {
        /// The raw stream of person and member onboarding information
        update: StoreProxyAction1Args<
            { person?: PersonOnboardingStatusUpdate; member?: MemberOnboardingStatusUpdate },
            void
        >(),
    },
    streams: {
        stream: StoreProxyStream<PersonOnboardingState>(),
        tasks: StoreProxyStream<PersonOnboardingTasksState>(),
    },
});
