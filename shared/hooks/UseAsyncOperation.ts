import { DependencyList, useCallback, useEffect, useState } from 'react';

import { Result } from '../models/Result';
import { getErrorMessage } from '../webUtils';

export function useAsyncOperation<T>(
    operation: () => Promise<T>,
    dependencies: DependencyList = []
): [Result<T, string> | undefined, () => Promise<void>] {
    const [result, setResult] = useState<Result<T, string> | undefined>(undefined);

    const runOperation = useCallback(async () => {
        try {
            const value = await operation();
            setResult({ ok: true, value });
        } catch (e) {
            setResult({ ok: false, error: getErrorMessage(e) });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [...dependencies, setResult]);

    useEffect(() => {
        runOperation();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [...dependencies]);

    return [result, runOperation];
}
