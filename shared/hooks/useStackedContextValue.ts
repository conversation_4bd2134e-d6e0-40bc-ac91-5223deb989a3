import { DependencyList, useCallback, useEffect, useRef } from 'react';

export type StackedContextValueHookFn<T> = (valueFn: () => T, dependencies: DependencyList) => void;

/**
 * Used for Contexts that want to publish state provided by child views.
 *
 * @param defaultValue The default value when no view has provided a value
 * @param callback A callback that is raised whenever the current resolved state changes
 * @param dependencies Dependencies that will cause the callback to re-fire
 * @returns A hook function that can be passed into a Context.  This hook function can be called by child
 * views to provide new state for the context.
 */
export function useStackedContextCallback<T>(
    defaultValue: T,
    callback: (t: T) => void,
    dependencies: DependencyList
): StackedContextValueHookFn<T> {
    // Tracks all states provided by child views
    const items = useRef(new Array<{ id: string; value: T }>());

    const hookFn: StackedContextValueHookFn<T> = useCallback(
        (valueFn: () => T, hookDependencies: DependencyList) => {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            useEffect(
                () => {
                    // Child is providing a new value -- add it to the list
                    const value = valueFn();
                    const thisId = crypto.randomUUID();

                    items.current.push({ id: thisId, value });
                    callback(value);

                    // On umount or dependency change, revoke this view's value, if there is one
                    return () => {
                        const index = items.current.findIndex(({ id }) => id === thisId);
                        if (index >= 0) {
                            items.current.splice(index, 1);
                            const newLastItem = items.current.at(-1);

                            if (newLastItem) {
                                callback(newLastItem.value);
                            } else {
                                callback(defaultValue);
                            }
                        }
                    };
                },
                // eslint-disable-next-line react-hooks/exhaustive-deps
                hookDependencies
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        dependencies
    );

    return hookFn;
}
