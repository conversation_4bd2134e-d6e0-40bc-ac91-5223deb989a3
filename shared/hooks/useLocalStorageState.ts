import { useCallback, useEffect, useRef, useState } from 'react';

import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';

function useLocalStorageStateImpl<T, ResolvedT = T>(
    key: string,
    defaultValue: T,
    resolvingValue: T | ResolvedT,
    getFromLocalStorage: (key: string) => Promise<T | undefined>,
    setInLocalStorage: (key: string, value: T) => Promise<void>
): [ResolvedT | T, (value: T) => void] {
    const [state, setState] = useState<T | ResolvedT>(resolvingValue);
    const hasValueBeenChanged = useRef(false);

    useEffect(() => {
        const getInitialValue = async () => {
            const value = await getFromLocalStorage(key);
            if (!hasValueBeenChanged.current) {
                if (value === undefined) {
                    setState(defaultValue);
                } else {
                    setState(value);
                }
            }
        };

        getInitialValue();
    }, [key, getFromLocalStorage, defaultValue]);

    const changeValue = useCallback(
        (value: T) => {
            hasValueBeenChanged.current = true;

            // If we're setting this to the default value, we can clear storage instead
            if (value === defaultValue) {
                ClientWorkspace.instance().removeLocalStorage(key);
            } else {
                setInLocalStorage(key, value);
            }
            setState(value);
        },
        [defaultValue, key, setInLocalStorage]
    );

    return [state, changeValue];
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateType<T>(
    key: string,
    defaultValue: T,
    resolvingValue: T = defaultValue,
    getter: (value?: string) => T,
    setter: (value: T) => string
): [T, (newValue: T) => void] {
    const getFn = useCallback(
        async (key: string) => {
            const storedValue = await ClientWorkspace.instance().getLocalStorage(key);
            return getter(storedValue);
        },
        [getter]
    );
    const setFn = useCallback(
        (key: string, value: T) => ClientWorkspace.instance().setLocalStorage(key, setter(value)),
        [setter]
    );

    return useLocalStorageStateImpl<T>(key, defaultValue, resolvingValue, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageState(
    key: string,
    defaultValue: string,
    resolvingValue: string = defaultValue
): [string, (newValue: string) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorage(key), []);
    const setFn = useCallback(
        (key: string, value: string) => ClientWorkspace.instance().setLocalStorage(key, value),
        []
    );

    return useLocalStorageStateImpl<string>(key, defaultValue, resolvingValue, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateOptional(
    key: string,
    defaultValue: string
): [string | undefined, (newValue: string) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorage(key), []);
    const setFn = useCallback(
        (key: string, value: string) => ClientWorkspace.instance().setLocalStorage(key, value),
        []
    );

    return useLocalStorageStateImpl<string, undefined>(key, defaultValue, undefined, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateBool(
    key: string,
    defaultValue: boolean,
    resolvingValue: boolean = defaultValue
): [boolean, (newValue: boolean) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorageBool(key), []);
    const setFn = useCallback(
        (key: string, value: boolean) => ClientWorkspace.instance().setLocalStorageBool(key, value),
        []
    );

    return useLocalStorageStateImpl<boolean>(key, defaultValue, resolvingValue, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateBoolOptional(
    key: string,
    defaultValue: boolean
): [boolean | undefined, (newValue: boolean) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorageBool(key), []);
    const setFn = useCallback(
        (key: string, value: boolean) => ClientWorkspace.instance().setLocalStorageBool(key, value),
        []
    );

    return useLocalStorageStateImpl<boolean, undefined>(key, defaultValue, undefined, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateNumber(
    key: string,
    defaultValue: number,
    resolvingValue: number = defaultValue
): [number, (newValue: number) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorageNumber(key), []);
    const setFn = useCallback(
        (key: string, value: number) => ClientWorkspace.instance().setLocalStorageNumber(key, value),
        []
    );

    return useLocalStorageStateImpl<number>(key, defaultValue, resolvingValue, getFn, setFn);
}

// Drop-in replacement for useState, which stores its state in local storage
export function useLocalStorageStateNumberOptional(
    key: string,
    defaultValue: number
): [number | undefined, (newValue: number) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorageNumber(key), []);
    const setFn = useCallback(
        (key: string, value: number) => ClientWorkspace.instance().setLocalStorageNumber(key, value),
        []
    );

    return useLocalStorageStateImpl<number, undefined>(key, defaultValue, undefined, getFn, setFn);
}

// Drop-in replacement for useState, which stores its date in local storage
export function useLocalStorageStateDate(
    key: string,
    defaultValue: Date | undefined,
    resolvingValue: Date | undefined = defaultValue
): [Date | undefined, (newValue: Date) => void] {
    const getFn = useCallback((key: string) => ClientWorkspace.instance().getLocalStorageDate(key), []);
    const setFn = useCallback(async (key: string, value: Date | undefined): Promise<void> => {
        if (value) {
            ClientWorkspace.instance().setLocalStorageDate(key, value);
        } else {
            ClientWorkspace.instance().removeLocalStorage(key);
        }
    }, []);

    return useLocalStorageStateImpl<Date | undefined>(key, defaultValue, resolvingValue, getFn, setFn);
}
