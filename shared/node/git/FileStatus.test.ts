import { FilePath } from '../../webUtils';
import { FileDeleted, FileRenamed, FileStatus } from './FileStatus';

describe('FileStatus', () => {
    test('valid rename', () => {
        const status: FileStatus = new FileRenamed(100, new FilePath('path/old.kt'), new FilePath('path/new.kt'));
        expect(status).toBeInstanceOf(FileRenamed);
        expect(status.similarity).toEqual(100);
        expect(status.oldPath).toEqual(new FilePath('path/old.kt'));
        expect(status.newPath).toEqual(new FilePath('path/new.kt'));
    });

    test('invalid rename', () => {
        expect(() => {
            new FileRenamed(-1, new FilePath('path/old.kt'), new FilePath('path/new.kt'));
        }).toThrow();
        expect(() => {
            new FileRenamed(101, new FilePath('path/old.kt'), new FilePath('path/new.kt'));
        }).toThrow();
        expect(() => {
            new FileRenamed(100, new FilePath('path/file.kt'), new FilePath('path/file.kt'));
        }).toThrow();
        expect(() => {
            new FileRenamed(100, new FilePath('path/file.kt'), new FilePath(''));
        }).toThrow();
        expect(() => {
            new FileRenamed(100, new FilePath(''), new FilePath('path/file.kt'));
        }).toThrow();
    });

    test('valid delete', () => {
        const status: FileStatus = new FileDeleted(new FilePath('path/old.kt'));
        expect(status).toBeInstanceOf(FileDeleted);
        expect(status.path).toEqual(new FilePath('path/old.kt'));
    });

    test('invalid delete', () => {
        expect(() => {
            new FileDeleted(new FilePath(''));
        }).toThrow();
    });
});
