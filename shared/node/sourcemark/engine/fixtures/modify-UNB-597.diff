diff --git shared/stores/ChannelPoller.ts |||shared/stores/ChannelPoller.ts
index a2b470128a58658ca56c414822594864f77bcde3..f98a5afa072384083205fd5cf0b954b3443817c3 100644
--- shared/stores/ChannelPoller.ts
+++ |||shared/stores/ChannelPoller.ts
@@ -2 +2,2 @@ import { API, GetChannelsModifiedSinceRequest } from '../api';
-import { ChannelModifiedSince, ChannelsModifiedSinceResponse } from '../api/models';
+import { ArrayUtils } from '../webUtils';
+import { ChannelsModifiedSinceResponse } from '../api/models';
@@ -9,0 +11,3 @@ const DEFAULT_POLL_MS = 1000;
+type ChannelNameFn = () => string[];
+export type ChannelIds = ChannelNameFn | string | string[];
+
@@ -14,2 +18,5 @@ interface ChannelInfo {
-    // The callbacks for this channel -- these are what fetch data for the channel
-    pollFns: ChannelPollFn[];
+    // The channel name to query for this channel
+    channelIds: ChannelIds;
+
+    // The callback for this channel -- this is what fetches data for the channel
+    pollFn: ChannelPollFn;
@@ -21 +28 @@ interface ChannelInfo {
-    nextPoll?: dayjs.Dayjs;
+    nextPoll: dayjs.Dayjs;
@@ -67 +74 @@ export class ChannelPoller {
-    private channelMap = new Map<string, ChannelInfo>();
+    private channels = new Array<ChannelInfo>();
@@ -85 +92 @@ export class ChannelPoller {
-    startPolling(channel: string, cb: ChannelPollFn, frequencyMS?: number | undefined): CancelPollFn {
+    startPolling(channelIds: ChannelIds, cb: ChannelPollFn, frequencyMS?: number | undefined): CancelPollFn {
@@ -87,6 +94,3 @@ export class ChannelPoller {
-        const channelInfo = this.channelMap.get(channel);
-        if (channelInfo) {
-            channelInfo.pollFns.push(cb);
-        } else {
-            this.channelMap.set(channel, {
-                pollFns: [cb],
+        const channelInfo = {
+            channelIds,
+            pollFn: cb,
@@ -93,0 +98 @@ export class ChannelPoller {
+            nextPoll: dayjs(), // Do first poll immediately
@@ -95,2 +100,2 @@ export class ChannelPoller {
-            });
-        }
+        };
+        this.channels.push(channelInfo);
@@ -107,17 +112,3 @@ export class ChannelPoller {
-            this.cancelPolling(channel, cb);
-        };
-    }
-
-    private cancelPolling(channel: string, cb: ChannelPollFn) {
-        const channelInfo = this.channelMap.get(channel);
-        if (!channelInfo) {
-            return;
-        }
-
-        const index = channelInfo.pollFns.indexOf(cb);
-        if (index >= 0) {
-            channelInfo.pollFns.splice(index, 1);
-        }
-
-        if (channelInfo.pollFns.length === 0) {
-            this.channelMap.delete(channel);
+            const idx = this.channels.indexOf(channelInfo);
+            if (idx >= 0) {
+                this.channels.splice(idx, 1);
@@ -124,0 +116 @@ export class ChannelPoller {
+        };
@@ -129 +121 @@ export class ChannelPoller {
-        if (this.channelMap.size === 0) {
+        if (this.channels.length === 0) {
@@ -143,3 +135,15 @@ export class ChannelPoller {
-        const channelsToPoll: ChannelModifiedSince[] = [];
-        const channelsToFetch: string[] = [];
-        const now = dayjs();
+        // Poll to see which channels have changed
+        const channelsThatChanged = await this.runPoll();
+        const channelsThatHaventRun = this.channels.filter((channel) => channel.lastModified === undefined);
+
+        const channelsToFetch = channelsThatHaventRun.concat(channelsThatChanged);
+
+        // For each channel that has changed, notify its subscribers
+        const promises = channelsToFetch.map((channelInfo) => this.fetchFn(channelInfo));
+        await Promise.all(promises);
+
+        // Wait awhile, then poll again
+        setTimeout(() => {
+            this.doPoll();
+        }, this.pollIntervalMS);
+    }
@@ -147,5 +151,5 @@ export class ChannelPoller {
-        this.channelMap.forEach((info, channel) => {
-            if (info.lastModified === undefined) {
-                channelsToFetch.push(channel);
-            } else if (info.lastModified && (!info.nextPoll || now.isAfter(info.nextPoll))) {
-                channelsToPoll.push({ channel, ifModifiedSince: info.lastModified });
+    private getChannelIds(ids: ChannelIds): string[] {
+        if (typeof ids === 'function') {
+            return ids();
+        } else if (Array.isArray(ids)) {
+            return ids;
@@ -153 +156,0 @@ export class ChannelPoller {
-        });
@@ -155,2 +158,22 @@ export class ChannelPoller {
-        // Poll to see which channels have changed
-        if (channelsToPoll.length > 0) {
+        return [ids];
+    }
+
+    // Execute a single poll of the getChannelsModifiedSince endpoint, for whichever channels require
+    // a poll.  Return the set of channels that have changed, and require a fetch.
+    private async runPoll(): Promise<ChannelInfo[]> {
+        const now = dayjs();
+
+        // Make a list of all subscriptions to all channels
+        const channelIdTuples = this.channels
+            .filter((channel) => !!channel.lastModified && now.isAfter(channel.nextPoll))
+            .flatMap((channel) => this.getChannelIds(channel.channelIds).map((channelId) => ({ channel, channelId })));
+
+        // Group subscriptions by channel, and map join their lastModified value
+        const channelsToPoll = ArrayUtils.groupByMap(channelIdTuples, (channelTuple) => channelTuple.channelId);
+
+        const pollArgs = Array.from(channelsToPoll.entries()).map(([channel, channelInfo]) => ({
+            channel,
+            ifModifiedSince: channelInfo[0]?.channel.lastModified || '',
+        }));
+
+        let result;
@@ -158 +181 @@ export class ChannelPoller {
-                const result = await this.channelPollFn({
+            result = await this.channelPollFn({
@@ -161 +184 @@ export class ChannelPoller {
-                        channels: channelsToPoll,
+                    channels: pollArgs,
@@ -164,9 +186,0 @@ export class ChannelPoller {
-                channelsToFetch.push(...result.channels);
-
-                // Set the next polling time for each channel
-                for (const channel of channelsToPoll) {
-                    const info = this.channelMap.get(channel.channel);
-                    if (info) {
-                        info.nextPoll = dayjs().add(info.pollIntervalMs);
-                    }
-                }
@@ -176 +190 @@ export class ChannelPoller {
-            }
+            return [];
@@ -179,6 +193,6 @@ export class ChannelPoller {
-        // For each channel that has changed, notify its subscribers
-        const promises = channelsToFetch.flatMap((channel) => {
-            const info = this.channelMap.get(channel);
-            return info ? info.pollFns.map((pollFn) => this.runPollFn(info, pollFn)) : [];
-        });
-        await Promise.all(promises);
+        // Set the next polling timestamp for each channel we polled
+        for (const [, channelTuples] of channelsToPoll) {
+            for (const channelTuple of channelTuples) {
+                channelTuple.channel.nextPoll = dayjs().add(channelTuple.channel.pollIntervalMs);
+            }
+        }
@@ -186,4 +200,3 @@ export class ChannelPoller {
-        // Wait awhile, then poll again
-        setTimeout(() => {
-            this.doPoll();
-        }, this.pollIntervalMS);
+        return result.channels.flatMap(
+            (channel) => channelsToPoll.get(channel)?.map((channelTuple) => channelTuple.channel) ?? []
+        );
@@ -193 +206 @@ export class ChannelPoller {
-    private async runPollFn(info: ChannelInfo, pollFn: ChannelPollFn) {
+    private async fetchFn(info: ChannelInfo) {
@@ -195 +208 @@ export class ChannelPoller {
-            const lastModified = await pollFn(info.lastModified || undefined);
+            const lastModified = await info.pollFn(info.lastModified || undefined);
