import { Stream } from 'xstream';

import { Mark } from '../../api/models';
import { FilePath, UUID } from '../../webUtils';
import { RepoInfo } from './engine/RepoInfo';
import { SourcePointResolution } from './engine/SourcePointResolution';
import { UnsavedChangeTracker } from './engine/UnsavedChangeTracker';
import { UnsavedFileEntry } from './engine/UnsavedFileEntry';

/**
 * Provide APIs to obtain sourcemarks that are up-to-date with respect to the Git workspace.
 */
export interface ISourceMarkProvider {
    hybridSourcemarkEngine: boolean;

    /**
     * Given a sourcemark ID returns the up-to-date point for the sourcemark with respect to the Git workspace.
     * When an up-to-date point cannot be calculated returns additional information to help resolve the point.
     *
     * @param repo repo information
     * @param sourceMarkId the sourcemark ID
     * @param unsavedFiles files that have not been changed in the IDE, but not yet saved to disk
     */
    getSourceMarkLatestPoint: (
        repo: RepoInfo,
        sourceMarkId: UUID,
        unsavedFiles: UnsavedFileEntry[]
    ) => Promise<SourcePointResolution>;

    /**
     * Given a file returns up-to-date points with respect to the Git workspace for every sourcemark in the file.
     * The stream will be subscribed while the IDE is editing this file, and unsubscribed when the file is no longer
     * being used in the IDE.
     * @param repo repo information
     * @param file the file of interest
     * @param unsavedChangeTracker an object that can return the set of locally-edited files at any time
     * @param refreshStream a stream that indicates when sourcemarks should be recalculated, based on editing and saving
     */
    getSourceMarkStreamForFile: (
        repo: RepoInfo,
        file: FilePath,
        unsavedChangeTracker: UnsavedChangeTracker,
        refreshStream: Stream<void>
    ) => Stream<Mark[]>;

    /**
     * Set the list of files in use by the IDE
     * Marks for these files will be prioritized and preloaded so that they can be
     * displayed quickly.
     * @param repo the repo
     * @param files the set of files that are active
     */
    addActiveFiles: (repo: RepoInfo, files: FilePath[]) => void;

    /**
     * Fully recalculate a repo.
     *
     * The sourcemark engine will compute updated sourcemark points. Returns a promise that will complete when
     * full recalculation has completed and new points have been uploaded.
     *
     * @param repo repo information
     */
    recalculateRepo(repo: RepoInfo): Promise<void>;
}
