import { SshHostnameResolver } from './SshHostnameResolver';

describe('SshHostnameResolver', () => {
    let mockRun: jest.Mock;
    let mockSshRunner: { run: jest.Mock };
    let sshHostnameResolver: SshHostnameResolver;

    beforeEach(() => {
        mockRun = jest.fn();
        mockSshRunner = { run: mockRun };
        sshHostnameResolver = new SshHostnameResolver(mockSshRunner);
    });

    describe('resolveHostname', () => {
        test('resolves hostname when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveHostname('bucket');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual('bitbucket.org');
        });

        test('fallback when hostname is missing from SSH config', async () => {
            mockRun.mockResolvedValue('user git\nport 22\n');

            const result = await sshHostnameResolver.resolveHostname('example.com');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'example.com'], false);
            expect(result).toEqual('example.com');
        });

        test('fallback when hostname is corrupt SSH config', async () => {
            mockRun.mockResolvedValue('user git\nhostname \nport 22\n');

            const result = await sshHostnameResolver.resolveHostname('example.com');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'example.com'], false);
            expect(result).toEqual('example.com');
        });

        test('fallback when SSH config is empty', async () => {
            mockRun.mockResolvedValue('');

            const result = await sshHostnameResolver.resolveHostname('example.com');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'example.com'], false);
            expect(result).toEqual('example.com');
        });

        test('fallback when SSH config is rejected', async () => {
            mockRun.mockRejectedValue('error');

            const result = await sshHostnameResolver.resolveHostname('example.com');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'example.com'], false);
            expect(result).toEqual('example.com');
        });

        test('leverages read through cache', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            // loop to ensure cache is hit
            for (let i = 0; i < 3; i++) {
                const result = await sshHostnameResolver.resolveHostname('bucket');
                expect(result).toEqual('bitbucket.org');
            }

            expect(mockSshRunner.run).toHaveBeenCalledTimes(1);
            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
        });
    });

    describe('resolveUrl', () => {
        test('resolves https url when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrl(new URL('https://bucket/org/repo.git'));

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual(new URL('https://bitbucket.org/org/repo.git'));
        });

        test('resolves ssh url when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrl(new URL('git+ssh://git@bucket/org/repo.git'));

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual(new URL('git+ssh://*****************/org/repo.git'));
        });
    });

    describe('resolveUrlString', () => {
        test('resolves https url when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrlString('https://bucket/org/repo.git');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual('https://bitbucket.org/org/repo.git');
        });

        test('resolves ssh url when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrlString('git+ssh://git@bucket/org/repo.git');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual('git+ssh://*****************/org/repo.git');
        });

        test('resolves scp url when mapped', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrlString('git@bucket:org/repo.git');

            expect(mockSshRunner.run).toHaveBeenCalledWith(['-G', 'bucket'], false);
            expect(result).toEqual('*****************:org/repo.git');
        });

        test('fallback when not a URL', async () => {
            mockRun.mockResolvedValue('user git\nhostname bitbucket.org\nport 22\n');

            const result = await sshHostnameResolver.resolveUrlString('not a url');

            expect(result).toEqual('not a url');
        });
    });
});
