import { CustomError } from '../../error/CustomError';

export class RunnerError extends CustomError {
    constructor(
        command: string,
        args: string[],
        public readonly stdout: string,
        public readonly stderr: string
    ) {
        const message = `Command failed: ${command} ${args.join(' ')}\nstdout:\n${stdout}\nstderr:\n${stderr}`;
        super('RunnerError', message);
    }
}
