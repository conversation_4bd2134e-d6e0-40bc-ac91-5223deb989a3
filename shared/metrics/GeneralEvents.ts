import { IdeActivityEvent } from '@shared/api/generatedApi';

import { API } from '../api';

export class GeneralEvents {
    public async viewContent(teamId: string): Promise<void> {
        try {
            await API.metrics.viewContent({
                teamId,
            });
        } catch (e) {
            console.error('ThreadMetrics.ViewDashboardContent', e);
            return;
        }
    }

    public async viewIdeInsights(teamId: string): Promise<void> {
        try {
            await API.metrics.viewIdeInsights({
                teamId,
            });
        } catch (e) {
            console.error('ThreadMetrics.ViewIdeInsights', teamId, e);
            return;
        }
    }

    public async viewIdeInsightsForTeams(teamIds: string[]): Promise<void> {
        await Promise.all(teamIds.map((teamId) => this.viewIdeInsights(teamId)));
    }

    public async closeIdeInsights(teamId: string): Promise<void> {
        try {
            await API.metrics.closeIdeInsights({
                teamId,
            });
        } catch (e) {
            console.error('ThreadMetrics.CloseIdeInsights', teamId, e);
            return;
        }
    }

    public async closeIdeInsightsForTeams(teamIds: string[]): Promise<void> {
        await Promise.all(teamIds.map((teamId) => this.closeIdeInsights(teamId)));
    }

    public async viewIdeSidebar(teamId: string): Promise<void> {
        try {
            await API.metrics.viewIdeSidebar({
                teamId,
            });
        } catch (e) {
            console.error('ThreadMetrics.ViewIdeSidebar', teamId, e);
            return;
        }
    }

    public async viewIdeSidebarForTeams(teamIds: string[]): Promise<void> {
        await Promise.all(teamIds.map((teamId) => this.viewIdeSidebar(teamId)));
    }

    public async closeIdeSidebar(teamId: string): Promise<void> {
        try {
            await API.metrics.closeIdeSidebar({
                teamId,
            });
        } catch (e) {
            console.error('ThreadMetrics.CloseIdeSidebar', teamId, e);
            return;
        }
    }

    public async closeIdeSidebarForTeams(teamIds: string[]): Promise<void> {
        await Promise.all(teamIds.map((teamId) => this.closeIdeSidebar(teamId)));
    }

    public async visitedProcessingComplete(teamId: string): Promise<void> {
        try {
            await API.metrics.visitedProcessingComplete({
                teamId,
            });
        } catch (e) {
            console.error('EmailMetrics.VisitedProcessingComplete', teamId, e);
            return;
        }
    }

    public async openedAnswerPreferencesTooltip(teamId: string): Promise<void> {
        try {
            await API.metrics.openAnswerPreferences({
                teamId,
            });
        } catch (e) {
            console.error('EmailMetrics.openAnswerPreferences', teamId, e);
            return;
        }
    }

    public async triggerIdeActivityEvent(
        teamId: string,
        ideActivityEvent: IdeActivityEvent | undefined
    ): Promise<void> {
        if (!ideActivityEvent) {
            return;
        }
        try {
            await API.metrics.triggerIdeActivityEvent({
                teamId,
                ideActivityEvent,
            });
        } catch (e) {
            console.error('ThreadMetrics.TriggerIdeActivityEvent', teamId, e);
            return;
        }
    }
}
