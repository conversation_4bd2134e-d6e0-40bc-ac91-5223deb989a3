/**
 * Any custom Error classes should extend from this class, instead of <PERSON><PERSON>r
 * directly, in order to maintain a correct prototype chain.
 *
 * See this for more information: https://stackoverflow.com/questions/41102060/typescript-extending-error-class
 */
export class CustomError extends Error {
    constructor(
        public name: string,
        message?: string
    ) {
        super(message);

        const actualProto = new.target.prototype;

        if (Object.setPrototypeOf) {
            Object.setPrototypeOf(this, actualProto);
        } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            this.__proto__ = actualProto;
        }
    }
}
