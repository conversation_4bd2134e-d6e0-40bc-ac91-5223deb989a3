package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.toInstallation
import com.nextchaptersoftware.db.models.toOrg
import org.jetbrains.exposed.sql.ResultRow

data class InstallationBundle(
    val org: Org,
    val installation: Installation,
)

fun ResultRow.toInstallationBundle() = InstallationBundle(
    org = toOrg(),
    installation = toInstallation(),
)
