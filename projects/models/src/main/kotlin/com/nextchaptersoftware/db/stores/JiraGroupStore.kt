package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.JiraGroup
import com.nextchaptersoftware.db.models.JiraGroupDAO
import com.nextchaptersoftware.db.models.JiraGroupId
import com.nextchaptersoftware.db.models.JiraGroupMemberModel
import com.nextchaptersoftware.db.models.JiraGroupModel
import com.nextchaptersoftware.db.models.JiraSiteId
import com.nextchaptersoftware.db.models.JiraSiteModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertIgnore

class JiraGroupStore internal constructor() {
    suspend fun list(
        jiraSiteId: JiraSiteId,
        additionalWhereClause: Op<Boolean>? = null,
        limit: Int? = null,
    ): List<JiraGroup> = suspendedTransaction {
        JiraGroupDAO.find {
            AllOp(
                JiraGroupModel.jiraSite eq jiraSiteId,
                additionalWhereClause,
            )
        }.apply {
            limit?.let(::limit)
        }.map {
            it.asDataModel()
        }
    }

    suspend fun upsert(
        jiraSiteId: JiraSiteId,
        groupId: String,
        applicationRoles: List<String>,
    ): Unit = suspendedTransaction {
        JiraGroupModel.insertIgnore {
            it[JiraGroupModel.jiraSite] = jiraSiteId
            it[JiraGroupModel.groupId] = groupId
            it[JiraGroupModel.applicationRoles] = applicationRoles
        }
    }

    suspend fun listGroupsForUser(
        installationId: InstallationId,
        userId: String,
    ): List<JiraGroup> {
        return suspendedTransaction {
            JiraGroupModel
                .join(
                    otherTable = JiraGroupMemberModel,
                    joinType = JoinType.INNER,
                    onColumn = JiraGroupModel.id,
                    otherColumn = JiraGroupMemberModel.jiraGroup,
                )
                .join(
                    otherTable = JiraSiteModel,
                    joinType = JoinType.INNER,
                    onColumn = JiraGroupModel.jiraSite,
                    otherColumn = JiraSiteModel.id,
                )
                .join(
                    otherTable = InstallationModel,
                    joinType = JoinType.INNER,
                    onColumn = JiraSiteModel.installation,
                    otherColumn = InstallationModel.id,
                )
                .select(JiraGroupModel.columns)
                .whereAll(
                    InstallationModel.id eq installationId,
                    InstallationStore.INSTALLATION_EXISTS,
                    JiraGroupMemberModel.userId eq userId,
                )
                .map { JiraGroupDAO.wrapRow(it).asDataModel() }
        }
    }

    suspend fun delete(
        jiraSiteId: JiraSiteId,
        ids: List<JiraGroupId>,
    ): Unit = suspendedTransaction {
        JiraGroupModel.deleteWhere {
            (JiraGroupModel.id inList ids.distinct()) and (JiraGroupModel.jiraSite eq jiraSiteId)
        }
    }
}
