@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

object OrgModel : ServiceModel<OrgId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val displayName = text(name = "displayName")
    val avatarUrl = text(name = "avatarUrl")
    val htmlUrl = text(name = "htmlUrl")

    /** Display name for the org set by the customer */
    val customDisplayName = text(name = "customDisplayName").nullable()

    /**
     * Identity that created the org.
     */
    val createdBy = optReference(
        name = "createdBy",
        foreign = IdentityModel,
        onDelete = ReferenceOption.SET_NULL,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    /** Org completed processing at this instant */
    val enabledAt = timestamp(name = "enabledAt").nullable()

    val isDeleted = bool("isDeleted").clientDefault { false }.index()

    /**
     * True if the org needs reprocessing.
     * This is set on some org that have completed processing, became inactive, and rather than maintaining their data, we reset the team.
     */
    val needsReprocessing = bool("needsReprocessing").nullable()

    val onboardingStates = uinteger("onboardingStates").clientDefault { 0u }.nullable()

    val subscribedReleaseChannel = enumerationByDbOrdinal("subscribedReleaseChannel", ReleaseChannel::class).nullable()
}

fun ResultRow.toOrg(alias: String? = null) = OrgDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toOrgOrNull(alias: String? = null) = OrgDAO.wrapRowOrNull(this, alias)?.asDataModel()

class OrgDAO(id: EntityID<OrgId>) : EntityExtensions<Org, OrgId>(id) {
    companion object : EntityClassExtensions<OrgId, OrgDAO>(OrgModel)

    override var createdAt by OrgModel.createdAt
    override var modifiedAt by OrgModel.modifiedAt

    var avatarUrl by OrgModel.avatarUrl
    var createdBy by IdentityDAO optionalReferencedOn OrgModel.createdBy
    var customDisplayName by OrgModel.customDisplayName
    var displayName by OrgModel.displayName
    var enabledAt by OrgModel.enabledAt
    var htmlUrl by OrgModel.htmlUrl
    var isDeleted by OrgModel.isDeleted
    var needsReprocessing by OrgModel.needsReprocessing
    var onboardingStates by OrgModel.onboardingStates
    var subscribedReleaseChannel by OrgModel.subscribedReleaseChannel

    override fun asDataModel() = readValues.let {
        Org(
            id = it[OrgModel.id].value,
            createdAt = it[OrgModel.createdAt],
            modifiedAt = it[OrgModel.modifiedAt],
            avatarUrl = it[OrgModel.avatarUrl].asUrl,
            createdBy = it[OrgModel.createdBy]?.value,
            displayName = it[OrgModel.customDisplayName] ?: it[OrgModel.displayName],
            enabledAt = it[OrgModel.enabledAt],
            htmlUrl = it[OrgModel.htmlUrl].asUrl,
            isDeleted = it[OrgModel.isDeleted],
            needsReprocessing = it[OrgModel.needsReprocessing] ?: false,
            onboardingStates = it[OrgModel.onboardingStates] ?: 0u,
            subscribedReleaseChannel = it[OrgModel.subscribedReleaseChannel] ?: ReleaseChannel.Stable,
        )
    }
}

object OrgIdConverter : ValueIdConverter<OrgId> {
    override val factory = ::OrgId
    override val extract = OrgId::value
}

internal object OrgIdSerializer : ValueIdSerializer<OrgId>(
    serialName = "OrgId",
    converter = OrgIdConverter,
)

@Serializable(with = OrgIdSerializer::class)
data class OrgId(val value: UUID) : ValueId {

    companion object : ValueIdClass<OrgId>(
        converter = OrgIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is OrgId)
        return value.compareTo(other.value)
    }
}

data class Org(
    val id: OrgId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val avatarUrl: Url,
    val createdBy: IdentityId?,
    val displayName: String,
    val enabledAt: Instant?,
    val htmlUrl: Url,
    val isDeleted: Boolean,
    val needsReprocessing: Boolean,
    val onboardingStates: UInt,
    val subscribedReleaseChannel: ReleaseChannel,
) {
    val isPending: Boolean
        get() = enabledAt == null

    val isProcessingComplete
        get() = !isPending

    fun hasOnboardingStateSet(state: OrgOnboardingState): Boolean {
        return state.isSet(onboardingStates)
    }

    val hasCompletedTeamOnboarding = hasOnboardingStateSet(OrgOnboardingState.HasCompletedTeamOnboarding)
}

fun Org.dashboardUrl(urlBuilderProvider: UrlBuilderProvider): Url {
    return urlBuilderProvider.dashboard().withOrg(id.value).build()
}
