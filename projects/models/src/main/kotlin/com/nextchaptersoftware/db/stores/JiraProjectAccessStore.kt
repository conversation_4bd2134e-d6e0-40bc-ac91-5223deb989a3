package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.JiraProjectAccess
import com.nextchaptersoftware.db.models.JiraProjectAccessDAO
import com.nextchaptersoftware.db.models.JiraProjectAccessModel
import com.nextchaptersoftware.db.models.JiraProjectId
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertIgnore

class JiraProjectAccessStore internal constructor() {
    suspend fun list(
        projectId: JiraProjectId,
    ): List<JiraProjectAccess> = suspendedTransaction {
        JiraProjectAccessDAO
            .find { JiraProjectAccessModel.project eq projectId }
            .map { it.asDataModel() }
    }

    suspend fun list(
        identityId: IdentityId,
    ): List<JiraProjectAccess> = suspendedTransaction {
        JiraProjectAccessDAO
            .find { JiraProjectAccessModel.identity eq identityId }
            .map { it.asDataModel() }
    }

    suspend fun upsert(
        projectId: JiraProjectId,
        identityId: IdentityId,
    ) = suspendedTransaction {
        JiraProjectAccessModel.insertIgnore {
            it[this.project] = projectId
            it[this.identity] = identityId
        }
    }

    suspend fun delete(
        projectId: JiraProjectId,
        identityId: IdentityId,
    ) = suspendedTransaction {
        JiraProjectAccessModel.deleteWhere {
            (this.project eq projectId) and (this.identity eq identityId)
        }
    }
}
