package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AWSAccount
import com.nextchaptersoftware.db.models.AWSAccountId
import com.nextchaptersoftware.db.models.AWSAccountModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.toAWSAccount
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.upsertReturning

class AWSAccountStore internal constructor() {
    suspend fun findByOrgId(
        orgId: OrgId,
    ): List<AWSAccount> {
        return suspendedTransaction {
            AWSAccountModel
                .selectAll()
                .where { AWSAccountModel.org eq orgId }
                .map { it.toAWSAccount() }
        }
    }

    suspend fun findById(
        awsAccountId: AWSAccountId,
    ): AWSAccount? {
        return findByIds(
            awsAccountIds = listOf(awsAccountId),
        ).firstOrNull()
    }

    suspend fun findByIds(
        awsAccountIds: List<AWSAccountId>,
    ): List<AWSAccount> {
        return suspendedTransaction {
            AWSAccountModel
                .selectAll()
                .whereAll(
                    AWSAccountModel.id inList awsAccountIds,
                )
                .map { it.toAWSAccount() }
        }
    }

    suspend fun deleteById(
        awsAccountId: AWSAccountId,
    ) {
        suspendedTransaction {
            AWSAccountModel.deleteWhere {
                AWSAccountModel.id eq awsAccountId
            }
        }
    }

    suspend fun upsert(
        orgId: OrgId,
        installationId: InstallationId,
        accountId: String,
        roleArn: String,
        externalId: String,
    ): AWSAccount {
        return suspendedTransaction {
            AWSAccountModel.upsertReturning(
                keys = arrayOf(AWSAccountModel.org, AWSAccountModel.installation, AWSAccountModel.accountId),
                onUpdateExclude = AWSAccountModel.columns - setOfNotNull(
                    AWSAccountModel.roleArn,
                    AWSAccountModel.externalId,
                ),
            ) { insertStatement ->
                insertStatement[this.org] = orgId
                insertStatement[this.installation] = installationId
                insertStatement[this.accountId] = accountId
                insertStatement[this.roleArn] = roleArn
                insertStatement[this.externalId] = externalId
            }.let {
                it.first().toAWSAccount()
            }
        }
    }
}
