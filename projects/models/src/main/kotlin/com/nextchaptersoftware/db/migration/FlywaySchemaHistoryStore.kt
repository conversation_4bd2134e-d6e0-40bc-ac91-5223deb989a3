package com.nextchaptersoftware.db.migration

import com.nextchaptersoftware.config.DatabaseFlywayMigrationConfig
import com.nextchaptersoftware.db.common.config.DbConfigProvider
import com.nextchaptersoftware.db.dbcp2.DynamicDataSource
import org.jetbrains.exposed.sql.Database
import org.jetbrains.exposed.sql.transactions.transaction

/**
 * Low-level helper for destructive / corrective operations on Flyway’s
 * schema-history table.
 *
 * Each function spins up its own [DynamicDataSource] from the supplied
 * [DbConfigProvider] (mirroring the pattern used by `FlywayMigrationRepository`).
 *
 * @param dbConfigProvider      Effective DB config (already merged with overrides).
 * @param flywayMigrationConfig Full Flyway migration settings (contains the table name).
 */
class FlywaySchemaHistoryStore(
    private val dbConfigProvider: DbConfigProvider,
    private val flywayMigrationConfig: DatabaseFlywayMigrationConfig,
) {
    private val tableName: String = flywayMigrationConfig.schemaHistoryTable

    // ---------------------------------------------------------------------
    // Public API
    // ---------------------------------------------------------------------

    /** Drop the entire Flyway schema-history table (if it exists). */
    fun dropTable() = withDatabase { db ->
        transaction(db) {
            exec("DROP TABLE IF EXISTS $tableName")
        }
    }

    /**
     * Rolls back to the specified migration version by deleting **all newer**
     * migration rows.
     *
     * @param version The migration version to keep (inclusive). Later versions
     *                will be deleted.
     */
    fun rollbackToVersion(version: Int) = withDatabase { db ->
        transaction(db) {
            exec(
                """
            DELETE FROM $tableName
            WHERE installed_rank >
                  (SELECT installed_rank
                     FROM $tableName
                    WHERE version = '$version')
            """.trimIndent(),
            )
        }
    }

    // ---------------------------------------------------------------------
    // Internal helpers
    // ---------------------------------------------------------------------

    private inline fun <R> withDatabase(block: (Database) -> R): R {
        val dataSource = DynamicDataSource(dbConfigProvider = dbConfigProvider)
        return block(Database.connect(dataSource))
    }
}
