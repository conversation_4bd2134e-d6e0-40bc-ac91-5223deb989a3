package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.SampleQuestion
import com.nextchaptersoftware.db.models.SampleQuestionDAO
import com.nextchaptersoftware.db.models.SampleQuestionId
import com.nextchaptersoftware.db.models.SampleQuestionModel
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.toSampleQuestion
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.update

class SampleQuestionStore internal constructor() {
    suspend fun create(
        orgMemberId: OrgMemberId,
        query: String,
    ): SampleQuestion = suspendedTransaction {
        SampleQuestionModel.insert {
            it[this.orgMember] = orgMemberId
            it[this.query] = query
        }.resultedValues?.firstOrNull()?.let {
            it.toSampleQuestion()
        } ?: throw IllegalStateException("Failed to create sample question")
    }

    suspend fun find(
        trx: Transaction? = null,
        orgId: OrgId,
        id: SampleQuestionId,
    ): SampleQuestion? = suspendedTransaction(trx) {
        SampleQuestionModel
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = SampleQuestionModel.orgMember,
                joinType = JoinType.INNER,
            ) {
                OrgMemberModel.org eq orgId
            }
            .select(SampleQuestionModel.columns)
            .where { (SampleQuestionModel.id eq id) }
            .map { it.toSampleQuestion() }
            .firstOrNull()
    }

    suspend fun findForMember(
        orgMemberId: OrgMemberId,
    ): List<SampleQuestion> = suspendedTransaction {
        SampleQuestionDAO.find {
            SampleQuestionModel.orgMember eq orgMemberId
        }.map { it.asDataModel() }
    }

    suspend fun deleteForOrgMember(
        orgMemberId: OrgMemberId,
    ) = suspendedTransaction {
        SampleQuestionModel.deleteWhere {
            this.orgMember eq orgMemberId
        }
    }

    suspend fun setThread(
        sampleQuestionId: SampleQuestionId,
        threadId: ThreadId,
    ) = suspendedTransaction {
        SampleQuestionModel.update(
            {
                SampleQuestionModel.id eq sampleQuestionId
            },
        ) {
            it[this.thread] = threadId
        }
    }

    suspend fun setInference(
        sampleQuestionId: SampleQuestionId,
        inferenceId: MLInferenceId,
    ) = suspendedTransaction {
        SampleQuestionModel.update(
            {
                SampleQuestionModel.id eq sampleQuestionId
            },
        ) {
            it[this.inference] = inferenceId
        }
    }

    suspend fun setSearchFailed(
        sampleQuestionId: SampleQuestionId,
    ) = suspendedTransaction {
        SampleQuestionModel.update(
            {
                SampleQuestionModel.id eq sampleQuestionId
            },
        ) {
            it[this.searchFailed] = true
        }
    }

    suspend fun deleteAllForPerson(
        personId: PersonId,
    ) = suspendedTransaction {
        OrgMemberModel
            .select(OrgMemberModel.id)
            .where { OrgMemberModel.person eq personId }
            .map { it[OrgMemberModel.id].value }
            .also { orgMemberIds ->
                SampleQuestionModel.deleteWhere {
                    this.orgMember inList orgMemberIds
                }
            }
    }
}
