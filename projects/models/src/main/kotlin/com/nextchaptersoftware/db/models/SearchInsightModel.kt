@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.db.common.column.tsvector
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

object SearchInsightModel : ServiceModel<SearchInsightId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val org = reference(
        name = "org",
        foreign = OrgModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val repo = reference(
        name = "repo",
        foreign = RepoModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).nullable()

    val insight = uuid("insight")

    val insightType = enumerationByDbOrdinal("insight_type", InsightType::class)

    val insightIsPrivate = bool("insight_is_private").nullable()

    val recency = timestamp("recency")

    val count = integer("count")

    val searchVector = tsvector("search_vector")

    // In practice this is a required field. SearchInsightStore upsert functions require it
    val provider = enumerationByDbOrdinal("provider", Provider::class).nullable()

    init {
        uniqueIndex(insight)
        index(isUnique = false, org, insightType, insightIsPrivate, recency)
        index(isUnique = false, org, provider)
        index(isUnique = false, repo)
        index(isUnique = false, recency)
        index(customIndexName = null, isUnique = false, searchVector, indexType = "GIN")
    }
}

fun ResultRow.toSearchInsight(alias: String? = null) = SearchInsightDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toSearchInsightOrNull(alias: String? = null) = SearchInsightDAO.wrapRowOrNull(this, alias)?.asDataModel()

class SearchInsightDAO(id: EntityID<SearchInsightId>) : EntityExtensions<SearchInsight, SearchInsightId>(id) {

    companion object : EntityClassExtensions<SearchInsightId, SearchInsightDAO>(SearchInsightModel)

    override var createdAt: Instant by SearchInsightModel.createdAt
    override var modifiedAt: Instant by SearchInsightModel.modifiedAt

    override fun asDataModel(): SearchInsight = readValues.let {
        SearchInsight(
            id = it[SearchInsightModel.id].value,
            createdAt = it[SearchInsightModel.createdAt],
            modifiedAt = it[SearchInsightModel.modifiedAt],
        )
    }
}

object SearchInsightIdConverter : ValueIdConverter<SearchInsightId> {
    override val factory = ::SearchInsightId
    override val extract = SearchInsightId::value
}

internal object SearchInsightIdSerializer : ValueIdSerializer<SearchInsightId>(
    serialName = "SearchInsightId",
    converter = SearchInsightIdConverter,
)

@Serializable(with = SearchInsightIdSerializer::class)
data class SearchInsightId(val value: UUID) : ValueId {

    companion object : ValueIdClass<SearchInsightId>(
        converter = SearchInsightIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is SearchInsightId)
        return value.compareTo(other.value)
    }
}

data class SearchInsight(
    val id: SearchInsightId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
)
