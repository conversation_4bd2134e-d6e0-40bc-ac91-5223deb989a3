@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgStore.Companion.ORG_EXISTS_CLAUSE
import com.nextchaptersoftware.db.stores.ScmTeamStore.Companion.SCM_TEAM_EXISTS_CLAUSE
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

object MemberModel : ServiceModel<MemberId>(
    overrideName = "teammembermodel",
    idColumnType = ValueIdConverter.idColumnType(),
) {

    val installation = reference(
        name = "installation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val identity = reference(
        name = "identity",
        foreign = IdentityModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    /**
     * An [OrgMemberModel] is a unifying model for members across teams for a person.
     */
    val orgMember = reference(
        name = "orgMember",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    /**
     * A [MemberDAO] can become inactive if they leave, or are removed, or are suspended from an [InstallationDAO].
     * Note that we NEVER delete the [Identity], and never delete the [MemberDAO] in those cases;
     * this is because we have references to the [Identity] and [MemberDAO] throughout the system that if deleted
     * would render content as gibberish. So instead we mark the [MemberDAO]'s [isCurrentMember] to false.
     */
    val isCurrentMember = bool("isCurrentMember").clientDefault { true }

    /**
     * A primary member is a direct member of the team.
     *
     * Confusingly, we create JIRA, Linear, and Slack [MemberModel]s _within the primary team_ for legacy reasons;
     * and these JIRA, Linear, and Slack members are not primary members.
     *
     * For example, consider a GitHub team where the user has also connected JIRA, Linear, and Slack.
     * All the members from GitHub, JIRA, Linear, and Slack are members of the _GitHub_ (primary) team.
     * The purpose of the [isPrimaryMember] flag is to distinguish between the primary members of the team (the GitHub members)
     * and the other members (the JIRA, Linear, and Slack members).
     *
     * A null value implies false.
     */
    val isPrimaryMember = bool("isPrimary").nullable()

    val ignoreThreads = bool("ignoreThreads").nullable()

    /**
     * Role of the member in the provider.
     */
    val providerRole = enumerationByDbOrdinal(name = "providerRole", klass = ProviderRole::class).nullable()

    /**
     * When the member first interacted with the service in a meaningful way.
     * Currently, this is only set for Slack members, and is the time the members first asked a question.
     */
    val firstInteractionAt = timestamp("firstUse").nullable()

    /**
     * This is the primary member that this member is associated with.
     * The primary member may be null if we cannot determine the relationship.
     *
     * If [isPrimaryMember] is true, then this value is always null.
     */
    val associatedPrimaryMember = reference(
        name = "primaryMember",
        foreign = MemberModel, // TODO remove this column
        onDelete = ReferenceOption.SET_NULL,
        onUpdate = ReferenceOption.CASCADE,
    ).nullable().index()

    // TODO move these flags to other tables
    val providerStates = uinteger("providerStates").clientDefault { 0u }.nullable()

    init {
        uniqueIndex(installation, identity)
        index(isUnique = false, modifiedAt)
        index(isUnique = false, identity)
        index(isUnique = false, orgMember)
        index(isUnique = false, installation, associatedPrimaryMember)
    }
}

fun ResultRow.toMember(alias: String? = null) = MemberDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toMemberOrNull(alias: String? = null) = MemberDAO.wrapRowOrNull(this, alias)?.asDataModel()

class MemberDAO(id: EntityID<MemberId>) : EntityExtensions<Member, MemberId>(id) {
    companion object : EntityClassExtensions<MemberId, MemberDAO>(MemberModel)

    override var createdAt by MemberModel.createdAt
    override var modifiedAt by MemberModel.modifiedAt

    var installation by InstallationDAO referencedOn MemberModel.installation
    var identity by IdentityDAO referencedOn MemberModel.identity
    var orgMember by OrgMemberDAO referencedOn MemberModel.orgMember

    var isCurrentMember by MemberModel.isCurrentMember
    var isPrimaryMember by MemberModel.isPrimaryMember
    var ignoreThreads by MemberModel.ignoreThreads
    var providerRole by MemberModel.providerRole
    var associatedPrimaryMember by MemberDAO optionalReferencedOn MemberModel.associatedPrimaryMember
    var providerStates by MemberModel.providerStates

    val orgMemberId: OrgMemberId
        get() = readValues[MemberModel.orgMember].value

    override fun asDataModel() = readValues.let {
        Member(
            id = it[MemberModel.id].value,
            createdAt = it[MemberModel.createdAt],
            modifiedAt = it[MemberModel.modifiedAt],
            installationId = it[MemberModel.installation].value,
            identityId = it[MemberModel.identity].value,
            orgMemberId = it[MemberModel.orgMember].value,
            isCurrentMember = it[MemberModel.isCurrentMember],
            isPrimaryMember = it[MemberModel.isPrimaryMember] ?: false,
            ignoreThreads = it[MemberModel.ignoreThreads] ?: false,
            providerRole = it[MemberModel.providerRole],
            primaryAssociation = it[MemberModel.associatedPrimaryMember]?.value,
            providerStates = it[MemberModel.providerStates] ?: 0u,
        )
    }
}

object MemberIdConverter : ValueIdConverter<MemberId> {
    override val factory = ::MemberId
    override val extract = MemberId::value
}

internal object MemberIdSerializer : ValueIdSerializer<MemberId>(
    serialName = "MemberId",
    converter = MemberIdConverter,
)

@Serializable(with = MemberIdSerializer::class)
data class MemberId(val value: UUID) : ValueId {

    companion object : ValueIdClass<MemberId>(
        converter = MemberIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is MemberId)
        return value.compareTo(other.value)
    }
}

data class Member(
    val id: MemberId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val installationId: InstallationId,
    val identityId: IdentityId,
    val orgMemberId: OrgMemberId,
    val isCurrentMember: Boolean,
    val isPrimaryMember: Boolean,
    val ignoreThreads: Boolean = false,
    val providerRole: ProviderRole? = null,
    val primaryAssociation: MemberId? = null,
    val providerStates: UInt = 0u,
) {
    fun hasProviderStateSet(state: MemberProviderState): Boolean {
        return state.isSet(this.providerStates)
    }

    val hasPromptedToCreateUnblockedAccount = hasProviderStateSet(MemberProviderState.SlackHasPromptedToCreateUnblockedAccount)
    val hasCompletedProviderOnboarding = hasProviderStateSet(MemberProviderState.SlackHasCompletedProviderOnboarding)
    val hasIntroducedAutoAnswer = hasProviderStateSet(MemberProviderState.SlackHasIntroducedAutoAnswer)
}

val MemberDAO.installationId: InstallationId
    get() = readValues[MemberModel.installation].value

suspend fun MemberDAO.Companion.legacyMemberForIdentity(
    trx: Transaction? = null,
    identityId: IdentityId,
    teamOrOrgId: UUID,
): MemberDAO? = suspendedTransaction(trx) {
    val teamId = ScmTeamId(teamOrOrgId)

    if (!Stores.scmTeamStore.exists(trx = trx, teamId = teamId)) {
        val orgId = teamOrOrgId.let(::OrgId)
        legacyMemberForIdentityWithOrgId(trx, identityId, orgId)
    } else {
        MemberModel
            .join(
                joinType = JoinType.INNER,
                otherTable = ScmTeamModel,
                otherColumn = ScmTeamModel.installation,
                onColumn = MemberModel.installation,
            ) {
                SCM_TEAM_EXISTS_CLAUSE and (ScmTeamModel.id eq teamId)
            }
            .select(MemberModel.columns)
            .whereAll(
                MemberModel.identity eq identityId,
                MemberStore.IS_CURRENT_MEMBER_CLAUSE,
            )
            .map { MemberDAO.wrapRow(it) }
            .firstOrNull()
    }
}

suspend fun MemberDAO.Companion.legacyMemberForIdentityWithOrgId(
    trx: Transaction? = null,
    identityId: IdentityId,
    orgId: OrgId,
): MemberDAO? {
    if (!Stores.orgStore.exists(trx, orgId = orgId)) {
        throw NotFoundException("Org not found.")
    } else {
        // TODO - MEGA BAD STUFF GOING ON HERE MUST FIX SOON
        // TODO - This assumes that there's only 1 SCMTeam on the org and is not stable otherwise
        // TODO - This hack is a holdover until we move to use OrgMember
        return MemberModel
            .join(
                joinType = JoinType.INNER,
                otherTable = InstallationModel,
                otherColumn = InstallationModel.id,
                onColumn = MemberModel.installation,
            ) {
                InstallationStore.INSTALLATION_EXISTS and (InstallationModel.org eq orgId)
            }
            .join(
                joinType = JoinType.INNER,
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = InstallationModel.org,
            ) {
                ORG_EXISTS_CLAUSE
            }
            .select(MemberModel.columns)
            .whereAll(
                MemberModel.identity eq identityId,
                MemberStore.IS_CURRENT_MEMBER_CLAUSE,
            )
            .map { MemberDAO.wrapRow(it) }
            .firstOrNull()
    }
}

suspend fun MemberDAO.Companion.legacyMemberIdForIdentity(
    trx: Transaction? = null,
    identityId: IdentityId,
    orgId: OrgId,
): MemberId? = suspendedTransaction(trx) {
    MemberModel
        .join(
            joinType = JoinType.INNER,
            otherTable = InstallationModel,
            otherColumn = InstallationModel.id,
            onColumn = MemberModel.installation,
        ) {
            InstallationStore.INSTALLATION_EXISTS
        }
        .select(MemberModel.id)
        .whereAll(
            InstallationModel.org eq orgId,
            MemberModel.identity eq identityId,
            MemberStore.IS_CURRENT_MEMBER_CLAUSE,
        )
        .map { it[MemberModel.id].value }
        .firstOrNull()
}
