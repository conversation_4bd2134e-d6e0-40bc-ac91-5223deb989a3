@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.DbOrdinal
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

enum class ConfluenceSpacePermissionEntityType(
    override val dbOrdinal: Int,
) : DbOrdinal {
    Group(
        dbOrdinal = 1,
    ),
    User(
        dbOrdinal = 2,
    ),
    Anonymous(
        dbOrdinal = 3,
    ),
}

object ConfluenceSpacePermissionModel : ServiceModel<ConfluenceSpacePermissionId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val space = reference(
        name = "space",
        foreign = ConfluenceSpaceModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val entityType = enumerationByDbOrdinal(
        name = "entityType",
        klass = ConfluenceSpacePermissionEntityType::class,
    )

    // If entityType is Group, this is the group key; if User, this is the user key; if Anonymous, this will equal ""
    val entityId = text(name = "entityId")

    init {
        uniqueIndex(space, entityType, entityId)
    }
}

fun ResultRow.toConfluenceSpacePermission(alias: String? = null) = ConfluenceSpacePermissionDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toConfluenceSpacePermissionOrNull(alias: String? = null) = ConfluenceSpacePermissionDAO.wrapRowOrNull(this, alias)?.asDataModel()

class ConfluenceSpacePermissionDAO(
    id: EntityID<ConfluenceSpacePermissionId>,
) : EntityExtensions<ConfluenceSpacePermission, ConfluenceSpacePermissionId>(id) {
    companion object : EntityClassExtensions<ConfluenceSpacePermissionId, ConfluenceSpacePermissionDAO>(ConfluenceSpacePermissionModel)

    override var createdAt by ConfluenceSpacePermissionModel.createdAt
    override var modifiedAt by ConfluenceSpacePermissionModel.modifiedAt

    var space by ConfluenceSpaceDAO referencedOn ConfluenceSpacePermissionModel.space
    var entityType by ConfluenceSpacePermissionModel.entityType
    var entityId by ConfluenceSpacePermissionModel.entityId

    override fun asDataModel() = readValues.let { row ->
        ConfluenceSpacePermission(
            id = row[ConfluenceSpacePermissionModel.id].value,
            createdAt = row[ConfluenceSpacePermissionModel.createdAt],
            modifiedAt = row[ConfluenceSpacePermissionModel.modifiedAt],
            confluenceSpaceId = row[ConfluenceSpacePermissionModel.space].value,
            entityType = row[ConfluenceSpacePermissionModel.entityType],
            entityId = row[ConfluenceSpacePermissionModel.entityId],
        )
    }
}

object ConfluenceSpacePermissionIdConverter : ValueIdConverter<ConfluenceSpacePermissionId> {
    override val factory = ::ConfluenceSpacePermissionId
    override val extract = ConfluenceSpacePermissionId::value
}

internal object ConfluenceSpacePermissionIdSerializer : ValueIdSerializer<ConfluenceSpacePermissionId>(
    serialName = "ConfluenceSpacePermissionId",
    converter = ConfluenceSpacePermissionIdConverter,
)

@Serializable(with = ConfluenceSpacePermissionIdSerializer::class)
data class ConfluenceSpacePermissionId(val value: UUID) : ValueId {

    companion object : ValueIdClass<ConfluenceSpacePermissionId>(
        converter = ConfluenceSpacePermissionIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is ConfluenceSpacePermissionId)
        return value.compareTo(other.value)
    }
}

data class ConfluenceSpacePermission(
    val id: ConfluenceSpacePermissionId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val confluenceSpaceId: ConfluenceSpaceId,
    val entityType: ConfluenceSpacePermissionEntityType,
    val entityId: String,
)
