package com.nextchaptersoftware.db.migration

import com.nextchaptersoftware.db.common.config.DbConfigProvider
import com.nextchaptersoftware.db.dbcp2.DynamicDataSource
import org.flywaydb.core.Flyway
import org.flywaydb.core.api.MigrationInfo
import org.flywaydb.core.api.MigrationState
import org.flywaydb.core.api.MigrationVersion
import org.flywaydb.core.extensibility.MigrationType

/**
 * Repository for loading Flyway migrations using Flyway's built-in resolver.
 */
class FlywayMigrationRepository(
    private val dbConfigProvider: DbConfigProvider,
) {

    /**
     * A simplified migration descriptor.
     * @param version Flyway version (null for repeatable migrations).
     * @param description Human-readable description.
     * @param type Flyway migration type (SQL, JDBC, REPEATABLE, UNDO, etc).
     * @param script The script or class name for the migration.
     * @param checksum Checksum computed by Flyway (CRC32 for SQL, hash for Java).
     * @param state Current state (PENDING, SUCCESS, etc).
     */
    data class Migration(
        val version: MigrationVersion?,
        val description: String,
        val type: MigrationType,
        val script: String,
        val checksum: Int?,
        val state: MigrationState,
    )

    /**
     * Load and resolve all migrations from the given classpath locations.
     * @param resourceLocations vararg of Flyway resource paths (e.g. "db/migration").
     * @return List of all migrations, applied and pending.
     */
    fun getMigrations(vararg resourceLocations: String): List<Migration> {
        val dataSource = DynamicDataSource(dbConfigProvider = dbConfigProvider)
        val flyway = Flyway.configure()
            .dataSource(dataSource)
            .locations(*resourceLocations)
            .load()

        return flyway.info().all().map { mi: MigrationInfo ->
            Migration(
                version = mi.version,
                description = mi.description ?: "",
                type = mi.type,
                script = mi.script,
                checksum = mi.resolvedChecksum,
                state = mi.state,
            )
        }
    }

    /**
     * Filter only pending migrations.
     * @param resourceLocations vararg of Flyway resource paths.
     */
    fun getPendingMigrations(vararg resourceLocations: String): List<Migration> =
        getMigrations(*resourceLocations).filter { it.state == MigrationState.PENDING }

    /**
     * Filter only applied migrations.
     * @param resourceLocations vararg of Flyway resource paths.
     */
    fun getAppliedMigrations(vararg resourceLocations: String): List<Migration> =
        getMigrations(*resourceLocations).filter { it.state == MigrationState.SUCCESS }
}
