package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.OperatorExtensions.isNullOrFalse
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.SocialNetwork
import com.nextchaptersoftware.db.models.SocialNetworkModel
import com.nextchaptersoftware.db.models.toOrgMember
import com.nextchaptersoftware.db.models.toSocialNetwork
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAny
import com.nextchaptersoftware.db.stores.IdentityStore.Companion.IS_NOT_BOT_CLAUSE
import com.nextchaptersoftware.db.stores.MemberInfo.Companion.toMemberInfo
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inSubQuery
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.selectAll

class SocialNetworkStore internal constructor() {
    @Suppress("MaxLineLength")
    suspend fun deleteAll(
        trx: Transaction? = null,
        orgId: OrgId,
    ) = suspendedTransaction(trx) {
        SocialNetworkModel.deleteWhere {
            authorOrgMember inSubQuery (
                OrgMemberModel
                    .select(OrgMemberModel.id)
                    .where(OrgMemberModel.org eq orgId)
            )
        }
    }

    @Suppress("LongMethod")
    suspend fun getRecommendedPeers(
        trx: Transaction? = null,
        orgId: OrgId,
        orgMemberId: OrgMemberId,
    ): List<MemberInfo> = suspendedTransaction(trx) {
        val peerWeights: List<Pair<OrgMemberId, Float>> = SocialNetworkModel
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = SocialNetworkModel.authorOrgMember,
                joinType = JoinType.INNER,
            ) {
                OrgMemberModel.org eq orgId
            }
            .select(
                SocialNetworkModel.authorOrgMember,
                SocialNetworkModel.reviewerOrgMember,
                SocialNetworkModel.weight,
            )
            .whereAny(
                SocialNetworkModel.authorOrgMember eq orgMemberId,
                SocialNetworkModel.reviewerOrgMember eq orgMemberId,
            )
            .orderBy(
                SocialNetworkModel.weight to SortOrder.DESC,
            )
            .associateBy(
                keySelector = { row ->
                    listOf(
                        row[SocialNetworkModel.authorOrgMember].value,
                        row[SocialNetworkModel.reviewerOrgMember].value,
                    )
                    .firstOrNull { it != orgMemberId }
                },
                valueTransform = { row ->
                    listOf(
                        row[SocialNetworkModel.authorOrgMember].value,
                        row[SocialNetworkModel.reviewerOrgMember].value,
                    )
                    .firstOrNull { it != orgMemberId }
                    ?.let {
                        Pair(
                            it,
                            row[SocialNetworkModel.weight],
                        )
                    }
                },
            )
            .mapNotNull { it.value }

        if (peerWeights.isEmpty()) return@suspendedTransaction emptyList()

        OrgMemberModel
            .join(
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = OrgMemberModel.org,
                joinType = JoinType.INNER,
            ) {
                OrgModel.id eq orgId
            }
            .join(
                otherTable = MemberModel,
                otherColumn = MemberModel.orgMember,
                onColumn = OrgMemberModel.id,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = IdentityModel,
                otherColumn = IdentityModel.id,
                onColumn = MemberModel.identity,
                joinType = JoinType.INNER,
            )
            .select(
                OrgMemberModel.columns +
                    OrgModel.columns +
                    MemberModel.columns +
                    IdentityModel.columns,
            )
            .whereAll(
                OrgMemberModel.id inList peerWeights.map { (orgMemberId, _) -> orgMemberId },
                MemberStore.IS_CURRENT_MEMBER_CLAUSE,
                IS_NOT_BOT_CLAUSE,
                MemberModel.ignoreThreads.isNullOrFalse(),
            )
            .map { row ->
                val orgMember = row.toOrgMember()
                toMemberInfo(
                    row = row,
                    weight = peerWeights.firstOrNull { (orgMemberId, _) -> orgMemberId == orgMember.id }?.second ?: 0.0F,
                )
            }
            .sortedByDescending { it.weight }
    }

    suspend fun getRank(
        trx: Transaction? = null,
        orgId: OrgId,
        reviewer: OrgMemberId,
        authors: Collection<OrgMemberId>,
    ): Float {
        return suspendedTransaction(trx) {
            SocialNetworkModel
                .join(
                    otherTable = OrgMemberModel,
                    joinType = JoinType.INNER,
                    onColumn = SocialNetworkModel.authorOrgMember,
                    otherColumn = OrgMemberModel.id,
                ) {
                    (OrgMemberModel.org eq orgId)
                }
                .select(SocialNetworkModel.weight)
                .whereAll(
                    SocialNetworkModel.reviewerOrgMember eq reviewer,
                    SocialNetworkModel.authorOrgMember inList authors,
                )
                .maxOfOrNull { it[SocialNetworkModel.weight] }
                ?: 0.0F
        }
    }

    suspend fun getByOrgId(
        orgId: OrgId,
    ): List<SocialNetwork> = suspendedTransaction {
        SocialNetworkModel
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = SocialNetworkModel.authorOrgMember,
                joinType = JoinType.INNER,
            ) {
                OrgMemberModel.org eq orgId
            }
            .selectAll()
            .map {
                it.toSocialNetwork()
            }
    }
}
