@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object TopicInsightModel : ServiceModel<TopicInsightId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val topic = reference(
        name = "topic",
        foreign = TopicModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val insight = uuid("insight")

    init {
        uniqueIndex(insight, topic)
        index(isUnique = false, topic)
    }
}

fun ResultRow.toTopicInsight(alias: String? = null) = TopicInsightDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toTopicInsightOrNull(alias: String? = null) = TopicInsightDAO.wrapRowOrNull(this, alias)?.asDataModel()

class TopicInsightDAO(id: EntityID<TopicInsightId>) : EntityExtensions<TopicInsight, TopicInsightId>(id) {

    companion object : EntityClassExtensions<TopicInsightId, TopicInsightDAO>(TopicInsightModel)

    override var createdAt by TopicInsightModel.createdAt
    override var modifiedAt by TopicInsightModel.modifiedAt

    var topic by TopicDAO referencedOn TopicInsightModel.topic
    var insight by TopicInsightModel.insight

    override fun asDataModel() = readValues.let {
        TopicInsight(
            id = it[TopicInsightModel.id].value,
            createdAt = it[TopicInsightModel.createdAt],
            modifiedAt = it[TopicInsightModel.modifiedAt],
            topicId = it[TopicInsightModel.topic].value,
            insightId = it[TopicInsightModel.insight],
        )
    }
}

object TopicInsightIdConverter : ValueIdConverter<TopicInsightId> {
    override val factory = ::TopicInsightId
    override val extract = TopicInsightId::value
}

internal object TopicInsightIdSerializer : ValueIdSerializer<TopicInsightId>(
    serialName = "TopicInsightId",
    converter = TopicInsightIdConverter,
)

@Serializable(with = TopicInsightIdSerializer::class)
data class TopicInsightId(val value: UUID) : ValueId {

    companion object : ValueIdClass<TopicInsightId>(
        converter = TopicInsightIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is TopicInsightId)
        return value.compareTo(other.value)
    }
}

data class TopicInsight(
    val id: TopicInsightId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val topicId: TopicId,
    val insightId: UUID,
)
