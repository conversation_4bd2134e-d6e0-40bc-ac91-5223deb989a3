@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.DbOrdinal
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

enum class CodaDocIngestionStatus(
    override val dbOrdinal: Int,
) : DbOrdinal {
    Ingested(dbOrdinal = 1),
    Error(dbOrdinal = 2),
}

object CodaDocModel : ServiceModel<CodaDocId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val codaOrganization = reference(
        name = "codaOrganization",
        foreign = CodaOrganizationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    /**
     * The Coda-assigned id of the page.
     * */
    val docId = text(name = "docId")

    /**
     * The Coda-assigned id of the folder where this page resides.
     * */
    val folderId = text(name = "folderId")

    /**
     * The email of the owner of this doc (usually the creator but ownership can be transferred).
     * */
    val owner = text(name = "owner")

    /**
     * The ingestion status of this doc. Null if this doc needs ingestion.
     * */
    val status = enumerationByDbOrdinal("status", CodaDocIngestionStatus::class).nullable()

    /**
     * The ingestion priority of this doc. Null if this doc does not need to be prioritized.
     * */
    val priority = integer(name = "priority").nullable()

    /**
     * The updatedAt value of this doc when it was last ingested. Null if this doc has not been ingested.
     * */
    val updatedAt = timestamp(name = "updatedAt").nullable()

    init {
        index(isUnique = true, codaOrganization, docId)
    }
}

fun ResultRow.toCodaDoc(alias: String? = null) = CodaDocDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toCodaDocOrNull(alias: String? = null) = CodaDocDAO.wrapRowOrNull(this, alias)?.asDataModel()

class CodaDocDAO(id: EntityID<CodaDocId>) : EntityExtensions<CodaDoc, CodaDocId>(id) {
    companion object : EntityClassExtensions<CodaDocId, CodaDocDAO>(CodaDocModel)

    override var createdAt by CodaDocModel.createdAt
    override var modifiedAt by CodaDocModel.modifiedAt

    var codaOrganization by CodaOrganizationDAO referencedOn CodaDocModel.codaOrganization
    var docId by CodaDocModel.docId
    var folderId by CodaDocModel.folderId
    var owner by CodaDocModel.owner
    var status by CodaDocModel.status
    var priority by CodaDocModel.priority
    var updatedAt by CodaDocModel.updatedAt

    override fun asDataModel() = readValues.let {
        CodaDoc(
            id = it[CodaDocModel.id].value,
            createdAt = it[CodaDocModel.createdAt],
            modifiedAt = it[CodaDocModel.modifiedAt],
            codaOrganizationId = it[CodaDocModel.codaOrganization].value,
            docId = it[CodaDocModel.docId],
            folderId = it[CodaDocModel.folderId],
            owner = it[CodaDocModel.owner],
        )
    }
}

object CodaDocIdConverter : ValueIdConverter<CodaDocId> {
    override val factory = ::CodaDocId
    override val extract = CodaDocId::value
}

internal object CodaDocIdSerializer : ValueIdSerializer<CodaDocId>(
    serialName = "CodaDocId",
    converter = CodaDocIdConverter,
)

@Serializable(with = CodaDocIdSerializer::class)
data class CodaDocId(val value: UUID) : ValueId {

    companion object : ValueIdClass<CodaDocId>(
        converter = CodaDocIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is CodaDocId)
        return value.compareTo(other.value)
    }
}

data class CodaDoc(
    val id: CodaDocId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val codaOrganizationId: CodaOrganizationId,
    val docId: String,
    val folderId: String,
    val owner: String,
)
