@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ResultRow

object AtlassianForgeInstallationModel : ServiceModel<AtlassianForgeInstallationId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val provider = enumerationByDbOrdinal("provider", Provider::class)
    val siteId = text(name = "siteId")
    val appSystemToken = binary(name = "appSystemToken")

    init {
        index(isUnique = true, provider, siteId)
    }
}

fun ResultRow.toAtlassianForgeInstallation(alias: String? = null) = AtlassianForgeInstallationDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toAtlassianForgeInstallationOrNull(alias: String? = null) = AtlassianForgeInstallationDAO.wrapRowOrNull(this, alias)?.asDataModel()

class AtlassianForgeInstallationDAO(
    id: EntityID<AtlassianForgeInstallationId>,
) : EntityExtensions<AtlassianForgeInstallation, AtlassianForgeInstallationId>(id) {
    companion object : EntityClassExtensions<AtlassianForgeInstallationId, AtlassianForgeInstallationDAO>(AtlassianForgeInstallationModel)

    override var createdAt by AtlassianForgeInstallationModel.createdAt
    override var modifiedAt by AtlassianForgeInstallationModel.modifiedAt

    var provider by AtlassianForgeInstallationModel.provider
    var siteId by AtlassianForgeInstallationModel.siteId
    var appSystemToken by AtlassianForgeInstallationModel.appSystemToken

    override fun asDataModel() = readValues.let { row ->
        AtlassianForgeInstallation(
            id = row[AtlassianForgeInstallationModel.id].value,
            createdAt = row[AtlassianForgeInstallationModel.createdAt],
            modifiedAt = row[AtlassianForgeInstallationModel.modifiedAt],
            provider = row[AtlassianForgeInstallationModel.provider],
            siteId = row[AtlassianForgeInstallationModel.siteId],
            appSystemToken = Ciphertext(row[AtlassianForgeInstallationModel.appSystemToken]),
        )
    }
}

object AtlassianForgeInstallationIdConverter : ValueIdConverter<AtlassianForgeInstallationId> {
    override val factory = ::AtlassianForgeInstallationId
    override val extract = AtlassianForgeInstallationId::value
}

internal object AtlassianForgeInstallationIdSerializer : ValueIdSerializer<AtlassianForgeInstallationId>(
    serialName = "AtlassianForgeInstallationId",
    converter = AtlassianForgeInstallationIdConverter,
)

@Serializable(with = AtlassianForgeInstallationIdSerializer::class)
data class AtlassianForgeInstallationId(val value: UUID) : ValueId {

    companion object : ValueIdClass<AtlassianForgeInstallationId>(
        converter = AtlassianForgeInstallationIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is AtlassianForgeInstallationId)
        return value.compareTo(other.value)
    }
}

data class AtlassianForgeInstallation(
    val id: AtlassianForgeInstallationId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val provider: Provider,
    val siteId: String,
    val appSystemToken: Ciphertext,
)
