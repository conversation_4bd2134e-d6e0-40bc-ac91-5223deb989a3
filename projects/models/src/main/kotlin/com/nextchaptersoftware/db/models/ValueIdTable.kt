package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.Id
import com.nextchaptersoftware.utils.KotlinUtils.required
import java.util.UUID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.ColumnType

/**
 * @see org.jetbrains.exposed.dao.id.IdTable
 * @see org.jetbrains.exposed.dao.id.UUIDTable
 */
open class ValueIdTable<VID : Id<VID>>(
    tableName: String = "",
    val idColumnType: ColumnType<VID>,
) : IdTable<VID>(tableName) {

    // We can't use `uuid().transform()` here due to a bug in exposed,
    // the class `ColumnWithTransform` has a private property which triggers an exception while cloning the column
    final override val id = registerColumn("id", idColumnType)
        .clientDefault { UUID.randomUUID().let(idColumnType::valueFromDB).required() }
        .entityId()

    final override val primaryKey = PrimaryKey(id)
}
