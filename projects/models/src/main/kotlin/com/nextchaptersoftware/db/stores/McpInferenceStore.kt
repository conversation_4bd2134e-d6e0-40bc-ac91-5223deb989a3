package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.compress.compress
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.McpInference
import com.nextchaptersoftware.db.models.McpInferenceDAO
import com.nextchaptersoftware.db.models.McpInferenceModel
import com.nextchaptersoftware.db.models.McpQueryId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.toMcpInference
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.upsertReturning

class McpInferenceStore internal constructor() {
    suspend fun upsert(
        orgId: OrgId,
        repoId: RepoId? = null,
        mcpQueryId: McpQueryId,
        mcpToolName: String? = null,
        orgMemberId: OrgMemberId,
        resultInferenceId: MLInferenceId? = null,
        parameters: Map<String, String>? = null,
        productAgent: ProductAgentType? = null,
    ): McpInference = suspendedTransaction {
        val keys = arrayOf(
            McpInferenceModel.mcpQueryId,
            McpInferenceModel.org,
            McpInferenceModel.questionerOrgMember,
        )
        McpInferenceModel.upsertReturning(
            keys = keys,
            onUpdateExclude = McpInferenceModel.columns - setOfNotNull(
                mcpToolName?.let { McpInferenceModel.mcpToolName },
                repoId?.let { McpInferenceModel.repo },
                resultInferenceId?.let { McpInferenceModel.resultInference },
                *keys,
            ),
        ) { insertStatement ->
            insertStatement[McpInferenceModel.org] = orgId
            insertStatement[McpInferenceModel.questionerOrgMember] = orgMemberId
            insertStatement[McpInferenceModel.mcpQueryId] = mcpQueryId.value
            mcpToolName?.also { insertStatement[McpInferenceModel.mcpToolName] = it }
            repoId?.also { insertStatement[McpInferenceModel.repo] = it }
            resultInferenceId?.also { insertStatement[McpInferenceModel.resultInference] = it }
            parameters?.also { insertStatement[McpInferenceModel.compressedParameters] = it.encode().compress() }
            productAgent?.also { insertStatement[McpInferenceModel.productAgent] = it }
        }.first().toMcpInference()
    }

    suspend fun find(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        mcpQueryId: McpQueryId,
    ): McpInference? = suspendedTransaction {
        McpInferenceDAO.find {
            (McpInferenceModel.org eq orgId) and
                    (McpInferenceModel.mcpQueryId eq mcpQueryId.value) and
                    (McpInferenceModel.questionerOrgMember eq orgMemberId)
        }.firstOrNull()?.asDataModel()
    }

    suspend fun findWithResult(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        mcpQueryId: McpQueryId,
    ): McpInferenceWithResult? = suspendedTransaction {
        McpInferenceDAO.find {
            (McpInferenceModel.org eq orgId) and
                    (McpInferenceModel.mcpQueryId eq mcpQueryId.value) and
                    (McpInferenceModel.questionerOrgMember eq orgMemberId)
        }.firstOrNull()?.let { mcpInferenceDAO ->
            McpInferenceWithResult(
                mcpInference = mcpInferenceDAO.asDataModel(),
                resultInference = mcpInferenceDAO.resultInference?.asDataModel(),
            )
        }
    }

    suspend fun findByResultInferenceId(resultInferenceId: MLInferenceId): McpInference? = suspendedTransaction {
        McpInferenceDAO.find {
            McpInferenceModel.resultInference eq resultInferenceId
        }.firstOrNull()?.asDataModel()
    }

    suspend fun delete(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        mcpQueryId: McpQueryId,
    ) = suspendedTransaction {
        McpInferenceModel.deleteWhere {
            (McpInferenceModel.org eq orgId) and
                    (McpInferenceModel.mcpQueryId eq mcpQueryId.value) and
                    (McpInferenceModel.questionerOrgMember eq orgMemberId)
        }
    }

    suspend fun all(): List<McpInference> = suspendedTransaction {
        McpInferenceDAO.all().map { it.asDataModel() }
    }
}

data class McpInferenceWithResult(
    val mcpInference: McpInference,
    val resultInference: MLInference?,
)
