package com.nextchaptersoftware.db.interceptors.explain
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import java.util.Stack
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ExplainPlanResult(
    val rawPlan: String,
    val plans: List<Plan>,
) {
    val breadthFirstPlans by lazy {
        val bfPlans = mutableListOf<Plan>()
        val planStack = Stack<Plan>()
        planStack.addAll(plans)
        while (planStack.isNotEmpty()) {
            val tmpPlan = planStack.pop()
            bfPlans.add(tmpPlan)
            planStack.addAll(tmpPlan.plans)
        }
        bfPlans.toList()
    }

    companion object {
        fun fromRawPlan(rawPlan: String): ExplainPlanResult {
            val planContainers: List<PlanContainer> = rawPlan.decode()
            return ExplainPlanResult(
                rawPlan = rawPlan,
                plans = planContainers.map { it.plan },
            )
        }
    }
}

@Serializable
data class PlanContainer(
    @SerialName("Plan")
    val plan: Plan,
)

@Serializable
data class Plan(
    @SerialName("Alias")
    val alias: String = "",
    @SerialName("Async Capable")
    val asyncCapable: Boolean = false,
    @SerialName("Index Cond")
    val indexCond: String = "",
    @SerialName("Index Name")
    val indexName: String = "",
    @SerialName("Filter")
    val filter: String = "",
    @SerialName("Node Type")
    val nodeType: String = "",
    @SerialName("Parallel Aware")
    val parallelAware: Boolean = false,
    @SerialName("Parent Relationship")
    val parentRelationship: String? = null,
    @SerialName("Plan Rows")
    val planRows: Int = 0,
    @SerialName("Plan Width")
    val planWidth: Int = 0,
    @SerialName("Plans")
    val plans: List<Plan> = listOf(),
    @SerialName("Recheck Cond")
    val recheckCond: String = "",
    @SerialName("Relation Name")
    val relationName: String = "",
    @SerialName("Sort Key")
    val sortKey: List<String> = listOf(),
    @SerialName("Startup Cost")
    val startupCost: Double = 0.0,
    @SerialName("Total Cost")
    val totalCost: Double = 0.0,
)
