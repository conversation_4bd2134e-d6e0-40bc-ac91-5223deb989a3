@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.MessageMentionDAO
import com.nextchaptersoftware.db.models.MessageMentionModel
import com.nextchaptersoftware.db.models.ThreadDAO
import org.jetbrains.exposed.sql.AndOp
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.update

class MessageMentionStore internal constructor() {
    suspend fun createMentions(
        trx: Transaction,
        thread: ThreadDAO,
        message: MessageDAO,
        mentions: List<Member>,
    ) = suspendedTransaction(trx) {
        MessageMentionModel.batchInsert(mentions, ignore = true) { member ->
            this[MessageMentionModel.thread] = thread.id
            this[MessageMentionModel.message] = message.id
            this[MessageMentionModel.member] = member.id
            this[MessageMentionModel.orgMember] = member.orgMemberId
        }
    }

    suspend fun findForMessages(
        trx: Transaction,
        messageIds: List<MessageId>,
    ) = suspendedTransaction(trx) {
        val clauses = listOfNotNull(
            MessageMentionModel.message inList messageIds,
            MessageMentionModel.isDeleted eq false,
        )

        MessageMentionDAO
            .find { AndOp(clauses) }
    }

    suspend fun deleteMentionsByMembers(
        trx: Transaction,
        mentions: List<MemberId>,
    ) = suspendedTransaction(trx) {
        MessageMentionModel.update({ MessageMentionModel.member inList mentions }) { it[this.isDeleted] = true }
    }
}
