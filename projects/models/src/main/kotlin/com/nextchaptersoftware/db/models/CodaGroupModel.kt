@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object CodaGroupModel : ServiceModel<CodaGroupId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val codaOrganization = reference(
        name = "codaOrganization",
        foreign = CodaOrganizationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val groupId = text(name = "groupId")
    val name = text(name = "name")

    init {
        index(isUnique = true, codaOrganization, groupId)
    }
}

fun ResultRow.toCodaGroup(alias: String? = null) = CodaGroupDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toCodaGroupOrNull(alias: String? = null) = CodaGroupDAO.wrapRowOrNull(this, alias)?.asDataModel()

class CodaGroupDAO(id: EntityID<CodaGroupId>) : EntityExtensions<CodaGroup, CodaGroupId>(id) {
    companion object : EntityClassExtensions<CodaGroupId, CodaGroupDAO>(CodaGroupModel)

    override var createdAt by CodaGroupModel.createdAt
    override var modifiedAt by CodaGroupModel.modifiedAt

    var codaOrganization by CodaOrganizationDAO referencedOn CodaGroupModel.codaOrganization
    var groupId by CodaGroupModel.groupId
    var name by CodaGroupModel.name

    override fun asDataModel() = readValues.let {
        CodaGroup(
            id = it[CodaGroupModel.id].value,
            createdAt = it[CodaGroupModel.createdAt],
            modifiedAt = it[CodaGroupModel.modifiedAt],
            codaOrganizationId = it[CodaGroupModel.codaOrganization].value,
            groupId = it[CodaGroupModel.groupId],
            name = it[CodaGroupModel.name],
        )
    }
}

object CodaGroupIdConverter : ValueIdConverter<CodaGroupId> {
    override val factory = ::CodaGroupId
    override val extract = CodaGroupId::value
}

internal object CodaGroupIdSerializer : ValueIdSerializer<CodaGroupId>(
    serialName = "CodaGroupId",
    converter = CodaGroupIdConverter,
)

@Serializable(with = CodaGroupIdSerializer::class)
data class CodaGroupId(val value: UUID) : ValueId {

    companion object : ValueIdClass<CodaGroupId>(
        converter = CodaGroupIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is CodaGroupId)
        return value.compareTo(other.value)
    }
}

data class CodaGroup(
    val id: CodaGroupId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val codaOrganizationId: CodaOrganizationId,
    val groupId: String,
    val name: String,
)
