@file:Suppress("MatchingDeclarationName", "ktlint:standard:filename")

package com.nextchaptersoftware.db.common

import com.nextchaptersoftware.db.common.index.PgtrmIndex
import com.nextchaptersoftware.db.models.ValueId
import com.nextchaptersoftware.db.models.ValueIdEntity
import com.nextchaptersoftware.db.models.ValueIdEntityClass
import com.nextchaptersoftware.db.models.ValueIdTable
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.reflect.KClass
import kotlinx.datetime.Instant
import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ColumnType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.SizedIterable
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

open class ModelExtensions<VID : Id<VID>>(
    overrideName: String = "",
    idColumnType: ColumnType<VID>,
) : ValueIdTable<VID>(
    tableName = overrideName,
    idColumnType = idColumnType,
) {
    /**
     * Automatic createdAt timestamp
     */
    val createdAt: Column<Instant> = timestamp("createdAt").clientDefault {
        Instant.nowWithMicrosecondPrecision()
    }

    /**
     * Automatic modifiedAt timestamp
     */
    val modifiedAt: Column<Instant> = timestamp("modifiedAt").clientDefault {
        Instant.nowWithMicrosecondPrecision()
    }

    private val _pgtrmIndices = mutableListOf<PgtrmIndex>()

    val pgtrmIndices: List<PgtrmIndex> get() = _pgtrmIndices

    protected fun pgtrmIndex(
        customIndexName: String? = null,
        indexFeature: String,
        indexType: String,
        vararg columns: Column<*>,
    ) {
        _pgtrmIndices.add(
            PgtrmIndex(customName = customIndexName, indexFeature = indexFeature, indexType = indexType, columns = columns.toList()),
        )
    }

    /**
     * Models a compact (binary) SHA-1 (Secure Hash Algorithm 1) value.
     */
    protected fun sha1(name: String): Column<ByteArray> {
        return binary(name = name)
    }

    /**
     * Only use enum classes that implement the [DbOrdinal] interface.
     *
     * Example
     *
     *    enum class Color(override val dbOrdinal: Int) : DbOrdinal {
     *       Red(dbOrdinal = 1),
     *       Blue(dbOrdinal = 2),
     *    }
     *
     * Do not use [enumeration]; reordering or removing an enum value will lead to data corruption, because it's based on enum ordinal.
     * Do not use [enumerationByName]; renaming an enum value will lead to data corruption, because it's based on the enum name.
     */
    protected fun <T> enumerationByDbOrdinal(
        name: String,
        klass: KClass<T>,
    ): Column<T>
        where T : Enum<T>, T : DbOrdinal {
        // Check for programmer error when specifying dbOrdinal
        val enumConstants: Array<T> = klass.java.enumConstants
        if (enumConstants.size != enumConstants.map { it.dbOrdinal }.toSet().size) {
            val collisions = enumConstants.groupBy { it.dbOrdinal }
                .filter { (_, duplicates) -> duplicates.size > 1 }
                .map { (key, _) -> enumConstants.filter { it.dbOrdinal == key }.map { it.name + '=' + key } }
            error("Enum class '${klass.simpleName}' has a dbOrdinal collision: $collisions")
        }

        return customEnumeration(
            name = name,
            sql = "INT",
            toDb = { it.dbOrdinal },
            fromDb = { dbValue: Any ->
                when (dbValue) {
                    is Int -> enumConstants.find { it.dbOrdinal == dbValue }
                        ?: error("Enum class ${klass.simpleName} does not have a dbOrdinal matching $dbValue.")

                    else -> error("Expected int, but got value: $dbValue")
                }
            },
        )
    }
}

abstract class EntityExtensions<MDL, VID : ValueId>(
    id: EntityID<VID>,
) : ValueIdEntity<VID>(id) {

    abstract var createdAt: Instant
    abstract var modifiedAt: Instant

    abstract fun asDataModel(): MDL

    val idValue: VID
        get() = id.value
}

open class EntityClassExtensions<VID, DAO>(
    private val extendedTable: ModelExtensions<VID>,
) : ValueIdEntityClass<DAO, VID>(table = extendedTable)
    where VID : ValueId,
          DAO : Entity<VID> {

    fun findModifiedSince(modifiedAt: Instant, where: (() -> Op<Boolean>)?): SizedIterable<DAO> = find {
        extendedTable.modifiedAt greater modifiedAt and (where?.let { it() } ?: Op.TRUE)
    }

    fun modifiedSince(modifiedAt: Instant, where: (() -> Op<Boolean>)?): Boolean = extendedTable.select(
        extendedTable.modifiedAt,
    ).where {
        extendedTable.modifiedAt greater modifiedAt and (where?.let { it() } ?: Op.TRUE)
    }.limit(1).count() > 0

    internal fun wrapRow(row: ResultRow, alias: String?): DAO {
        return when (alias) {
            null -> wrapRow(row)
            else -> wrapRow(row, extendedTable.alias(alias))
        }
    }

    internal fun wrapRowOrNull(row: ResultRow, alias: String?): DAO? {
        val id = when (alias) {
            null -> extendedTable.id
            else -> extendedTable.alias(alias)[extendedTable.id]
        }
        return row.getOrNull(id)?.run { wrapRow(row, alias) }
    }
}
