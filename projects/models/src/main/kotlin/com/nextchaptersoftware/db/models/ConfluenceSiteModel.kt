@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object ConfluenceSiteModel : ServiceModel<ConfluenceSiteId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val installation = reference(
        name = "installation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val siteId = text(name = "siteId")
    val name = text(name = "name")
    val baseUrl = text(name = "baseUrl")
    val avatarUrl = text(name = "avatarUrl")

    val confluenceSpaceIngestionType = enumerationByDbOrdinal(
        name = "confluenceSpaceIngestionType",
        klass = ConfluenceSpaceIngestionType::class,
    ).nullable()

    val ingestPersonalSpaces = bool(name = "ingestPersonalSpaces").nullable()

    init {
        index(isUnique = true, installation)
    }
}

fun ResultRow.toConfluenceSite(alias: String? = null) = ConfluenceSiteDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toConfluenceSiteOrNull(alias: String? = null) = ConfluenceSiteDAO.wrapRowOrNull(this, alias)?.asDataModel()

class ConfluenceSiteDAO(id: EntityID<ConfluenceSiteId>) : EntityExtensions<ConfluenceSite, ConfluenceSiteId>(id) {
    companion object : EntityClassExtensions<ConfluenceSiteId, ConfluenceSiteDAO>(ConfluenceSiteModel)

    override var createdAt by ConfluenceSiteModel.createdAt
    override var modifiedAt by ConfluenceSiteModel.modifiedAt

    var installation by InstallationDAO referencedOn ConfluenceSiteModel.installation

    var siteId by ConfluenceSiteModel.siteId
    var name by ConfluenceSiteModel.name
    var baseUrl by ConfluenceSiteModel.baseUrl
    var avatarUrl by ConfluenceSiteModel.avatarUrl

    var confluenceSpaceIngestionType by ConfluenceSiteModel.confluenceSpaceIngestionType
    var ingestPersonalSpaces by ConfluenceSiteModel.ingestPersonalSpaces

    override fun asDataModel() = readValues.let { row ->
        ConfluenceSite(
            id = row[ConfluenceSiteModel.id].value,
            createdAt = row[ConfluenceSiteModel.createdAt],
            modifiedAt = row[ConfluenceSiteModel.modifiedAt],
            installationId = row[ConfluenceSiteModel.installation].value,
            name = row[ConfluenceSiteModel.name],
            siteId = row[ConfluenceSiteModel.siteId],
            baseUrl = Url(row[ConfluenceSiteModel.baseUrl]),
            avatarUrl = Url(row[ConfluenceSiteModel.avatarUrl]),
            confluenceSpaceIngestionType = row[ConfluenceSiteModel.confluenceSpaceIngestionType],
            ingestPersonalSpaces = row[ConfluenceSiteModel.ingestPersonalSpaces] ?: false,
        )
    }
}

object ConfluenceSiteIdConverter : ValueIdConverter<ConfluenceSiteId> {
    override val factory = ::ConfluenceSiteId
    override val extract = ConfluenceSiteId::value
}

internal object ConfluenceSiteIdSerializer : ValueIdSerializer<ConfluenceSiteId>(
    serialName = "ConfluenceSiteId",
    converter = ConfluenceSiteIdConverter,
)

@Serializable(with = ConfluenceSiteIdSerializer::class)
data class ConfluenceSiteId(val value: UUID) : ValueId {

    companion object : ValueIdClass<ConfluenceSiteId>(
        converter = ConfluenceSiteIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is ConfluenceSiteId)
        return value.compareTo(other.value)
    }
}

data class ConfluenceSite(
    val id: ConfluenceSiteId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val installationId: InstallationId,
    val siteId: String,
    val baseUrl: Url,
    val avatarUrl: Url,
    val name: String,
    val confluenceSpaceIngestionType: ConfluenceSpaceIngestionType? = null,
    val ingestPersonalSpaces: Boolean,
) {
    val userAuthBaseUrl: Url
        get() = Url("https://api.atlassian.com/ex/confluence/$siteId")

    fun shouldIngest(space: ConfluenceSpace): Boolean {
        if (space.isArchived) {
            return false
        }

        return when (confluenceSpaceIngestionType) {
            ConfluenceSpaceIngestionType.AllSpaces -> true
            ConfluenceSpaceIngestionType.SelectedSpacesOnly -> space.shouldIngest
            null -> false
        }
    }
}
