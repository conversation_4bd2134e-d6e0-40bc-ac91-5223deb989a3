@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument", "MaxLineLength")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

/**
 * An [InstallationSuppression] is a record that a [member] has suppressed the connection indicator for an [installation].
 */
object InstallationSuppressionModel : ServiceModel<InstallationSuppressionId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {

    val member = reference(
        name = "member",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val installation = reference(
        name = "installation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    init {
        index(isUnique = true, member, installation)
    }
}

fun ResultRow.toInstallationSuppression(alias: String? = null) = InstallationSuppressionDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toInstallationSuppressionOrNull(alias: String? = null) = InstallationSuppressionDAO.wrapRowOrNull(this, alias)?.asDataModel()

class InstallationSuppressionDAO(id: EntityID<InstallationSuppressionId>) : EntityExtensions<InstallationSuppression, InstallationSuppressionId>(id) {
    companion object : EntityClassExtensions<InstallationSuppressionId, InstallationSuppressionDAO>(InstallationSuppressionModel)

    override var createdAt by InstallationSuppressionModel.createdAt
    override var modifiedAt by InstallationSuppressionModel.modifiedAt

    var member by InstallationSuppressionModel.member
    var installation by InstallationSuppressionModel.installation

    override fun asDataModel() = readValues.let {
        InstallationSuppression(
            id = it[InstallationSuppressionModel.id].value,
            createdAt = it[InstallationSuppressionModel.createdAt],
            modifiedAt = it[InstallationSuppressionModel.modifiedAt],
            // FIXME: mrtn, orgMemberId into memberId
            member = it[InstallationSuppressionModel.member].value.value.let(::MemberId),
            installation = it[InstallationSuppressionModel.installation].value,
        )
    }
}

object InstallationSuppressionIdConverter : ValueIdConverter<InstallationSuppressionId> {
    override val factory = ::InstallationSuppressionId
    override val extract = InstallationSuppressionId::value
}

internal object InstallationSuppressionIdSerializer : ValueIdSerializer<InstallationSuppressionId>(
    serialName = "InstallationSuppressionId",
    converter = InstallationSuppressionIdConverter,
)

@Serializable(with = InstallationSuppressionIdSerializer::class)
data class InstallationSuppressionId(val value: UUID) : ValueId {

    companion object : ValueIdClass<InstallationSuppressionId>(
        converter = InstallationSuppressionIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is InstallationSuppressionId)
        return value.compareTo(other.value)
    }
}

data class InstallationSuppression(
    val id: InstallationSuppressionId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val member: MemberId,
    val installation: InstallationId,
)
