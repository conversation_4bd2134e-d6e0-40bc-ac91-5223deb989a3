package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object SlackPendingQuestionModel : ServiceModel<SlackPendingQuestionId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val slackChannel = reference(
        name = "slackChannel",
        foreign = SlackChannelModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val orgMember = reference(
        name = "orgMember",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val slackExternalTeamId = text("slackExternalTeamId")

    val slackExternalChannelId = text("slackExternalChannelId")

    val slackExternalUserId = text("slackExternalUserId")

    val slackTs = text("slackTs")

    val slackThreadTs = text("slackThreadTs")

    val responseTs = text("pendingResponseTs").nullable()

    val ephemeralResponseUrl = text("ephemeralResponseUrl").nullable()

    val question = text("question")

    val pendingSource = enumerationByDbOrdinal("pendingSource", SlackPendingQuestionSource::class).nullable()

    init {
        index(isUnique = false, slackExternalUserId)
        index(isUnique = false, orgMember)
        index(isUnique = false, slackChannel)
        index(isUnique = true, slackTs, slackExternalChannelId)
        index(isUnique = false, pendingSource)
    }
}

fun ResultRow.toSlackPendingQuestion(alias: String? = null) = SlackPendingQuestionDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toSlackPendingQuestionOrNull(alias: String? = null) = SlackPendingQuestionDAO.wrapRowOrNull(this, alias)?.asDataModel()

class SlackPendingQuestionDAO(id: EntityID<SlackPendingQuestionId>) : EntityExtensions<SlackPendingQuestion, SlackPendingQuestionId>(id) {
    companion object : EntityClassExtensions<SlackPendingQuestionId, SlackPendingQuestionDAO>(SlackPendingQuestionModel)

    override var createdAt by SlackPendingQuestionModel.createdAt
    override var modifiedAt by SlackPendingQuestionModel.modifiedAt

    var slackChannel by SlackChannelDAO referencedOn SlackPendingQuestionModel.slackChannel
    var orgMember by OrgMemberDAO referencedOn SlackPendingQuestionModel.orgMember
    var slackExternalTeamId by SlackPendingQuestionModel.slackExternalTeamId
    var slackExternalChannelId by SlackPendingQuestionModel.slackExternalChannelId
    var slackExternalUserId by SlackPendingQuestionModel.slackExternalUserId
    var slackTs by SlackPendingQuestionModel.slackTs
    var slackThreadTs by SlackPendingQuestionModel.slackThreadTs
    var responseTs by SlackPendingQuestionModel.responseTs
    var ephemeralResponseUrl by SlackPendingQuestionModel.ephemeralResponseUrl
    var question by SlackPendingQuestionModel.question
    var pendingSource by SlackPendingQuestionModel.pendingSource

    override fun asDataModel() = readValues.let {
        SlackPendingQuestion(
            id = it[SlackPendingQuestionModel.id].value,
            createdAt = it[SlackPendingQuestionModel.createdAt],
            modifiedAt = it[SlackPendingQuestionModel.modifiedAt],
            slackChannelId = it[SlackPendingQuestionModel.slackChannel].value,
            orgMemberId = it[SlackPendingQuestionModel.orgMember].value,
            slackExternalTeamId = it[SlackPendingQuestionModel.slackExternalTeamId],
            slackExternalChannelId = it[SlackPendingQuestionModel.slackExternalChannelId],
            slackExternalUserId = it[SlackPendingQuestionModel.slackExternalUserId],
            slackTs = it[SlackPendingQuestionModel.slackTs],
            slackThreadTs = it[SlackPendingQuestionModel.slackThreadTs],
            responseTs = it[SlackPendingQuestionModel.responseTs],
            ephemeralResponseUrl = it[SlackPendingQuestionModel.ephemeralResponseUrl],
            question = it[SlackPendingQuestionModel.question],
            pendingSource = it[SlackPendingQuestionModel.pendingSource] ?: SlackPendingQuestionSource.AccountConnectionRequired,
        )
    }
}

object SlackPendingQuestionIdConverter : ValueIdConverter<SlackPendingQuestionId> {
    override val factory = ::SlackPendingQuestionId
    override val extract = SlackPendingQuestionId::value
}

internal object SlackPendingQuestionSerializer : ValueIdSerializer<SlackPendingQuestionId>(
    serialName = "SlackPendingQuestionId",
    converter = SlackPendingQuestionIdConverter,
)

@Serializable(with = SlackPendingQuestionSerializer::class)
data class SlackPendingQuestionId(val value: UUID) : ValueId {

    companion object : ValueIdClass<SlackPendingQuestionId>(
        converter = SlackPendingQuestionIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is SlackPendingQuestionId)
        return value.compareTo(other.value)
    }
}

@Serializable
data class SlackPendingQuestion(
    val id: SlackPendingQuestionId = SlackPendingQuestionId.random(),
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val slackChannelId: SlackChannelId,
    val orgMemberId: OrgMemberId,
    val slackExternalTeamId: String,
    val slackExternalChannelId: String,
    val slackExternalUserId: String,
    val slackTs: String,
    val slackThreadTs: String,
    val responseTs: String?,
    val ephemeralResponseUrl: String?,
    val question: String,
    val pendingSource: SlackPendingQuestionSource,
)
