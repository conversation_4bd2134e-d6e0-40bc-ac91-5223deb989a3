@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument", "MaxLineLength")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption.CASCADE
import org.jetbrains.exposed.sql.ResultRow

object PersonEmailPreferencesModel : ServiceModel<PersonEmailPreferencesId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val person = reference(
        name = "person",
        foreign = PersonModel,
        onDelete = CASCADE,
        onUpdate = CASCADE,
    )

    val digestEmails = PersonEmailPreferencesModel.bool("digestEmails").clientDefault { true }
    val threadInviteEmails = PersonEmailPreferencesModel.bool("threadInviteEmails").clientDefault { true }
    val recommendedInviteeEmails = PersonEmailPreferencesModel.bool("recommendedInviteeEmails").nullable()

    const val DEFAULT_RECOMMENDED_INVITEE_EMAILS = true

    init {
        index(isUnique = false, person)
    }
}

fun ResultRow.toPersonEmailPreferences(alias: String? = null) = PersonEmailPreferencesDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toPersonEmailPreferencesOrNull(alias: String? = null) = PersonEmailPreferencesDAO.wrapRowOrNull(this, alias)?.asDataModel()

class PersonEmailPreferencesDAO(id: EntityID<PersonEmailPreferencesId>) : EntityExtensions<PersonEmailPreferences, PersonEmailPreferencesId>(id) {
    companion object : EntityClassExtensions<PersonEmailPreferencesId, PersonEmailPreferencesDAO>(PersonEmailPreferencesModel)

    override var createdAt by PersonEmailPreferencesModel.createdAt
    override var modifiedAt by PersonEmailPreferencesModel.modifiedAt

    var person by PersonDAO referencedOn PersonEmailPreferencesModel.person

    var digestEmails by PersonEmailPreferencesModel.digestEmails
    var threadInviteEmails by PersonEmailPreferencesModel.threadInviteEmails
    var recommendedInviteeEmails by PersonEmailPreferencesModel.recommendedInviteeEmails

    override fun asDataModel() = readValues.let {
        PersonEmailPreferences(
            id = it[PersonEmailPreferencesModel.id].value,
            createdAt = it[PersonEmailPreferencesModel.createdAt],
            modifiedAt = it[PersonEmailPreferencesModel.modifiedAt],
            personId = it[PersonEmailPreferencesModel.person].value,
            digestEmails = it[PersonEmailPreferencesModel.digestEmails],
            threadInviteEmails = it[PersonEmailPreferencesModel.threadInviteEmails],
            recommendedInviteeEmails = it[PersonEmailPreferencesModel.recommendedInviteeEmails]
                ?: PersonEmailPreferencesModel.DEFAULT_RECOMMENDED_INVITEE_EMAILS,
        )
    }
}

object PersonEmailPreferencesIdConverter : ValueIdConverter<PersonEmailPreferencesId> {
    override val factory = ::PersonEmailPreferencesId
    override val extract = PersonEmailPreferencesId::value
}

internal object PersonEmailPreferencesIdSerializer : ValueIdSerializer<PersonEmailPreferencesId>(
    serialName = "PersonEmailPreferencesId",
    converter = PersonEmailPreferencesIdConverter,
)

@Serializable(with = PersonEmailPreferencesIdSerializer::class)
data class PersonEmailPreferencesId(val value: UUID) : ValueId {

    companion object : ValueIdClass<PersonEmailPreferencesId>(
        converter = PersonEmailPreferencesIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is PersonEmailPreferencesId)
        return value.compareTo(other.value)
    }
}

data class PersonEmailPreferences(
    val id: PersonEmailPreferencesId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),

    val personId: PersonId,
    val digestEmails: Boolean,
    val threadInviteEmails: Boolean,
    val recommendedInviteeEmails: Boolean,
) {
    companion object {
        fun getDefault(personId: PersonId) = PersonEmailPreferences(
            id = PersonEmailPreferencesId.random(),
            personId = personId,
            digestEmails = true,
            threadInviteEmails = true,
            recommendedInviteeEmails = PersonEmailPreferencesModel.DEFAULT_RECOMMENDED_INVITEE_EMAILS,
        )
    }
}
