@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument", "MaxLineLength")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object MLInferenceRuntimeConfigurationModel : ServiceModel<MLInferenceRuntimeConfigurationId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val org = reference(
        name = "org",
        foreign = OrgModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val template = reference(
        name = "template",
        foreign = MLInferenceTemplateModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val type = enumerationByDbOrdinal("type", MLInferenceRuntimeConfigurationType::class)

    // DEPRECATED: golden record is different for every validation record
    val goldenRecord = text("goldenRecord").nullable()

    val validationRecord = text("validationRecord").nullable() // serialized list of MLInferenceExample IDs

    val testRunName = text("testRunName").nullable()
}

fun ResultRow.toMLInferenceRuntimeConfiguration(alias: String? = null) = MLInferenceRuntimeConfigurationDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toMLInferenceRuntimeConfigurationOrNull(alias: String? = null) = MLInferenceRuntimeConfigurationDAO.wrapRowOrNull(this, alias)?.asDataModel()

class MLInferenceRuntimeConfigurationDAO(id: EntityID<MLInferenceRuntimeConfigurationId>) : EntityExtensions<MLInferenceRuntimeConfiguration, MLInferenceRuntimeConfigurationId>(id) {
    companion object : EntityClassExtensions<MLInferenceRuntimeConfigurationId, MLInferenceRuntimeConfigurationDAO>(MLInferenceRuntimeConfigurationModel)

    override var createdAt by MLInferenceRuntimeConfigurationModel.createdAt
    override var modifiedAt by MLInferenceRuntimeConfigurationModel.modifiedAt

    var org by OrgDAO referencedOn MLInferenceRuntimeConfigurationModel.org
    var template by MLInferenceTemplateDAO referencedOn MLInferenceRuntimeConfigurationModel.template
    var type by MLInferenceRuntimeConfigurationModel.type
    var goldenRecord by MLInferenceRuntimeConfigurationModel.goldenRecord
    var validationRecord by MLInferenceRuntimeConfigurationModel.validationRecord
    var testRunName by MLInferenceRuntimeConfigurationModel.testRunName

    override fun asDataModel() = readValues.let {
        MLInferenceRuntimeConfiguration(
            id = it[MLInferenceRuntimeConfigurationModel.id].value,
            testRunName = it[MLInferenceRuntimeConfigurationModel.testRunName],
            type = it[MLInferenceRuntimeConfigurationModel.type],
            createdAt = it[MLInferenceRuntimeConfigurationModel.createdAt],
            modifiedAt = it[MLInferenceRuntimeConfigurationModel.modifiedAt],
            orgId = it[MLInferenceRuntimeConfigurationModel.org].value,
            templateId = it[MLInferenceRuntimeConfigurationModel.template].value,
            goldenRecord = it[MLInferenceRuntimeConfigurationModel.goldenRecord]?.decode(),
            validationRecord = it[MLInferenceRuntimeConfigurationModel.validationRecord]?.decode(),
        )
    }
}

object MLInferenceRuntimeConfigurationIdConverter : ValueIdConverter<MLInferenceRuntimeConfigurationId> {
    override val factory = ::MLInferenceRuntimeConfigurationId
    override val extract = MLInferenceRuntimeConfigurationId::value
}

internal object MLInferenceRuntimeConfigurationIdSerializer : ValueIdSerializer<MLInferenceRuntimeConfigurationId>(
    serialName = "MLInferenceRuntimeConfigurationId",
    converter = MLInferenceRuntimeConfigurationIdConverter,
)

@Serializable(with = MLInferenceRuntimeConfigurationIdSerializer::class)
data class MLInferenceRuntimeConfigurationId(val value: UUID) : ValueId {

    companion object : ValueIdClass<MLInferenceRuntimeConfigurationId>(
        converter = MLInferenceRuntimeConfigurationIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is MLInferenceRuntimeConfigurationId)
        return value.compareTo(other.value)
    }
}

data class MLInferenceRuntimeConfiguration(
    val id: MLInferenceRuntimeConfigurationId,
    val testRunName: String?,
    val type: MLInferenceRuntimeConfigurationType,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val orgId: OrgId,
    val templateId: MLInferenceTemplateId,
    val goldenRecord: List<UUID>?,
    val validationRecord: List<UUID>?,
)
