@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.common.model.Message.MessageBody
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PullRequestComment
import com.nextchaptersoftware.db.models.PullRequestCommentDAO
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestCommentModel
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.update

class PullRequestCommentStore internal constructor() {
    suspend fun find(
        trx: Transaction,
        pullRequestId: PullRequestId,
        id: PullRequestCommentId,
    ): PullRequestComment? = suspendedTransaction(trx) {
        PullRequestCommentDAO
            .find { (PullRequestCommentModel.id eq id) and (PullRequestCommentModel.pullRequest eq pullRequestId) }
            .firstOrNull()?.asDataModel()
    }

    suspend fun list(
        trx: Transaction?,
        pullRequestId: PullRequestId,
        modifiedSince: Instant? = null,
        limit: Int? = null,
    ): List<PullRequestComment> = suspendedTransaction(trx) {
        PullRequestCommentDAO
            .find {
                AllOp(
                    PullRequestCommentModel.pullRequest eq pullRequestId,
                    modifiedSince?.let { PullRequestCommentModel.modifiedAt greater it },
                )
            }
            .let { if (limit == null) it else it.limit(limit) }
            .map { it.asDataModel() }
            .sortedBy { it.createdAt }
    }

    suspend fun findByCommentId(
        trx: Transaction,
        pullRequestId: PullRequestId,
        prCommentId: String,
    ): PullRequestCommentDAO? = suspendedTransaction(trx) {
        PullRequestCommentDAO
            .find {
                AllOp(
                    PullRequestCommentModel.pullRequest eq pullRequestId,
                    PullRequestCommentModel.prCommentId eq prCommentId,
                )
            }
            .firstOrNull()
    }

    suspend fun findByPullRequest(
        pullRequestId: PullRequestId,
    ): List<PullRequestComment> = suspendedTransaction {
        PullRequestCommentDAO
            .find { PullRequestCommentModel.pullRequest eq pullRequestId }
            .map { it.asDataModel() }
    }

    suspend fun deleteByCommentId(
        trx: Transaction,
        pullRequestId: PullRequestId,
        prCommentId: String,
    ) = suspendedTransaction(trx) {
        findByCommentId(
            trx = trx,
            pullRequestId = pullRequestId,
            prCommentId = prCommentId,
        )?.let {
            if (!it.isDeleted) {
                it.content = ByteArray(0)
                it.isDeleted = true
            }
        }
    }

    suspend fun countByPullRequestIds(
        pullRequestIds: List<PullRequestId>,
    ): Map<PullRequestId, Int> = suspendedTransaction {
        PullRequestCommentModel
            .select(listOf(PullRequestCommentModel.pullRequest, PullRequestCommentModel.id.count()))
            .whereAll(
                PullRequestCommentModel.pullRequest inList pullRequestIds,
                PullRequestCommentModel.isDeleted eq false,
            )
            .groupBy(PullRequestCommentModel.pullRequest)
            .associate {
                it[PullRequestCommentModel.pullRequest].value to it[PullRequestCommentModel.id.count()].toInt()
            }
    }

    suspend fun create(
        trx: Transaction,
        pullRequestId: PullRequestId,
        authorId: MemberId,
        authorOrgMemberId: OrgMemberId,
        createdAt: Instant,
        content: MessageBody,
        prCommentId: String,
        prCommentUrl: Url,
        prCommentBodyHash: Hash,
    ): PullRequestComment = suspendedTransaction(trx) {
        PullRequestCommentModel.insert {
            it[this.pullRequest] = pullRequestId
            it[this.author] = authorId
            it[this.authorOrgMember] = authorOrgMemberId
            it[this.createdAt] = createdAt
            it[this.content] = content.toByteArray()
            it[this.contentVersion] = content.version
            it[this.prCommentId] = prCommentId.toString()
            it[this.prCommentUrl] = prCommentUrl.asString
            it[this.prCommentBodyHash] = prCommentBodyHash.value
        }.resultedValues?.map { PullRequestCommentDAO.wrapRow(it) }?.first()?.asDataModel()
            ?: error("Could not insert PullRequestCommentModel")
    }

    suspend fun update(
        trx: Transaction,
        pullRequestId: PullRequestId,
        id: PullRequestCommentId,
        content: ByteArray,
        contentVersion: String,
    ): PullRequestComment = suspendedTransaction(trx) {
        PullRequestCommentModel.update(
            {
                AllOp(
                    PullRequestCommentModel.id eq id,
                    PullRequestCommentModel.pullRequest eq pullRequestId,
                    PullRequestCommentModel.isDeleted neq true,
                )
            },
        ) {
            it[this.content] = content
            it[this.contentVersion] = contentVersion
            it[this.editedAt] = Instant.nowWithMicrosecondPrecision()
        }

        PullRequestCommentDAO.findById(id)?.asDataModel()
            ?: throw IllegalStateException("PullRequestCommentDAO not found") // Just updated, shouldn't happen
    }

    suspend fun delete(
        trx: Transaction,
        pullRequestId: PullRequestId,
        id: PullRequestCommentId,
    ) = suspendedTransaction(trx) {
        PullRequestCommentModel.update(
            {
                (PullRequestCommentModel.id eq id) and (PullRequestCommentModel.pullRequest eq pullRequestId)
            },
        ) {
            it[this.content] = ByteArray(0)
            it[this.isDeleted] = true
        }
    }

    suspend fun setPrCommentDetails(
        comment: PullRequestCommentDAO,
        prCommentId: String,
        prCommentUrl: Url,
        prCommentBodyHash: Hash,
    ) = suspendedTransaction {
        comment.prCommentId = prCommentId
        comment.prCommentUrl = prCommentUrl.asString
        comment.prCommentBodyHash = prCommentBodyHash.value
    }

    suspend fun updatePrCommentDetails(
        comment: PullRequestCommentDAO,
        prCommentBodyHash: Hash,
    ) = suspendedTransaction {
        comment.prCommentBodyHash = prCommentBodyHash.value
    }

    suspend fun clearPrCommentDetails(
        comment: PullRequestCommentDAO,
    ) = suspendedTransaction {
        comment.prCommentId = null
        comment.prCommentUrl = null
    }
}
