@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.db.common.column.valueId
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ResultRow

object EmbeddingDeleteModel : ServiceModel<EmbeddingDeleteId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val namespaceId = valueId("namespaceId", OrgIdConverter)

    val installationId = valueId("installationId", InstallationIdConverter).nullable()

    val groupId = uuid("groupId").nullable()

    val revision = integer("revision").nullable()

    val status = enumerationByDbOrdinal("status", EmbeddingDeleteStatus::class).clientDefault { EmbeddingDeleteStatus.NotStarted }

    init {
        index(false, namespaceId)
        index(false, status)
    }
}

fun ResultRow.toEmbeddingDelete(alias: String? = null) = EmbeddingDeleteDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toEmbeddingDeleteOrNull(alias: String? = null) = EmbeddingDeleteDAO.wrapRowOrNull(this, alias)?.asDataModel()

class EmbeddingDeleteDAO(id: EntityID<EmbeddingDeleteId>) : EntityExtensions<EmbeddingDelete, EmbeddingDeleteId>(id) {
    companion object : EntityClassExtensions<EmbeddingDeleteId, EmbeddingDeleteDAO>(EmbeddingDeleteModel)

    override var createdAt by EmbeddingDeleteModel.createdAt
    override var modifiedAt by EmbeddingDeleteModel.modifiedAt

    var namespaceId by EmbeddingDeleteModel.namespaceId
    var installationId by EmbeddingDeleteModel.installationId
    var groupId by EmbeddingDeleteModel.groupId
    var revision by EmbeddingDeleteModel.revision
    var status by EmbeddingDeleteModel.status

    override fun asDataModel() = readValues.let {
        EmbeddingDelete(
            id = it[EmbeddingDeleteModel.id].value,
            createdAt = it[EmbeddingDeleteModel.createdAt],
            modifiedAt = it[EmbeddingDeleteModel.modifiedAt],
            namespaceId = it[EmbeddingDeleteModel.namespaceId],
            installationId = it[EmbeddingDeleteModel.installationId],
            groupId = it[EmbeddingDeleteModel.groupId],
            revision = it[EmbeddingDeleteModel.revision],
            status = it[EmbeddingDeleteModel.status],
        )
    }
}

object EmbeddingDeleteIdConverter : ValueIdConverter<EmbeddingDeleteId> {
    override val factory = ::EmbeddingDeleteId
    override val extract = EmbeddingDeleteId::value
}

internal object EmbeddingDeleteIdSerializer : ValueIdSerializer<EmbeddingDeleteId>(
    serialName = "EmbeddingDeleteId",
    converter = EmbeddingDeleteIdConverter,
)

@Serializable(with = EmbeddingDeleteIdSerializer::class)
data class EmbeddingDeleteId(val value: UUID) : ValueId {

    companion object : ValueIdClass<EmbeddingDeleteId>(
        converter = EmbeddingDeleteIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is EmbeddingDeleteId)
        return value.compareTo(other.value)
    }
}

data class EmbeddingDelete(
    val id: EmbeddingDeleteId,
    val createdAt: Instant,
    val modifiedAt: Instant,
    val namespaceId: OrgId,
    val installationId: InstallationId?,
    val groupId: UUID?,
    val revision: Int?,
    val status: EmbeddingDeleteStatus,
)
