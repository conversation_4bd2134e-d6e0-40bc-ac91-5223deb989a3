package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.GrowthMetrics
import com.nextchaptersoftware.db.models.GrowthMetricsDAO
import com.nextchaptersoftware.db.models.GrowthMetricsModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.deleteAll
import org.jetbrains.exposed.sql.update

class GrowthMetricsStore internal constructor() {

    suspend fun clearAll() = suspendedTransaction {
        GrowthMetricsModel.deleteAll()
    }

    suspend fun clearSlackOnly() = suspendedTransaction {
        suspendedTransaction {
            GrowthMetricsModel
                .update({ GrowthMetricsModel.dausActive.isNotNull() }) {
                    it[this.dausNewAcquisition] = null
                    it[this.wausNewAcquisition] = null
                    it[this.mausNewAcquisition] = null

                    it[this.dausExistingAcquisition] = null
                    it[this.wausExistingAcquisition] = null
                    it[this.mausExistingAcquisition] = null

                    it[this.dausActive] = null
                    it[this.wausActive] = null
                    it[this.mausActive] = null

                    it[this.dausReturning] = null
                    it[this.wausReturning] = null
                    it[this.mausReturning] = null
                }
        }
    }

    suspend fun latestByCohort(cohort: Cohort): GrowthMetrics? {
        return suspendedTransaction {
            GrowthMetricsDAO
                .find { GrowthMetricsModel.cohort eq cohort }
                .orderBy(GrowthMetricsModel.ending to SortOrder.DESC)
                .limit(1)
                .firstOrNull()
                ?.asDataModel()
        }
    }

    suspend fun oldestMissingSlack(cohort: Cohort): GrowthMetrics? {
        return suspendedTransaction {
            GrowthMetricsDAO
                .find {
                    AllOp(
                        GrowthMetricsModel.cohort eq cohort,
                        GrowthMetricsModel.dausActive.isNull(),
                    )
                }
                .orderBy(GrowthMetricsModel.ending to SortOrder.ASC)
                .limit(1)
                .firstOrNull()
                ?.asDataModel()
        }
    }
}
