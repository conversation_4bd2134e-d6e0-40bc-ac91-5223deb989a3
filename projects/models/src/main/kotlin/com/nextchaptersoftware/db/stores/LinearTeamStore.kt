package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.LinearOrganizationId
import com.nextchaptersoftware.db.models.LinearTeam
import com.nextchaptersoftware.db.models.LinearTeamDAO
import com.nextchaptersoftware.db.models.LinearTeamId
import com.nextchaptersoftware.db.models.LinearTeamModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import io.ktor.http.Url
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsertReturning

class LinearTeamStore internal constructor() {
    suspend fun findById(id: LinearTeamId): LinearTeam? {
        return Database.suspendedTransaction {
            LinearTeamDAO.findById(id)?.asDataModel()
        }
    }

    suspend fun find(
        linearOrganizationId: LinearOrganizationId,
        linearTeamId: String,
    ) = Database.suspendedTransaction {
        LinearTeamDAO.find {
            AllOp(
                LinearTeamModel.linearOrganization eq linearOrganizationId,
                LinearTeamModel.linearTeamId eq linearTeamId,
            )
        }.limit(1).firstOrNull()?.asDataModel()
    }

    suspend fun list(
        linearOrganizationId: LinearOrganizationId,
        additionalWhereClause: Op<Boolean>? = null,
    ) = Database.suspendedTransaction {
        LinearTeamDAO.find {
            AllOp(
                LinearTeamModel.linearOrganization eq linearOrganizationId,
                additionalWhereClause,
            )
        }.map { it.asDataModel() }
    }

    suspend fun listShouldIngest(
        linearOrganizationId: LinearOrganizationId,
        shouldIngest: Boolean,
    ) = list(
        linearOrganizationId = linearOrganizationId,
        additionalWhereClause = LinearTeamModel.shouldIngest eq shouldIngest,
    )

    suspend fun countShouldIngest(
        linearOrganizationId: LinearOrganizationId,
    ): Int = Database.suspendedTransaction {
        LinearTeamModel
            .select(LinearTeamModel.id.count())
            .where { (LinearTeamModel.linearOrganization eq linearOrganizationId) and (LinearTeamModel.shouldIngest eq true) }
            .first()[LinearTeamModel.id.count()].toInt()
    }

    suspend fun updateShouldIngest(
        trx: Transaction?,
        linearOrganizationId: LinearOrganizationId,
        linearTeamIds: List<LinearTeamId>,
    ): Unit = Database.suspendedTransaction(trx) {
        val organizationClause = LinearTeamModel.linearOrganization eq linearOrganizationId

        LinearTeamModel.update({ AllOp(organizationClause, LinearTeamModel.id inList linearTeamIds) }) {
            it[shouldIngest] = true
        }

        LinearTeamModel.update({ AllOp(organizationClause, LinearTeamModel.id notInList linearTeamIds) }) {
            it[shouldIngest] = false
        }
    }

    suspend fun upsert(
        trx: Transaction? = null,
        linearOrganizationId: LinearOrganizationId,
        linearTeamId: String,
        name: String,
        htmlUrl: Url? = null,
        avatarUrl: Url? = null,
    ) = Database.suspendedTransaction(trx) {
        LinearTeamModel.upsertReturning(
            keys = arrayOf(LinearTeamModel.linearOrganization, LinearTeamModel.linearTeamId),
            onUpdateExclude = LinearTeamModel.columns - setOf(LinearTeamModel.name, LinearTeamModel.avatarUrl, LinearTeamModel.htmlUrl),
        ) {
            it[this.linearOrganization] = linearOrganizationId
            it[this.linearTeamId] = linearTeamId
            it[this.name] = name
            it[this.avatarUrl] = avatarUrl?.asString
            it[this.htmlUrl] = htmlUrl?.asString
        }.let {
            LinearTeamDAO.wrapRow(it.first())
        }
    }
}
