package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.SlackPendingQuestion
import com.nextchaptersoftware.db.models.SlackPendingQuestionDAO
import com.nextchaptersoftware.db.models.SlackPendingQuestionId
import com.nextchaptersoftware.db.models.SlackPendingQuestionModel
import com.nextchaptersoftware.db.models.SlackPendingQuestionSource
import com.nextchaptersoftware.db.models.toSlackPendingQuestion
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertIgnore
import org.jetbrains.exposed.sql.or

class SlackPendingQuestionStore internal constructor() {
    suspend fun findPendingQuestionsBySlackUserId(
        slackExternalUserId: String,
        pendingQuestionSource: SlackPendingQuestionSource,
    ): List<SlackPendingQuestion> = suspendedTransaction {
        SlackPendingQuestionDAO.find {
            (SlackPendingQuestionModel.slackExternalUserId eq slackExternalUserId) and
                    (
                        (
                            (SlackPendingQuestionModel.pendingSource.isNull()) or
                                    (SlackPendingQuestionModel.pendingSource eq pendingQuestionSource)
                        )
                    )
        }.map { it.asDataModel() }
    }

    suspend fun findPendingQuestionsByOrgId(
        orgId: OrgId,
        pendingQuestionSource: SlackPendingQuestionSource,
    ): List<SlackPendingQuestion> = suspendedTransaction {
        SlackPendingQuestionModel
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = SlackPendingQuestionModel.orgMember,
                joinType = JoinType.INNER,
            ) {
                OrgMemberModel.org eq orgId
            }.select(
                SlackPendingQuestionModel.columns,
            ).where {
                (SlackPendingQuestionModel.pendingSource eq pendingQuestionSource)
            }.map {
                it.toSlackPendingQuestion()
            }
    }

    suspend fun create(
        slackPendingQuestion: SlackPendingQuestion,
    ): Unit = suspendedTransaction {
        SlackPendingQuestionModel.insertIgnore {
            it[this.id] = slackPendingQuestion.id
            it[this.slackChannel] = slackPendingQuestion.slackChannelId
            it[this.orgMember] = slackPendingQuestion.orgMemberId
            it[this.slackExternalTeamId] = slackPendingQuestion.slackExternalTeamId
            it[this.slackExternalChannelId] = slackPendingQuestion.slackExternalChannelId
            it[this.slackExternalUserId] = slackPendingQuestion.slackExternalUserId
            it[this.slackTs] = slackPendingQuestion.slackTs
            it[this.slackThreadTs] = slackPendingQuestion.slackThreadTs
            it[this.responseTs] = slackPendingQuestion.responseTs
            it[this.ephemeralResponseUrl] = slackPendingQuestion.ephemeralResponseUrl
            it[this.question] = slackPendingQuestion.question
            it[this.pendingSource] = slackPendingQuestion.pendingSource
        }
    }

    suspend fun delete(
        id: SlackPendingQuestionId,
    ): Unit = suspendedTransaction {
        SlackPendingQuestionModel.deleteWhere {
            SlackPendingQuestionModel.id eq id
        }
    }
}
