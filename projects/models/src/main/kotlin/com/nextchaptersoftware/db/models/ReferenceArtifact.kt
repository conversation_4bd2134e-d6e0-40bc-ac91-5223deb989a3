@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.types.MLReference
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Represents a reference to a document.
 * @see MLTypedDocument
 */
@Serializable
sealed class ReferenceArtifact {

    abstract val reference: MLReference
    abstract val provider: Provider
    abstract val score: Float
    abstract val denseRank: Int?
    abstract val hybridRank: Int?
    abstract val sparseRank: Int?
    abstract val rrfRank: Int?
    abstract val cerrRank: Int?
    abstract val repoId: RepoId?
    abstract val referenceResolverSource: MLReferenceResolverType?

    @Serializable
    @SerialName("Thread")
    data class Thread(

        override val reference: MLReference,

        override val provider: Provider,

        override val referenceResolverSource: MLReferenceResolverType? = null,

        @Contextual
        override val score: Float = 0f,

        @Contextual
        override val denseRank: Int? = null,

        @Contextual
        override val sparseRank: Int? = null,

        @Contextual
        override val hybridRank: Int? = null,

        @Contextual
        override val rrfRank: Int? = null,

        @Contextual
        override val cerrRank: Int? = null,

        override val repoId: RepoId?,

        val isPrivate: Boolean? = null,

        @Contextual
        val threadId: ThreadId,

    ) : ReferenceArtifact()

    @Serializable
    @SerialName("PullRequest")
    data class PullRequest(

        override val reference: MLReference,

        override val provider: Provider,

        override val referenceResolverSource: MLReferenceResolverType? = null,

        @Contextual
        override val score: Float = 0f,

        @Contextual
        override val denseRank: Int? = null,

        @Contextual
        override val hybridRank: Int? = null,

        @Contextual
        override val sparseRank: Int? = null,

        @Contextual
        override val rrfRank: Int? = null,

        @Contextual
        override val cerrRank: Int? = null,

        override val repoId: RepoId,

        val prId: PullRequestId,
    ) : ReferenceArtifact()

    @Serializable
    @SerialName("SourceCode")
    data class SourceCode(

        override val reference: MLReference,

        override val provider: Provider,

        override val referenceResolverSource: MLReferenceResolverType? = null,

        @Contextual
        override val score: Float = 0f,

        @Contextual
        override val denseRank: Int? = null,

        @Contextual
        override val hybridRank: Int? = null,

        @Contextual
        override val sparseRank: Int? = null,

        @Contextual
        override val rrfRank: Int? = null,

        @Contextual
        override val cerrRank: Int? = null,

        override val repoId: RepoId,

        val filePath: String,
        val fileName: String,
    ) : ReferenceArtifact()

    @Serializable
    @SerialName("Document")
    data class Document(

        override val reference: MLReference,

        override val provider: Provider,

        override val referenceResolverSource: MLReferenceResolverType? = null,

        @Contextual
        override val score: Float = 0f,

        @Contextual
        override val denseRank: Int? = null,

        @Contextual
        override val hybridRank: Int? = null,

        @Contextual
        override val sparseRank: Int? = null,

        @Contextual
        override val rrfRank: Int? = null,

        @Contextual
        override val cerrRank: Int? = null,

        override val repoId: RepoId? = null,

        val title: String,

        val externalUrl: String,

        val isPrivate: Boolean? = null,

        val id: String? = null, // optional for backwards compatibility
    ) : ReferenceArtifact()
}

fun List<ReferenceArtifact>.containsPrivate(): Boolean = any {
    when (it) {
        is ReferenceArtifact.Thread -> it.isPrivate == true
        is ReferenceArtifact.Document -> it.isPrivate == true
        else -> false
    }
}
