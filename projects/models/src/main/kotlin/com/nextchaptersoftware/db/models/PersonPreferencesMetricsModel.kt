@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument", "MaxLineLength")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.db.models.UserEngagementMetricsModel.ending
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

object PersonPreferencesMetricsModel : ServiceModel<PersonPreferencesMetricsId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val ending = timestamp("ending")

    /** Sum of all users as of [ending] */
    val countUsers = long("countUsers")

    /** Sum of users with incognito mode enabled as of [ending] */
    val countIncognitoMode = long("countIncognitoMode").nullable()

    init {
        index(isUnique = false, ending)
    }
}

fun ResultRow.toPersonPreferencesMetrics(alias: String? = null) = PersonPreferencesMetricsDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toPersonPreferencesMetricsOrNull(alias: String? = null) = PersonPreferencesMetricsDAO.wrapRowOrNull(this, alias)?.asDataModel()

class PersonPreferencesMetricsDAO(id: EntityID<PersonPreferencesMetricsId>) : EntityExtensions<PersonPreferencesMetrics, PersonPreferencesMetricsId>(id) {
    companion object : EntityClassExtensions<PersonPreferencesMetricsId, PersonPreferencesMetricsDAO>(PersonPreferencesMetricsModel)

    override var createdAt by PersonPreferencesMetricsModel.createdAt
    override var modifiedAt by PersonPreferencesMetricsModel.modifiedAt

    var ending by PersonPreferencesMetricsModel.ending
    var countUsers by PersonPreferencesMetricsModel.countUsers
    var countIncognitoMode by PersonPreferencesMetricsModel.countIncognitoMode

    override fun asDataModel() = readValues.let {
        PersonPreferencesMetrics(
            id = it[PersonPreferencesMetricsModel.id].value,
            createdAt = it[PersonPreferencesMetricsModel.createdAt],
            modifiedAt = it[PersonPreferencesMetricsModel.modifiedAt],
            ending = it[PersonPreferencesMetricsModel.ending],
            countUsers = it[PersonPreferencesMetricsModel.countUsers],
            countIncognitoMode = it[PersonPreferencesMetricsModel.countIncognitoMode] ?: 0,
        )
    }
}

object PersonPreferencesMetricsIdConverter : ValueIdConverter<PersonPreferencesMetricsId> {
    override val factory = ::PersonPreferencesMetricsId
    override val extract = PersonPreferencesMetricsId::value
}

internal object PersonPreferencesMetricsIdSerializer : ValueIdSerializer<PersonPreferencesMetricsId>(
    serialName = "PersonPreferencesMetricsId",
    converter = PersonPreferencesMetricsIdConverter,
)

@Serializable(with = PersonPreferencesMetricsIdSerializer::class)
data class PersonPreferencesMetricsId(val value: UUID) : ValueId {

    companion object : ValueIdClass<PersonPreferencesMetricsId>(
        converter = PersonPreferencesMetricsIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ValueId): Int {
        check(other is PersonPreferencesMetricsId)
        return value.compareTo(other.value)
    }
}

data class PersonPreferencesMetrics(
    val id: PersonPreferencesMetricsId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val ending: Instant,
    val countUsers: Long,
    val countIncognitoMode: Long,
) {
    val incognitoModeEnabledRatio: Double
        get() = countIncognitoMode.toDouble() / countUsers.toDouble()
}
