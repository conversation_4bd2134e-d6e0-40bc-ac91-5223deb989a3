package com.nextchaptersoftware.db.common

import com.nextchaptersoftware.db.common.column.TSVector
import com.nextchaptersoftware.db.common.column.TSVectorColumnType
import com.nextchaptersoftware.utils.safeTake
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.CustomFunction
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.FloatColumnType
import org.jetbrains.exposed.sql.Function
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.append
import org.jetbrains.exposed.sql.kotlin.datetime.KotlinInstantColumnType
import org.jetbrains.exposed.sql.stringLiteral
import org.jetbrains.exposed.sql.stringParam
import org.jetbrains.exposed.sql.transactions.TransactionManager

object FunctionExtensions {
    private const val DEFAULT_TEXT_SEARCH_CONFIG = "english"

    private const val MAX_QUERY_LENGTH = 500

    class TSVectorFunction(private val text: String) : Function<TSVector>(TSVectorColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder): Unit = queryBuilder {
            append("to_tsvector", '(')
            append(stringParam(DEFAULT_TEXT_SEARCH_CONFIG), "::regconfig")
            append(',')
            append(stringParam(text))
            append(')')
        }
    }

    class WebsearchTSQueryFunction(private val query: String) : Function<String>(TSVectorColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder): Unit = queryBuilder {
            append("websearch_to_tsquery", '(')
            append(stringParam(DEFAULT_TEXT_SEARCH_CONFIG), "::regconfig")
            append(',')
            append(stringParam(query.safeTake(MAX_QUERY_LENGTH)))
            append(')')
        }
    }

    // https://www.postgresql.org/docs/current/textsearch-controls.html
    class SetWeightFunction(tsVector: TSVectorFunction, weight: Weight) : CustomFunction<TSVector>(
        functionName = "setweight",
        columnType = TSVectorColumnType(),
        tsVector,
        stringLiteral(weight.name),
    ) {
        enum class Weight {
            A,
            B,
            C,
            D,
        }
    }

    class TsRankCdFunction(
        val column: Column<TSVector>,
        val query: String,
    ) : Function<Float>(FloatColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder): Unit = queryBuilder {
            append("ts_rank_cd(")
            append(column)
            append(", ")
            append(WebsearchTSQueryFunction(query.safeTake(MAX_QUERY_LENGTH)))
            append(")")
        }
    }

    /**
     * Converts a timestamp from the base DB timezone to another timezone.
     */
    class TimezoneFunction(
        private val expr: Expression<*>,
        private val targetTimezone: TimeZone,
        private val baseTimezone: TimeZone = TimeZone.UTC,
    ) : Function<Instant>(KotlinInstantColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
            append(expr, " AT TIME ZONE '${baseTimezone.id}' AT TIME ZONE '${targetTimezone.id}'")
        }
    }

    fun <T : Instant?> Expression<T>.atTimeZone(timezone: TimeZone) = TimezoneFunction(this, targetTimezone = timezone)

    /**
     * Truncates a date to the specified precision.
     */
    class DateTruncateFunction(
        private val expr: Expression<*>,
        private val precision: DateTruncatePrecision,
        private val timeZone: TimeZone?,
    ) : Function<Instant>(KotlinInstantColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
            append(
                "date_trunc('",
                precision.name,
                "', ",
                expr,
            )
            if (timeZone != null) {
                append(", '${timeZone.id}'")
            }
            append(
                ")",
            )
        }
    }

    fun <T : Instant?> Expression<T>.dateTruncate(precision: DateTruncatePrecision, timeZone: TimeZone?) =
        DateTruncateFunction(this, precision, timeZone)

    class DistinctOn<T>(
        private val expr: Column<T>,
        val columns: Array<out Expression<*>>,
    ) : Function<T>(expr.columnType) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) {
            queryBuilder {
                val txm = TransactionManager.current()
                val distinctNames = listOf(expr, *columns).joinToString {
                    when (it) {
                        is Column<*> -> txm.fullIdentity(it)
                        is Coalesce<*, *> -> it.toString()
                        else -> it.toString()
                    }
                }
                append("DISTINCT ON ($distinctNames) ${txm.fullIdentity(expr)}")
            }
        }
    }

    fun Column<*>.distinctOn(vararg extraColumns: Expression<*>) = DistinctOn(this, extraColumns)
}

@Suppress("EnumEntryName", "EnumNaming")
enum class DateTruncatePrecision {
    microseconds,
    milliseconds,
    second,
    minute,
    hour,
    day,
    week,
    month,
    quarter,
    year,
    decade,
    century,
    millennium,
}
