package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TeamSettingsStoreTest : DatabaseTestsBase() {
    private val store = Stores.teamSettingsStore

    private lateinit var scmTeam: ScmTeamDAO

    private suspend fun setup() {
        scmTeam = makeScmTeam()
    }

    @Test
    fun `set and get disableCommentSignatures`() = suspendingDatabaseTest {
        setup()
        assertThat(store.getDisableCommentSignatures(teamId = scmTeam.idValue)).isTrue

        store.upsert(teamId = scmTeam.idValue)
        assertThat(store.getDisableCommentSignatures(teamId = scmTeam.idValue)).isTrue

        store.upsert(teamId = scmTeam.idValue, disableCommentSignatures = false)
        assertThat(store.getDisableCommentSignatures(teamId = scmTeam.idValue)).isFalse

        store.upsert(teamId = scmTeam.idValue)
        assertThat(store.getDisableCommentSignatures(teamId = scmTeam.idValue)).isFalse

        store.upsert(teamId = scmTeam.idValue, disableCommentSignatures = true)
        assertThat(store.getDisableCommentSignatures(teamId = scmTeam.idValue)).isTrue
    }
}
