package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.junit.jupiter.api.Test

internal class MessageModelTest : DatabaseTestsBase() {
    @Test
    fun hasBeenEdited() = suspendingDatabaseTest {
        val message = makeMessage()
        assertThat(message.editedAt).isNull()
        assertThat(message.hasBeenEdited).isFalse

        suspendedTransaction {
            message.editedAt = Instant.nowWithMicrosecondPrecision()
        }

        suspendedTransaction {
            val found = MessageDAO.findById(message.id)
            assertThat(found).isNotNull
            assertThat(found?.editedAt).isNotNull
            assertThat(found?.hasBeenEdited).isTrue
        }
    }

    @Test
    fun findOrderedMessages() = suspendingDatabaseTest {
        val threadId = suspendedTransaction {
            val message = makeMessage(trx = this, content = "#1".toByteArray())
            makeMessage(trx = this, content = "#2".toByteArray(), thread = message.thread)
            makeMessage(trx = this, content = "#3".toByteArray(), thread = message.thread)
            makeMessage(trx = this, content = "#4".toByteArray(), thread = message.thread)
            message.thread.id
        }

        assertThat(
            suspendedTransaction {
                MessageDAO.find { MessageModel.thread eq threadId }
                    .orderBy(Pair(MessageModel.createdAt, SortOrder.ASC))
                    .map { String(it.content) }
            },
        ).isEqualTo(listOf("#1", "#2", "#3", "#4"))
    }

    @Test
    fun deletingThreadCascades() = suspendingDatabaseTest {
        val org = ModelBuilders.makeOrg()
        val thread = ModelBuilders.makeThread(org = org)
        makeMessage(thread = thread)

        // delete thread...
        suspendedTransaction {
            ThreadModel.deleteWhere { ThreadModel.id eq thread.id.value }
        }

        // ... expect message to be deleted
        suspendedTransaction {
            assertThat(ThreadDAO.findById(thread.id.value)).isNull()
            assertThat(OrgDAO.findById(org.id.value)).isNotNull
            assertThat(MessageDAO.find { MessageModel.thread eq thread.id.value }.count()).isZero
        }
    }
}
