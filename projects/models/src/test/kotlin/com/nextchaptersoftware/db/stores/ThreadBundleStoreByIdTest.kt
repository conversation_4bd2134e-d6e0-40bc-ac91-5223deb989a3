package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSlackChannel
import com.nextchaptersoftware.db.ModelBuilders.makeSlackChannelMember
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.ModelBuilders.makeThreadParticipant
import com.nextchaptersoftware.db.ModelBuilders.makeThreadUnread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadUnreadDAO
import com.nextchaptersoftware.db.models.UnreadStatus
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.NotModifiedException
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.fail

class ThreadBundleStoreByIdTest : DatabaseTestsBase() {
    private val store = Stores.threadBundleStore

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repoA: RepoDAO
    private lateinit var meOrgMember: OrgMemberDAO
    private lateinit var me: MemberDAO
    private lateinit var notMeOrgMember: OrgMemberDAO
    private lateinit var notMe: MemberDAO
    private lateinit var now: Instant
    private lateinit var thread: ThreadDAO
    private lateinit var privateThread: ThreadDAO
    private lateinit var myThreadUnread: ThreadUnreadDAO
    private lateinit var latestMessage: MessageDAO

    suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        repoA = makeRepo(scmTeam = scmTeam)
        meOrgMember = makeOrgMember(org = org)
        me = makeMember(scmTeam = scmTeam, orgMember = meOrgMember)
        notMeOrgMember = makeOrgMember(org = org)
        notMe = makeMember(scmTeam = scmTeam, orgMember = notMeOrgMember)
        now = Instant.nowWithMicrosecondPrecision()
        thread = makeThread(org = org, repo = repoA)
        privateThread = makeThread(org = org, repo = repoA, isPrivate = true)

        makeMessage(thread = thread)
        makeSourceMark(scmTeam = scmTeam, repo = repoA, thread = thread).also { mark ->
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = false)
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = false)
        }
        makeMessage(thread = thread)
        makeMessage(thread = thread)
        latestMessage = makeMessage(thread = thread)
        myThreadUnread = makeThreadUnread(thread = thread, member = me, latestMessage = latestMessage)
        makeThreadParticipant(thread = thread, member = me)
        makeThreadParticipant(thread = thread, member = notMe)

        makeThreadParticipant(thread = privateThread, member = me)
    }

    @Test
    fun `threadsById contains info`() = suspendingDatabaseTest {
        setup()

        store.getThreadBundleById(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            threadId = thread.idValue,
            associatedSlackMemberIds = null,
        )?.let { threadInfo ->

            // unread status
            val unread = threadInfo.unread
            assertThat(unread).isNotNull
            unread?.let {
                assertThat(it.isUnread).isTrue
                assertThat(it.status).isEqualTo(UnreadStatus.NoMessagesRead)
                assertThat(it.threadId).isEqualTo(thread.idValue)
            }

            // all messages
            assertThat(threadInfo.messages).size().isEqualTo(4)

            // all participants
            assertThat(threadInfo.participantOrgMemberIds).size().isEqualTo(2)

            // repo Id
            assertThat(threadInfo.repo?.id).isEqualTo(repoA.idValue)

            // all sourcemarks
            assertThat(threadInfo.sourcemark).isNotNull
            assertThat(threadInfo.sourcemark?.repoId).isEqualTo(repoA.idValue)

            // only original sourcepoints
            assertThat(threadInfo.originalSourcePoints).size().isEqualTo(3)
            assertThat(threadInfo.originalSourcePoints.map { it.isOriginal }.distinct()).containsExactly(true)
        } ?: fail("Expected thread, but got none.")
    }

    @Test
    fun `getThreadExists where thread unread is touched`() = suspendingDatabaseTest {
        setup()

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = thread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = now,
                associatedSlackMemberIds = null,
            ),
        ).isEqualTo(thread.idValue)

        val newNow = Instant.nowWithMicrosecondPrecision()

        assertThrows<NotModifiedException> {
            store.getThreadById(
                orgId = org.idValue,
                threadId = thread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = newNow,
                associatedSlackMemberIds = null,
            )
        }

        // Touch myThreadUnread
        suspendedTransaction { myThreadUnread.modifiedAt = Instant.nowWithMicrosecondPrecision() }

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = thread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = newNow,
                associatedSlackMemberIds = null,
            ),
        ).isEqualTo(thread.idValue)
    }

    @Test
    fun `threadsById returns info on private thread I am participant of`() = suspendingDatabaseTest {
        setup()

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = privateThread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = now,
                associatedSlackMemberIds = null,
            ),
        ).isEqualTo(privateThread.idValue)
    }

    @Test
    fun `threadsById does not return info on private thread I am not participant of`() = suspendingDatabaseTest {
        setup()

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = privateThread.idValue,
                orgMemberId = notMeOrgMember.idValue,
                modifiedSince = now,
                associatedSlackMemberIds = null,
            ),
        ).isNull()
    }

    @Test
    fun `threadsById only return info on slack thread where I am a member of the slack channel`() = suspendingDatabaseTest {
        setup()

        val slackTeam = makeSlackTeam(org = org)
        val slackMember = makeMember(scmTeam = scmTeam, identity = makeIdentity(provider = Provider.Slack), association = me.idValue)
        val privateSlackChannel = makeSlackChannel(slackTeam = slackTeam, isPrivate = true)
        val slackThread = makeThread(org = org, slackChannel = privateSlackChannel)

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = slackThread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = now,
                associatedSlackMemberIds = listOf(slackMember.idValue),
            ),
        ).isNull()

        // Make me a member of the channel
        makeSlackChannelMember(slackTeam = slackTeam, slackChannel = privateSlackChannel, member = slackMember)

        assertThat(
            store.getThreadById(
                orgId = org.idValue,
                threadId = slackThread.idValue,
                orgMemberId = meOrgMember.idValue,
                modifiedSince = now,
                associatedSlackMemberIds = listOf(slackMember.idValue),
            ),
        ).isEqualTo(slackThread.idValue)
    }
}
