@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.db.common.schema

import com.nextchaptersoftware.db.common.withDatabase
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode

@Execution(ExecutionMode.SAME_THREAD)
@Disabled
class PlatformSchemaLockServiceTest : DatabaseTestsBase() {
    companion object {
        private const val OPERATION = "DUMMY"
    }

    private fun PlatformSchemaLockService.assertWithSchemaLock(
        expectedSchemaUpdate: Boolean = true,
        operation: String,
        body: () -> Unit,
    ): SchemaStatus {
        var executedSchemaUpdate = false
        return this.withSchemaLock(operation = operation) {
            executedSchemaUpdate = true
            body()
        }.also {
            assertThat(executedSchemaUpdate).withFailMessage(
                "Expected schema update condition failed",
                executedSchemaUpdate,
                expectedSchemaUpdate,
            ).isEqualTo(expectedSchemaUpdate)
        }
    }

    @Test
    fun `test basic schema lock`() = suspendingDatabaseTest {
        val schemaStatus = PlatformSchemaLockService().assertWithSchemaLock(operation = OPERATION) {}
        assertThat(schemaStatus).isEqualTo(SchemaStatus.Finished)
    }

    @Test
    fun `test offset schema locks for same version results in finished`() = suspendingDatabaseTest {
        runBlocking {
            val schemaStatus1 = PlatformSchemaLockService().withSchemaLock(OPERATION) {}
            val schemaStatus2 = PlatformSchemaLockService().assertWithSchemaLock(expectedSchemaUpdate = false, operation = OPERATION) {}
            assertThat(schemaStatus1).isEqualTo(SchemaStatus.Finished)
            assertThat(schemaStatus2).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test parallel schema locks for same version results in busy`() = suspendingDatabaseTest {
        runBlocking {
            val asyncJob = async(Dispatchers.IO.withDatabase(currentCoroutineContext())) {
                PlatformSchemaLockService().assertWithSchemaLock(operation = OPERATION) {
                    runBlocking {
                        delay(2000)
                    }
                }
            }

            delay(1000)
            val schemaStatus = PlatformSchemaLockService().assertWithSchemaLock(operation = OPERATION, expectedSchemaUpdate = false) {}
            assertThat(schemaStatus).isEqualTo(SchemaStatus.Busy)
            assertThat(asyncJob.await()).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test parallel schema lock for different versions results in finished`() = suspendingDatabaseTest {
        runBlocking {
            val asyncJob = async(Dispatchers.IO.withDatabase(currentCoroutineContext())) {
                PlatformSchemaLockService(platformVersion = "version1", platformBuildNumber = 1).assertWithSchemaLock(
                    operation = OPERATION,
                ) {}
            }

            delay(1000)

            val schemaStatus = PlatformSchemaLockService(platformVersion = "version2", platformBuildNumber = 2).assertWithSchemaLock(
                operation = OPERATION,
            ) {}
            assertThat(schemaStatus).isIn(SchemaStatus.Finished)
            assertThat(asyncJob.await()).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test parallel schema lock with timeout results in finished`() = suspendingDatabaseTest {
        runBlocking {
            val asyncJob = async(Dispatchers.IO.withDatabase(currentCoroutineContext())) {
                PlatformSchemaLockService().assertWithSchemaLock(operation = OPERATION) {
                    runBlocking {
                        delay(2000)
                    }
                }
            }

            delay(500)

            val schemaStatus = PlatformSchemaLockService(
                busyExpiry = 5.milliseconds,
            ).assertWithSchemaLock(operation = OPERATION) {}
            assertThat(schemaStatus).isIn(SchemaStatus.Finished)
            assertThat(asyncJob.await()).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test offset schema lock with higher build number preempts lower build number lock`() = suspendingDatabaseTest {
        runBlocking {
            val schemaStatus1 = PlatformSchemaLockService(platformVersion = "2", platformBuildNumber = 2).assertWithSchemaLock(
                operation = OPERATION,
            ) {}
            val schemaStatus2 = PlatformSchemaLockService(platformVersion = "1", platformBuildNumber = 1).assertWithSchemaLock(
                expectedSchemaUpdate = false,
                operation = OPERATION,
            ) {}
            assertThat(schemaStatus1).isEqualTo(SchemaStatus.Finished)
            assertThat(schemaStatus2).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test offset schema lock with ordered build numbers results in both locks being invoked`() = suspendingDatabaseTest {
        runBlocking {
            val schemaStatus1 = PlatformSchemaLockService(platformVersion = "1", platformBuildNumber = 1).assertWithSchemaLock(
                operation = OPERATION,
            ) {}
            val schemaStatus2 = PlatformSchemaLockService(platformVersion = "2", platformBuildNumber = 2).assertWithSchemaLock(
                operation = OPERATION,
            ) {}
            assertThat(schemaStatus1).isEqualTo(SchemaStatus.Finished)
            assertThat(schemaStatus2).isEqualTo(SchemaStatus.Finished)
        }
    }

    @Test
    fun `test schema lock with different operations with both locks being invoked`() = suspendingDatabaseTest {
        runBlocking {
            val schemaStatus1 = PlatformSchemaLockService(platformVersion = "1", platformBuildNumber = 1).assertWithSchemaLock(
                operation = OPERATION + "1",
            ) {}
            val schemaStatus2 = PlatformSchemaLockService(platformVersion = "2", platformBuildNumber = 2).assertWithSchemaLock(
                operation = OPERATION + "2",
            ) {}
            assertThat(schemaStatus1).isEqualTo(SchemaStatus.Finished)
            assertThat(schemaStatus2).isEqualTo(SchemaStatus.Finished)
        }
    }
}
