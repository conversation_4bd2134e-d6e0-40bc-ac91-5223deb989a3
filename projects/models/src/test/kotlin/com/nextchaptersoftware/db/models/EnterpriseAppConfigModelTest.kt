package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.ModelBuilders.makeGitHubEnterpriseAppConfig
import com.nextchaptersoftware.db.ModelBuilders.makeGitLabEnterpriseAppConfig
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class EnterpriseAppConfigModelTest : DatabaseTestsBase() {

    @Nested
    inner class GitHub {

        @Test
        fun getAuthority() = suspendingDatabaseTest {
            // default port
            makeGitHubEnterpriseAppConfig(hostAndPort = "git.acme.com:443").also {
                assertThat(it.asDataModel().hostAndPort).isEqualTo("git.acme.com:443")
                assertThat(it.asDataModel().authority).isEqualTo("git.acme.com")
            }

            // non-default port
            makeGitHubEnterpriseAppConfig(hostAndPort = "git.acme.com:8443").also {
                assertThat(it.asDataModel().hostAndPort).isEqualTo("git.acme.com:8443")
                assertThat(it.asDataModel().authority).isEqualTo("git.acme.com:8443")
            }
        }
    }

    @Nested
    inner class GitLab {

        @Test
        fun getAuthority() = suspendingDatabaseTest {
            // default port
            makeGitLabEnterpriseAppConfig(hostAndPort = "git.acme.com:443").also {
                assertThat(it.asDataModel().hostAndPort).isEqualTo("git.acme.com:443")
                assertThat(it.asDataModel().authority).isEqualTo("git.acme.com")
            }

            // non-default port
            makeGitLabEnterpriseAppConfig(hostAndPort = "git.acme.com:8443").also {
                assertThat(it.asDataModel().hostAndPort).isEqualTo("git.acme.com:8443")
                assertThat(it.asDataModel().authority).isEqualTo("git.acme.com:8443")
            }
        }
    }
}
