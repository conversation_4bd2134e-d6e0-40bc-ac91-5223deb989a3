package com.nextchaptersoftware.db

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.stores.PullRequestStore
import java.util.UUID
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.Instant
import org.mockito.Mockito.anyList
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock

object MockPullRequestStore {
    @Suppress("LongMethod")
    fun get(pullRequests: List<PullRequest> = emptyList()): PullRequestStore = runBlocking {
        val mockStore = mock<PullRequestStore>()

        `when`(mockStore.findById(anyOrNull(), any(), any())).thenAnswer { invocationOnMock ->
            val pullRequestId = (invocationOnMock.arguments[2] as UUID)
            pullRequests.firstOrNull { it.id == pullRequestId }
        }

        `when`(mockStore.find(trx = anyOrNull(), orgId = any(), ids = anyList(), modifiedSince = anyOrNull(), limit = anyOrNull()))
            .thenAnswer { invocationOnMock ->
                val pullRequestIds = (invocationOnMock.arguments[2] as List<*>)
                pullRequests.filter { it.id in pullRequestIds }
            }

        `when`(mockStore.findOpen(any(), any(), any(), any(), anyOrNull())).thenAnswer { invocationOnMock ->
            val modifiedSince = (invocationOnMock.arguments[3] as Instant?)
            pullRequests
                .filter {
                    when (modifiedSince) {
                        null -> true
                        else -> it.modifiedAt >= modifiedSince
                    }
                }
        }

        `when`(mockStore.findByCommitHashes(any(), any())).thenAnswer { invocationOnMock ->
            val repoId = invocationOnMock.arguments[0] as UUID
            val commitHashes = invocationOnMock.arguments[1] as List<*>
            pullRequests
                .filter { it.repoId == repoId }
                .filter { commitHashes.contains(it.mergeCommitSha) }
        }

        mockStore
    }
}
