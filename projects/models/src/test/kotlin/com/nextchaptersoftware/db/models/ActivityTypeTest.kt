package com.nextchaptersoftware.db.models

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ActivityTypeTest {

    @Test
    fun isEngagementSignal() {
        ActivityType.entries.forEach { activity ->
            when (activity) {
                ActivityType.ContentViewed,
                ActivityType.IdeInsightsPanelOpened,
                ActivityType.IdeSidebarPanelOpened,
                ActivityType.IntegrationChange,
                ActivityType.MessageCreated,
                ActivityType.PullRequestViewed,
                ActivityType.MemberDescriptionChange,
                ActivityType.ThreadCreated,
                ActivityType.ThreadViewed,
                ActivityType.TopicExpertiseChange,
                ActivityType.VisitedProcessingComplete,
                ActivityType.TeamUsageViewed,
                ActivityType.IdeThreadViewedFromMyQuestions,
                ActivityType.IdeThreadViewedFromGutterIcon,
                ActivityType.IdeThreadViewedFromPRExplorerInsights,
                ActivityType.IdeThreadViewedFromQAExplorerInsights,
                ActivityType.AnswerPreferencesTooltipViewed,
                    -> assertThat(activity.isEngagementSignal).isTrue

                ActivityType.IdeInsightsPanelClosed,
                ActivityType.IdeInsightsViewed,
                ActivityType.IdeSidebarPanelClosed,
                ActivityType.IdeSidebarViewed,
                ActivityType.Search,
                    -> assertThat(activity.isEngagementSignal).isFalse
            }
        }
    }

    @Test
    fun isHighEngagementSignal() {
        ActivityType.entries.forEach { activity ->
            when (activity) {
                ActivityType.MessageCreated,
                ActivityType.ThreadCreated,
                    -> assertThat(activity.isHighEngagementSignal).isTrue

                ActivityType.ContentViewed,
                ActivityType.IdeInsightsPanelClosed,
                ActivityType.IdeInsightsPanelOpened,
                ActivityType.IdeInsightsViewed,
                ActivityType.IdeSidebarPanelClosed,
                ActivityType.IdeSidebarPanelOpened,
                ActivityType.IdeSidebarViewed,
                ActivityType.IntegrationChange,
                ActivityType.PullRequestViewed,
                ActivityType.Search,
                ActivityType.MemberDescriptionChange,
                ActivityType.ThreadViewed,
                ActivityType.TopicExpertiseChange,
                ActivityType.VisitedProcessingComplete,
                ActivityType.IdeThreadViewedFromMyQuestions,
                ActivityType.IdeThreadViewedFromGutterIcon,
                ActivityType.IdeThreadViewedFromPRExplorerInsights,
                ActivityType.IdeThreadViewedFromQAExplorerInsights,
                ActivityType.TeamUsageViewed,
                ActivityType.AnswerPreferencesTooltipViewed,
                    -> assertThat(activity.isHighEngagementSignal).isFalse
            }
        }
    }

    @Test
    fun shouldThrottle() {
        ActivityType.entries.forEach { activity ->
            when (activity) {
                ActivityType.IdeInsightsViewed,
                ActivityType.IdeSidebarViewed,
                ActivityType.TeamUsageViewed,
                    -> assertThat(activity.shouldThrottle).isTrue

                ActivityType.ContentViewed,
                ActivityType.IdeInsightsPanelClosed,
                ActivityType.IdeInsightsPanelOpened,
                ActivityType.IdeSidebarPanelClosed,
                ActivityType.IdeSidebarPanelOpened,
                ActivityType.IntegrationChange,
                ActivityType.MessageCreated,
                ActivityType.PullRequestViewed,
                ActivityType.Search,
                ActivityType.MemberDescriptionChange,
                ActivityType.ThreadCreated,
                ActivityType.ThreadViewed,
                ActivityType.TopicExpertiseChange,
                ActivityType.VisitedProcessingComplete,
                ActivityType.IdeThreadViewedFromMyQuestions,
                ActivityType.IdeThreadViewedFromGutterIcon,
                ActivityType.IdeThreadViewedFromPRExplorerInsights,
                ActivityType.IdeThreadViewedFromQAExplorerInsights,
                ActivityType.AnswerPreferencesTooltipViewed,
                    -> assertThat(activity.shouldThrottle).isFalse
            }
        }
    }
}
