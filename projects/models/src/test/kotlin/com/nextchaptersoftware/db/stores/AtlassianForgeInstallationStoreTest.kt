package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AtlassianForgeInstallationStoreTest : DatabaseTestsBase() {
    private val store = Stores.atlassianForgeInstallationStore

    @Test
    fun `upsert and find`() = suspendingDatabaseTest {
        val provider = Provider.Jira
        val siteId = UUID.randomUUID().toString()

        val token = Ciphertext("token".toByteArray())

        val result = store.upsert(
            provider = provider,
            siteId = siteId,
            appSystemToken = token,
        )

        assertThat(result.appSystemToken).isEqualTo(token)
        assertThat(store.find(provider = provider, siteId = siteId)).isEqualTo(result)
        assertThat(store.find(provider = Provider.Confluence, siteId = siteId)).isNull()
        assertThat(store.find(provider = provider, siteId = UUID.randomUUID().toString())).isNull()

        val newToken = Ciphertext("newToken".toByteArray())

        val updated = store.upsert(
            provider = provider,
            siteId = siteId,
            appSystemToken = newToken,
        )

        assertThat(updated.appSystemToken).isEqualTo(newToken)
        assertThat(store.find(provider = provider, siteId = siteId)).isEqualTo(updated)
        assertThat(store.find(provider = Provider.Confluence, siteId = siteId)).isNull()
        assertThat(store.find(provider = provider, siteId = UUID.randomUUID().toString())).isNull()
    }
}
