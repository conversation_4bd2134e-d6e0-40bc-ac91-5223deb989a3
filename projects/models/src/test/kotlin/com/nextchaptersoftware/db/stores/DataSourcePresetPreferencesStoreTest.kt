package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class DataSourcePresetPreferencesStoreTest : DatabaseTestsBase() {
    private val store = Stores.dataSourcePresetPreferencesStore

    @Test
    fun `upsert, find, and delete`() = suspendingDatabaseTest {
        val orgMember = makeOrgMember().asDataModel()
        val dataSourcePresetA = Stores.dataSourcePresetStore.upsert(
            id = DataSourcePresetId.random(),
            orgId = orgMember.orgId,
            name = "A",
            avatarUrl = "https://getunblocked.com/public/red-bot.svg",
        )
        val dataSourcePresetB = Stores.dataSourcePresetStore.upsert(
            id = DataSourcePresetId.random(),
            orgId = orgMember.orgId,
            name = "B",
            avatarUrl = "https://getunblocked.com/public/red-bot.svg",
        )

        assertThat(store.find(orgMemberId = orgMember.id)).isNull()

        val dataSourcePresetPreferences = store.upsert(orgMemberId = orgMember.id, dataSourcePresetId = dataSourcePresetA.id)

        assertThat(dataSourcePresetPreferences.orgMemberId).isEqualTo(orgMember.id)
        assertThat(dataSourcePresetPreferences.dataSourcePresetId).isEqualTo(dataSourcePresetA.id)
        assertThat(store.find(orgMemberId = orgMember.id)).isEqualTo(dataSourcePresetPreferences)

        val dataSourcePresetPreferencesUpdated = store.upsert(orgMemberId = orgMember.id, dataSourcePresetId = dataSourcePresetB.id)

        assertThat(dataSourcePresetPreferencesUpdated.orgMemberId).isEqualTo(orgMember.id)
        assertThat(dataSourcePresetPreferencesUpdated.dataSourcePresetId).isEqualTo(dataSourcePresetB.id)
        assertThat(store.find(orgMemberId = orgMember.id)).isEqualTo(dataSourcePresetPreferencesUpdated)

        store.delete(orgMemberId = orgMember.id)

        assertThat(store.find(orgMemberId = orgMember.id)).isNull()
    }
}
