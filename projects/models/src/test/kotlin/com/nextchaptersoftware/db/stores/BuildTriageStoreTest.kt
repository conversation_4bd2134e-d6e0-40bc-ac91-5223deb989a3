package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeBuild
import com.nextchaptersoftware.db.ModelBuilders.makeBuildJob
import com.nextchaptersoftware.db.ModelBuilders.makeBuildTriage
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelFactory.randomSha1
import com.nextchaptersoftware.db.ModelFactory.randomStrings
import com.nextchaptersoftware.db.models.BuildDAO
import com.nextchaptersoftware.db.models.BuildTriageDAO
import com.nextchaptersoftware.db.models.BuildTriageState
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.test.utils.Clock
import kotlin.time.Duration.Companion.microseconds
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assumptions.assumeThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class BuildTriageStoreTest : DatabaseTestsBase() {

    private val store = Stores.buildTriageStore

    @Nested
    inner class MarkTriagesOnSuccessOrFailure {

        private suspend fun makeBuildJobAndTriage(
            build: BuildDAO,
            displayName: String,
            state: BuildTriageState,
        ): BuildTriageDAO {
            val job = makeBuildJob(build = build, displayName = displayName)
            val triage = makeBuildTriage(build = build, buildJobs = setOf(job), state = state)
            return triage
        }

        private suspend fun PullRequestDAO.markTriagesOnSuccess(jobDisplayName: String) {
            store.markTriagesOnSuccess(
                pullRequestId = idValue,
                jobDisplayName = jobDisplayName,
                commitFixedSha = randomSha1(),
            )
        }

        private suspend fun PullRequestDAO.markTriagesOnFailure(jobDisplayName: String) {
            store.markTriagesOnFailure(
                pullRequestId = idValue,
                jobDisplayName = jobDisplayName,
            )
        }

        private fun List<BuildTriageDAO>.refresh() = onEach { it.refresh() }

        // ------ ON SUCCESS

        @Test
        fun `markTriagesOnSuccess -- build unique -- display name is X`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            val triages = mutableListOf<BuildTriageDAO>()
            val build = makeBuild(pullRequest = pullRequest)

            val triage1 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triage2 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triage3 = makeBuildJobAndTriage(build = build, displayName = "C", state = BuildTriageState.Obsolete).also(triages::add)
            val triage4 = makeBuildJobAndTriage(build = build, displayName = "D", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnSuccess(jobDisplayName = "X")
            triages.refresh()

            // no change
            assertThat(triage1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triage2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triage3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triage4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnSuccess -- build unique -- display name is A`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            val triages = mutableListOf<BuildTriageDAO>()
            val build = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnSuccess(jobDisplayName = "A")
            triages.refresh()

            // A: transitions
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Fixed) // was Open
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Obsolete) // was Outdated
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Fixed)
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnSuccess -- build multiple -- display name is X`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            val triages = mutableListOf<BuildTriageDAO>()
            val build1 = makeBuild(pullRequest = pullRequest)
            val build2 = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnSuccess(jobDisplayName = "X")
            triages.refresh()

            // A: no change
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Fixed)
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnSuccess -- build multiple -- display name is A`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            val triages = mutableListOf<BuildTriageDAO>()
            val build1 = makeBuild(pullRequest = pullRequest)
            val build2 = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnSuccess(jobDisplayName = "A")
            triages.refresh()

            // A: transitions
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Fixed) // was Open
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Obsolete) // was Outdated
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Fixed)
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }

        // ------ ON FAILURE

        @Test
        fun `markTriagesOnFailure -- build unique -- display name is X`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            val triages = mutableListOf<BuildTriageDAO>()
            val build = makeBuild(pullRequest = pullRequest)

            val triage1 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triage2 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triage3 = makeBuildJobAndTriage(build = build, displayName = "C", state = BuildTriageState.Obsolete).also(triages::add)
            val triage4 = makeBuildJobAndTriage(build = build, displayName = "D", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnFailure(jobDisplayName = "X")
            triages.refresh()

            // no change
            assertThat(triage1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triage2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triage3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triage4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnFailure -- build unique -- display name is A`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            val triages = mutableListOf<BuildTriageDAO>()
            val build = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnFailure(jobDisplayName = "A")
            triages.refresh()

            // A: transitions
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Outdated) // was Open
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Obsolete) // was Fixed
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnFailure -- build multiple -- display name is X`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            val triages = mutableListOf<BuildTriageDAO>()
            val build1 = makeBuild(pullRequest = pullRequest)
            val build2 = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnFailure(jobDisplayName = "X")
            triages.refresh()

            // A: no change
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Fixed)
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `markTriagesOnFailure -- build multiple -- display name is A`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            val triages = mutableListOf<BuildTriageDAO>()
            val build1 = makeBuild(pullRequest = pullRequest)
            val build2 = makeBuild(pullRequest = pullRequest)

            val triageA1 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Open).also(triages::add)
            val triageA2 = makeBuildJobAndTriage(build = build1, displayName = "A", state = BuildTriageState.Outdated).also(triages::add)
            val triageA3 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Obsolete).also(triages::add)
            val triageA4 = makeBuildJobAndTriage(build = build2, displayName = "A", state = BuildTriageState.Fixed).also(triages::add)

            val triageB1 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Open).also(triages::add)
            val triageB2 = makeBuildJobAndTriage(build = build1, displayName = "B", state = BuildTriageState.Outdated).also(triages::add)
            val triageB3 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Obsolete).also(triages::add)
            val triageB4 = makeBuildJobAndTriage(build = build2, displayName = "B", state = BuildTriageState.Fixed).also(triages::add)

            pullRequest.markTriagesOnFailure(jobDisplayName = "A")
            triages.refresh()

            // A: transitions
            assertThat(triageA1.state).isEqualTo(BuildTriageState.Outdated) // was Open
            assertThat(triageA2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageA3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageA4.state).isEqualTo(BuildTriageState.Obsolete) // was Fixed
            // B: no change
            assertThat(triageB1.state).isEqualTo(BuildTriageState.Open)
            assertThat(triageB2.state).isEqualTo(BuildTriageState.Outdated)
            assertThat(triageB3.state).isEqualTo(BuildTriageState.Obsolete)
            assertThat(triageB4.state).isEqualTo(BuildTriageState.Fixed)
        }
    }

    @Nested
    inner class FindTriageStateForTest {

        @Test
        fun `findTriageStateFor -- no triages -- pr triage null`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            assertThat(pullRequest.ciTriageState).isNull()
            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isNull()
        }

        @ParameterizedTest
        @EnumSource(BuildTriageState::class)
        fun `findTriageStateFor -- no triages -- pr triage unset`(
            ciTriageState: BuildTriageState,
        ) = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo, ciTriageState = ciTriageState)
            assertThat(pullRequest.ciTriageState).isEqualTo(ciTriageState)
            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isNull()
        }

        private suspend fun makeBuildTriageRandom(
            pullRequest: PullRequestDAO,
            state: BuildTriageState,
        ): BuildTriageDAO {
            val build = makeBuild(
                pullRequest = pullRequest,
            )
            val buildJob = makeBuildJob(
                build = build,
            )
            return makeBuildTriage(
                state = state,
                build = build,
                buildJobs = buildJob.let(::setOf),
                header = randomStrings.nextPrint(10),
                details = randomStrings.nextPrint(20),
            )
        }

        @ParameterizedTest
        @EnumSource(BuildTriageState::class)
        fun `findTriageStateFor -- triage single -- isActive is true`(
            ciTriageState: BuildTriageState,
        ) = suspendingDatabaseTest {
            assumeThat(ciTriageState.isActive).isTrue()
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            makeBuildTriageRandom(pullRequest = pullRequest, state = ciTriageState)
            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isEqualTo(BuildTriageState.Open)
        }

        @ParameterizedTest
        @EnumSource(BuildTriageState::class)
        fun `findTriageStateFor -- triage single -- isActive is false`(
            ciTriageState: BuildTriageState,
        ) = suspendingDatabaseTest {
            assumeThat(ciTriageState.isActive).isFalse()
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)
            makeBuildTriageRandom(pullRequest = pullRequest, state = ciTriageState)
            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isEqualTo(
                BuildTriageState.Fixed,
            )
        }

        @Test
        fun `findTriageStateFor -- triage multiple -- none isActive`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            makeBuildTriageRandom(pullRequest = pullRequest, state = BuildTriageState.Obsolete)
            makeBuildTriageRandom(pullRequest = pullRequest, state = BuildTriageState.Fixed)

            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isEqualTo(BuildTriageState.Fixed)
        }

        @Test
        fun `findTriageStateFor -- triage multiple -- some isActive`() = suspendingDatabaseTest {
            val repo = makeRepo()
            val pullRequest = makePullRequest(repo = repo)

            makeBuildTriageRandom(pullRequest = pullRequest, state = BuildTriageState.Obsolete)
            makeBuildTriageRandom(pullRequest = pullRequest, state = BuildTriageState.Fixed)
            makeBuildTriageRandom(pullRequest = pullRequest, state = BuildTriageState.Outdated)

            assertThat(
                store.findTriageStateFor(
                    pullRequestId = pullRequest.idValue,
                ),
            ).isEqualTo(BuildTriageState.Open)
        }
    }

    @Nested
    inner class FindBestBeforeReaction {

        @Test
        fun `findBestBeforeReaction -- when no triages`() = suspendingDatabaseTest {
            val clock = Clock()
            val pullRequest = makePullRequest()

            val (build, jobs) = makeBuild(pullRequest = pullRequest, createdAt = clock.time).let { build ->
                val job1 = makeBuildJob(build = build, createdAt = clock.tick(10.seconds))
                val job2 = makeBuildJob(build = build, createdAt = clock.tick(20.seconds))
                build to listOf(job1, job2)
            }

            // no triages
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = clock.tick(),
                ),
            ).isNull()

            makeBuildTriage(
                build = build,
                buildJobs = jobs[0].let(::setOf),
                createdAt = jobs[0].createdAt.plus(2.seconds),
                isVisible = false,
            )

            // triages exists but not user visible
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = clock.tick(),
                ),
            ).isNull()
        }

        @Suppress("LongMethod")
        @Test
        fun `findBestsTriagesBeforeReaction -- when triages`() = suspendingDatabaseTest {
            /*
                | Bx1
                | └────────────────> Jx1 ──────────────> Jx2
                | .                  └> T1               └> T2
                | .                  .                   .
                | .        By2        .                   .
                | .        └────────────────> Jy1 ─────> Jy2
                | .        .         .        .          └──────> T3
                | .        .         .        .          .
                + 0 ------ 5 ------ 10 ------ 15 ------ 20 ------ 25 --------> time
             */
            val thresholdLarge = 1.minutes // large threshold means multiple triages => no correlation expected
            val thresholdSmall = 10.seconds // small threshold means single triage => correlation expected

            val clock = Clock()
            val pullRequest = makePullRequest()

            val (buildX, jobsX) = makeBuild(pullRequest = pullRequest, createdAt = clock.time).let { build ->
                val job1 = makeBuildJob(build = build, createdAt = clock.tick(10.seconds))
                val job2 = makeBuildJob(build = build, createdAt = clock.tick(20.seconds))
                build to listOf(job1, job2)
            }

            val (buildY, jobsY) = makeBuild(pullRequest = pullRequest, createdAt = clock.tick(5.seconds)).let { build ->
                val job1 = makeBuildJob(build = build, createdAt = clock.tick(15.seconds))
                val job2 = makeBuildJob(build = build, createdAt = clock.tick(20.seconds))
                build to listOf(job1, job2)
            }

            // no triages -> null
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = clock.tick(),
                ),
            ).isNull()

            val triage1 = makeBuildTriage(
                build = buildX,
                buildJobs = jobsX[0].let(::setOf),
                createdAt = jobsX[0].createdAt.plus(2.seconds),
                isVisible = true,
            ).asDataModel()

            // before triage 1 -> null
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage1.createdAt.minus(1.microseconds),
                ),
            ).isNull()

            // after triage 1 -> triage 1
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage1.createdAt.plus(1.microseconds),
                ),
            ).isEqualTo(triage1)

            val triage2 = makeBuildTriage(
                build = buildX,
                buildJobs = jobsX[1].let(::setOf),
                createdAt = jobsX[1].createdAt.plus(2.seconds),
                isVisible = true,
            ).asDataModel()

            // before triage 2 -> triage 1
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage2.createdAt.minus(1.microseconds),
                ),
            ).isEqualTo(triage1)

            // after triage 2 -> triage 2
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage2.createdAt.plus(1.microseconds),
                    threshold = thresholdLarge,
                ),
            ).isNull()

            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage2.createdAt.plus(1.microseconds),
                    threshold = thresholdSmall,
                ),
            ).isEqualTo(triage2)

            val triage3 = makeBuildTriage(
                build = buildY,
                buildJobs = jobsY[1].let(::setOf),
                createdAt = jobsY[1].createdAt.plus(5.seconds),
                isVisible = true,
            ).asDataModel()

            // before triage 3 -> triage 2
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage3.createdAt.minus(1.microseconds),
                    threshold = thresholdLarge,
                ),
            ).isNull()

            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage3.createdAt.minus(1.microseconds),
                    threshold = thresholdSmall,
                ),
            ).isEqualTo(triage2)

            // after triage 3 -> triage 2
            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage3.createdAt.plus(1.microseconds),
                    threshold = thresholdLarge,
                ),
            ).isNull()

            assertThat(
                store.findBestBeforeReaction(
                    pullRequestId = pullRequest.idValue,
                    reactedAt = triage3.createdAt.plus(1.microseconds),
                    threshold = thresholdSmall,
                ),
            ).isEqualTo(triage3)
        }
    }
}
