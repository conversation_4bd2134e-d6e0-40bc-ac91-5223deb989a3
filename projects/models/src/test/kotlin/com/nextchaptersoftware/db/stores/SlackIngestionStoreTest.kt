package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.SlackIngestionDAO
import com.nextchaptersoftware.db.models.SlackIngestionStatus
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackIngestionStoreTest : DatabaseTestsBase() {
    private val store = Stores.slackIngestionStore

    private lateinit var org: OrgDAO
    private lateinit var installation: InstallationDAO
    private lateinit var slackTeam: SlackTeamDAO

    private suspend fun setup() {
        org = makeOrg()
        installation = makeInstallation(provider = Provider.Slack, org = org)
        slackTeam = makeSlackTeam(org = org, installation = installation)
    }

    @Test
    fun upsert() = suspendingDatabaseTest {
        setup()
        val daos = store.upsert(
            slackTeamId = slackTeam.idValue,
            status = SlackIngestionStatus.Error,
        )

        assertThat(daos.installation.id.value).isEqualTo(installation.id.value)
        assertThat(daos.org.id.value).isEqualTo(org.id.value)

        val slackIngestion = suspendedTransaction {
            SlackIngestionDAO.findById(daos.slackIngestion.id.value)
        }

        checkNotNull(slackIngestion)
        assertThat(slackIngestion.status).isEqualTo(SlackIngestionStatus.Error)
    }
}
