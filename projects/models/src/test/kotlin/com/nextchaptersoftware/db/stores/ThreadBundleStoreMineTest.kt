package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.ModelBuilders.makeThreadParticipant
import com.nextchaptersoftware.db.ModelBuilders.makeThreadUnread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.cursors.OpaqueCursor.toCursor
import com.nextchaptersoftware.db.cursors.ThreadCursor
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.UnreadStatus
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.hours
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.Transaction
import org.junit.jupiter.api.Test

class ThreadBundleStoreMineTest : DatabaseTestsBase() {
    private val store = Stores.threadBundleStore

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repoA: RepoDAO
    private lateinit var meOrgMember: OrgMemberDAO
    private lateinit var me: MemberDAO
    private lateinit var notMeOrgMember: OrgMemberDAO
    private lateinit var notMe: MemberDAO
    private lateinit var now: Instant

    suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        repoA = makeRepo(scmTeam = scmTeam)
        meOrgMember = makeOrgMember(org = org)
        me = makeMember(scmTeam = scmTeam, orgMember = meOrgMember)
        notMeOrgMember = makeOrgMember(org = org)
        notMe = makeMember(scmTeam = scmTeam, orgMember = notMeOrgMember)
        now = Instant.nowWithMicrosecondPrecision()
    }

    @Test
    fun `handles multiple original sourcepoints`() = suspendingDatabaseTest {
        setup()
        makeThread(org = org, repo = repoA).also {
            makeThreadParticipant(thread = it, member = me)
            makeSourceMark(scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
        }
    }

    @Test
    fun `filter excludes archived`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(this)
            createTestThread(this).also {
                it.archivedAt = now
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
            assertThat(it.first().info.thread.archivedAt).isNull()
        }
    }

    @Test
    fun `filter includes only threads where I am a participant`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(this, title = "my thread")
            createTestThread(this, member = notMe)
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
            assertThat(it.first().info.thread.title).isEqualTo("my thread")
        }
    }

    @Test
    fun `filter includes no threads when I am NOT participating in any threads`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(this, member = notMe)
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).isEmpty()
        }
    }

    @Test
    fun `filter includes private threads when I am a participant`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(this, isPrivate = true)
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
            assertThat(it.first().info.thread.archivedAt).isNull()
        }
    }

    @Test
    fun `filter includes threads for specified repos`() = suspendingDatabaseTest {
        setup()
        val repoB = makeRepo(scmTeam = scmTeam)

        suspendedTransaction {
            createTestThread(this, repo = repoA, title = "A")
            createTestThread(this, repo = repoB, title = "B")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).hasSize(2)
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            repoIds = listOf(repoA.id.value),
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
            assertThat(it.first().info.thread.title).isEqualTo("A")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            repoIds = listOf(repoB.id.value),
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isOne
            assertThat(it.first().info.thread.title).isEqualTo("B")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            repoIds = listOf(repoA.id.value, repoB.id.value),
            botMemberIds = emptySet(),
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.map { it.info.thread.title }).containsExactlyInAnyOrder("A", "B")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            repoIds = listOf(RepoId.random()),
            botMemberIds = emptySet(),
            limit = 100,
        ).also {
            assertThat(it).size().isZero
        }
    }

    @Test
    fun `contains info`() = suspendingDatabaseTest {
        setup()
        val thread = makeThread(org = org, repo = repoA).also {
            makeMessage(thread = it)
            makeSourceMark(scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = false)
                makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = false)
            }
            makeMessage(thread = it).also { message ->
                makeThreadUnread(thread = it, member = me, latestMessage = message)
            }
            makeThreadParticipant(thread = it, member = me)
            makeThreadParticipant(thread = it, member = notMe)
        }

        val myThreadInfos = store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        )
        assertThat(myThreadInfos).size().isOne
        myThreadInfos[0].let { result ->

            // unread status
            val unread = result.info.unread
            assertThat(unread).isNotNull
            unread?.let {
                assertThat(it.isUnread).isTrue
                assertThat(it.status).isEqualTo(UnreadStatus.NoMessagesRead)
                assertThat(it.threadId).isEqualTo(thread.idValue)
            }

            // all messages
            assertThat(result.info.messages).hasSize(2)

            // all participants
            assertThat(result.info.participantOrgMemberIds).hasSize(2)

            // repo Id
            assertThat(result.info.repo?.id).isEqualTo(repoA.idValue)

            // all sourcemarks
            assertThat(result.info.sourcemark).isNotNull
            assertThat(result.info.sourcemark?.repoId).isEqualTo(repoA.idValue)

            // only original sourcepoints
            assertThat(result.info.originalSourcePoints).hasSize(2)
            assertThat(result.info.originalSourcePoints.map { it.isOriginal }).containsExactly(true, true)

            // last modified
            assertThat(result.info.lastModified).isEqualTo(max(result.info.thread.modifiedAt, unread?.modifiedAt))
        }
    }

    @Test
    fun `sorts by unread`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(this).also {
                makeMessage(trx = this, thread = it).also { msg ->
                    it.lastMessageCreatedAt = msg.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = msg, latestReadMessage = msg)
                }
            }
            createTestThread(this).also {
                makeMessage(trx = this, thread = it).also { msg ->
                    it.lastMessageCreatedAt = msg.createdAt
                }
            }
            createTestThread(this).also {
                makeMessage(trx = this, thread = it).also { msg ->
                    it.lastMessageCreatedAt = msg.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = msg)
                }
            }
        }

        val myThreadInfos = store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        )

        assertThat(myThreadInfos).hasSize(3)
        assertThat(myThreadInfos.map { it.info.unread?.isUnread }).containsExactly(true, null, null)
    }

    @Test
    fun `sorts by lastMessageCreatedAt`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            createTestThread(trx = this, title = "oldest").also {
                it.lastMessageCreatedAt = now.minus(20.hours)
            }
            createTestThread(trx = this, title = "latest").also {
                it.lastMessageCreatedAt = now
            }
            createTestThread(trx = this, title = "older").also {
                it.lastMessageCreatedAt = now.minus(10.hours)
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(3)
            assertThat(result.map { it.info.thread.title }).isEqualTo(listOf("latest", "older", "oldest"))
        }
    }

    @Test
    @Suppress("LongMethod")
    fun `sorts by unread then lastMessageCreatedAt`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            createTestThread(trx = this, title = "unreadOldest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(35.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
            createTestThread(trx = this, title = "readOldest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(30.hours)
                    it.lastMessageCreatedAt = message.createdAt
                }
            }
            createTestThread(trx = this, title = "unreadLatest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(15.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
            createTestThread(trx = this, title = "readLatest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(10.hours)
                    it.lastMessageCreatedAt = message.createdAt
                }
            }
            createTestThread(trx = this, title = "readOlder").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(20.hours)
                    it.lastMessageCreatedAt = message.createdAt
                }
            }
            createTestThread(trx = this, title = "unreadOlder").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(25.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(6)
            assertThat(result.map { it.info.thread.title }).containsExactly(
                "unreadLatest",
                "unreadOlder",
                "unreadOldest",
                "readLatest",
                "readOlder",
                "readOldest",
            )
        }
    }

    @Suppress("LongMethod")
    @Test
    fun limits() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            createTestThread(trx = this, title = "first").also {
                makeThreadParticipant(trx = this, thread = it) // someone else
                makeThreadParticipant(trx = this, thread = it) // someone else
                makeMessage(trx = this, thread = it).also { message ->
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                    makeThreadUnread(trx = this, thread = it, member = notMe, latestMessage = message)
                }
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
            createTestThread(trx = this, title = "second").also {
                makeThreadParticipant(trx = this, thread = it) // someone else
                makeThreadParticipant(trx = this, thread = it) // someone else
                makeMessage(trx = this, thread = it).also { message ->
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                    makeThreadUnread(trx = this, thread = it, member = notMe, latestMessage = message)
                }
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repoA, thread = it).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
                it.lastMessageCreatedAt = now.minus(1.hours)
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 1,
        ).also {
            assertThat(it).size().isOne
            assertThat(it[0].info.thread.title).isEqualTo("first")
        }
        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 2,
        ).also {
            assertThat(it).hasSize(2)
            assertThat(it[0].info.thread.title).isEqualTo("first")
            assertThat(it[1].info.thread.title).isEqualTo("second")
        }
        store.getThreadBundlesForMe(
            orgId = org.idValue,
            botMemberIds = emptySet(),
            orgMemberId = meOrgMember.idValue,
            limit = 100,
        ).also {
            assertThat(it).hasSize(2)
            assertThat(it[0].info.thread.title).isEqualTo("first")
            assertThat(it[1].info.thread.title).isEqualTo("second")
        }
    }

    @Suppress("LongMethod", "ComplexMethod")
    @Test
    fun `pages by compound cursor (unread and lastMessageCreatedAt)`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            createTestThread(trx = this, title = "unreadLatest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(15.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
            createTestThread(trx = this, title = "unreadOlder").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(25.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
            createTestThread(trx = this, title = "unreadOldest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(35.hours)
                    it.lastMessageCreatedAt = message.createdAt
                    makeThreadUnread(trx = this, thread = it, member = me, latestMessage = message)
                }
            }
            createTestThread(trx = this, title = "readLatest").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(10.hours)
                    it.lastMessageCreatedAt = message.createdAt
                }
            }
            createTestThread(trx = this, title = "readOlder").also {
                makeMessage(trx = this, thread = it).also { message ->
                    message.createdAt = now.minus(20.hours)
                    it.lastMessageCreatedAt = message.createdAt
                }
            }
        }

        val page1 = store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 2,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.map { it.info.thread.title }).containsExactly("unreadLatest", "unreadOlder")
            assertThat(result.last().cursor).isEqualTo(toCursor(ThreadCursor.Mine(true, now.minus(25.hours))))
        }

        val page2 = store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 3,
            after = page1.last().cursor,
        ).also { result ->
            assertThat(result).hasSize(3)
            assertThat(result.map { it.info.thread.title }).containsExactly("unreadOlder", "unreadOldest", "readLatest")
            assertThat(result.last().cursor).isEqualTo(toCursor(ThreadCursor.Mine(false, now.minus(10.hours))))
        }

        val page3 = store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            limit = 2,
            after = page2.last().cursor,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.map { it.info.thread.title }).containsExactly("readLatest", "readOlder")
            assertThat(result.last().cursor).isEqualTo(toCursor(ThreadCursor.Mine(false, now.minus(20.hours))))
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            before = page3.last().cursor,
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(5)
            assertThat(result.map { it.info.thread.title }).containsExactly(
                "unreadLatest",
                "unreadOlder",
                "unreadOldest",
                "readLatest",
                "readOlder",
            )
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            before = page2.last().cursor,
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(4)
            assertThat(result.map { it.info.thread.title }).containsExactly("unreadLatest", "unreadOlder", "unreadOldest", "readLatest")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            before = page1.last().cursor,
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.map { it.info.thread.title }).containsExactly("unreadLatest", "unreadOlder")
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            before = page2.last().cursor,
            limit = 2,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.map { it.info.thread.title }).containsExactly("unreadOldest", "readLatest")
        }
    }

    @Test
    fun `modifiedSince returns all updated threads`() = suspendingDatabaseTest(
        schemaUpdateConfigure = {
            // Disabling this as database triggers will screw around with modifiedAt checks
            createTriggers = false
        },
    ) {
        setup()

        suspendedTransaction {
            createTestThread(trx = this, title = "latest").also {
                it.lastMessageCreatedAt = now
                it.modifiedAt = now.minus(5.hours)
            }
            createTestThread(trx = this, title = "older").also {
                it.lastMessageCreatedAt = now.minus(10.hours)
                it.modifiedAt = now.minus(15.hours)
            }
            createTestThread(trx = this, title = "oldest").also {
                it.lastMessageCreatedAt = now.minus(20.hours)
                it.modifiedAt = now
            }
        }

        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            modifiedSince = now,
            limit = 100,
        ).also { result ->
            assertThat(result).size().isZero
        }
        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            modifiedSince = now.minus(10.hours),
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(2)
            assertThat(result.first().info.thread.title).isEqualTo("latest")
            assertThat(result.first().info.lastModified).isEqualTo(now.minus(5.hours))
            assertThat(result.last().info.thread.title).isEqualTo("oldest")
            assertThat(result.last().info.lastModified).isEqualTo(now)
        }
        store.getThreadBundlesForMe(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            botMemberIds = emptySet(),
            modifiedSince = now.minus(100.hours),
            limit = 100,
        ).also { result ->
            assertThat(result).hasSize(3)
        }
    }

    private suspend fun createTestThread(
        trx: Transaction,
        title: String? = null,
        repo: RepoDAO = repoA,
        member: MemberDAO = me,
        isPrivate: Boolean = false,
    ): ThreadDAO {
        return trx.run {
            makeThread(trx = this, org = org, repo = repo, title = title, isPrivate = isPrivate).also { thread ->
                makeThreadParticipant(trx = this, scmTeam = scmTeam, thread = thread, member = member)
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repo, thread = thread).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
        }
    }

    private fun max(vararg instants: Instant?): Instant {
        return instants.filterNotNull().maxOrNull()
            ?: error("Must specify at least one non-null Instant")
    }
}
