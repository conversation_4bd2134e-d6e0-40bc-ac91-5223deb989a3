package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.StackOverflowTeamsInstallation
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.test.utils.TestArguments
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsSource

class InstallationStoreTest : DatabaseTestsBase() {

    private val store = installationStore
    private lateinit var org: OrgDAO

    private suspend fun setup() {
        org = makeOrg()
    }

    @Test
    fun `getIntegrationInstallations and findAllByProvider`() = suspendingDatabaseTest {
        setup()

        assertThat(store.getIntegrationInstallations(orgId = org.idValue)).isEmpty()
        assertThat(store.findAllByProvider(orgId = org.idValue, provider = Provider.Jira)).isEmpty()

        val installation = makeInstallation(org = org, provider = Provider.Jira).asDataModel()
        val deletedInstallation = makeInstallation(org = org, provider = Provider.Jira, deletedInstallationExternalId = "blah")

        // Sanity checks
        assertThat(suspendedTransaction { InstallationDAO.findById(installation.id) }).isNotNull
        assertThat(suspendedTransaction { InstallationDAO.findById(deletedInstallation.id) }).isNotNull

        assertThat(store.getIntegrationInstallations(orgId = org.idValue).single().id).isEqualTo(installation.id)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, providers = listOf(Provider.Jira)).single().id).isEqualTo(installation.id)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, providers = listOf(Provider.Slack))).isEmpty()
        assertThat(store.getIntegrationInstallations(orgId = null, providers = listOf(Provider.Jira)).single().id).isEqualTo(installation.id)
        assertThat(store.getIntegrationInstallations(orgId = null, providers = listOf(Provider.Slack))).isEmpty()

        assertThat(store.findAllByProvider(orgId = org.idValue, provider = Provider.Jira).single()).isEqualTo(installation.id)
        assertThat(store.findAllByProvider(orgId = org.idValue, provider = Provider.Slack)).isEmpty()
    }

    @Test
    fun `getIntegrationInstallations -- with modified at`() = suspendingDatabaseTest {
        setup()

        val orgId = org.idValue
        assertThat(store.getIntegrationInstallations(orgId = orgId)).isEmpty()

        val installationA = makeInstallation(org = org, provider = Provider.Jira).asDataModel()
        val installationB = makeInstallation(org = org, provider = Provider.Confluence).asDataModel()
        makeInstallation(org = org, provider = Provider.Jira, deletedInstallationExternalId = "blah") // Deleted installation

        assertThat(store.getIntegrationInstallations(orgId = orgId).map { it.id })
            .containsExactlyInAnyOrder(installationA.id, installationB.id)

        val modifiedSince = installationA.modifiedAt.minus(1.milliseconds)

        assertThat(store.getIntegrationInstallations(orgId = orgId, modifiedSince = modifiedSince).map { it.id })
            .containsExactlyInAnyOrder(installationA.id, installationB.id)

        assertThat(store.getIntegrationInstallations(orgId = orgId, providers = listOf(Provider.Jira), modifiedSince = modifiedSince).map { it.id })
            .containsExactlyInAnyOrder(installationA.id)

        assertThat(
            store.getIntegrationInstallations(
                orgId = orgId,
                providers = listOf(Provider.Confluence),
                modifiedSince = modifiedSince,
            ).map { it.id },
        ).containsExactlyInAnyOrder(installationB.id)

        assertThat(store.getIntegrationInstallations(orgId = orgId, providers = listOf(Provider.Notion), modifiedSince = modifiedSince).map { it.id })
            .isEmpty()

        assertThat(store.getIntegrationInstallations(orgId = orgId, modifiedSince = installationA.modifiedAt).map { it.id })
            .containsExactlyInAnyOrder(installationB.id)

        assertThat(store.getIntegrationInstallations(orgId = orgId, modifiedSince = installationB.modifiedAt))
            .isEmpty()
    }

    @Test
    fun `getIntegrationInstallations -- with limit`() = suspendingDatabaseTest {
        setup()
        val installations = mutableListOf<Installation>()

        Provider.entries.forEach { provider ->
            makeInstallation(org = org, provider = provider).asDataModel().also(installations::add)
        }

        assertThat(store.getIntegrationInstallations(orgId = org.idValue)).hasSize(installations.size)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, limit = 1)).hasSize(1)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, limit = 3)).hasSize(3)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, limit = 7)).hasSize(7)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, limit = installations.size - 5)).hasSize(installations.size - 5)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue, limit = installations.size * 2)).hasSize(installations.size)
    }

    @Test
    fun deleteById() = suspendingDatabaseTest {
        setup()

        val installation = makeInstallation(org = org).asDataModel()
        val installationToDelete = makeInstallation(org = org, deletedInstallationExternalId = "blah").asDataModel()

        store.deleteById(id = InstallationId.random()) // Sanity check
        assertNotNull(suspendedTransaction { InstallationDAO.findById(installation.id) })
        assertNotNull(suspendedTransaction { InstallationDAO.findById(installationToDelete.id) })

        assertThrows<IllegalArgumentException> { store.deleteById(id = installation.id) } // Should not delete if not marked for deletion
        assertNotNull(suspendedTransaction { InstallationDAO.findById(installation.id) })
        assertNotNull(suspendedTransaction { InstallationDAO.findById(installationToDelete.id) })

        store.deleteById(id = installationToDelete.id)
        assertNotNull(suspendedTransaction { InstallationDAO.findById(installation.id) })
        assertNull(suspendedTransaction { InstallationDAO.findById(installationToDelete.id) })
    }

    @Test
    fun `insert and updateRawAccessToken`() = suspendingDatabaseTest {
        setup()

        val id = InstallationId.random()

        val installation = suspendedTransaction {
            store.insert(
                trx = this,
                id = id,
                orgId = org.idValue,
                installationExternalId = "davidlam",
                provider = Provider.StackOverflowTeams,
                htmlUrl = Url("http://google.com"),
                rawAccessToken = Ciphertext("blah".toByteArray()),
            ) as StackOverflowTeamsInstallation
        }

        store.findById(installationId = id).let { it as StackOverflowTeamsInstallation }.also {
            assertThat(it.id).isEqualTo(installation.id)
            assertThat(it.orgId).isEqualTo(installation.orgId)
            assertThat(it.provider).isEqualTo(installation.provider)
            assertThat(it.installationExternalId).isEqualTo(installation.installationExternalId)
            assertThat(it.accessToken?.value).isEqualTo("blah".toByteArray())
            assertThat(installation.accessToken?.value).isEqualTo("blah".toByteArray())
        }

        suspendedTransaction {
            store.updateRawAccessToken(
                trx = this,
                id = id,
                orgId = org.idValue,
                rawAccessToken = Ciphertext("bleep".toByteArray()),
            )
        }

        store.findById(installationId = id).let { it as StackOverflowTeamsInstallation }.also {
            assertThat(it.id).isEqualTo(installation.id)
            assertThat(it.orgId).isEqualTo(installation.orgId)
            assertThat(it.provider).isEqualTo(installation.provider)
            assertThat(it.installationExternalId).isEqualTo(installation.installationExternalId)
            assertThat(it.accessToken?.value).isEqualTo("bleep".toByteArray())
        }
    }

    @Test
    fun `check installation for org`() = suspendingDatabaseTest {
        setup()
        val installation = makeInstallation(org = org, provider = Provider.Jira)
        assertThat(store.isInstallationForOrg(orgId = org.idValue, installationId = installation.idValue)).isTrue()
        assertThat(store.isInstallationForOrg(orgId = OrgId.random(), installationId = installation.idValue)).isFalse()
    }

    @Test
    fun `markForDeletion and takeOldestDeletedInstallation`() = suspendingDatabaseTest {
        setup()

        assertThat(store.getIntegrationInstallations(orgId = org.idValue)).isEmpty()

        val installation = makeInstallation(org = org, provider = Provider.Jira).asDataModel()
        assertThat(store.getIntegrationInstallations(orgId = org.idValue).single().id).isEqualTo(installation.id)
        assertThat(store.takeOldestDeletedInstallation()).isNull()

        assertThat(store.markForDeletion(trx = null, orgId = org.idValue, installationId = installation.id)?.id).isEqualTo(installation.id)
        assertThat(store.getIntegrationInstallations(orgId = org.idValue)).isEmpty()
        assertThat(store.takeOldestDeletedInstallation()?.id).isEqualTo(installation.id)
    }

    @Test
    fun setConnectingMember() = suspendingDatabaseTest {
        setup()

        val installation = makeInstallation(org = org).asDataModel()
        assertThat(installation.connectingMember).isNull() // sanity check

        val memberId = MemberId.random()

        store.setConnectingMember(
            orgId = org.idValue,
            installationId = installation.id,
            connectingMemberId = memberId,
        )

        val updated = suspendedTransaction { InstallationDAO[installation.id].asDataModel() }
        assertThat(updated.connectingMember).isEqualTo(memberId)

        // Check that we don't overwrite the connecting member when not null
        val anotherMemberId = MemberId.random()
        store.setConnectingMember(
            orgId = org.idValue,
            installationId = installation.id,
            connectingMemberId = anotherMemberId,
        )
        val updatedAgain = suspendedTransaction { InstallationDAO[installation.id].asDataModel() }
        assertThat(updatedAgain.connectingMember).isEqualTo(memberId)
    }

    data class FindByScmTeamIdInput(
        val scmTeamId: ScmTeamId,
        val expected: InstallationId?,
    )

    class FindByScmTeamIdCases : TestArguments<Arguments>(
        Arguments.of(
            "no team found",
            suspend {
                FindByScmTeamIdInput(
                    scmTeamId = ScmTeamId.random(),
                    expected = null,
                )
            },
        ),
        Arguments.of(
            "team -- happy path",
            suspend {
                val scmTeam = makeScmTeam()

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = scmTeam.installation.idValue,
                )
            },
        ),
        Arguments.of(
            "team -- deleted team",
            suspend {
                val scmTeam = makeScmTeam()
                scmTeamStore.deleteTeam(teamId = scmTeam.idValue)

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = scmTeam.installation.idValue,
                )
            },
        ),
        Arguments.of(
            "team -- deleted installation",
            suspend {
                val scmTeam = makeScmTeam()
                installationStore.markForDeletion(orgId = scmTeam.orgId, installationId = scmTeam.installation.idValue)
                installationStore.deleteById(id = scmTeam.installation.idValue)

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = null,
                )
            },
        ),
        Arguments.of(
            "multiple teams, same installation (1)",
            suspend {
                val scmTeam = makeInstallation().let {
                    makeScmTeam(installation = it).also { _ ->
                        makeScmTeam(installation = it)
                        makeScmTeam(installation = it)
                    }
                }

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = scmTeam.installation.idValue,
                )
            },
        ),
        Arguments.of(
            "multiple teams, same installation (2)",
            suspend {
                val scmTeam = makeInstallation().let {
                    makeScmTeam(installation = it)
                    makeScmTeam(installation = it).also { _ ->
                        makeScmTeam(installation = it)
                    }
                }

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = scmTeam.installation.idValue,
                )
            },
        ),
        Arguments.of(
            "multiple teams, same installation (3)",
            suspend {
                val scmTeam = makeInstallation().let {
                    makeScmTeam(installation = it)
                    makeScmTeam(installation = it)
                    makeScmTeam(installation = it)
                }

                FindByScmTeamIdInput(
                    scmTeamId = scmTeam.idValue,
                    expected = scmTeam.installation.idValue,
                )
            },
        ),
    )

    @ParameterizedTest(name = "[{index}] = {0}")
    @ArgumentsSource(FindByScmTeamIdCases::class)
    fun findByScmTeamId(
        @Suppress("UNUSED_PARAMETER")
        name: String,
        make: suspend () -> FindByScmTeamIdInput,
    ) = suspendingDatabaseTest {
        val input = make()
        val result = store.findByScmTeamId(
            scmTeamId = input.scmTeamId,
        )
        assertThat(result?.id)
            .isEqualTo(input.expected)
    }

    @Test
    fun `test upsert -- insert new installation`() = suspendingDatabaseTest {
        setup()

        val installationExternalId = "external-id-1"
        val installation = store.upsert(
            orgId = org.idValue,
            installationExternalId = installationExternalId,
            provider = Provider.GitHub,
            displayName = "GitHub Installation",
            htmlUrl = Url("https://github.com/org"),
            avatarUrl = Url("https://avatars.githubusercontent.com/u/12345"),
        )

        assertThat(installation.orgId).isEqualTo(org.id.value)
        assertThat(installation.installationExternalId).isEqualTo(installationExternalId)
        assertThat(installation.provider).isEqualTo(Provider.GitHub)
        assertThat(installation.displayName).isEqualTo("GitHub Installation")
        assertThat(installation.htmlUrl?.asString).isEqualTo("https://github.com/org")
        assertThat(installation.avatarUrl?.asString).isEqualTo("https://avatars.githubusercontent.com/u/12345")

        val fetchedInstallation = suspendedTransaction {
            InstallationDAO.findById(installation.id)?.asDataModel()
        }
        assertThat(fetchedInstallation).isNotNull
        assertThat(fetchedInstallation?.installationExternalId).isEqualTo(installationExternalId)
    }

    @Test
    fun `test upsert -- update existing installation`() = suspendingDatabaseTest {
        setup()

        val installationExternalId = "external-id-2"

        // First upsert (insertion)
        val initialInstallation = store.upsert(
            orgId = org.idValue,
            installationExternalId = installationExternalId,
            provider = Provider.GitHub,
            displayName = "Initial GitHub Installation",
            htmlUrl = Url("https://github.com/org"),
            avatarUrl = Url("https://avatars.githubusercontent.com/u/12345"),
        )

        assertThat(initialInstallation.displayName).isEqualTo("Initial GitHub Installation")

        // Second upsert (update)
        val updatedInstallation = store.upsert(
            orgId = org.idValue,
            installationExternalId = installationExternalId,
            provider = Provider.GitHub,
            displayName = "Updated GitHub Installation",
            htmlUrl = Url("https://github.com/org-new"),
            avatarUrl = Url("https://avatars.githubusercontent.com/u/54321"),
        )

        assertThat(updatedInstallation.displayName).isEqualTo("Updated GitHub Installation")
        assertThat(updatedInstallation.htmlUrl?.asString).isEqualTo("https://github.com/org-new")
        assertThat(updatedInstallation.avatarUrl?.asString).isEqualTo("https://avatars.githubusercontent.com/u/54321")

        // Verify that the changes were persisted
        val fetchedInstallation = suspendedTransaction {
            InstallationDAO.findById(updatedInstallation.id)?.asDataModel()
        }
        assertThat(fetchedInstallation).isNotNull
        assertThat(fetchedInstallation?.displayName).isEqualTo("Updated GitHub Installation")
        assertThat(fetchedInstallation?.htmlUrl?.asString).isEqualTo("https://github.com/org-new")
        assertThat(fetchedInstallation?.avatarUrl?.asString).isEqualTo("https://avatars.githubusercontent.com/u/54321")
    }

    @Nested
    inner class TouchModifiedAtTest {

        @Test
        fun `touchModifiedAt -- when no installation`() = suspendingDatabaseTest {
            assertThat(
                store.touchModifiedAt(installationId = InstallationId.random()),
            ).isFalse()
        }

        @Test
        fun `touchModifiedAt -- happy path`() = suspendingDatabaseTest {
            val lastModified = Instant.nowWithMicrosecondPrecision()
            val installation = makeInstallation(
                modifiedAt = lastModified,
            )

            assertThat(
                store.touchModifiedAt(installationId = installation.idValue),
            ).isTrue()

            installation.refresh()

            assertThat(
                installation.modifiedAt,
            ).isGreaterThan(lastModified)
        }
    }
}
