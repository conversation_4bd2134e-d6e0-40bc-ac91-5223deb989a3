package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeJiraProject
import com.nextchaptersoftware.db.ModelBuilders.makeJiraSite
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JiraProjectAccessStoreTest : DatabaseTestsBase() {
    private val store = Stores.jiraProjectAccessStore

    @Test
    fun `list, upsert, and delete`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val site = makeJiraSite(installation = installation)

        val projectA = makeJiraProject(site = site)
        val projectB = makeJiraProject(site = site)

        val identityA = makeIdentity()
        val identityB = makeIdentity()

        assertThat(store.list(projectId = projectA.idValue)).isEmpty()
        assertThat(store.list(projectId = projectB.idValue)).isEmpty()
        assertThat(store.list(identityId = identityA.idValue)).isEmpty()
        assertThat(store.list(identityId = identityB.idValue)).isEmpty()

        store.upsert(projectId = projectA.idValue, identityId = identityA.idValue)
        store.upsert(projectId = projectB.idValue, identityId = identityB.idValue)

        assertThat(store.list(projectId = projectA.idValue).map { it.identityId }).containsExactly(identityA.idValue)
        assertThat(store.list(projectId = projectB.idValue).map { it.identityId }).containsExactly(identityB.idValue)
        assertThat(store.list(identityId = identityA.idValue).map { it.projectId }).containsExactly(projectA.idValue)
        assertThat(store.list(identityId = identityB.idValue).map { it.projectId }).containsExactly(projectB.idValue)

        store.delete(projectId = projectA.idValue, identityId = identityA.idValue)

        assertThat(store.list(projectId = projectA.idValue)).isEmpty()
        assertThat(store.list(projectId = projectB.idValue).map { it.identityId }).containsExactly(identityB.idValue)
        assertThat(store.list(identityId = identityA.idValue)).isEmpty()
        assertThat(store.list(identityId = identityB.idValue).map { it.projectId }).containsExactly(projectB.idValue)

        store.delete(projectId = projectB.idValue, identityId = identityA.idValue) // should be a no-op

        assertThat(store.list(projectId = projectA.idValue)).isEmpty()
        assertThat(store.list(projectId = projectB.idValue).map { it.identityId }).containsExactly(identityB.idValue)
        assertThat(store.list(identityId = identityA.idValue)).isEmpty()
        assertThat(store.list(identityId = identityB.idValue).map { it.projectId }).containsExactly(projectB.idValue)
    }
}
