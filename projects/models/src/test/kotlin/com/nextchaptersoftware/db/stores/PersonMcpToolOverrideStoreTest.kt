package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.interceptors.explain.SlowQueryConfig
import com.nextchaptersoftware.db.models.McpSearchMode
import com.nextchaptersoftware.db.models.McpToolParameter
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.mcp.McpToolDefinition
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PersonMcpToolOverrideStoreTest : DatabaseTestsBase() {
    private val store = Stores.personMcpToolOverrideStore
    private val mcpToolStore = Stores.mcpToolStore

    @Test
    fun `upsert creates new override when none exists`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create person override
        val override = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "Person Tool",
            description = "Person-specific description",
            parameters = listOf(McpToolParameter("query", "Person query description")),
            enabled = false,
        )

        assertThat(override.personId).isEqualTo(person.idValue)
        assertThat(override.mcpToolId).isEqualTo(globalTool.id)
        assertThat(override.displayName).isEqualTo("Person Tool")
        assertThat(override.description).isEqualTo("Person-specific description")
        assertThat(override.enabled).isEqualTo(false)
        assertThat(override.parameters).hasSize(1)
        assertThat(override.parameters?.first()?.name).isEqualTo("query")
        assertThat(override.parameters?.first()?.description).isEqualTo("Person query description")
    }

    @Test
    fun `upsert updates existing override`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create initial override
        val firstOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "First Tool",
            description = "First description",
            enabled = false,
        )

        // Update the override
        val secondOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "Updated Tool",
            description = "Updated description",
            enabled = true,
        )

        // Should be the same record (upsert behavior)
        assertThat(secondOverride.id).isEqualTo(firstOverride.id)
        assertThat(secondOverride.displayName).isEqualTo("Updated Tool")
        assertThat(secondOverride.description).isEqualTo("Updated description")
        assertThat(secondOverride.enabled).isEqualTo(true)
    }

    @Test
    fun `upsert with null values overwrites existing fields`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create initial override with all fields
        val firstOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "First Tool",
            description = "First description",
            parameters = listOf(McpToolParameter("query", "First query description")),
            enabled = false,
        )

        // Update only the enabled field (other fields will be set to null)
        val secondOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            enabled = true,
        )

        // Should be the same record but with null values for unspecified fields
        assertThat(secondOverride.id).isEqualTo(firstOverride.id)
        assertThat(secondOverride.displayName).isNull()
        assertThat(secondOverride.description).isNull()
        assertThat(secondOverride.enabled).isEqualTo(true)
        assertThat(secondOverride.parameters).isNull()
    }

    @Test
    fun `find returns override when it exists`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create override
        val createdOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "Person Tool",
            description = "Person-specific description",
            enabled = false,
        )

        // Find the override
        val foundOverride = store.find(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )

        assertThat(foundOverride).isNotNull
        assertThat(foundOverride?.id).isEqualTo(createdOverride.id)
        assertThat(foundOverride?.displayName).isEqualTo("Person Tool")
        assertThat(foundOverride?.description).isEqualTo("Person-specific description")
        assertThat(foundOverride?.enabled).isEqualTo(false)
    }

    @Test
    fun `find returns null when override does not exist`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Try to find non-existent override
        val foundOverride = store.find(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )

        assertThat(foundOverride).isNull()
    }

    @Test
    fun `findByPersonAndToolName returns override when it exists`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create override
        val createdOverride = store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "Person Tool",
            description = "Person-specific description",
            enabled = false,
        )

        // Find by person and tool name
        val foundOverride = store.findByPersonAndToolName(
            personId = person.idValue,
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
        )

        assertThat(foundOverride).isNotNull
        assertThat(foundOverride?.id).isEqualTo(createdOverride.id)
        assertThat(foundOverride?.displayName).isEqualTo("Person Tool")
        assertThat(foundOverride?.description).isEqualTo("Person-specific description")
        assertThat(foundOverride?.enabled).isEqualTo(false)
    }

    @Test
    fun `findByPersonAndToolName returns null when override does not exist`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Try to find non-existent override
        val foundOverride = store.findByPersonAndToolName(
            personId = person.idValue,
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
        )

        assertThat(foundOverride).isNull()
    }

    @Test
    fun `findAllForPerson returns all overrides for a person`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person1 = makePerson()
        val person2 = makePerson()

        // Create global tools
        val tool1 = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Tool 1",
            description = "Tool 1 description",
            parameters = listOf(McpToolParameter("query", "Query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        val tool2 = mcpToolStore.upsert(
            mcpToolName = "tool2",
            displayName = "Tool 2",
            description = "Tool 2 description",
            parameters = listOf(McpToolParameter("param", "Param description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create overrides for person1
        val override1 = store.upsert(
            personId = person1.idValue,
            mcpToolId = tool1.id,
            displayName = "Person1 Tool1",
            enabled = false,
        )

        val override2 = store.upsert(
            personId = person1.idValue,
            mcpToolId = tool2.id,
            displayName = "Person1 Tool2",
            enabled = true,
        )

        // Create override for person2 (should not be returned)
        store.upsert(
            personId = person2.idValue,
            mcpToolId = tool1.id,
            displayName = "Person2 Tool1",
            enabled = true,
        )

        // Find all overrides for person1
        val person1Overrides = store.findAllForPerson(personId = person1.idValue)

        assertThat(person1Overrides).hasSize(2)
        assertThat(person1Overrides.map { it.id }).containsExactlyInAnyOrder(override1.id, override2.id)
        assertThat(person1Overrides.map { it.displayName }).containsExactlyInAnyOrder("Person1 Tool1", "Person1 Tool2")
    }

    @Test
    fun `findAllForPerson returns empty list when no overrides exist`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        val overrides = store.findAllForPerson(personId = person.idValue)

        assertThat(overrides).isEmpty()
    }

    @Test
    fun `delete removes override when it exists`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Create override
        store.upsert(
            personId = person.idValue,
            mcpToolId = globalTool.id,
            displayName = "Person Tool",
            description = "Person-specific description",
            enabled = false,
        )

        // Verify override exists
        val beforeDelete = store.find(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )
        assertThat(beforeDelete).isNotNull

        // Delete the override
        store.delete(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )

        // Verify override is deleted
        val afterDelete = store.find(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )
        assertThat(afterDelete).isNull()
    }

    @Test
    fun `delete does nothing when override does not exist`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person = makePerson()

        // Create a global tool first
        val globalTool = mcpToolStore.upsert(
            mcpToolName = McpToolDefinition.AskUnblockedWhy.NAME,
            displayName = "Global Tool",
            description = "Global description",
            parameters = listOf(McpToolParameter("query", "Global query description")),
            searchMode = McpSearchMode.DEFAULT,
            enabled = true,
        )

        // Try to delete non-existent override (should not throw)
        store.delete(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )

        // Verify still no override exists
        val afterDelete = store.find(
            personId = person.idValue,
            mcpToolId = globalTool.id,
        )
        assertThat(afterDelete).isNull()
    }
}
