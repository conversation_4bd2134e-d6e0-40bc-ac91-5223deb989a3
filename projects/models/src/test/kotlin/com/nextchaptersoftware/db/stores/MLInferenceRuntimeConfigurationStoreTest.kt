package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMLInference
import com.nextchaptersoftware.db.ModelBuilders.makeMLInferenceTemplate
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.models.MLInferenceDAO
import com.nextchaptersoftware.db.models.MLInferenceRuntimeConfigurationType
import com.nextchaptersoftware.db.models.MLInferenceTemplateDAO
import com.nextchaptersoftware.db.models.MLInferenceType
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.test.utils.TestUtils.assertNotNull
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class MLInferenceRuntimeConfigurationStoreTest : DatabaseTestsBase() {
    private val store = Stores.mlInferenceRuntimeConfigurationStore

    private lateinit var org: OrgDAO
    private lateinit var template: MLInferenceTemplateDAO
    private lateinit var goldenRecord1: MLInferenceDAO
    private lateinit var goldenRecord2: MLInferenceDAO
    private lateinit var validationRecord1: MLInferenceDAO
    private lateinit var validationRecord2: MLInferenceDAO

    private suspend fun setup() {
        org = makeOrg()
        template = makeMLInferenceTemplate()
        goldenRecord1 = makeMLInference(
            org = org,
            type = MLInferenceType.Golden,
        )
        goldenRecord2 = makeMLInference(
            org = org,
            type = MLInferenceType.Golden,
        )
        validationRecord1 = makeMLInference(
            org = org,
            type = MLInferenceType.Validation,
        )
        validationRecord2 = makeMLInference(
            org = org,
            type = MLInferenceType.Validation,
        )
    }

    @Test
    fun testCreateRuntimeConfiguration() = suspendingDatabaseTest {
        setup()
        val runtimeConfiguration = assertNotNull(
            store.createRuntimeConfiguration(
                orgId = org.idValue,
                templateId = template.id.value,
                type = MLInferenceRuntimeConfigurationType.TestRun,
                goldenRecord = listOf(
                    goldenRecord1.id.value.value,
                    goldenRecord2.id.value.value,
                ),
                validationRecord = listOf(
                    validationRecord1.id.value.value,
                    validationRecord2.id.value.value,
                ),
            ),
        )
        assertThat(runtimeConfiguration.orgId).isEqualTo(org.idValue)
        assertThat(runtimeConfiguration.templateId).isEqualTo(template.id.value)
        assertThat(runtimeConfiguration.goldenRecord).containsExactlyInAnyOrder(
            goldenRecord1.id.value.value,
            goldenRecord2.id.value.value,
        )
        assertThat(runtimeConfiguration.validationRecord).containsExactlyInAnyOrder(
            validationRecord1.id.value.value,
            validationRecord2.id.value.value,
        )
    }
}
