package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.interceptors.explain.SlowQueryConfig
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlin.time.Duration.Companion.hours
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

internal class ThreadModelTest : DatabaseTestsBase() {
    private val now = Instant.nowWithMicrosecondPrecision()

    @Test
    fun deletingOrgCascades() = suspendingDatabaseTest {
        val thread = makeThread()
        val orgId = suspendedTransaction {
            thread.org.id
        }

        // delete org...
        suspendedTransaction {
            OrgModel.deleteWhere { OrgModel.id eq orgId }
        }

        // ... expect thread to be deleted
        suspendedTransaction {
            assertThat(OrgDAO.findById(orgId.value)).isNull()
            assertThat(ThreadDAO.find { ThreadModel.org eq orgId }.count()).isZero
        }
    }

    @Disabled("Flaky test")
    @Test
    fun `can find threads modified since`() = suspendingDatabaseTest {
        val thread1 = makeThread(modifiedAt = now.minus(10.hours))
        val thread2 = makeThread(modifiedAt = now.minus(10.hours))
        val thread1ModifiedAt = thread1.modifiedAt

        val newTitle = UUID.randomUUID().toString()
        suspendedTransaction {
            thread1.title = newTitle
            thread2.title = newTitle
        }

        val threads = suspendedTransaction {
            ThreadDAO.findModifiedSince(thread1ModifiedAt) {
                ThreadModel.title eq newTitle
            }
        }
        suspendedTransaction {
            assertThat(threads.count()).isEqualTo(2)
        }
    }

    @Test
    fun `has modified threads since`() = suspendingDatabaseTest {
        val org = makeOrg()
        val thread1 = makeThread(org = org, modifiedAt = now.minus(10.hours))
        val thread2 = makeThread(org = org, modifiedAt = now.minus(10.hours))

        val newTitle = "newTitle"
        suspendedTransaction {
            thread1.title = newTitle
            thread2.title = newTitle
        }

        val modifiedSince = suspendedTransaction {
            ThreadDAO.modifiedSince(now.minus(20.hours)) {
                (ThreadModel.title eq newTitle) and (ThreadModel.org eq org.id)
            }
        }
        assertThat(modifiedSince).isTrue
    }

    @Test
    fun `has no modified threads from now`() = suspendingDatabaseTest(
        {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val thread1 = makeThread(modifiedAt = now.minus(10.hours))
        val thread2 = makeThread(modifiedAt = now.minus(10.hours))

        val newTitle = UUID.randomUUID().toString()
        suspendedTransaction {
            thread1.title = newTitle
            thread2.title = newTitle
        }

        val modifiedSince = suspendedTransaction {
            ThreadDAO.modifiedSince(now.plus(1.hours)) {
                ThreadModel.title eq newTitle
            }
        }
        assertThat(modifiedSince).isFalse
    }

    @Test
    fun `construct thread url for pr`() {
        val standardUrlBuilderProvider = StandardUrlBuilderProvider()

        val orgId = OrgId.random()
        val threadId = ThreadId.random()
        val prId = PullRequestId.random()

        val thread = MockDataClasses.thread(
            id = threadId,
            orgId = orgId,
            pullRequestId = prId,
        )
        thread.dashboardUrl(urlBuilderProvider = standardUrlBuilderProvider).asString.also {
            assertThat(it).isEqualTo(
                "http://localhost:9000/team/$orgId/pullRequest/$prId?thread=$threadId",
            )
        }
    }
}
