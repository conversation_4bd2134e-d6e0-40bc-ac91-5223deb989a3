package com.nextchaptersoftware.ml.api.models.graphrag

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GraphRagParameters(
    @SerialName("max_tokens") val maxTokens: Int? = null,
    @SerialName("top_k") val topK: Int? = null,
    @SerialName("vector_index_name") val vectorIndexName: String? = null,
    @SerialName("fulltext_index_name") val fulltextIndexName: String? = null,
)

@Serializable
data class GraphRagRequest(
    @SerialName("query") val query: String,
    @SerialName("parameters") val parameters: GraphRagParameters? = null,
)
