package com.nextchaptersoftware.atlassian.api

import com.nextchaptersoftware.atlassian.api.AtlassianHttpClient.setAuthorizationHeader
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.HttpClientPagination.batchStream
import com.nextchaptersoftware.ktor.utils.UrlExtensions.parameterMap
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.http.HttpHeaders
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow

object AtlassianSiteApi {
    suspend inline fun <reified T> get(
        url: Url,
        sharedSecret: Secret,
        atlassianConnectAppKey: String,
        client: HttpClient,
    ): T {
        val parameterMap = url.parameterMap()

        val jwtToken = AtlassianJwtBuilder.createJwt(
            method = "GET",
            contextPath = "/",
            apiPath = url.encodedPath,
            parameterMap = parameterMap,
            atlassianConnectAppKey = atlassianConnectAppKey,
            sharedSecret = sharedSecret,
        )

        return client.get(url) {
            headers[HttpHeaders.Authorization] = "JWT $jwtToken" // Not a typo
        }.body()
    }

    suspend inline fun <reified T> get(
        url: Url,
        tokens: OAuthTokens,
        client: HttpClient,
    ): T {
        return client.get(url) {
            setAuthorizationHeader(accessToken = tokens.accessToken)
        }.body()
    }

    inline fun <reified T> batchStream(
        url: Url,
        client: HttpClient,
        noinline dataProvider: suspend (HttpResponse) -> List<T>,
        noinline nextUriProvider: suspend (HttpResponse) -> Url?,
        crossinline block: HttpRequestBuilder.() -> Unit,
    ): Flow<HttpClientBatch<T>> {
        return client.batchStream<T>(
            initialUri = url.toString(),
            dataProvider = dataProvider,
            nextUriProvider = nextUriProvider,
            block = block,
        )
    }
}
