package com.nextchaptersoftware.intercom.api

import com.nextchaptersoftware.intercom.models.DataAttributeModel.Contact
import com.nextchaptersoftware.intercom.models.DataAttributeType.Integer
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class DataAttributeApiTest {

    @Disabled
    @Test
    fun `test data attribute creation`() = runTest {
        val dataAttributeApi = DataAttributeApi()
        dataAttributeApi.upsertDataAttribute("testAttribute", Contact, Integer)
    }
}
