package com.nextchaptersoftware.notion.api

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgAndMemberAndIdentity
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.user.secret.UserSecretServiceInterface
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.and

class NotionOauthTokenProvider(
    private val memberStore: MemberStore = Stores.memberStore,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val userSecretService: UserSecretServiceInterface,
) {
    suspend fun getConnectedIdentities(installationId: InstallationId): List<OrgAndMemberAndIdentity> {
        return memberStore.findByInstallationId(
            installationId = installationId,
            additionalWhereClause = IdentityModel.rawAccessToken.isNotNull(),
        )
    }

    @Suppress("ThrowsCount")
    suspend fun getDefaultConnectedIdentity(installationId: InstallationId): Identity {
        val installation = installationStore.findById(installationId = installationId)
            ?: throw IllegalStateException("No installation found for installationId=$installationId")

        val memberId = installation.connectingMember
            ?: throw IllegalStateException("No connecting member found for installationId=$installationId")

        return getConnectedIdentities(installationId = installation.id).firstOrNull { it.member.id == memberId }?.identity
            ?: throw IllegalStateException("No identity with access token found for installationId=$installationId")
    }

    suspend fun getOAuthTokens(installationId: InstallationId, identityId: IdentityId): OAuthTokens {
        val identity = memberStore.findByInstallationId(
            installationId = installationId,
            additionalWhereClause = IdentityModel.id eq identityId and IdentityModel.rawAccessToken.isNotNull(),
        ).firstOrNull()?.identity
            ?: throw IllegalStateException("No identity with access token found for installationId=$installationId and identityId=$identityId")

        return userSecretService.getDecryptedIdentityTokens(null, identity.id)
            ?: throw IllegalStateException("Unable to get OAuth tokens for identityId=${identity.id}")
    }
}
