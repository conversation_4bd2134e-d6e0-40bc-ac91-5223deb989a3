package com.nextchaptersoftware.asana.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

@OptIn(kotlinx.serialization.ExperimentalSerializationApi::class)
@JsonClassDiscriminator("resource_type")
@Serializable
sealed class Resource {
    abstract val gid: String
    abstract val resourceType: String
}

@Serializable
sealed class NamedResource : Resource() {
    abstract val name: String?
}

@Serializable
data class Reference(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
    override val name: String? = null,
) : NamedResource()
