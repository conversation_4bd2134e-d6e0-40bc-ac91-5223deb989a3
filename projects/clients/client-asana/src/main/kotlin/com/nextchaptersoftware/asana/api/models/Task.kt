package com.nextchaptersoftware.asana.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("task")
data class Task(
    override val gid: String,
    override val name: String? = null,
    @SerialName("resource_type")
    override val resourceType: String = "task",
    @SerialName("resource_subtype")
    val resourceSubtype: String? = null,
    val completed: Boolean? = null,
    @SerialName("completed_at")
    val completedAt: String? = null,
    @SerialName("completed_by")
    val completedBy: User? = null,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("due_at")
    val dueAt: String? = null,
    @SerialName("due_on")
    val dueOn: String? = null,
    @SerialName("start_at")
    val startAt: String? = null,
    @SerialName("start_on")
    val startOn: String? = null,
    @SerialName("html_notes")
    val htmlNotes: String? = null,
    @SerialName("modified_at")
    val modifiedAt: String? = null,
    val notes: String? = null,
    val assignee: User? = null,
    // Deprecated
    @SerialName("assignee_status")
    val assigneeStatus: String? = null,
    val projects: List<Reference>? = null,
    @SerialName("permalink_url")
    val permalinkURL: String? = null,
    val workspace: Reference? = null,
    val parent: Reference? = null,
    val followers: List<User>? = null,
    val memberships: List<TaskMembership>? = null,
    @SerialName("custom_fields")
    val customFields: List<CustomField>? = null,

    // Unblocked additions
    // Add stories pertaining to this task for extraction and embedding
    val stories: List<Story>? = null,
) : NamedResource()

@Serializable
data class TaskMembership(
    val project: Reference? = null,
    val section: Reference? = null,
)
