package com.nextchaptersoftware.asana.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NextPage(
    val offset: String? = null,
    val path: String? = null,
    val uri: String? = null,
)

@Serializable
data class Results<T>(
    val data: List<T>,
    @SerialName("next_page")
    val nextPage: NextPage? = null,
)

@Serializable
data class Single<T>(
    val data: T,
)
