package com.nextchaptersoftware.asana.api.models

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class HumanReadableAsanaStoryTest {

    @Test
    fun `fromStory should create HumanReadableAsanaStory from Story`() {
        // Create a test Story
        val story = Story(
            gid = "123456",
            createdAt = "2025-05-12T10:30:00.000Z",
            createdBy = User(
                gid = "789012",
                name = "<PERSON>",
                resourceType = "user",
            ),
            text = "This is a test comment",
            type = "comment",
            resourceType = "story",
        )

        // Convert to HumanReadableAsanaStory
        val humanReadableStory = HumanReadableAsanaStory.fromStory(story)

        // Verify the conversion
        assertThat(humanReadableStory).isNotNull
        assertThat(humanReadableStory?.author).isEqualTo("Alice Smith")
        assertThat(humanReadableStory?.date).isEqualTo("2025-05-12T10:30:00.000Z")
        assertThat(humanReadableStory?.body).isEqualTo("This is a test comment")
    }

    @Test
    fun `fromStory should handle null createdBy`() {
        // Create a story with null createdBy
        val story = Story(
            gid = "123456",
            createdAt = "2025-05-12T10:30:00.000Z",
            createdBy = null,
            text = "System generated comment",
            type = "system",
            resourceType = "story",
        )

        // Convert to HumanReadableAsanaStory
        val humanReadableStory = HumanReadableAsanaStory.fromStory(story)

        // Verify the conversion
        assertThat(humanReadableStory).isNotNull
        assertThat(humanReadableStory?.author).isEqualTo("null") // toString() of null
        assertThat(humanReadableStory?.date).isEqualTo("2025-05-12T10:30:00.000Z")
        assertThat(humanReadableStory?.body).isEqualTo("System generated comment")
    }

    @Test
    fun `asMarkdown should format story properly`() {
        // Create a test HumanReadableAsanaStory
        val story = HumanReadableAsanaStory(
            author = "Bob Johnson",
            date = "2025-05-12T15:45:00.000Z",
            body = "This is a multiline comment\nwith several\nlines of text.",
        )

        // Generate markdown
        val markdown = story.asMarkdown()

        // Verify markdown format
        val expectedMarkdown = """
            |### Bob Johnson - 2025-05-12T15:45:00.000Z
            |
            |This is a multiline comment
            |with several
            |lines of text.
            |
        """.trimMargin()
        assertThat(markdown).isEqualTo(expectedMarkdown)
    }
}
