package com.nextchaptersoftware.jira.api

import com.nextchaptersoftware.atlassian.api.AtlassianDataCenterAuthProvider
import com.nextchaptersoftware.atlassian.api.AtlassianHttpClient.setAuthorizationHeader
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.jira.models.DataCenterPermissionScheme
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.path
import kotlinx.serialization.json.JsonObject

class JiraDataCenterProjectsApi(
    private val client: HttpClient,
    private val dataCenterAuthProvider: AtlassianDataCenterAuthProvider,
) {
    companion object {
        // https://developer.atlassian.com/server/jira/platform/rest/v10006/api-group-role/#api-api-2-role-get
        private fun urlRoles(
            baseUrl: Url,
            projectKey: String,
        ): Url {
            return URLBuilder(baseUrl.asString.trim('/')).also { builder ->
                builder.path("/rest/api/2/project/$projectKey/role")
            }.build()
        }

        // https://developer.atlassian.com/server/jira/platform/rest/v10006/api-group-project/#api-api-2-project-projectkeyorid-permissionscheme-get
        private fun urlPermissionScheme(
            baseUrl: Url,
            projectKey: String,
        ): Url {
            return URLBuilder(baseUrl.asString.trim('/')).also { builder ->
                builder.path("/rest/api/2/project/$projectKey/permissionscheme")
            }.build()
        }
    }

    suspend fun getRoles(
        baseUrl: Url,
        projectKey: String,
        accessToken: Ciphertext,
    ): Map<String, Url> = get(
        url = urlRoles(baseUrl = baseUrl, projectKey = projectKey),
        accessToken = accessToken,
    )

    suspend fun getRole(
        url: Url,
        accessToken: Ciphertext,
    ): JsonObject = get(
        url = url,
        accessToken = accessToken,
    )

    suspend fun getPermissionScheme(
        baseUrl: Url,
        projectKey: String,
        accessToken: Ciphertext,
    ): DataCenterPermissionScheme = get(
        url = urlPermissionScheme(baseUrl = baseUrl, projectKey = projectKey),
        accessToken = accessToken,
    )

    private suspend inline fun <reified T> get(
        url: Url,
        accessToken: Ciphertext,
    ): T {
        return client.get(url) {
            setAuthorizationHeader(dataCenterAuthProvider.getAccessToken(accessToken))
        }.body()
    }
}
