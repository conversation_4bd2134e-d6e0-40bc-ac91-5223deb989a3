package com.nextchaptersoftware.jira.api

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.jira.api.utils.JiraTestUtils
import com.nextchaptersoftware.jira.models.User
import com.nextchaptersoftware.test.utils.SkipInCI
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

@SkipInCI(extraCondition = JiraTestUtils.OnlyRunIfJiraTokenExists::class)
class JiraUsersApiTest {
    private val tokens = OAuthTokens(accessToken = JiraTestUtils.JIRA_CLOUD_API_TOKEN)
    private val api = JiraApiProvider().jiraUsersApi

    @Test
    fun getUsers() = runTest {
        val users = mutableSetOf<User>()

        var hasMoreIssues = true
        var startAt = 0

        while (hasMoreIssues) {
            val url = JiraUsersApi.urlUsers(
                baseUrl = JiraTestUtils.JIRA_CLOUD_BASE_URL,
                maxResults = 10,
                startAt = startAt,
            )

            val results = api.getUsers(url = url, tokens = tokens)

            users.addAll(results)
            startAt += results.size
            hasMoreIssues = results.isNotEmpty()
        }

        assertThat(users).hasSize(34)
        assertThat(users.filter { !it.isBot }).hasSize(10)
    }

    @Test
    fun urlUsersWithBrowsePermission() = runTest {
        val users = mutableSetOf<User>()

        var hasMoreIssues = true
        var startAt = 0

        while (hasMoreIssues) {
            val url = JiraUsersApi.urlUsersWithBrowsePermission(
                baseUrl = JiraTestUtils.JIRA_CLOUD_BASE_URL,
                projectKey = "PP2",
                maxResults = 10,
                startAt = startAt,
            )

            val results = api.getUsersWithBrowsePermission(url = url, tokens = tokens)

            users.addAll(results)
            startAt += results.size
            hasMoreIssues = results.isNotEmpty()
        }

        assertThat(users).hasSize(17)
        assertThat(users.filter { !it.isBot }).hasSize(2)
    }
}
