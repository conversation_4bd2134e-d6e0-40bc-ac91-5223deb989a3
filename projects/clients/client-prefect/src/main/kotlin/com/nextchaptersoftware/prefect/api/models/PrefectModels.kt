package com.nextchaptersoftware.prefect.api.models

import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class Flow(
    @Contextual @SerialName("id") val id: UUID,
    @SerialName("name") val name: String,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @Contextual @SerialName("created") val created: OffsetDateTime,
    @Contextual @SerialName("updated") val updated: OffsetDateTime,
)

@Serializable
data class FlowCreateRequest(
    @SerialName("name") val name: String,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
)

@Serializable
data class FlowUpdateRequest(
    @SerialName("tags") val tags: List<String>? = null,
)

@Serializable
data class FlowFilterRequest(
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("limit") val limit: Int? = null,
    @SerialName("offset") val offset: Int? = null,
)

@Serializable
data class DeploymentFilter(
    @SerialName("concurrency_limit") val concurrencyLimit: DeploymentFilterConcurrencyLimit? = null,
    @SerialName("flow_or_deployment_name") val flowOrDeploymentName: DeploymentOrFlowNameFilter? = null,
    @SerialName("id") val id: DeploymentFilterId? = null,
    @SerialName("name") val name: DeploymentFilterName? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
    @SerialName("paused") val paused: DeploymentFilterPaused? = null,
    @SerialName("tags") val tags: DeploymentFilterTags? = null,
    @SerialName("work_queue_name") val workQueueName: DeploymentFilterWorkQueueName? = null,
)

@Serializable
data class FlowRunPolicy(
    @SerialName("max_retries") val maxRetries: Int? = null,
    @SerialName("pause_keys") val pauseKeys: List<String>? = null,
    @SerialName("resuming") val resuming: Boolean? = false,
    @SerialName("retries") val retries: Int? = null,
    @SerialName("retry_delay") val retryDelay: Int? = null,
    @SerialName("retry_type") val retryType: String? = null,
)

@Serializable
data class StateCreate(
    @SerialName("data") val data: Map<String, String>? = null,
    @SerialName("message") val message: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("state_details") val stateDetails: StateDetails? = null,
    @SerialName("type") val type: StateType,
)

@Serializable
data class StateDetails(
    @SerialName("cache_expiration") val cacheExpiration: String? = null,
    @SerialName("cache_key") val cacheKey: String? = null,
    @Contextual @SerialName("child_flow_run_id") val childFlowRunId: UUID? = null,
    @SerialName("deferred") val deferred: Boolean? = false,
    @Contextual @SerialName("flow_run_id") val flowRunId: UUID? = null,
    @SerialName("pause_key") val pauseKey: String? = null,
    @SerialName("pause_reschedule") val pauseReschedule: Boolean = false,
    @SerialName("pause_timeout") val pauseTimeout: String? = null,
    @SerialName("refresh_cache") val refreshCache: Boolean? = null,
    @SerialName("retriable") val retriable: Boolean? = null,
    @SerialName("run_input_keyset") val runInputKeyset: Map<String, String>? = null,
    @SerialName("scheduled_time") val scheduledTime: String? = null,
    @SerialName("task_parameters_id") val taskParametersId: String? = null,
    @Contextual @SerialName("task_run_id") val taskRunId: UUID? = null,
    @SerialName("traceparent") val traceparent: String? = null,
    @Contextual @SerialName("transition_id") val transitionId: UUID? = null,
    @SerialName("untrackable_result") val untrackableResult: Boolean = false,
)

@Serializable
enum class StateType {
    SCHEDULED,
    PENDING,
    RUNNING,
    COMPLETED,
    FAILED,
    CANCELLED,
    CRASHED,
    PAUSED,
    CANCELLING,
}

@Serializable
enum class Operator {
    AND,
    OR,
}

@Serializable
data class WorkQueueFilter(
    @SerialName("id") val id: WorkQueueFilterId? = null,
    @SerialName("name") val name: WorkQueueFilterName? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class WorkQueueFilterId(
    @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class WorkQueueFilterName(
    @SerialName("names") val names: List<String>? = null,
    @SerialName("startswith") val startsWith: List<String>? = null,
)

@Serializable
data class DeploymentFilterConcurrencyLimit(
    @SerialName("ge_") val greaterThanOrEqual: Int? = null,
    @SerialName("is_null_") val isNull: Boolean? = null,
    @SerialName("le_") val lessThanOrEqual: Int? = null,
)

@Serializable
data class DeploymentOrFlowNameFilter(
    @SerialName("like_") val like: String? = null,
)

@Serializable
data class DeploymentFilterId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class DeploymentFilterName(
    @SerialName("any_") val names: List<String>? = null,
    @SerialName("like_") val like: String? = null,
)

@Serializable
data class DeploymentFilterPaused(
    @SerialName("eq_") val equals: Boolean? = null,
)

@Serializable
data class DeploymentFilterTags(
    @SerialName("all_") val allTags: List<String>? = null,
    @SerialName("any_") val anyTags: List<String>? = null,
    @SerialName("is_null_") val isNull: Boolean? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class DeploymentFilterWorkQueueName(
    @SerialName("any_") val names: List<String>? = null,
)

@Serializable
data class FlowFilter(
    @SerialName("id") val id: FlowFilterId? = null,
    @SerialName("name") val name: FlowFilterName? = null,
    @SerialName("tags") val tags: FlowFilterTags? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class FlowFilterId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class FlowFilterName(
    @SerialName("any_") val names: List<String>? = null,
    @SerialName("like_") val like: String? = null,
)

@Serializable
data class FlowFilterTags(
    @SerialName("all_") val allTags: List<String>? = null,
    @SerialName("any_") val anyTags: List<String>? = null,
    @SerialName("is_null_") val isNull: Boolean? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class FlowRunFilter(
    @SerialName("id") val id: FlowRunFilterId? = null,
    @SerialName("state") val state: FlowRunFilterState? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
    @SerialName("expected_start_time") val expectedStartTime: FlowRunExpectedStartTime? = null,
    @SerialName("start_time") val startTime: FlowRunFilterStartTime? = null,
    @SerialName("end_time") val endTime: FlowRunFilterEndTime? = null,
    @SerialName("next_scheduled_start_time") val nextScheduledStartTime: FlowRunFilterNextScheduledStartTime? = null,
)

@Serializable
data class FlowRunFilterNextScheduledStartTime(
    @Contextual @SerialName("after_") val after: OffsetDateTime? = null,
    @Contextual @SerialName("before_") val before: OffsetDateTime? = null,
)

@Serializable
data class FlowRunExpectedStartTime(
    @Contextual @SerialName("after_") val after: OffsetDateTime? = null,
    @Contextual @SerialName("before_") val before: OffsetDateTime? = null,
)

@Serializable
data class FlowRunFilterStartTime(
    @Contextual @SerialName("after_") val after: OffsetDateTime? = null,
    @Contextual @SerialName("before_") val before: OffsetDateTime? = null,
    @SerialName("is_null_") val isNull: Boolean? = null,
)

@Serializable
data class FlowRunFilterEndTime(
    @Contextual @SerialName("after_") val after: OffsetDateTime? = null,
    @Contextual @SerialName("before_") val before: OffsetDateTime? = null,
    @SerialName("is_null_") val isNull: Boolean? = null,
)

@Serializable
data class FlowRunFilterId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class FlowRunFilterState(
    @SerialName("type") val type: FlowRunFilterStateType? = null, // Change here
    @SerialName("name") val name: String? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class FlowRunFilterStateType(
    @SerialName("any_") val any: List<StateType>? = null,
    @SerialName("not_any_") val notAny: List<StateType>? = null,
)

@Serializable
data class FlowRunFilterFlowId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class TaskRunFilter(
    @SerialName("id") val id: TaskRunFilterId? = null,
    @SerialName("state") val state: TaskRunFilterState? = null,
    @SerialName("task_id") val taskId: TaskRunFilterTaskId? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class TaskRunFilterId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class TaskRunFilterState(
    @SerialName("type") val type: StateType? = null,
    @SerialName("name") val name: String? = null,
)

@Serializable
data class TaskRunFilterTaskId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class WorkPoolFilter(
    @SerialName("id") val id: WorkPoolFilterId? = null,
    @SerialName("name") val name: WorkPoolFilterName? = null,
    @SerialName("type") val type: WorkPoolFilterType? = null,
    @SerialName("operator") val operator: Operator = Operator.AND,
)

@Serializable
data class WorkPoolFilterId(
    @Contextual @SerialName("any_") val ids: List<@Contextual UUID>? = null,
)

@Serializable
data class WorkPoolFilterName(
    @SerialName("any_") val names: List<String>? = null,
    @SerialName("like_") val like: String? = null,
)

@Serializable
data class WorkPoolFilterType(
    @SerialName("any_") val types: List<WorkPoolType>? = null,
)

@Serializable
data class DeploymentCreateRequest(
    @SerialName("name") val name: String,
    @Contextual @SerialName("flow_id") val flowId: UUID,
    @SerialName("description") val description: String? = null,
    @SerialName("enforce_parameter_schema") val enforceParameterSchema: Boolean? = null,
    @SerialName("entrypoint") val entryPoint: String? = null,
    @SerialName("job_variables") val jobVariables: JsonElement? = null,
    @SerialName("work_pool_name") val workPoolName: String? = null,
    @SerialName("work_queue_name") val workQueueName: String? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @SerialName("parameter_openapi_schema") val parameterOpenAPISchema: JsonElement? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("path") val path: String? = null,
)

@Serializable
data class DeploymentUpdateRequest(
    @SerialName("description") val description: String? = null,
    @SerialName("entrypoint") val entrypoint: String? = null,
    @SerialName("job_variables") val jobVariables: JsonElement? = null,
    @SerialName("work_pool_name") val workPoolName: String? = null,
    @SerialName("work_queue_name") val workQueueName: String? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("parameter_openapi_schema") val parameterOpenAPISchema: JsonElement? = null,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("path") val path: String? = null,
)

@Serializable
data class DeploymentFilterRequest(
    @SerialName("deployments") val deployments: DeploymentFilter? = null,
    @SerialName("flow_runs") val flowRuns: FlowRunFilter? = null,
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("task_runs") val taskRuns: TaskRunFilter? = null,
    @SerialName("work_pool_queues") val workPoolQueues: WorkQueueFilter? = null,
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
)

@Serializable
data class DeploymentFlowRunCreate(
    @SerialName("context") val context: Map<String, String>? = null,
    @SerialName("empirical_policy") val empiricalPolicy: FlowRunPolicy? = null,
    @SerialName("enforce_parameter_schema") val enforceParameterSchema: Boolean? = null,
    @SerialName("idempotency_key") val idempotencyKey: String? = null,
    @SerialName("infrastructure_document_id") val infrastructureDocumentId: String? = null,
    @SerialName("job_variables") val jobVariables: JsonElement? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @Contextual @SerialName("parent_task_run_id") val parentTaskRunId: UUID? = null,
    @SerialName("state") val state: StateCreate? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("work_queue_name") val workQueueName: String? = null,
)

enum class DeploymentStatus {
    READY,
    NOT_READY,
}

@Serializable
data class Deployment(
    @Contextual @SerialName("id") val id: UUID,
    @SerialName("name") val name: String,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @Contextual @SerialName("flow_id") val flowId: UUID,
    @SerialName("work_queue_name") val workQueueName: String? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("job_variables") val jobVariables: JsonElement? = null,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("disabled") val disabled: Boolean = false,
    @SerialName("paused") val paused: Boolean = false,
    @SerialName("status") val status: DeploymentStatus? = null,
    @Contextual @SerialName("last_polled") val lastPolled: OffsetDateTime? = null,
)

@Serializable
data class DeploymentPaginationRequest(
    @SerialName("deployments") val deployments: DeploymentFilter? = null,
    @SerialName("flow_runs") val flowRuns: FlowRunFilter? = null,
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("task_runs") val taskRuns: TaskRunFilter? = null,
    @SerialName("work_pool_queues") val workPoolQueues: WorkQueueFilter? = null,
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
    @SerialName("limit") val limit: Int? = null,
    @SerialName("page") val page: Int = 1,
    @SerialName("sort") val sort: DeploymentSort = DeploymentSort.NAME_ASC,
)

@Serializable
data class DeploymentPaginationResponse(
    @SerialName("results") val results: List<Deployment>,
    @SerialName("count") val count: Int,
    @SerialName("limit") val limit: Int,
    @SerialName("pages") val pages: Int,
    @SerialName("page") val page: Int,
)

@Serializable
enum class DeploymentSort {
    CREATED_DESC,
    UPDATED_DESC,
    NAME_ASC,
    NAME_DESC,
}

@Serializable
data class FlowRunHistoryRequest(
    @SerialName("deployments") val deployments: DeploymentFilter? = null,
    @SerialName("flow_runs") val flowRuns: FlowRunFilter? = null,
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("task_runs") val taskRuns: TaskRunFilter? = null,
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
    @SerialName("work_queues") val workQueues: WorkQueueFilter? = null,
    @Contextual @SerialName("history_start") val historyStart: OffsetDateTime,
    @Contextual @SerialName("history_end") val historyEnd: OffsetDateTime,
    @SerialName("history_interval_seconds") val historyInterval: Int,
)

@Serializable
data class FlowRunHistoryResponse(
    @SerialName("interval_start") val intervalStart: String,
    @SerialName("interval_end") val intervalEnd: String,
    @SerialName("states") val states: List<FlowrunHistoryResponseState>,
)

@Serializable
data class FlowrunHistoryResponseState(
    @SerialName("state_type") val stateType: StateType,
    @SerialName("state_name") val stateName: String,
    @SerialName("count_runs") val countRuns: Int,
    @SerialName("sum_estimated_run_time") val sumEstimatedRunTime: Double,
    @SerialName("sum_estimated_lateness") val sumEstimatedLateness: Double,
)

@Serializable
data class FlowRunCreateRequest(
    @Contextual @SerialName("flow_id") val flowId: UUID,
    @SerialName("name") val name: String? = null,
    @SerialName("state") val state: StateCreate? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("context") val context: Map<String, String>? = null,
)

@Serializable
data class FlowRunUpdateRequest(
    @SerialName("name") val name: String? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("tags") val tags: List<String>? = null,
)

@Serializable
data class FlowRunReadFilterRequest(
    @SerialName("deployments") val deployments: DeploymentFilter? = null,
    @SerialName("flow_runs") val flowRuns: FlowRunFilter? = null,
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("limit") val limit: Int? = null,
    @SerialName("offset") val offset: Int? = null,
    @SerialName("sort") val sort: FlowRunSort? = null,
    @SerialName("task_runs") val taskRuns: TaskRunFilter? = null,
    @SerialName("work_pool_queues") val workPoolQueues: WorkQueueFilter? = null,
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
)

@Serializable
data class FlowRunCountFilterRequest(
    @SerialName("deployments") val deployments: DeploymentFilter? = null,
    @SerialName("flow_runs") val flowRuns: FlowRunFilter? = null,
    @SerialName("flows") val flows: FlowFilter? = null,
    @SerialName("task_runs") val taskRuns: TaskRunFilter? = null,
    @SerialName("work_pool_queues") val workPoolQueues: WorkQueueFilter? = null,
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
)

@Serializable
data class State(
    @Contextual @SerialName("id") val id: UUID,
    @SerialName("type") val type: StateType,
    @SerialName("data") val data: Map<String, String>? = null,
    @SerialName("message") val message: String? = null,
    @SerialName("name") val name: String? = null,
    @Contextual @SerialName("timestamp") val timestamp: OffsetDateTime,
)

@Serializable
data class CreatedBy(
    @Contextual @SerialName("id") val id: UUID? = null,
    @SerialName("display_value") val displayValue: String? = null,
    @SerialName("type") val type: String? = null,
)

@Serializable
data class FlowRun(
    @Contextual @SerialName("id") val id: UUID,
    @Contextual @SerialName("flow_id") val flowId: UUID,
    @SerialName("name") val name: String,
    @SerialName("state") val state: State? = null,
    @SerialName("state_type") val stateType: StateType? = null,
    @SerialName("parameters") val parameters: JsonElement? = null,
    @SerialName("job_variables") val jobVariables: JsonElement? = null,
    @Contextual @SerialName("start_time") val startTime: OffsetDateTime? = null,
    @Contextual @SerialName("end_time") val endTime: OffsetDateTime? = null,
    @SerialName("auto_scheduled") val autoScheduled: Boolean = false,
    @SerialName("context") val context: Map<String, String>? = null,
    @Contextual @SerialName("created") val created: OffsetDateTime,
    @SerialName("created_by") val createdBy: CreatedBy? = null,
    @Contextual @SerialName("deployment_id") val deploymentId: UUID? = null,
    @SerialName("deployment_version") val deploymentVersion: String? = null,
    @SerialName("empirical_policy") val empiricalPolicy: FlowRunPolicy? = null,
    @SerialName("estimated_run_time") val estimatedRunTime: Double = 0.0,
    @SerialName("estimated_start_time_delta") val estimatedStartTimeDelta: Double = 0.0,
    @Contextual @SerialName("expected_start_time") val expectedStartTime: OffsetDateTime? = null,
    @SerialName("flow_version") val flowVersion: String? = null,
    @SerialName("idempotency_key") val idempotencyKey: String? = null,
    @Contextual @SerialName("infrastructure_document_id") val infrastructureDocumentId: UUID? = null,
    @SerialName("infrastructure_pid") val infrastructurePid: String? = null,
    @SerialName("labels") val labels: Map<String, String>? = null,
    @Contextual @SerialName("next_scheduled_start_time") val nextScheduledStartTime: OffsetDateTime? = null,
    @Contextual @SerialName("parent_task_run_id") val parentTaskRunId: UUID? = null,
    @SerialName("run_count") val runCount: Int = 0,
    @Contextual @SerialName("state_id") val stateId: UUID? = null,
    @SerialName("state_name") val stateName: String? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("total_run_time") val totalRunTime: Double = 0.0,
    @Contextual @SerialName("updated") val updated: OffsetDateTime? = null,
    @Contextual @SerialName("work_queue_id") val workQueueId: UUID? = null,
    @SerialName("work_queue_name") val workQueueName: String? = null,
)

@Serializable
enum class FlowRunSort {
    ID_DESC,
    START_TIME_ASC,
    START_TIME_DESC,
    EXPECTED_START_TIME_ASC,
    EXPECTED_START_TIME_DESC,
    NAME_ASC,
    NAME_DESC,
    NEXT_SCHEDULED_START_TIME_ASC,
    END_TIME_DESC,
}

@Serializable
enum class WorkQueueStatus {
    READY,
    NOT_READY,
    PAUSED,
}

@Serializable
data class WorkQueue(
    @Contextual @SerialName("id") val id: UUID,
    @SerialName("name") val name: String,
    @SerialName("created") val created: String,
    @SerialName("updated") val updated: String,
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean = false,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @Contextual @SerialName("work_pool_id") val workPoolId: UUID,
    @SerialName("priority") val priority: Int? = null,
    @Contextual @SerialName("last_polled") val lastPolled: OffsetDateTime? = null,
    @SerialName("work_pool_name") val workPoolName: String? = null,
    @SerialName("status") val status: WorkQueueStatus? = null,
)

@Serializable
data class WorkQueueCreateRequest(
    @SerialName("name") val name: String,
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean = false,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("priority") val priority: Int? = null,
)

@Serializable
data class WorkQueueUpdateRequest(
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean? = null,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("priority") val priority: Int? = null,
)

@Serializable
enum class WorkPoolStatus {
    READY,
    NOT_READY,
    PAUSED,
}

@Serializable
enum class WorkPoolType(val description: String) {
    @SerialName("process")
    PROCESS("Executes flow runs in subprocesses"),

    @SerialName("kubernetes")
    KUBERNETES("Executes flow runs as Kubernetes jobs"),

    @SerialName("docker")
    DOCKER("Executes flow runs within Docker containers"),

    @SerialName("ecs")
    ECS("Executes flow runs as ECS tasks"),

    @SerialName("cloud-run-v2")
    CLOUD_RUN_V2("Executes flow runs as Google Cloud Run jobs"),

    @SerialName("vertex-ai")
    VERTEX_AI("Executes flow runs as Google Cloud Vertex AI jobs"),

    @SerialName("azure-container-instance")
    AZURE_CONTAINER_INSTANCE("Execute flow runs in ACI containers"),
}

    @Serializable
data class WorkPool(
    @Contextual @SerialName("id") val id: UUID,
    @SerialName("name") val name: String,
    @SerialName("type") val type: WorkPoolType,
    @SerialName("created") val created: String,
    @SerialName("updated") val updated: String,
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean = false,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("base_job_template") val baseJobTemplate: JsonElement? = null,
    @SerialName("status") val status: WorkPoolStatus? = null,
)

@Serializable
data class WorkPoolCreateRequest(
    @SerialName("name") val name: String,
    @SerialName("type") val type: WorkPoolType,
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean = false,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("base_job_template") val baseJobTemplate: JsonElement? = null,
)

@Serializable
data class WorkPoolUpdateRequest(
    @SerialName("description") val description: String? = null,
    @SerialName("is_paused") val isPaused: Boolean? = null,
    @SerialName("concurrency_limit") val concurrencyLimit: Int? = null,
    @SerialName("base_job_template") val baseJobTemplate: JsonElement? = null,
)

@Serializable
data class WorkPoolFilterRequest(
    @SerialName("work_pools") val workPools: WorkPoolFilter? = null,
    @SerialName("limit") val limit: Int? = null,
    @SerialName("offset") val offset: Int? = null,
)
