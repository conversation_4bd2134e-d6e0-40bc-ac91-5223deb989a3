package com.nextchaptersoftware.prefect

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.prefect.api.PrefectAuthString
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BasicAuthCredentials
import io.ktor.client.plugins.auth.providers.basic
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.ContentType
import io.ktor.http.Url
import io.ktor.http.contentType
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes

class PrefectApiClient(
    private val baseApiUri: Url,
    private val timeout: Duration = 1.minutes,
    private val authString: PrefectAuthString,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) {
    @Suppress("MagicNumber")
    val instance = HttpClient(clientEngine) {
        install(ContentNegotiation) {
            installJsonSerializer()
        }
        install(HttpRequestRetry) {
            retryIf(maxRetries = 3) { _, response ->
                response.status.value.let {
                    it in 500..599
                }
            }
        }
        install(Auth) {
            basic {
                credentials {
                    BasicAuthCredentials(username = authString.username, password = authString.password.value)
                }
                sendWithoutRequest { request ->
                    request.url.host == baseApiUri.host
                }
            }
        }
        install(HttpTimeout) {
            requestTimeoutMillis = timeout.inWholeMilliseconds
            socketTimeoutMillis = timeout.inWholeMilliseconds
        }
        install(KtorClientTracing)
        expectSuccess = true

        defaultRequest {
            url(baseApiUri.asString)
            contentType(ContentType.Application.Json)
        }
    }
}
