package com.nextchaptersoftware.prefect.api.utils

import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

enum class OpenAPIType(val typeName: String) {
    STRING("string"),
    INTEGER("integer"),
    BOOLEAN("boolean"),
    OBJECT("object"),
    ARRAY("array"),
    NUMBER("number"),
}

data class NestedItemType(
    val type: OpenAPIType,
    val nestedItems: NestedItemType? = null,
)

data class PrefectParameter(
    val type: OpenAPIType,
    val title: String,
    val position: Int,
    val isRequired: Boolean,
    val itemType: OpenAPIType? = null,
    val nestedItems: NestedItemType? = null,
    val defaultValue: Any? = null, // New property for default values
)

object PrefectParameterSchemaGenerator {

    private fun buildItemsSchema(nestedItem: NestedItemType): JsonObject {
        return buildJsonObject {
            put("type", nestedItem.type.typeName)
            if (nestedItem.type == OpenAPIType.ARRAY && nestedItem.nestedItems != null) {
                put("items", buildItemsSchema(nestedItem.nestedItems))
            }
        }
    }

    private fun getItemsJsonElement(value: Any?): JsonElement {
        return when (value) {
            is String -> JsonPrimitive(value)

            is Number -> JsonPrimitive(value)

            is Boolean -> JsonPrimitive(value)

            is List<*> -> buildJsonArray {
                value.forEach { add(getItemsJsonElement(it)) }
            }

            is Map<*, *> -> buildJsonObject {
                value.forEach { (key, v) ->
                    if (key is String) {
                        put(key, getItemsJsonElement(v))
                    }
                }
            }

            else -> JsonPrimitive(value.toString())
        }
    }

    fun generateSchema(prefectParameters: List<PrefectParameter>): JsonElement {
        val properties = buildJsonObject {
            prefectParameters.forEach { param ->
                put(
                    param.title,
                    buildJsonObject {
                        put("type", param.type.typeName)
                        put("title", param.title)
                        put("position", param.position)
                        if (param.defaultValue != null) {
                            put("default", getItemsJsonElement(param.defaultValue))
                        }
                        if (param.type == OpenAPIType.ARRAY) {
                            if (param.nestedItems != null) {
                                put("items", buildItemsSchema(param.nestedItems))
                            } else if (param.itemType != null) {
                                put(
                                    "items",
                                    buildJsonObject {
                                        put("type", param.itemType.typeName)
                                    },
                                )
                            }
                        }
                    },
                )
            }
        }

        val requiredArray = buildJsonArray {
            prefectParameters.filter { it.isRequired }.forEach { param ->
                add(JsonPrimitive(param.title))
            }
        }

        return buildJsonObject {
            put("type", "object")
            put("title", "Parameters")
            put("required", requiredArray)
            put("properties", properties)
        }
    }
}
