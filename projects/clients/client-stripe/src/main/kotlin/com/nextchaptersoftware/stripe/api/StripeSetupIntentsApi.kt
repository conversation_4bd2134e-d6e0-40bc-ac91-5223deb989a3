package com.nextchaptersoftware.stripe.api

import com.stripe.StripeClient
import com.stripe.model.SetupIntent
import com.stripe.param.SetupIntentCreateParams
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StripeSetupIntentsApi(
    private val client: StripeClient,
) {
    suspend fun createSetupIntent(
        customerId: String,
    ): SetupIntent {
        val automaticPaymentMethods = SetupIntentCreateParams.AutomaticPaymentMethods.builder()
            .setEnabled(true)
            .build()

        val params = SetupIntentCreateParams.builder()
            .setCustomer(customerId)
            .setAutomaticPaymentMethods(automaticPaymentMethods)
            .build()

        return withContext(Dispatchers.IO) {
            client.setupIntents().create(params)
        }
    }
}
