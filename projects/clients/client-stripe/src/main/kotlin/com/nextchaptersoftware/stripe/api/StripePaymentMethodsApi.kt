package com.nextchaptersoftware.stripe.api

import com.stripe.StripeClient
import com.stripe.model.PaymentMethod
import com.stripe.model.StripeCollection
import com.stripe.param.PaymentMethodListParams
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StripePaymentMethodsApi(
    private val client: StripeClient,
) {
    suspend fun listPaymentMethods(
        customerId: String,
    ): StripeCollection<PaymentMethod> {
        val params = PaymentMethodListParams.builder()
            .setCustomer(customerId)
            .setType(PaymentMethodListParams.Type.CARD)
            .build()

        return withContext(Dispatchers.IO) {
            client.paymentMethods().list(params)
        }
    }

    suspend fun getPaymentMethod(
        paymentMethodId: String,
    ): PaymentMethod {
        return withContext(Dispatchers.IO) {
            client.paymentMethods().retrieve(paymentMethodId)
        }
    }
}
