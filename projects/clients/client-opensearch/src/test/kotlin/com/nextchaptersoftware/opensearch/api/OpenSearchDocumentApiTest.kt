package com.nextchaptersoftware.opensearch.api

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.opensearch.api.utils.OpenSearchTestUtils
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestMethodOrder
import org.opensearch.client.opensearch._types.FieldValue
import org.opensearch.client.opensearch._types.Refresh
import org.opensearch.client.opensearch._types.mapping.TypeMapping
import org.opensearch.client.opensearch.core.BulkResponse
import org.opensearch.client.opensearch.core.DeleteResponse
import org.opensearch.client.opensearch.core.GetResponse
import org.opensearch.client.opensearch.core.IndexResponse
import org.opensearch.client.opensearch.core.MgetResponse
import org.opensearch.client.opensearch.indices.IndexSettings
import software.amazon.awssdk.http.auth.aws.internal.signer.V4RequestSigner.query

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(OrderAnnotation::class)
class OpenSearchDocumentApiTest {
    data class TestDocument(val field: String)

    companion object {
        private const val TEST_INDEX_NAME = "test-document-index"
        private const val TEST_DOCUMENT_ID = "test-doc-id"
        private val openSearchApiProvider = OpenSearchTestUtils.OPENSEARCH_API_PROVIDER
        private val openSearchDocumentApi = openSearchApiProvider.openSearchDocumentApi

        @BeforeAll
        @JvmStatic
        fun setUp() {
            // Ensure the index exists before running tests
            runSuspendCatching {
                openSearchApiProvider.openSearchIndexApi.createIndex(TEST_INDEX_NAME, IndexSettings.Builder().build(), TypeMapping.Builder().build())
            }
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            runSuspendCatching {
                openSearchApiProvider.openSearchIndexApi.deleteIndex(TEST_INDEX_NAME)
            }
        }
    }

    @Test
    @Order(1)
    fun `test create document by id`() {
        val document = mapOf("field" to "value")
        val response: IndexResponse = openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, TEST_DOCUMENT_ID, document)
        assertThat(response).isNotNull
    }

    @Test
    @Order(2)
    fun `test get document by id`() {
        val response: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, TEST_DOCUMENT_ID)
        assertThat(response).isNotNull
        assertThat(response.source()).containsEntry("field", "value")
    }

    @Test
    @Order(3)
    fun `test delete document by id`() {
        val response: DeleteResponse = openSearchDocumentApi.deleteDocumentById(TEST_INDEX_NAME, TEST_DOCUMENT_ID)
        assertThat(response).isNotNull
    }

    @Test
    @Order(4)
    fun `test upsert creates document`() {
        val newDocumentId = "new-doc-id"
        val document = mapOf("field" to "initial value")

        // Perform upsert operation
        val upsertResponse = openSearchDocumentApi.upsertDocumentById(TEST_INDEX_NAME, newDocumentId, document)
        assertThat(upsertResponse).isNotNull

        // Verify document creation
        val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, newDocumentId)
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).containsEntry("field", "initial value")
    }

    @Test
    @Order(5)
    fun `test upsert updates document`() {
        val updatedDocument = mapOf("field" to "updated value")

        // Perform upsert operation on existing document
        val upsertResponse = openSearchDocumentApi.upsertDocumentById(TEST_INDEX_NAME, TEST_DOCUMENT_ID, updatedDocument)
        assertThat(upsertResponse).isNotNull

        // Verify document update
        val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, TEST_DOCUMENT_ID)
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).containsEntry("field", "updated value")
    }

    @Test
    @Order(6)
    fun `test create document without id`() {
        val document = mapOf("field" to "value without id")

        // Perform creation without specifying an ID
        val response: IndexResponse = openSearchDocumentApi.createDocumentWithoutId(TEST_INDEX_NAME, document)
        assertThat(response).isNotNull
        assertThat(response.id()).isNotEmpty() // Ensure an ID was generated

        // Optionally, retrieve the document using the generated ID and verify
        val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, response.id())
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).containsEntry("field", "value without id")
    }

    @Test
    @Order(7)
    fun `test bulk document creation`() {
        val documents = listOf(
            mapOf("field" to "bulk value 1"),
            mapOf("field" to "bulk value 2"),
        )

        // Perform bulk operation
        val bulkResponse: BulkResponse = openSearchDocumentApi.bulk {
            documents.forEachIndexed { index, doc ->
                this.operations { op ->
                    op.index { idx ->
                        idx.index(TEST_INDEX_NAME)
                        idx.id("bulk-doc-id-$index")
                        idx.document(doc)
                    }
                }
            }
        }
        assertThat(bulkResponse).isNotNull
        assertThat(bulkResponse.items()).hasSize(documents.size)

        // Verify each document was created
        documents.forEachIndexed { index, doc ->
            val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "bulk-doc-id-$index")
            assertThat(getResponse).isNotNull
            assertThat(getResponse.source()).containsEntry("field", doc["field"])
        }
    }

    @Test
    @Order(8)
    fun `test create document by id with class`() {
        val document = TestDocument("class value")
        val response: IndexResponse = openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "class-doc-id", document)
        assertThat(response).isNotNull

        // Verify document creation
        val getResponse: GetResponse<TestDocument> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "class-doc-id")
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).isEqualTo(document)
    }

    @Test
    @Order(9)
    fun `test upsert document by id with class`() {
        val initialDocument = TestDocument("initial class value")
        val updatedDocument = TestDocument("updated class value")

        // First, create the document
        openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "upsert-class-doc-id", initialDocument)

        // Perform upsert operation
        val upsertResponse = openSearchDocumentApi.upsertDocumentById(TEST_INDEX_NAME, "upsert-class-doc-id", updatedDocument)
        assertThat(upsertResponse).isNotNull

        // Verify document update
        val getResponse: GetResponse<TestDocument> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "upsert-class-doc-id")
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).isEqualTo(updatedDocument)
    }

    @Test
    @Order(10)
    fun `test update document by id with class`() {
        val initialDocument = TestDocument("initial class value")
        val updatedDocument = TestDocument("updated class value")

        // First, create the document
        openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "update-class-doc-id", initialDocument)

        // Perform update operation
        val updateResponse = openSearchDocumentApi.updateDocumentById(TEST_INDEX_NAME, "update-class-doc-id", updatedDocument)
        assertThat(updateResponse).isNotNull

        // Verify document update
        val getResponse: GetResponse<TestDocument> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "update-class-doc-id")
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).isEqualTo(updatedDocument)
    }

    @Test
    @Order(11)
    fun `test delete by query`() {
        // Setup: Add documents to be deleted by query
        val documentsToDelete = listOf(
            mapOf("field" to "delete value 1"),
            mapOf("field" to "delete value 2"),
        )

        documentsToDelete.forEachIndexed { index, doc ->
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "delete-doc-id-$index", doc) {
                refresh(Refresh.True)
            }
        }

        // Perform delete by query operation
        val deleteResponse = openSearchDocumentApi.deleteDocumentByQuery(TEST_INDEX_NAME) {
            query { q ->
                q.match { m ->
                    m.field("field").query(FieldValue.of("delete value 1"))
                }
            }
        }
        assertThat(deleteResponse).isNotNull

        // Verify document deletion
        val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "delete-doc-id-0")
        assertThat(getResponse.found()).isFalse
    }

    @Test
    @Order(12)
    fun `test delete by query with substring matching`() {
        // Setup: Add documents to be deleted by query
        val documentsToDelete = listOf(
            mapOf("field" to "delete value 1"),
            mapOf("field" to "delete value 2"),
            mapOf("field" to "other value"),
        )

        documentsToDelete.forEachIndexed { index, doc ->
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "delete-doc-id-$index", doc) {
                refresh(Refresh.True)
            }
        }

        // Perform delete by query operation using a wildcard query
        val deleteResponse = openSearchDocumentApi.deleteDocumentByQuery(TEST_INDEX_NAME) {
            query { q ->
                q.wildcard { w ->
                    w.field("field").value("delete*")
                }
            }
        }
        assertThat(deleteResponse).isNotNull

        // Verify document deletion
        documentsToDelete.forEachIndexed { index, _ ->
            val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "delete-doc-id-$index")
            if (index < 2) { // Only the first two documents should be deleted
                assertThat(getResponse.found()).isFalse
            } else {
                assertThat(getResponse.found()).isTrue
            }
        }
    }

    @Test
    @Order(13)
    fun `test update by query`() {
        // Setup: Add documents to be updated by query
        val documentsToUpdate = listOf(
            mapOf("field" to "update value 1"),
            mapOf("field" to "update value 2"),
        )

        documentsToUpdate.forEachIndexed { index, doc ->
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "update-doc-id-$index", doc) {
                refresh(Refresh.True)
            }
        }

        // Perform update by query operation
        val updateResponse = openSearchDocumentApi.updateDocumentByQuery(TEST_INDEX_NAME) {
            query { q ->
                q.match { m ->
                    m.field("field").query(FieldValue.of("update value 1"))
                }
            }
            script { s ->
                s.inline {
                    it.source("ctx._source.field = 'updated'")
                }
            }
        }
        assertThat(updateResponse).isNotNull

        // Verify document update
        val getResponse: GetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentById(TEST_INDEX_NAME, "update-doc-id-0")
        assertThat(getResponse).isNotNull
        assertThat(getResponse.source()).containsEntry("field", "updated")
    }

    @Test
    @Order(14)
    fun `test get documents by ids`() {
        // Setup: Add documents to be retrieved
        val documents = listOf(
            "doc-id-1" to mapOf("field" to "value 1"),
            "doc-id-2" to mapOf("field" to "value 2"),
            "doc-id-3" to mapOf("field" to "value 3"),
        )

        documents.forEach { (id, doc) ->
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, id, doc) {
                refresh(Refresh.True)
            }
        }

        // Retrieve documents by IDs
        val documentIds = documents.map { it.first }
        val response: MgetResponse<Map<String, Any>> = openSearchDocumentApi.getDocumentsByIds(TEST_INDEX_NAME, documentIds)

        // Assert response is not null
        assertThat(response).isNotNull

        // Verify each document was retrieved correctly
        response.docs().forEachIndexed { index, docResponse ->
            assertThat(docResponse.result()).isNotNull
            assertThat(docResponse.result().source()).containsEntry("field", "value ${index + 1}")
        }
    }
}
