# The paid subscription of an organization.
fragment PaidSubscription on PaidSubscription {
    __typename
    # The creator of the subscription.
    creator {
        id
    }
    # The date the subscription was canceled, if any.
    canceledAt
    # The date the subscription will be billed next.
    nextBillingAt
    # The last time at which the entity was meaningfully updated, i.e. for all changes of syncable properties except those
    #     for which updates should not produce an update to updatedAt (see skipUpdatedAtKeys). This is the same as the creation time if the entity hasn't
    #     been updated after creation.
    updatedAt
    # The maximum number of seats that will be billed in the subscription.
    seatsMaximum
    # The minimum number of seats that will be billed in the subscription.
    seatsMinimum
    # The number of seats in the subscription.
    seats
    # The subscription type of a pending change. Null if no change pending.
    pendingChangeType
    # The subscription type.
    type
    # The time at which the entity was archived. Null if the entity has not been archived.
    archivedAt
    # The time at which the entity was created.
    createdAt
    # The unique identifier of the entity.
    id
}

# An organization. Organizations are root-level objects that contain user accounts and teams.
fragment Organization on Organization {
    __typename
    # Allowed authentication providers, empty array means all are allowed
    allowedAuthServices
    # How git branches are formatted. If null, default formatting will be used.
    gitBranchFormat
    # Number of active users in the organization.
    userCount
    # Number of issues in the organization.
    createdIssueCount
    # Previously used URL keys for the organization (last 3 are kept and redirected).
    previousUrlKeys
    # Rolling 30-day total upload volume for the organization, in megabytes.
    periodUploadVolume
    # The hour at which to prompt for project updates.
    projectUpdateRemindersHour
    # The last time at which the entity was meaningfully updated, i.e. for all changes of syncable properties except those
    #     for which updates should not produce an update to updatedAt (see skipUpdatedAtKeys). This is the same as the creation time if the entity hasn't
    #     been updated after creation.
    updatedAt
    # The organization's logo URL.
    logoUrl
    # The organization's name.
    name
    # The organization's subscription to a paid plan.
    subscription {
        ...PaidSubscription
    }
    # The organization's unique URL key.
    urlKey
    # The time at which deletion of the organization was requested.
    deletionRequestedAt
    # The time at which the entity was archived. Null if the entity has not been archived.
    archivedAt
    # The time at which the entity was created.
    createdAt
    # The time at which the trial of the plus plan will end.
    trialEndsAt
    # The unique identifier of the entity.
    id
    # Whether SAML authentication is enabled for organization.
    samlEnabled
    # Whether SCIM provisioning is enabled for organization.
    scimEnabled
    # Whether the Git integration linkback messages should be sent to private repositories.
    gitLinkbackMessagesEnabled
    # Whether the Git integration linkback messages should be sent to public repositories.
    gitPublicLinkbackMessagesEnabled
    # Whether the organization is using a roadmap.
    roadmapEnabled
}

# The user's organization.
query OrganizationQuery {
    organization {
        ...Organization
    }
}
