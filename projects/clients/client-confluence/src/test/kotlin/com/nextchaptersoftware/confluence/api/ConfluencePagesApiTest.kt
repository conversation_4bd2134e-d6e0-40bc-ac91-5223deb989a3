package com.nextchaptersoftware.confluence.api

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encodePretty
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.confluence.models.ContentStatus
import com.nextchaptersoftware.confluence.models.Page
import com.nextchaptersoftware.confluence.models.toPage
import com.nextchaptersoftware.test.utils.SkipInCI
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

@SkipInCI
class ConfluencePagesApiTest {
    private val tokens = OAuthTokens(accessToken = Utils.CONFLUENCE_CLOUD_API_TOKEN)
    private val api = ConfluenceApiProvider().confluencePagesApi

    @Test
    fun streamPages() = runTest {
        val pages = mutableListOf<Page>()

        val url = ConfluencePagesApi.url(
            baseUrl = Utils.CONFLUENCE_CLOUD_BASE_URL,
            limit = 2,
        )

        var totalPages = 0

        api.streamPages(
            url = url,
            tokens = tokens,
        ).onEach { batch ->
            pages.addAll(batch.items.map { it.toPage() })
            println(batch.nextCursor)
            totalPages++
        }.catch {
            println("error")
        }.collect()

        assertThat(pages).hasSize(24)
        assertThat(totalPages).isEqualTo(12)
        assertThat(pages).allMatch { it.body.atlasDocFormat != null }
        assertThat(pages).allMatch { it.status == ContentStatus.Current }
    }

    @Test
    fun getPage() = runTest {
        val url = ConfluencePagesApi.urlForPage(
            baseUrl = Utils.CONFLUENCE_CLOUD_BASE_URL,
            pageId = "6225921",
        )

        val page = api.getPage(
            url = url,
            tokens = tokens,
        ).toPage()
        assertThat(page.id).isEqualTo("6225921")
        assertThat(page.body.atlasDocFormat).isNotNull
        println(page.encodePretty())
    }

    @Test
    fun getPages() = runTest {
        val pages = mutableListOf<Page>()

        val url = ConfluencePagesApi.url(
            baseUrl = Utils.CONFLUENCE_CLOUD_BASE_URL,
            ids = listOf("6225921"),
            includeContents = false, // We don't need contents here so don't ask for it in hopes of getting a faster response
        )

        api.streamPages(
            url = url,
            tokens = tokens,
        ).onEach { batch ->
            pages.addAll(batch.items.map { it.toPage() })
            println(batch.nextCursor)
        }.catch {
            println(it)
        }.collect()

        assertThat(pages).hasSize(1)
        assertThat(pages).allMatch { it.id == "6225921" }
        assertThat(pages).allMatch { it.body.atlasDocFormat == null }
    }
}
