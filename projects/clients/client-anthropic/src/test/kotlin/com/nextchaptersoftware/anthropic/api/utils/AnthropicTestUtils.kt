package com.nextchaptersoftware.anthropic.api.utils

import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl

object AnthropicTestUtils {
    private val ANTHROPIC_API_CONFIGURATION = AnthropicApiConfiguration(
        baseApiUri = GlobalConfig.INSTANCE.anthropic.baseApiUri.asUrl,
        timeout = GlobalConfig.INSTANCE.anthropic.defaultTimeout,
        token = GlobalConfig.INSTANCE.anthropic.apiKey,
    )

    val ANTHROPIC_API_PROVIDER by lazy {
        AnthropicApiProvider(config = ANTHROPIC_API_CONFIGURATION)
    }
}
