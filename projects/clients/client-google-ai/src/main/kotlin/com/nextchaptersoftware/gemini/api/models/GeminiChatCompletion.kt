package com.nextchaptersoftware.gemini.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GeminiChatCompletion(
    @SerialName("candidates")
    val candidates: List<GeminiChatCompletionCandidate>,

    @SerialName("usageMetadata")
    val usageMetadata: GeminiChatCompletionUsageMetadata,
)

@Serializable
data class GeminiChatCompletionCandidate(
    @SerialName("content")
    val content: GeminiChatMessage,
)

@Serializable
data class GeminiChatCompletionUsageMetadata(
    @SerialName("promptTokenCount")
    val promptTokenCount: Int,

    @SerialName("candidatesTokenCount")
    val candidatesTokenCount: Int,

    @SerialName("totalTokenCount")
    val totalTokenCount: Int,
)
