package com.nextchaptersoftware.gemini.config

import com.nextchaptersoftware.config.ConfigLoader
import com.sksamuel.hoplite.Secret
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class GeminiApiProviderConfig(
    @Contextual val apiKey: Secret,
    val baseApiUrl: String,
)

@Serializable
data class GeminiApiConfig(
    val gemini: GeminiApiProviderConfig,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<GeminiApiConfig>(scope = "gemini-api-config")
    }
}
