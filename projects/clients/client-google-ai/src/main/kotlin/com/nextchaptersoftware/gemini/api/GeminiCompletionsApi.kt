package com.nextchaptersoftware.gemini.api

import com.nextchaptersoftware.gemini.api.models.GeminiChatCompletion
import com.nextchaptersoftware.gemini.api.models.GeminiChatCompletionRequest
import com.nextchaptersoftware.gemini.api.models.GeminiChatCompletionUsageMetadata
import com.nextchaptersoftware.gemini.api.models.GeminiChatMessage
import com.nextchaptersoftware.gemini.api.models.GeminiChatMessagePart
import com.nextchaptersoftware.gemini.api.models.GeminiChatRole
import com.nextchaptersoftware.gemini.api.models.GeminiCompletionsModel
import com.nextchaptersoftware.gemini.api.models.GeminiGenerationConfig
import com.nextchaptersoftware.gemini.api.models.GeminiModelId
import com.nextchaptersoftware.gemini.api.utils.GeminiChatCompletionPromptParser
import com.nextchaptersoftware.gemini.api.utils.streamEventsFrom
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.trace.coroutine.withSpan
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.nullIfBlank
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.preparePost
import io.ktor.client.request.setBody
import io.ktor.http.HttpHeaders
import io.ktor.http.headers
import io.ktor.http.path
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class GeminiCompletionsApi(
    private val client: GeminiApiClient,
) {
    private suspend fun chatCompletion(
        model: GeminiModelId = GeminiCompletionsModel.Gemini15Pro.modelId,
        messages: List<GeminiChatMessage>,
        topP: Float? = null,
        topK: Int? = null,
        temperature: Float? = null,
        respondWithJson: Boolean,
    ): GeminiChatCompletion {
        val messageBundle = messageBundleForMessages(messages, respondWithJson)

        val request = GeminiChatCompletionRequest(
            systemInstructions = messageBundle.systemPrompt,
            contents = messageBundle.chatMessages,
            generationConfig = GeminiGenerationConfig(
                temperature = temperature,
                topP = topP,
                topK = topK,
            ),
        )

        return client.instance.post {
            url {
                path("${model.id}:generateContent")
            }
            setBody(request)
        }.body<GeminiChatCompletion>().let {
            if (respondWithJson) {
                it.copy(
                    candidates = listOfNotNull(
                        it.candidates.firstOrNull()?.let { candidate ->
                            candidate.copy(
                                content = candidate.content.copy(
                                    parts = listOf(GeminiChatMessagePart("{")) + candidate.content.parts,
                                ),
                            )
                        },
                    ),
                )
            } else {
                it
            }
        }
        .also { it.recordUsage() }
    }

    suspend fun chatCompletion(
        model: GeminiModelId = GeminiCompletionsModel.Gemini15Pro.modelId,
        prompt: String,
        topP: Float? = null,
        topK: Int? = null,
        temperature: Float? = null,
        respondWithJson: Boolean,
    ): GeminiChatCompletion = chatCompletion(
        model = model,
        messages = GeminiChatCompletionPromptParser.parse(prompt),
        topP = topP,
        topK = topK,
        temperature = temperature,
        respondWithJson = respondWithJson,
    )

    fun chatCompletions(
        model: GeminiModelId = GeminiCompletionsModel.Gemini15Pro.modelId,
        prompt: String,
        topP: Float? = null,
        topK: Int? = null,
        temperature: Float? = null,
        respondWithJson: Boolean,
    ): Flow<GeminiChatCompletion> = suspendedFlow fn@{
        val messageBundle = messageBundleForMessages(GeminiChatCompletionPromptParser.parse(prompt), respondWithJson)

        val request = GeminiChatCompletionRequest(
            systemInstructions = messageBundle.systemPrompt,
            contents = messageBundle.chatMessages,
            generationConfig = GeminiGenerationConfig(
                temperature = temperature,
                topP = topP,
                topK = topK,
            ),
        )

        val requestExecutor = client.instance.preparePost {
            url {
                path("${model.id}:streamGenerateContent")
            }
            parameter("alt", "sse")
            setBody(request)
            headers {
                append(HttpHeaders.CacheControl, "no-cache")
                append(HttpHeaders.Connection, "keep-alive")
            }
        }

        return@fn flow {
            requestExecutor.execute {
                streamEventsFrom(it, respondWithJson)
            }
        }
    }

    private fun messageBundleForMessages(
        messages: List<GeminiChatMessage>,
        respondWithJson: Boolean,
    ): GeminiMessageBundle {
        val (systemMessages, otherMessages) = messages.partition { it.role == GeminiChatRole.SYSTEM }
        val systemPrompt = systemMessages.joinToString("\n") { it.parts.joinToString("") }.nullIfBlank()?.let {
            GeminiChatMessage(
                parts = listOf(GeminiChatMessagePart(it)),
            )
        }
        val messagesStartingWithUser = otherMessages.dropWhile { it.role != GeminiChatRole.USER }
        val chatMessages = messagesStartingWithUser + if (respondWithJson) {
            listOf(
                GeminiChatMessage(
                    role = GeminiChatRole.ASSISTANT,
                    parts = listOf(
                        GeminiChatMessagePart("I will now output the JSON and then stop. Here is the JSON: {"),
                    ),
                ),
            )
        } else {
            emptyList()
        }
        return GeminiMessageBundle(
            systemPrompt = systemPrompt,
            chatMessages = chatMessages,
        )
    }
}

private data class GeminiMessageBundle(
    val systemPrompt: GeminiChatMessage? = null,
    val chatMessages: List<GeminiChatMessage>,
)

private suspend fun GeminiChatCompletion.recordUsage() = usageMetadata.record()

private suspend fun GeminiChatCompletionUsageMetadata.record() = withSpan(
    spanName = "GeminiCompletion",
    attributes = mapOf(
        "gemini.promptTokens" to promptTokenCount,
        "gemini.candidatesTokens" to candidatesTokenCount,
        "gemini.totalTokens" to totalTokenCount,
    ),
) {
    LOGGER.infoAsync(
        "gemini.promptTokens" to promptTokenCount,
        "gemini.candidatesTokens" to candidatesTokenCount,
        "gemini.totalTokens" to totalTokenCount,
    ) { "Gemini Completion" }
}
