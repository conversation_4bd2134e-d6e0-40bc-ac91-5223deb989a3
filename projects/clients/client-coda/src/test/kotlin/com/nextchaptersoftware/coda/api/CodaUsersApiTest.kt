package com.nextchaptersoftware.coda.api

import com.nextchaptersoftware.config.GlobalConfig
import com.sksamuel.hoplite.Secret
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled("For manual testing only")
class CodaUsersApiTest {
    private val codaApiProvider = CodaApiProvider(config = GlobalConfig.INSTANCE.providers.coda)

    private val apiKey = Secret(System.getenv("CODA_API_TOKEN"))
    private val organizationId = "org-8n3MIHl57r"
    private val workspaceId = "ws-4nUu6yD1BE"

    @Test
    fun listUsers() = runTest {
        codaApiProvider.usersApi.listUsers(
            organizationId = organizationId,
            workspaceId = workspaceId,
            accessToken = apiKey,
        ).also {
            assertThat(it.items).hasSize(2)
        }
    }
}
