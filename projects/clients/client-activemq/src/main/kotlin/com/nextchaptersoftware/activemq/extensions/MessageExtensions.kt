package com.nextchaptersoftware.activemq.extensions

import com.nextchaptersoftware.activemq.models.MessageProperties
import com.nextchaptersoftware.log.kotlin.errorSync
import jakarta.jms.BytesMessage
import jakarta.jms.Message
import jakarta.jms.Session
import jakarta.jms.TextMessage
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

object MessageExtensions {
    /**
     * WARNING: This is NOT an idempotent call if it is byte-based message
     * You will get serialization failures in those instances if you make multiple calls to this.
     */
    fun Message.body(): String {
        return when (this) {
            is TextMessage -> this.text

            is BytesMessage -> {
                val bytes = ByteArray(this.bodyLength.toInt())
                this.readBytes(bytes)
                String(bytes, charset = Charsets.UTF_8)
            }

            else -> {
                LOGGER.errorSync(
                    "class" to this::class.java.simpleName,
                ) { "Failed to parse jms message body" }
                error("Failed to parse jms message body")
            }
        }
    }

    fun Message.setProperties(properties: MessageProperties) {
        properties.apply(this)
    }

    fun Message.clone(session: Session): Message {
        val originalMessage = this
        val newMessage: Message = when (originalMessage) {
            is TextMessage -> {
                session.createTextMessage(originalMessage.text)
            }

            is BytesMessage -> {
                val content = ByteArray(originalMessage.bodyLength.toInt())
                originalMessage.readBytes(content)
                val newBytesMessage = session.createBytesMessage()
                newBytesMessage.writeBytes(content)
                newBytesMessage
            }

            else -> {
                throw IllegalArgumentException("Unsupported message type: ${originalMessage.javaClass.name}")
            }
        }

        // Copy message properties from the original message to the new message
        val propertyNames = originalMessage.propertyNames
        while (propertyNames.hasMoreElements()) {
            val propertyName = propertyNames.nextElement() as? String
            propertyName?.let {
                val propertyValue = originalMessage.getObjectProperty(propertyName)
                newMessage.setObjectProperty(propertyName, propertyValue)
            }
        }

        // Copy message attributes
        newMessage.jmsMessageID = originalMessage.jmsMessageID
        newMessage.jmsCorrelationID = originalMessage.jmsCorrelationID
        newMessage.jmsReplyTo = originalMessage.jmsReplyTo
        newMessage.jmsDestination = originalMessage.jmsDestination
        newMessage.jmsDeliveryMode = originalMessage.jmsDeliveryMode
        newMessage.jmsExpiration = originalMessage.jmsExpiration
        newMessage.jmsPriority = originalMessage.jmsPriority
        newMessage.jmsRedelivered = originalMessage.jmsRedelivered
        newMessage.jmsTimestamp = originalMessage.jmsTimestamp

        return newMessage
    }
}
