package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.scm.models.ScmCommentComponent
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.test.utils.TestUtils
import com.nextchaptersoftware.types.Hash
import io.ktor.http.Url
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabNoteTest {

    @Test
    fun asScmPrComment() {
        val raw = TestUtils.getResource(this, "/scm/gitlab/merge_request_discussions.json")

        val discussions = raw.decode<List<GitLabDiscussion>>()
        assertThat(discussions).hasSize(2)

        val discussion = discussions.single { it.notes.none(GitLabNote::system) }
        assertThat(discussion.notes).hasSize(1)

        val lineComment = discussion.notes[0].asScmPrComment(
            inReplyToId = 123,
            mergeRequestHtmlUrl = Url("https://gitlab.com/davidkwlam/gitlabproject/-/merge_requests/29"),
        ) as ScmPrComment.LineLevel
        assertThat(lineComment.id).isEqualTo(1339719908)
        assertThat(lineComment.comment.associatedCommentId).isEqualTo(123)
        assertThat(lineComment.comment.body).isEqualTo("Line-level comment")
        assertThat(lineComment.comment.bodyHash).isEqualTo(Hash.parse("af9dd4d73fa7f58684b906c12b02b467be1aa6ee"))
        assertThat(lineComment.comment.createdAt).isEqualTo(Instant.parse("2023-04-03T22:29:09.965Z"))
        assertThat(lineComment.comment.updatedAt).isEqualTo(Instant.parse("2023-04-03T22:29:09.966Z"))
        assertThat(lineComment.comment.htmlUrl).isEqualTo(
            Url("https://gitlab.com/davidkwlam/gitlabproject/-/merge_requests/29#note_1339719908"),
        )
        assertThat(lineComment.comment.id).isEqualTo(1339719908)
        assertThat(lineComment.pullRequest.pullRequestNumber).isEqualTo(29)
        assertThat(lineComment.pullRequest.pullRequestReviewId).isNull()
        assertThat(lineComment.comment.user).isEqualTo(
            ScmUser(
                externalId = "13651997",
                login = "richie-block",
                displayName = "Richie Bresnan",
                htmlUrl = Url("https://gitlab.com/richie-block"),
                avatarUrl = Url("https://gitlab.com/uploads/-/system/user/avatar/13651997/avatar.png"),
            ),
        )

        assertThat(lineComment.file.filePath).isEqualTo("README.md")
        assertThat(lineComment.file.commitSha).isEqualTo("c30f77e2e70834890f3838bf1e3a884e82f31356")
        assertThat(lineComment.file.originalCommitSha).isEqualTo("c30f77e2e70834890f3838bf1e3a884e82f31356")

        assertThat(lineComment.inline.startLine).isEqualTo(10)
        assertThat(lineComment.inline.originalStartLine).isEqualTo(10)
        assertThat(lineComment.inline.line).isEqualTo(12)
        assertThat(lineComment.inline.originalLine).isEqualTo(12)
        assertThat(lineComment.inline.side).isEqualTo(ScmCommentComponent.LineInterface.DiffSide.RIGHT)
        assertThat(lineComment.inline.diffHunk).isEqualTo("")
        assertThat(lineComment.inline.isOutdated).isFalse()
    }
}
