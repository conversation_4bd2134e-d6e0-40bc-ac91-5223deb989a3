package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.TestDataFactory
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource

object AzureDevOpsConnectionDataTest {

    val decoder = { text: String -> text.decode<AzureDevOpsConnectionData>() }

    class Resources : TestDataFactory<AzureDevOpsConnectionData>(
        {
            testData(
                file = "/scm/azure-devops/connection-data-matt.json",
                decoder = decoder,
            )
        },
        {
            testData(
                file = "/scm/azure-devops/connection-data-mrtn.json",
                decoder = decoder,
            )
        },
        {
            testData(
                file = "/scm/azure-devops/connection-data-richie.json",
                decoder = decoder,
            )
        },
    )

    @ParameterizedTest
    @ArgumentsSource(Resources::class)
    fun parse(make: () -> AzureDevOpsConnectionData) {
        val instance = make()
        assertThat(instance).isNotNull()
    }
}
