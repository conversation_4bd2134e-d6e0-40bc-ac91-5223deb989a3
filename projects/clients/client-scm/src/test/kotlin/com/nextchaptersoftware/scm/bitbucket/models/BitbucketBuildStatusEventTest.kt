package com.nextchaptersoftware.scm.bitbucket.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BitbucketBuildStatusEventTest {

    private val decoder = { text: String -> text.decode<BitbucketBuildStatusEvent>() }

    private fun test(
        file: String,
        test: (BitbucketBuildStatusEvent) -> Unit = {},
    ) {
        testData(
            file = file,
            decoder = decoder,
        ) {
            assertThat(it)
                .isNotNull
                .satisfies(test)
        }
    }

    @Test
    fun `build-1 -- in progress`() = test("/scm/bitbucket/build-1-in-progress.json") {
        assertThat(it.pullRequestNumber()).isEqualTo(4)
        assertThat(it.pipelineNumber()).isEqualTo(5)
    }

    @Test
    fun `build-2 -- failed`() = test("/scm/bitbucket/build-2-failed.json") {
        assertThat(it.pullRequestNumber()).isEqualTo(4)
        assertThat(it.pipelineNumber()).isEqualTo(5)
    }

    @Test
    fun `build-3 -- no refname`() = test("/scm/bitbucket/build-3-no-refname.json.gz") {
        assertThat(it.commitStatus.refname).isNull()
        assertThat(it.pullRequestNumber()).isNull()
        assertThat(it.pipelineNumber()).isNull()
    }

    @Test
    fun `build-4 -- state stopped`() = test("/scm/bitbucket/build-4-state-stopped.json.gz") {
        assertThat(it.commitStatus.state).isEqualTo(BitbucketCommitStatus.State.Stopped)
        assertThat(it.pullRequestNumber()).isNull()
        assertThat(it.pipelineNumber()).isEqualTo(270220)
    }

    @Test
    fun `build-5 -- commit minimal`() = test("/scm/bitbucket/build-5-minimal.json.gz") {
        assertThat(it.commitStatus.commit).isInstanceOf(BitbucketCommitMinimal::class.java)
        assertThat(it.pullRequestNumber()).isNull()
        assertThat(it.pipelineNumber()).isNull()
    }
}
