package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.test.utils.TestDataFactory
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource

object AzureDevOpsIdentityTest {

    class Resources : TestDataFactory<AzureDevOpsIdentity>(
        AzureDevOpsIdentities::Matt,
        AzureDevOpsIdentities::<PERSON>,
        AzureDevOpsIdentities::<PERSON>,
    )

    @ParameterizedTest
    @ArgumentsSource(Resources::class)
    fun parse(make: () -> AzureDevOpsIdentity) {
        val instance = make()
        assertThat(instance).isNotNull()
    }
}
