package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData

object AzureDevOpsProfiles {

    private val decoder = { text: String -> text.decode<AzureDevOpsProfile>() }

    val Martin = testData(
        file = "/scm/azure-devops/profile-martin-complete.json",
        decoder = decoder,
    )

    val MartinBasic = testData(
        file = "/scm/azure-devops/profile-martin-basic.json",
        decoder = decoder,
    )
}
