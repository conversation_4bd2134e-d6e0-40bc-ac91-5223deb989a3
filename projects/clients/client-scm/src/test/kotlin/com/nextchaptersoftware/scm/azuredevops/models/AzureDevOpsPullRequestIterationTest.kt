package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.TestDataFactory
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource

object AzureDevOpsPullRequestIterationTest {

    val decoder = { text: String -> text.decode<AzureDevOpsPullRequestIteration>() }

    class Resources : TestDataFactory<AzureDevOpsPullRequestIteration>(
        {
            testData(
                file = "/scm/azure-devops/pr-unblocked-no2-iteration-no1.json",
                decoder = decoder,
            )
        },
    )

    @ParameterizedTest
    @ArgumentsSource(Resources::class)
    fun parse(make: () -> AzureDevOpsPullRequestIteration) {
        val instance = make()
        assertThat(instance).isNotNull()
    }
}
