{"id": 327, "createdDate": 1717010938257, "user": {"name": "admin", "emailAddress": "<EMAIL>", "active": true, "displayName": "admin", "id": 3, "slug": "admin", "type": "NORMAL", "links": {"self": [{"href": "https://atlassian-test.secops.getunblocked.com:7990/users/admin"}]}}, "action": "COMMENTED", "commentAction": "ADDED", "comment": {"properties": {"repositoryId": 1}, "id": 26, "version": 0, "text": "this is a file level comment", "author": {"name": "admin", "emailAddress": "<EMAIL>", "active": true, "displayName": "admin", "id": 3, "slug": "admin", "type": "NORMAL", "links": {"self": [{"href": "https://atlassian-test.secops.getunblocked.com:7990/users/admin"}]}}, "createdDate": 1717010938238, "updatedDate": 1717010938238, "comments": [{"properties": {"repositoryId": 1}, "id": 27, "version": 0, "text": "plus a reply", "author": {"name": "admin", "emailAddress": "<EMAIL>", "active": true, "displayName": "admin", "id": 3, "slug": "admin", "type": "NORMAL", "links": {"self": [{"href": "https://atlassian-test.secops.getunblocked.com:7990/users/admin"}]}}, "createdDate": 1717010946454, "updatedDate": 1717010946454, "comments": [], "threadResolved": false, "severity": "NORMAL", "state": "OPEN", "permittedOperations": {"editable": true, "transitionable": true, "deletable": true}}], "threadResolved": false, "severity": "NORMAL", "state": "OPEN", "permittedOperations": {"editable": true, "transitionable": true, "deletable": true}}, "commentAnchor": {"fromHash": "7fbf7f7cc2e09ffd66898ab1b35030c9aacf3902", "toHash": "cfaf40ee37296853f0116d4c85b591d7c11744c1", "path": "README.md", "diffType": "EFFECTIVE", "orphaned": false}}