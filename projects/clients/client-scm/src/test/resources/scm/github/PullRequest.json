{"url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15", "id": 805635463, "node_id": "PR_kwDOGgjkzs4wBQWH", "html_url": "https://github.com/NextChapterSoftware/unblocked/pull/15", "diff_url": "https://github.com/NextChapterSoftware/unblocked/pull/15.diff", "patch_url": "https://github.com/NextChapterSoftware/unblocked/pull/15.patch", "issue_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/15", "number": 15, "state": "closed", "locked": false, "title": "Introduce Side Navigator and Frame", "user": {"login": "jef<PERSON>-<PERSON>", "id": 1553313, "node_id": "MDQ6VXNlcjE1NTMzMTM=", "avatar_url": "https://avatars.githubusercontent.com/u/1553313?v=4", "gravatar_id": "", "url": "https://api.github.com/users/jeffrey-ng", "html_url": "https://github.com/jeffrey-ng", "followers_url": "https://api.github.com/users/jeffrey-ng/followers", "following_url": "https://api.github.com/users/jeffrey-ng/following{/other_user}", "gists_url": "https://api.github.com/users/jeffrey-ng/gists{/gist_id}", "starred_url": "https://api.github.com/users/jeffrey-ng/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/jeffrey-ng/subscriptions", "organizations_url": "https://api.github.com/users/jeffrey-ng/orgs", "repos_url": "https://api.github.com/users/jeffrey-ng/repos", "events_url": "https://api.github.com/users/jeffrey-ng/events{/privacy}", "received_events_url": "https://api.github.com/users/jeffrey-ng/received_events", "type": "User", "site_admin": false}, "body": null, "created_at": "2021-12-17T19:45:26Z", "updated_at": "2021-12-20T21:55:30Z", "closed_at": "2021-12-20T21:55:29Z", "merged_at": "2021-12-20T21:55:29Z", "merge_commit_sha": "86f142318894fbb324c175586784a25482ff219c", "assignee": null, "assignees": [], "requested_reviewers": [], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15/commits", "review_comments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15/comments", "review_comment_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/15/comments", "statuses_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/statuses/11bc00e103dab0a80f39d9766276a03aab755893", "head": {"label": "NextChapterSoftware:jeff/navigator", "ref": "jeff/navigator", "sha": "11bc00e103dab0a80f39d9766276a03aab755893", "user": {"login": "NextChapterSoftware", "id": 91906527, "node_id": "O_kgDOBXph3w", "avatar_url": "https://avatars.githubusercontent.com/u/91906527?v=4", "gravatar_id": "", "url": "https://api.github.com/users/NextChapterSoftware", "html_url": "https://github.com/NextChapterSoftware", "followers_url": "https://api.github.com/users/NextChapterSoftware/followers", "following_url": "https://api.github.com/users/NextChapterSoftware/following{/other_user}", "gists_url": "https://api.github.com/users/NextChapterSoftware/gists{/gist_id}", "starred_url": "https://api.github.com/users/NextChapterSoftware/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/NextChapterSoftware/subscriptions", "organizations_url": "https://api.github.com/users/NextChapterSoftware/orgs", "repos_url": "https://api.github.com/users/NextChapterSoftware/repos", "events_url": "https://api.github.com/users/NextChapterSoftware/events{/privacy}", "received_events_url": "https://api.github.com/users/NextChapterSoftware/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 436790478, "node_id": "R_kgDOGgjkzg", "name": "unblocked", "full_name": "NextChapterSoftware/unblocked", "private": true, "owner": {"login": "NextChapterSoftware", "id": 91906527, "node_id": "O_kgDOBXph3w", "avatar_url": "https://avatars.githubusercontent.com/u/91906527?v=4", "gravatar_id": "", "url": "https://api.github.com/users/NextChapterSoftware", "html_url": "https://github.com/NextChapterSoftware", "followers_url": "https://api.github.com/users/NextChapterSoftware/followers", "following_url": "https://api.github.com/users/NextChapterSoftware/following{/other_user}", "gists_url": "https://api.github.com/users/NextChapterSoftware/gists{/gist_id}", "starred_url": "https://api.github.com/users/NextChapterSoftware/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/NextChapterSoftware/subscriptions", "organizations_url": "https://api.github.com/users/NextChapterSoftware/orgs", "repos_url": "https://api.github.com/users/NextChapterSoftware/repos", "events_url": "https://api.github.com/users/NextChapterSoftware/events{/privacy}", "received_events_url": "https://api.github.com/users/NextChapterSoftware/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/NextChapterSoftware/unblocked", "description": null, "fork": false, "url": "https://api.github.com/repos/NextChapterSoftware/unblocked", "forks_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/forks", "keys_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/teams", "hooks_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/hooks", "issue_events_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/events{/number}", "events_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/events", "assignees_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/assignees{/user}", "branches_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/branches{/branch}", "tags_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/tags", "blobs_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/refs{/sha}", "trees_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/statuses/{sha}", "languages_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/languages", "stargazers_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/stargazers", "contributors_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contributors", "subscribers_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/subscribers", "subscription_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/subscription", "commits_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/commits{/sha}", "git_commits_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/commits{/sha}", "comments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/comments{/number}", "issue_comment_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/comments{/number}", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/{+path}", "compare_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/merges", "archive_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/downloads", "issues_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues{/number}", "pulls_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls{/number}", "milestones_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/milestones{/number}", "notifications_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/labels{/name}", "releases_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/releases{/id}", "deployments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/deployments", "created_at": "2021-12-09T23:27:40Z", "updated_at": "2022-01-28T23:47:10Z", "pushed_at": "2022-02-24T18:55:27Z", "git_url": "git://github.com/NextChapterSoftware/unblocked.git", "ssh_url": "**************:NextChapterSoftware/unblocked.git", "clone_url": "https://github.com/NextChapterSoftware/unblocked.git", "svn_url": "https://github.com/NextChapterSoftware/unblocked", "homepage": null, "size": 8142, "stargazers_count": 1, "watchers_count": 1, "language": "<PERSON><PERSON><PERSON>", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": false, "has_pages": false, "forks_count": 0, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 9, "license": null, "allow_forking": false, "is_template": false, "topics": [], "visibility": "private", "forks": 0, "open_issues": 9, "watchers": 1, "default_branch": "main"}}, "base": {"label": "NextChapterSoftware:main", "ref": "main", "sha": "0a96fef00a5ac9c0b2174e3ebe28f9f7c41163f2", "user": {"login": "NextChapterSoftware", "id": 91906527, "node_id": "O_kgDOBXph3w", "avatar_url": "https://avatars.githubusercontent.com/u/91906527?v=4", "gravatar_id": "", "url": "https://api.github.com/users/NextChapterSoftware", "html_url": "https://github.com/NextChapterSoftware", "followers_url": "https://api.github.com/users/NextChapterSoftware/followers", "following_url": "https://api.github.com/users/NextChapterSoftware/following{/other_user}", "gists_url": "https://api.github.com/users/NextChapterSoftware/gists{/gist_id}", "starred_url": "https://api.github.com/users/NextChapterSoftware/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/NextChapterSoftware/subscriptions", "organizations_url": "https://api.github.com/users/NextChapterSoftware/orgs", "repos_url": "https://api.github.com/users/NextChapterSoftware/repos", "events_url": "https://api.github.com/users/NextChapterSoftware/events{/privacy}", "received_events_url": "https://api.github.com/users/NextChapterSoftware/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 436790478, "node_id": "R_kgDOGgjkzg", "name": "unblocked", "full_name": "NextChapterSoftware/unblocked", "private": true, "owner": {"login": "NextChapterSoftware", "id": 91906527, "node_id": "O_kgDOBXph3w", "avatar_url": "https://avatars.githubusercontent.com/u/91906527?v=4", "gravatar_id": "", "url": "https://api.github.com/users/NextChapterSoftware", "html_url": "https://github.com/NextChapterSoftware", "followers_url": "https://api.github.com/users/NextChapterSoftware/followers", "following_url": "https://api.github.com/users/NextChapterSoftware/following{/other_user}", "gists_url": "https://api.github.com/users/NextChapterSoftware/gists{/gist_id}", "starred_url": "https://api.github.com/users/NextChapterSoftware/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/NextChapterSoftware/subscriptions", "organizations_url": "https://api.github.com/users/NextChapterSoftware/orgs", "repos_url": "https://api.github.com/users/NextChapterSoftware/repos", "events_url": "https://api.github.com/users/NextChapterSoftware/events{/privacy}", "received_events_url": "https://api.github.com/users/NextChapterSoftware/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/NextChapterSoftware/unblocked", "description": null, "fork": false, "url": "https://api.github.com/repos/NextChapterSoftware/unblocked", "forks_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/forks", "keys_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/teams", "hooks_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/hooks", "issue_events_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/events{/number}", "events_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/events", "assignees_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/assignees{/user}", "branches_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/branches{/branch}", "tags_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/tags", "blobs_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/refs{/sha}", "trees_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/statuses/{sha}", "languages_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/languages", "stargazers_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/stargazers", "contributors_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contributors", "subscribers_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/subscribers", "subscription_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/subscription", "commits_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/commits{/sha}", "git_commits_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/git/commits{/sha}", "comments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/comments{/number}", "issue_comment_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/comments{/number}", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/{+path}", "compare_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/merges", "archive_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/downloads", "issues_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues{/number}", "pulls_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls{/number}", "milestones_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/milestones{/number}", "notifications_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/labels{/name}", "releases_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/releases{/id}", "deployments_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/deployments", "created_at": "2021-12-09T23:27:40Z", "updated_at": "2022-01-28T23:47:10Z", "pushed_at": "2022-02-24T18:55:27Z", "git_url": "git://github.com/NextChapterSoftware/unblocked.git", "ssh_url": "**************:NextChapterSoftware/unblocked.git", "clone_url": "https://github.com/NextChapterSoftware/unblocked.git", "svn_url": "https://github.com/NextChapterSoftware/unblocked", "homepage": null, "size": 8142, "stargazers_count": 1, "watchers_count": 1, "language": "<PERSON><PERSON><PERSON>", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": false, "has_pages": false, "forks_count": 0, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 9, "license": null, "allow_forking": false, "is_template": false, "topics": [], "visibility": "private", "forks": 0, "open_issues": 9, "watchers": 1, "default_branch": "main"}}, "_links": {"self": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15"}, "html": {"href": "https://github.com/NextChapterSoftware/unblocked/pull/15"}, "issue": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/15"}, "comments": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/issues/15/comments"}, "review_comments": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15/comments"}, "review_comment": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/pulls/15/commits"}, "statuses": {"href": "https://api.github.com/repos/NextChapterSoftware/unblocked/statuses/11bc00e103dab0a80f39d9766276a03aab755893"}}, "author_association": "CONTRIBUTOR", "auto_merge": null, "active_lock_reason": null, "merged": true, "mergeable": null, "rebaseable": null, "mergeable_state": "unknown", "merged_by": {"login": "jef<PERSON>-<PERSON>", "id": 1553313, "node_id": "MDQ6VXNlcjE1NTMzMTM=", "avatar_url": "https://avatars.githubusercontent.com/u/1553313?v=4", "gravatar_id": "", "url": "https://api.github.com/users/jeffrey-ng", "html_url": "https://github.com/jeffrey-ng", "followers_url": "https://api.github.com/users/jeffrey-ng/followers", "following_url": "https://api.github.com/users/jeffrey-ng/following{/other_user}", "gists_url": "https://api.github.com/users/jeffrey-ng/gists{/gist_id}", "starred_url": "https://api.github.com/users/jeffrey-ng/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/jeffrey-ng/subscriptions", "organizations_url": "https://api.github.com/users/jeffrey-ng/orgs", "repos_url": "https://api.github.com/users/jeffrey-ng/repos", "events_url": "https://api.github.com/users/jeffrey-ng/events{/privacy}", "received_events_url": "https://api.github.com/users/jeffrey-ng/received_events", "type": "User", "site_admin": false}, "comments": 0, "review_comments": 14, "maintainer_can_modify": false, "commits": 15, "additions": 410, "deletions": 21, "changed_files": 18}