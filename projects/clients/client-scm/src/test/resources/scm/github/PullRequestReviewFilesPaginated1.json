[{"sha": "48960dea014ccafbe2a26ff9eb65ff8b42469454", "filename": ".github/workflows/ci.yml", "status": "modified", "additions": 0, "deletions": 2, "changes": 2, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/.github/workflows/ci.yml", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/.github/workflows/ci.yml", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/.github/workflows/ci.yml?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -44,7 +44,6 @@ jobs:\n           token: ${{ secrets.GITHUB_TOKEN }}\n           projectToken: ${{ secrets.CHROMATIC_VSCODE_PROJECT_TOKEN }}\n           storybookBuildDir: .storybook-dist/dist\n-          exitZeroOnChanges: false\n           workingDir: vscode\n \n   web_test:\n@@ -71,5 +70,4 @@ jobs:\n           token: ${{ secrets.GITHUB_TOKEN }}\n           projectToken: ${{ secrets.CHROMATIC_WEB_PROJECT_TOKEN }}\n           storybookBuildDir: .storybook-dist/dist\n-          exitZeroOnChanges: false\n           workingDir: web"}, {"sha": "73f68d51b75a4b752d94b96789be2911672a0c32", "filename": "web/.storybook/main.js", "status": "modified", "additions": 10, "deletions": 0, "changes": 10, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/.storybook/main.js", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/.storybook/main.js", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/.storybook/main.js?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -6,10 +6,20 @@ module.exports = {\n     addons: [\n         '@storybook/addon-links',\n         '@storybook/addon-essentials',\n+        '@storybook/addon-interactions',\n         { name: '@storybook/addon-postcss', options: { postcssLoaderOptions: { implementation: require('postcss') } } },\n     ],\n     core: {\n         builder: 'webpack5',\n     },\n+    webpackFinal: (config) => {\n+        config.resolve.alias = {\n+            ...config.resolve.alias,\n+            '@components': path.resolve(__dirname, '../src/components'),\n+            '@views': path.resolve(__dirname, '../src/views'),\n+            '@utils': path.resolve(__dirname, '../src/utils'),\n+        };\n+        return config;\n+    },\n     // staticDirs: ['../.storybook-dist/assets'],\n };"}, {"sha": "2f9b516f92f791cdce89d55d5fc3b6d2c0bc2298", "filename": "web/.storybook/preview.js", "status": "modified", "additions": 16, "deletions": 1, "changes": 17, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/.storybook/preview.js", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/.storybook/preview.js", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/.storybook/preview.js?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -1,5 +1,5 @@\n import '../src/style.css';\n-\n+import { INITIAL_VIEWPORTS } from '@storybook/addon-viewport';\n export const parameters = {\n     actions: { argTypesRegex: '^on[A-Z].*' },\n     controls: {\n@@ -8,6 +8,21 @@ export const parameters = {\n             date: /Date$/,\n         },\n     },\n+    backgrounds: {\n+        values: [\n+            {\n+                name: 'white',\n+                value: '#FFFFFF',\n+            },\n+            {\n+                name: 'navy',\n+                value: '#2B345F',\n+            },\n+        ],\n+    },\n+    viewport: {\n+        viewports: INITIAL_VIEWPORTS,\n+    },\n     // disables snapshotting on a global level\n     chromatic: { disableSnapshot: true },\n };"}, {"sha": "4a843feab60f19c0d4538ae7b83e0796f7b6d07b", "filename": "web/src/app.tsx", "status": "modified", "additions": 0, "deletions": 3, "changes": 3, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/app.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/app.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/app.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -1,4 +1,3 @@\n-import { Button } from './components';\n export interface HelloWorldProps {\n     userName: string;\n     lang: string;\n@@ -8,8 +7,6 @@ const App = (props: HelloWorldProps) => (\n         <h1>\n             Hi {props.userName} from React! Welcome to {props.lang}!\n         </h1>\n-\n-        <Button>HELLO</Button>\n     </div>\n );\n "}, {"sha": "931ed77e36631a5c41b6a39d325d61d477f6c1ee", "filename": "web/src/components/button/button.tsx", "status": "modified", "additions": 1, "deletions": 4, "changes": 5, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/button/button.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/button/button.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/button/button.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -19,17 +19,14 @@ export const Button = ({ icon, onClick, type = 'primary', isDisabled = false, ch\n     const buttonTypeClass = useMemo(() => {\n         switch (type) {\n             case 'primary':\n-                return 'text-white bg-sea-foam-100 hover:bg-sea-foam-hover disabled:bg-primary-button-disabled disabled:bg-white';\n+                return 'text-white-100 bg-sea-foam-100 hover:bg-sea-foam-hover disabled:bg-primary-button-disabled disabled:bg-white';\n             case 'secondary':\n                 // TODO: Fix disabled state\n                 return 'text-space-cadet-100 bg-blue-crayola-6 hover:bg-blue-crayola-15 active:text-white active:bg-blue-crayola-100 disabled:bg-secondary-button-disabled disabled:bg-white disabled:opacity-30';\n-\n-            // return 'text-space-cadet-100 bg-blue-crayola-6 hover:bg-blue-crayola-15 active:text-white active:bg-blue-crayola-100 disabled:bg-space-cadet-3 disabled:opacity-30';\n         }\n     }, [type]);\n \n     const buttonClassNames = `${baseClasses} ${buttonTypeClass}`;\n-    console.log(icon);\n \n     return (\n         <button onClick={onClick} disabled={isDisabled} className={buttonClassNames}>"}, {"sha": "113a5748f226c4b703cd24e13aba3e6e64a5a75e", "filename": "web/src/components/button/index.ts", "status": "removed", "additions": 0, "deletions": 2, "changes": 2, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/0a96fef00a5ac9c0b2174e3ebe28f9f7c41163f2/web/src/components/button/index.ts", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/0a96fef00a5ac9c0b2174e3ebe28f9f7c41163f2/web/src/components/button/index.ts", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/button/index.ts?ref=0a96fef00a5ac9c0b2174e3ebe28f9f7c41163f2", "patch": "@@ -1,2 +0,0 @@\n-import { Button } from './button';\n-export default Button;"}, {"sha": "0aae58a434fb095bd8d83c4890a71ec08f2afecd", "filename": "web/src/components/filterField/filterField.tsx", "status": "modified", "additions": 2, "deletions": 2, "changes": 4, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/filterField/filterField.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/filterField/filterField.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/filterField/filterField.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -46,7 +46,7 @@ export const FilterField = ({ selectedValues, updatedValues, placeholder, values\n                         >\n                             {/* <div className=\"relative\"> */}\n                             <span className=\"inline-block w-full rounded-md\">\n-                                <Listbox.Button className=\"cursor-default relative w-full rounded-md bg-white pl-3 pr-10 py-2 text-left focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition ease-in-out duration-150 sm:text-sm sm:leading-5\">\n+                                <Listbox.Button className=\"cursor-default relative w-full rounded-md bg-white-100 pl-3 pr-10 py-2 text-left focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition ease-in-out duration-150 sm:text-sm sm:leading-5\">\n                                     {!selectedValues.length && placeholder}\n                                     <div className=\"space-x-1\">\n                                         {selectedValues.map((value) => (\n@@ -76,7 +76,7 @@ export const FilterField = ({ selectedValues, updatedValues, placeholder, values\n                                 leave=\"transition ease-in duration-100\"\n                                 leaveFrom=\"opacity-100\"\n                                 leaveTo=\"opacity-0\"\n-                                className=\"mt-1 w-full rounded-md bg-white\"\n+                                className=\"mt-1 w-full rounded-md bg-white-100\"\n                             >\n                                 <Listbox.Options\n                                     static"}, {"sha": "c5b32316a2d3586f6943407307df6c185aa8defe", "filename": "web/src/components/navigator/navigationRow.tsx", "status": "added", "additions": 68, "deletions": 0, "changes": 68, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigationRow.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigationRow.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/navigator/navigationRow.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -0,0 +1,68 @@\n+import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\n+import { IconDefinition } from '@fortawesome/fontawesome-svg-core';\n+import { classNames } from '@utils';\n+\n+// Workaround for different icon states.\n+// Alternative would be to introduce dynamic imports of FA Icons to allow for typealias icon props\n+// https://fontawesome.com/v6.0/docs/web/use-with/react/add-icons#dynamic-icon-importing\n+interface NavigationIcons {\n+    base: IconDefinition;\n+    active: IconDefinition;\n+}\n+// TODO: Introduce abstracted Icon protocol. Will allow for both font awesome Icons and local icons\n+export interface NavigationItem {\n+    name: string;\n+    href: string;\n+    icon?: NavigationIcons;\n+    count?: number;\n+    current?: boolean;\n+}\n+\n+export interface NavigationRowProps {\n+    item: NavigationItem;\n+}\n+\n+export const NavigationRow = ({ item }: NavigationRowProps) => {\n+    return (\n+        <a\n+            key={item.name}\n+            href={item.href}\n+            className={classNames(\n+                item.current\n+                    ? 'bg-sea-foam-100 font-bold text-white-100'\n+                    : 'text-white-70 hover:bg-white-10 hover: active:bg-sea-foam-100 active:text-white-100',\n+                'group flex items-center px-2 py-2 font-effra text-body leading-body rounded-md '\n+            )}\n+        >\n+            {item.icon && (\n+                <span className=\"flex content-center justify-center mr-2 w-4\">\n+                    {item.current ? (\n+                        <FontAwesomeIcon className=\"text-body inline-block \" icon={item.icon.active} />\n+                    ) : (\n+                        <>\n+                            <FontAwesomeIcon\n+                                className=\"text-body inline-block group-active:hidden\"\n+                                icon={item.icon.base}\n+                            />\n+                            <FontAwesomeIcon\n+                                className=\"text-body hidden group-active:inline-block\"\n+                                icon={item.icon.active}\n+                            />\n+                        </>\n+                    )}\n+                </span>\n+            )}\n+            <span className=\"truncate\">{item.name}</span>\n+            {item.count ? (\n+                <span\n+                    className={classNames(\n+                        item.current ? 'bg-space-cadet-100' : 'bg-white-10 text-white-100',\n+                        'ml-auto inline-block px-2 text-xs leading-snug rounded-full group-active:bg-space-cadet-100'\n+                    )}\n+                >\n+                    {item.count}\n+                </span>\n+            ) : null}\n+        </a>\n+    );\n+};"}, {"sha": "3a1626d777effc72d21dbb07f0cedc6eb1fd7b22", "filename": "web/src/components/navigator/navigationSection.tsx", "status": "added", "additions": 21, "deletions": 0, "changes": 21, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigationSection.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigationSection.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/navigator/navigationSection.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -0,0 +1,21 @@\n+import { NavigationItem, NavigationRow } from './navigationRow';\n+export interface NavigationSectionItem {\n+    title: string;\n+    items: NavigationItem[];\n+}\n+\n+export interface NavigationSectionProps {\n+    section: NavigationSectionItem;\n+}\n+export const NavigationSection = ({ section }: NavigationSectionProps) => {\n+    return (\n+        <section className=\"mb-6\">\n+            <h3 className=\"text-white-100 font-effra font-bold text-body leading-body mb-3\" id={section.title}>\n+                {section.title}\n+            </h3>\n+            {section.items.map((item) => (\n+                <NavigationRow key={item.name} item={item} />\n+            ))}\n+        </section>\n+    );\n+};"}, {"sha": "243a81485356cdbec6e1aa7550e300a297841b34", "filename": "web/src/components/navigator/navigator.stories.tsx", "status": "added", "additions": 161, "deletions": 0, "changes": 161, "blob_url": "https://github.com/NextChapterSoftware/unblocked/blob/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigator.stories.tsx", "raw_url": "https://github.com/NextChapterSoftware/unblocked/raw/11bc00e103dab0a80f39d9766276a03aab755893/web/src/components/navigator/navigator.stories.tsx", "contents_url": "https://api.github.com/repos/NextChapterSoftware/unblocked/contents/web/src/components/navigator/navigator.stories.tsx?ref=11bc00e103dab0a80f39d9766276a03aab755893", "patch": "@@ -0,0 +1,161 @@\n+import { ComponentMeta, ComponentStory } from '@storybook/react';\n+import {\n+    faBars,\n+    faCrown,\n+    faFlag,\n+    faHandPaper,\n+    faMapPin,\n+    faSparkles,\n+    faUnlink,\n+    faUser,\n+    faWaterRise,\n+} from '@fortawesome/pro-regular-svg-icons';\n+import {\n+    faBars as faBarsSolid,\n+    faCrown as faCrownSolid,\n+    faFlag as faFlagSolid,\n+    faHandPaper as faHandPaperSolid,\n+    faMapPin as faMapPinSolid,\n+    faSparkles as faSparklesSolid,\n+    faUnlink as faUnlinkSolid,\n+    faUser as faUserSolid,\n+    faWaterRise as faWaterRiseSolid,\n+} from '@fortawesome/pro-solid-svg-icons';\n+import { userEvent, within } from '@storybook/testing-library';\n+import { NavigationSectionItem } from './navigationSection';\n+import { Navigator } from './navigator';\n+\n+// More on default export: https://storybook.js.org/docs/react/writing-stories/introduction#default-export\n+export default {\n+    title: 'Web/Navigation',\n+    component: Navigator,\n+    parameters: {\n+        layout: 'fullscreen',\n+        chromatic: { disableSnapshot: false },\n+    },\n+} as ComponentMeta<typeof Navigator>;\n+\n+// More on component templates: https://storybook.js.org/docs/react/writing-stories/introduction#using-args\n+const Template: ComponentStory<typeof Navigator> = (args) => <Navigator {...args} />;\n+\n+const sections: NavigationSectionItem[] = [\n+    {\n+        title: 'My Conversations',\n+        items: [\n+            {\n+                name: 'All',\n+                href: '#',\n+                icon: {\n+                    base: faBars,\n+                    active: faBarsSolid,\n+                },\n+                count: 15,\n+                current: true,\n+            },\n+            {\n+                name: \"Questions I've asked\",\n+                href: '#',\n+                icon: {\n+                    base: faHandPaper,\n+                    active: faHandPaperSolid,\n+                },\n+            },\n+            {\n+                name: 'Questions for me',\n+                href: '#',\n+                icon: {\n+                    base: faCrown,\n+                    active: faCrownSolid,\n+                },\n+                count: 2,\n+            },\n+            {\n+                name: 'Bookmarked',\n+                href: '#',\n+                icon: {\n+                    base: faFlag,\n+                    active: faFlagSolid,\n+                },\n+            },\n+        ],\n+    },\n+    {\n+        title: 'Discover',\n+        items: [\n+            {\n+                name: 'All',\n+                href: '#',\n+                icon: {\n+                    base: faBars,\n+                    active: faBarsSolid,\n+                },\n+            },\n+            {\n+                name: \"Near areas you're coding\",\n+                href: '#',\n+                icon: {\n+                    base: faMapPin,\n+                    active: faMapPinSolid,\n+                },\n+            },\n+            {\n+                name: 'New conversations',\n+                href: '#',\n+                icon: {\n+                    base: faSparkles,\n+                    active: faSparklesSolid,\n+                },\n+            },\n+            {\n+                name: 'Starting to swell',\n+                href: '#',\n+                icon: {\n+                    base: faWaterRise,\n+                    active: faWaterRiseSolid,\n+                },\n+            },\n+            {\n+                name: 'Group conversations',\n+                href: '#',\n+                icon: {\n+                    base: faUser,\n+                    active: faUserSolid,\n+                },\n+            },\n+            {\n+                name: 'Detached',\n+                href: '#',\n+                icon: {\n+                    base: faUnlink,\n+                    active: faUnlinkSolid,\n+                },\n+            },\n+        ],\n+    },\n+];\n+\n+export const NavigatorStory = Template.bind({});\n+NavigatorStory.args = {\n+    sections,\n+    children: <div>Children</div>,\n+};\n+\n+export const MobileNavigatorStory = Template.bind({});\n+MobileNavigatorStory.args = {\n+    sections,\n+    children: <div>Mobile Children</div>,\n+};\n+MobileNavigatorStory.parameters = {\n+    viewport: {\n+        defaultViewport: 'iphone12',\n+    },\n+    // Set the viewports in Chromatic at a story level.\n+    chromatic: { viewports: [390] },\n+};\n+\n+MobileNavigatorStory.play = async ({ canvasElement }) => {\n+    // Need to wait for viewport to change to mobile\n+    const canvas = within(canvasElement);\n+    const button = await canvas.findByRole('button', { name: 'menu' });\n+    await userEvent.click(button);\n+};"}]