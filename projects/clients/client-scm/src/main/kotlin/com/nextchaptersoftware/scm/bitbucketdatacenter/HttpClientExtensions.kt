package com.nextchaptersoftware.scm.bitbucketdatacenter

import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterApplication
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get

internal suspend fun HttpClient.bitbucketDataCenterApplication(
    configure: io.ktor.client.request.HttpRequestBuilder.() -> Unit = {},
) = this
    .get("application-properties", configure)
    .body<BitbucketDataCenterApplication>()
