package com.nextchaptersoftware.scm.azuredevops.models

import kotlinx.serialization.Serializable

@Serializable
data class AzureDevOpsGraphUserEntitlement(
    val id: String,
    val user: AzureDevOpsGraphUser,
    val projectEntitlements: List<AzureDevOpsProjectEntitlement>,
) {
    fun asScmUser(
        avatar: AzureDevOpsGraphAvatar? = null,
    ) = user.asScmUser(
        avatar = avatar,
    )
}
