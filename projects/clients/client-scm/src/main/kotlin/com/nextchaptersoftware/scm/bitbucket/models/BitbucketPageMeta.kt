package com.nextchaptersoftware.scm.bitbucket.models

import io.ktor.http.Url
import kotlin.reflect.full.memberProperties
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
internal data class BitbucketPageMeta(
    val pagelen: Int,
    val size: Int,
    val page: Int,

    @Contextual
    val next: Url? = null,

    @Contextual
    val previous: Url? = null,
) {
    companion object {
        val FIELD_NAMES by lazy {
            BitbucketPageMeta::class.memberProperties.map { it.name }.toTypedArray()
        }
    }
}
