@file:Suppress("UnusedPrivateMember")

package com.nextchaptersoftware.scm.azuredevops

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.kotlinx.coroutines.flow.asFlatItemsFlow
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory.makeScmHttpClient
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.azuredevops.models.RepoExtensions.accountName
import com.nextchaptersoftware.scm.azuredevops.models.RepoExtensions.projectName
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmGitCredentials
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.FlowExtensions.takeOnNotNull
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant

internal class AzureDevOpsRepoApi(
    private val tokenProvider: ScmUserTokenProvider,
    repo: Repo,
    identityId: IdentityId?,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmRepoApi {

    private val client = AzureDevOpsClient(
        client = makeScmHttpClient(
            tokenProvider = tokenProvider,
            baseApiUrl = baseApiUrl,
            allowRedirects = false,
            clientEngine = clientEngine,
        ),
        identityId = identityId,
    )

    private val accountName = repo.accountName()
    private val projectName = repo.projectName()
    private val repositoryId = repo.externalId

    override suspend fun repo(): ScmRepository {
        return client.azureDevOpsRepository(
            organization = accountName,
            project = projectName,
            repositoryId = repositoryId,
        )
            .asScmRepository
    }

    override suspend fun headCommitShaOrNull(): Hash? {
        return headCommitOrNull()?.sha
    }

    override suspend fun headCommitOrNull(): ScmCommit? {
        return client.azureDevOpsCommits(
            organization = accountName,
            projectName = projectName,
            repositoryId = repositoryId,
            top = 1,
            showOldestCommitsFirst = true,
        )
        .asFlatItemsFlow()
        .toList()
        .firstOrNull()
        ?.asScmCommit
    }

    override suspend fun pullRequest(pullRequestNumber: Int): ScmPullRequest {
        return client.azureDevOpsPullRequest(
            organization = accountName,
            project = projectName,
            pullRequestNumber = pullRequestNumber,
        )
            .asScmPullRequest()
    }

    override suspend fun pullRequestDiff(
        pullRequestNumber: Int,
        base: String?,
        head: String?,
    ): String {
        TODO("Not yet implemented")
    }

    override suspend fun latestPullRequest(): ScmPullRequest? {
        TODO("Not yet implemented")
    }

    override fun allIssues(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssue>> {
        TODO("Not yet implemented")
    }

    override fun allIssueComments(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssueComment>> {
        TODO("Not yet implemented")
    }

    override fun allPullRequests(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmPullRequest>> {
        val url = client.azureDevOpsPullRequestsUrl(
            organization = accountName,
            project = projectName,
            repositoryId = repositoryId,
            status = "all",
        )

        return client.azureDevOpsPullRequests(
            url = url.build(),
            continuationToken = initialBatchUrl?.parameters?.get("continuationToken"),
        )
        .map { page ->
            page.items
                .map {
                    val lastContentUpdatedDate = client.azureDevOpsPullRequestThreads(
                        organization = accountName,
                        project = projectName,
                        repository = repositoryId,
                        pullRequestId = it.pullRequestId,
                    )
                        .asFlatItemsFlow()
                        .toList()
                        .mapNotNull { it.lastContentUpdatedDate }
                        .maxOrNull()

                    it.asScmPullRequest(
                        lastContentUpdatedDate = lastContentUpdatedDate,
                    )
                }
                // ADO does not support querying by "modified after this date"
                // so we will post-filter those that have been already processed
                .filter { it.updatedAt > (since ?: Instant.DISTANT_PAST) }
                .let {
                    HttpClientBatch(
                        items = it,
                        nextCursor = page.continuationToken?.let { continuationToken ->
                            url.apply {
                                parameters["continuationToken"] = continuationToken
                            }.build()
                        },
                    )
                }
        }
    }

    /**
     * @see pullRequestAllComments
     */
    override fun allPullRequestComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment>> {
        // azure devops does not support loading comments globally
        return emptyFlow()
    }

    override fun allPullRequestCodeComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
        return emptyFlow() // FIXME: mrtn
    }

    override fun pullRequestFiles(pullRequestNumber: Int, maxItems: Int?): Flow<ScmPullRequestFile> {
        return client.azureDevOpsPullRequestIterations(
            organization = accountName,
            project = projectName,
            repository = repositoryId,
            pullRequestId = pullRequestNumber,
        )
            .asFlatItemsFlow()
            .map { iteration ->
                client.azureDevOpsPullRequestIterationChanges(
                    organization = accountName,
                    project = projectName,
                    repository = repositoryId,
                    pullRequestId = pullRequestNumber,
                    iterationId = iteration.id,
                )
                .asScmPullRequestFiles()
            }
            .asFlatItemsFlow()
            .takeOnNotNull(maxItems)
    }

    override fun pullRequestReviews(pullRequestNumber: Int): Flow<ScmPullRequestReview> {
        return emptyFlow() // FIXME: mrtn
    }

    override suspend fun fileContents(path: String, ref: String?): String {
        TODO("Not yet implemented")
    }

    override fun pullRequestAllComments(pullRequestNumber: Int): Flow<ScmPrComment> {
        return client.azureDevOpsPullRequestThreads(
            organization = accountName,
            project = projectName,
            repository = repositoryId,
            pullRequestId = pullRequestNumber,
        )
        .asFlatItemsFlow()
        .map { it.comments }
        .asFlatItemsFlow()
        .filterNot { it.isSystem }
        .filterNot { it.isDeleted }
        .map {
            it.asScmPrComment(
                accountName = accountName,
                pullRequestId = pullRequestNumber,
            )
        }
    }

    override fun fileCommits(path: String, maxItems: Int?): Flow<ScmCommit> {
        TODO("Not yet implemented")
    }

    override fun commitsBetweenCommits(base: Hash, head: Hash?, maxItems: Int?): Flow<ScmCommit> {
        TODO("Not yet implemented")
    }

    override fun commitsBetweenDates(since: Instant, until: Instant?, maxItems: Int?, authorUsername: String?): Flow<ScmCommit> {
        TODO("Not yet implemented")
    }

    override suspend fun commit(sha: Hash): ScmCommit {
        return client.azureDevOpsCommit(
            organization = accountName,
            project = projectName,
            repository = repositoryId,
            sha = sha,
        )
            .asScmCommit
    }

    override fun commits(author: String?, includeDiffs: Boolean, maxItems: Int?): Flow<ScmCommit> {
        TODO("Not yet implemented")
    }

    override fun contributorStats(): Flow<ScmContributorStats> {
        TODO("Not yet implemented")
    }

    override suspend fun commitDiff(sha: Hash): String? {
        TODO("Not yet implemented")
    }

    override suspend fun markdown(text: String): String {
        TODO("Not yet implemented")
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun gitCredentials(scmTokenConfig: ScmTokenConfig?): ScmGitCredentials {
        return ScmGitCredentials.Bearer(
            token = tokenProvider.getTokens(leeway = scmTokenConfig?.leeway).accessToken,
        )
    }

    override fun close() {
        client.close()
    }
}
