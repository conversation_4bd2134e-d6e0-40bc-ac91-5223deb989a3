package com.nextchaptersoftware.scm.github.models

import com.nextchaptersoftware.scm.models.CiBuildRunJobLegacy
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitHubWorkflowJob(
    val id: Long,
    val name: String,

    @SerialName("run_id")
    val runId: Long,

    @SerialName("run_attempt")
    val runAttempt: Long,

    @Contextual
    @SerialName("html_url")
    val htmlUrl: Url,

    @SerialName("url")
    @Contextual
    val apiUrl: Url,

    val status: GitHubCheckRunStatus,
    val conclusion: GitHubCheckRunConclusion? = null,

    @SerialName("created_at")
    val createdAt: Instant? = null,

    @SerialName("started_at")
    val startedAt: Instant? = null,

    @SerialName("completed_at")
    val completedAt: Instant? = null,
) {
    fun asCiBuildRunJobLegacy(
        output: Flow<String>? = null,
    ) = CiBuildRunJobLegacy(
        id = id.toString(),
        displayName = name,
        htmlUrl = htmlUrl,
        status = status.asCiStatusLegacy(),
        result = conclusion.asCiResultLegacy(),
        startedAt = startedAt,
        completedAt = completedAt,
        output = output,
    )
}
