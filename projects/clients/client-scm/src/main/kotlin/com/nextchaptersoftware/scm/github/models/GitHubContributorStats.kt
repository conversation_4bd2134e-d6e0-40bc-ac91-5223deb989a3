package com.nextchaptersoftware.scm.github.models

import com.nextchaptersoftware.scm.models.ScmContributor
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmContributorTotals
import com.nextchaptersoftware.scm.models.ScmContributorWeeklyStats
import io.ktor.http.Url
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitHubContributorStats(
    @SerialName("author")
    val author: GitHubContributor? = null,
    @SerialName("total")
    val totalCommits: Int,
    @SerialName("weeks")
    val weeklyStats: List<GitHubWeeklyContributorStats>,
) {
    @Serializable
    data class GitHubContributor(
        @SerialName("id")
        val id: Long? = null,

        @SerialName("login")
        val login: String? = null,

        @SerialName("name")
        val name: String? = null,

        @Contextual
        @SerialName("html_url")
        val htmlUrl: Url? = null,
    ) {
        val asScmContributor: ScmContributor?
            get() = ScmContributor(
                externalId = id?.toString(),
                login = login ?: "Unknown",
                displayName = name,
                htmlUrl = htmlUrl,
            )
    }
}

@Serializable
data class GitHubWeeklyContributorStats(
    @SerialName("w")
    val week: Long,
    @SerialName("c")
    val commits: Long,
    @SerialName("a")
    val additions: Long,
    @SerialName("d")
    val deletions: Long,
)

val GitHubContributorStats.asScmContributorStats: ScmContributorStats?
    get() = author?.let {
        ScmContributorStats(
            user = author.asScmContributor,
            weeklyStats = weeklyStats.map { weekly ->
                ScmContributorWeeklyStats(
                    startOfWeek = Instant.fromEpochSeconds(weekly.week),
                    totals = ScmContributorTotals(
                        commits = weekly.commits,
                        additions = weekly.additions,
                        deletions = weekly.deletions,
                    ),
                )
            },
        )
    }
