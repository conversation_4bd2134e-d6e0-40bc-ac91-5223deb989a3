package com.nextchaptersoftware.scm.github

import arrow.fx.coroutines.parMapNotNull
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.toHttpStatusCode
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.error.ScmDiffTooLargeException
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewCreateBody
import com.nextchaptersoftware.scm.models.ScmCommentComponent
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmGitCredentials
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmReaction
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.scm.models.transformers.asScmPullRequestReview
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import io.ktor.client.plugins.ResponseException
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

internal class GitHubRepoApi(
    private val repo: Repo,
    private val orgV3Api: GitHubAppApi.V3Org,
    private val orgV4Api: GitHubAppApi.V4Org,
) : ScmRepoApi {

    override suspend fun repo(): ScmRepository {
        return orgV3Api.repository(repo.externalId)
    }

    override suspend fun headCommitShaOrNull(): Hash? {
        return runSuspendCatching {
            orgV3Api.headCommitSha(repo.externalId)
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.Conflict) {
                LOGGER.warnAsync(it) { "Conflict when fetching head commit" }
                null
            } else {
                throw it
            }
        }
    }

    override suspend fun headCommitOrNull(): ScmCommit? {
        return runSuspendCatching {
            orgV3Api.headCommit(repo.externalId)
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.Conflict) {
                LOGGER.warnAsync(it) { "Conflict when fetching head commit" }
                null
            } else {
                throw it
            }
        }
    }

    override suspend fun pullRequest(pullRequestNumber: Int): ScmPullRequest {
        return orgV3Api.pullRequest(repo.externalId, pullRequestNumber)
    }

    override suspend fun pullRequestDiff(
        pullRequestNumber: Int,
        base: String?,
        head: String?,
    ): String {
        return runSuspendCatching {
            when {
                base != null && head != null -> diff(base, head)
                else -> pullRequestDiff0(pullRequestNumber)
            }
        }
            .getOrElse {
                when (it.toHttpStatusCode()) {
                    HttpStatusCode.NotAcceptable -> throw ScmDiffTooLargeException()
                    else -> throw it
                }
            }
    }

    private suspend fun pullRequestDiff0(
        pullRequestNumber: Int,
    ): String {
        return orgV3Api.pullRequestDiff(repo.externalId, pullRequestNumber)
    }

    override suspend fun latestPullRequest(): ScmPullRequest? {
        return orgV3Api.latestPullRequest(repo.externalId)
    }

    override fun contributorStats(): Flow<ScmContributorStats> {
        return orgV3Api.contributorStats(repo.externalId)
    }

    override fun allIssues(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssue>> {
        return orgV3Api.allIssues(
            repoExternalId = repo.externalId,
            initialBatchUrl = initialBatchUrl,
            since = since,
        )
    }

    override fun allIssueComments(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssueComment>> {
        return orgV3Api.allIssueComments(
            repoExternalId = repo.externalId,
            initialBatchUrl = initialBatchUrl,
        )
    }

    override fun allPullRequests(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmPullRequest>> {
        return orgV3Api.allPullRequests(
            repoExternalId = repo.externalId,
            initialBatchUrl = initialBatchUrl,
            since = since,
        )
    }

    override fun allPullRequestComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment>> {
        return orgV3Api.allPullRequestComments(
            repoExternalId = repo.externalId,
            initialBatchUrl = initialBatchUrl,
        )
    }

    override fun allPullRequestCodeComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
        return orgV3Api.allPullRequestReviewComments(
            repoExternalId = repo.externalId,
            initialBatchUrl = initialBatchUrl,
        )
    }

    override suspend fun pullRequestReviewCreate(
        pullRequestNumber: Int,
        commitId: String,
        text: String,
    ): ScmPullRequestReview {
        return orgV3Api.pullRequestReviewCreate(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            pullRequestNumber = pullRequestNumber,
            body = GitHubPullRequestReviewCreateBody(
                commitId = commitId,
                body = text,
                event = GitHubPullRequestReviewCreateBody.Event.COMMENT,
            ),
        )
            .asScmPullRequestReview
    }

    override suspend fun pullRequestReviewUpdate(
        pullRequestNumber: Int,
        reviewId: String,
        text: String,
    ): ScmPullRequestReview {
        return orgV3Api.pullRequestReviewUpdate(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            pullRequestNumber = pullRequestNumber,
            reviewId = reviewId.toLong(),
            body = GitHubBody(
                body = text,
            ),
        )
            .asScmPullRequestReview
    }

    override suspend fun pullRequestCommentCreate(
        pullRequestNumber: Int,
        text: String,
    ): ScmCommentComponent.Comment {
        return orgV3Api.issueCommentCreate(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            issueNumber = pullRequestNumber,
            body = GitHubBody(
                body = text,
            ),
        )
            .asScmIssueComment
            .comment
    }

    override fun pullRequestCommentReactions(
        commentId: String,
    ): Flow<ScmReaction> {
        return orgV3Api.issueCommentReactions(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            commentId = commentId,
        )
            .map { it.asScmReaction() }
    }

    override suspend fun pullRequestCommentUpdate(
        pullRequestNumber: Int,
        commentId: String,
        text: String,
    ): ScmCommentComponent.Comment {
        return orgV3Api.issueCommentUpdate(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            commentId = commentId.toLong(),
            body = GitHubBody(
                body = text,
            ),
        )
            .asScmIssueComment
            .comment
    }

    override suspend fun pullRequestCommentDelete(
        pullRequestNumber: Int,
        commentId: String,
    ): Boolean = runSuspendCatching {
        return orgV3Api.issueCommentDelete(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            commentId = commentId.toLong(),
        )
    }.getOrElse {
        when (it.toHttpStatusCode()) {
            HttpStatusCode.NotFound -> true
            else -> throw it
        }
    }

    override fun fileCommits(path: String, maxItems: Int?): Flow<ScmCommit> {
        return orgV4Api.fileCommits(
            owner = repo.externalOwner,
            repoName = repo.externalName,
            path = path,
            maxItems = maxItems,
        )
    }

    override suspend fun fileContents(path: String, ref: String?): String {
        return orgV3Api.fileContents(repo.externalId, path, ref)
    }

    override fun commitsBetweenCommits(base: Hash, head: Hash?, maxItems: Int?): Flow<ScmCommit> {
        return orgV3Api.commitsBetweenCommits(
            repoExternalId = repo.externalId,
            base = base,
            head = head,
            maxItems = maxItems,
        )
    }

    override fun commitsBetweenDates(
        since: Instant,
        until: Instant?,
        maxItems: Int?,
        authorUsername: String?,
    ): Flow<ScmCommit> {
        return orgV3Api.commits(
            repoExternalId = repo.externalId,
            authorUsername = authorUsername,
            since = since,
            until = until,
            maxItems = maxItems,
        )
    }

    override suspend fun commit(sha: Hash): ScmCommit {
        return orgV3Api.commit(repo.externalId, sha)
    }

    override suspend fun commitDiff(sha: Hash): String {
        return orgV3Api.commitDiff(repo.externalId, sha)
    }

    override fun commits(author: String?, includeDiffs: Boolean, maxItems: Int?): Flow<ScmCommit> = suspendedFlow {
        orgV3Api.commits(
            repoExternalId = repo.externalId,
            authorUsername = author,
            since = null,
            until = null,
            maxItems = maxItems,
        ).let { commits ->
            if (includeDiffs) {
                commits.toList().let { commitsList ->
                    commitsList.parMapNotNull(context = Dispatchers.IO) { commit ->
                        commit.sha?.let { sha ->
                            runSuspendCatching {
                                commit.copy(diff = commitDiff(sha))
                            }.getOrElse {
                                LOGGER.errorAsync(it) { "Failed to fetch diff for commit" }
                                commit
                            }
                        }
                    }
                }.asFlow()
            } else {
                commits
            }
        }
    }

    override suspend fun diff(base: String, head: String): String {
        return orgV3Api.diff(repo.fullName, base, head)
    }

    override suspend fun markdown(text: String): String {
        return orgV3Api.markdown(repo.fullName, text)
    }

    override fun pullRequestFiles(pullRequestNumber: Int, maxItems: Int?): Flow<ScmPullRequestFile> {
        return orgV3Api.pullRequestFilesForPR(repo.externalId, pullRequestNumber, maxItems)
    }

    override fun pullRequestReviews(pullRequestNumber: Int): Flow<ScmPullRequestReview> {
        return orgV3Api.pullRequestReviewsForPR(repo.externalId, pullRequestNumber)
    }

    override fun pullRequestAllComments(pullRequestNumber: Int): Flow<ScmPrComment> {
        return orgV3Api.pullRequestCodeCommentsForPR(repo.externalId, pullRequestNumber)
    }

    override suspend fun rateLimit(): ScmRateLimit {
        return orgV3Api.rateLimit()
    }

    override suspend fun gitCredentials(scmTokenConfig: ScmTokenConfig?): ScmGitCredentials {
        return ScmGitCredentials.Basic(
            username = "x-access-token",
            password = orgV3Api.token(scmTokenConfig = scmTokenConfig),
        )
    }

    override fun close() {
        orgV3Api.close()
        orgV4Api.close()
    }
}
