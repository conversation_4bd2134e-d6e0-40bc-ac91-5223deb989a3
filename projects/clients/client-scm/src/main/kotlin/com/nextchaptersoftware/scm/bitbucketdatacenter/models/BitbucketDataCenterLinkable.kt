package com.nextchaptersoftware.scm.bitbucketdatacenter.models

import io.ktor.http.URLBuilder
import io.ktor.http.Url

internal interface BitbucketDataCenterLinkable {
    val href: Url

    /**
     * Creates a children url, derivative from this linkable
     */
    fun childHref(block: URLBuilder.() -> Unit): Url {
        return URLBuilder(href)
            .apply(block)
            .build()
    }
}
