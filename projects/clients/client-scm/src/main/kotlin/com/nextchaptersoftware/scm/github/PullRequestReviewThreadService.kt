package com.nextchaptersoftware.scm.github

/**
 * Utility class for retrieving all review threads and comments for pull requests
 * */
class PullRequestReviewThreadService(
    private val gitHubClient: GitHubAppApi.V4Org,
    private val owner: String,
    private val repoName: String,
) {
    /**
     * Get the [prPageSize] PRs after [prAfterCursor]
     * */
    suspend fun pullRequestsWithReviewComments(
        prPageSize: Int = 100,
        prAfterCursor: String?,
    ): PullRequestReviewThreadsResult {
        return gitHubClient.pullRequestReviewThreads(
            owner = owner,
            repoName = repoName,
            prAfterCursor = prAfterCursor,
            prPageSize = prPageSize,
        )
    }
}
