package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.scm.models.ScmRole
import kotlinx.serialization.Serializable

@Serializable
data class AzureDevOpsGraphGroup(
    val descriptor: String,
    val principalName: String,
    val originId: String,
    val displayName: String,
) {
    companion object {
        private val teamRegex = Regex(
            """
                \[(\w+)]\\\1 Team
            """.trimIndent(),
        )
    }

    fun asRole(): ScmRole {
        return when {
            principalName.contains("Administrator") -> ScmRole.Admin
            principalName.contains("Contributor") -> ScmRole.Read
            principalName.contains("Invited") -> ScmRole.Read
            principalName.contains("Reader") -> ScmRole.Read
            principalName.contains("Users") -> ScmRole.Read
            principalName.contains(teamRegex) -> ScmRole.Write
            else -> ScmRole.None
        }
    }
}
