package com.nextchaptersoftware.scm.github.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#pull_request_review
 */
@Serializable
data class GitHubPullRequestReviewEvent(
    val action: Action,
    val repository: GitHubRepo,
    @SerialName("pull_request") val pullRequest: GitHubPullRequest,
    val review: GitHubPullRequestReview,
) {
    @Serializable
    enum class Action {
        @SerialName("submitted")
        Submitted,

        @SerialName("edited")
        Edited,

        @SerialName("dismissed")
        Dismissed,
    }

    val ownerExternalId: String
        get() = repository.owner.id.toString()

    val repoExternalId: String
        get() = repository.id.toString()

    val reviewId: String
        get() = review.id.toString()
}
