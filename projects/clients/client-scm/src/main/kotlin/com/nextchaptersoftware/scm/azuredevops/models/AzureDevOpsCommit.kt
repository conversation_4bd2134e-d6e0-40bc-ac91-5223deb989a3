package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmCommitAuthor
import com.nextchaptersoftware.types.Hash
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
data class AzureDevOpsCommit(
    val commitId: String,
    val comment: String,
    val author: AzureDevOpsCommitUserDate,
    val committer: AzureDevOpsCommitUserDate,
    val remoteUrl: String,
) {
    val asScmCommit: ScmCommit
        get() = ScmCommit(
            sha = commitId.let(Hash::parse),
            author = author.asScmCommitAuthor,
            message = comment,
            commitDate = minOf(
                author.date,
                committer.date,
            ),
            htmlUrl = remoteUrl.asUrl,
            diff = null, // not available
        )
}

@Serializable
data class AzureDevOpsCommitUserDate(
    val name: String,
    @Contextual
    val date: Instant,
) {
    val asScmCommitAuthor: ScmCommitAuthor
        get() = ScmCommitAuthor(
            name = name,
            externalUserId = null, // not available
            email = null, // not yet implemented
        )
}
