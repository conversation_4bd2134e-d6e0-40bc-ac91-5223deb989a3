package com.nextchaptersoftware.openai.api

import com.aallam.openai.api.chat.ChatCompletion
import com.aallam.openai.api.chat.ChatCompletionChunk
import com.aallam.openai.api.chat.ChatCompletionRequest
import com.aallam.openai.api.chat.ChatMessage
import com.aallam.openai.api.chat.ChatResponseFormat
import com.aallam.openai.api.core.Usage
import com.aallam.openai.client.OpenAI
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.openai.api.models.OpenAITextModel
import com.nextchaptersoftware.openai.api.utils.OpenAIChatCompletionPromptParser
import com.nextchaptersoftware.trace.coroutine.withSpan
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.lastOrNull
import kotlinx.coroutines.flow.onEach
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class OpenAICompletionsApi(
    private val client: OpenAI,
) {
    suspend fun chatCompletion(
        model: OpenAITextModel = OpenAITextModel.GPT4OmniMini,
        messages: List<ChatMessage>,
        topP: Double? = null,
        temperature: Double? = null,
        maxCompletionTokens: Int? = null, // defaults to 16
        responseFormat: ChatResponseFormat? = null,
    ): ChatCompletion {
        val request = ChatCompletionRequest(
            model = model.modelId,
            messages = messages,
            maxTokens = if (!model.supportsMaxCompletionTokens) maxCompletionTokens else null,
            maxCompletionTokens = if (model.supportsMaxCompletionTokens) maxCompletionTokens else null,
            temperature = if (model.supportsTemperature) temperature else null,
            topP = topP,
            responseFormat = responseFormat,
        )

        return client.chatCompletion(request = request)
            .also { it.recordUsage() }
    }

    suspend fun chatCompletion(
        model: OpenAITextModel = OpenAITextModel.GPT4OmniMini,
        prompt: String,
        topP: Double? = null,
        temperature: Double? = null,
        maxCompletionTokens: Int? = null, // defaults to 16
        responseFormat: ChatResponseFormat? = null,
    ): ChatCompletion = chatCompletion(
        model = model,
        messages = OpenAIChatCompletionPromptParser.parse(
            prompt = prompt,
            convertSystemToUser = model == OpenAITextModel.GPTo1 || model == OpenAITextModel.GPTo1Mini,
        ),
        topP = topP,
        temperature = if (model.supportsTemperature) temperature else null,
        maxCompletionTokens = maxCompletionTokens,
        responseFormat = responseFormat,
    )

    fun chatCompletions(
        model: OpenAITextModel = OpenAITextModel.GPT4OmniMini,
        prompt: String,
        topP: Double? = null,
        temperature: Double? = null,
        maxCompletionTokens: Int? = null, // defaults to 16
        responseFormat: ChatResponseFormat? = null,
    ): Flow<ChatCompletionChunk> {
        val request = ChatCompletionRequest(
            model = model.modelId,
            messages = OpenAIChatCompletionPromptParser.parse(prompt),
            maxTokens = if (!model.supportsMaxCompletionTokens) maxCompletionTokens else null,
            maxCompletionTokens = if (model.supportsMaxCompletionTokens) maxCompletionTokens else null,
            temperature = if (model.supportsTemperature) temperature else null,
            topP = topP,
            responseFormat = responseFormat,
        )
        val completionFlow = client.chatCompletions(request)
        return flow {
            completionFlow
                .onEach {
                    this.emit(it)
                }.lastOrNull()?.also {
                    it.recordUsage()
                }
        }
    }
}

private suspend fun Usage.record(model: String) = withSpan(
    spanName = "OpenAICompletion",
    attributes = mapOf(
        "openai.totalTokens" to (totalTokens ?: 0),
        "openai.promptTokens" to (promptTokens ?: 0),
        "openai.completionTokens" to (completionTokens ?: 0),
        "openai.model" to model,
    ),
) {
    LOGGER.infoAsync { "OpenAI Completion" }
}

private suspend fun ChatCompletion.recordUsage() = usage?.also {
    it.record(model.id)
}

private suspend fun ChatCompletionChunk.recordUsage() = usage?.also {
    it.record(model.id)
}
