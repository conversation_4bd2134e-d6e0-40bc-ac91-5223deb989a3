package com.nextchaptersoftware.ci.circleci.models

import com.nextchaptersoftware.ci.models.CIExternalWebhook
import com.sksamuel.hoplite.Secret
import io.ktor.http.Url
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CircleCiWebhook(
    @Contextual
    @SerialName("id")
    val externalId: UUID,

    @SerialName("name")
    val name: String,

    @SerialName("url")
    val url: Url,

    @SerialName("events")
    val events: List<CircleCiWebhookEvents>,

    @SerialName("verify_tls")
    val verifyTls: <PERSON><PERSON>an,

    @SerialName("signing_secret")
    val secret: String,
) {

    private val isValid: Boolean
        get() = events.containsAll(CircleCiWebhookEvents.entries) && verifyTls

    val asCIExternalWebhook
        get() = CIExternalWebhook(
            externalId = externalId,
            url = url,
            name = name,
            secret = Secret(secret),
            valid = isValid,
        )
}
