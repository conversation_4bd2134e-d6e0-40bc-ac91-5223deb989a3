package com.nextchaptersoftware.ci.github.rules

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.rules.Result
import com.nextchaptersoftware.ci.rules.Rule
import com.nextchaptersoftware.scm.github.models.GitHubRepo

internal object GitHubRepoRule : Rule<GitHubRepo> {

    private val ciConfig = CIConfig.INSTANCE

    override fun evaluate(
        event: GitHubRepo,
    ): Result {
        val denyList = ciConfig.github.repoDenyList
            ?: return Result.Allow

        return when (denyList.contains(event.fullName)) {
            true -> Result.Deny(rule = name, item = event.fullName)
            else -> Result.Allow
        }
    }
}
