package com.nextchaptersoftware.ci.buildkite.models

import com.nextchaptersoftware.ci.models.CiExecution

interface BuildkiteEvent {
    val build: BuildkiteBuild
    val pipeline: BuildkitePipeline

    fun asCiBuild() = CiExecution.CiBuild(
        type = javaClass.simpleName,
        runner = "Buildkite",
        externalId = build.number.toString(),
        displayName = pipeline.name,
        displayNumber = build.number,
        pullRequestNumber = build.pullRequest?.id?.toIntOrNull(),
        baseSha = null, // not available
        headSha = build.commit,
        apiUrl = build.url,
        htmlUrl = build.webUrl,
        createdAt = build.createdAt,
        startedAt = build.startedAt,
        completedAt = build.finishedAt,
        status = build.state.asCiStatus(),
        result = build.state.asCiResult(),
    )
}
