package com.nextchaptersoftware.ci.circleci.models

import com.nextchaptersoftware.ci.models.CiResult
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * https://circleci.com/docs/workflows/#states
 */
@Serializable
enum class CircleCiWorkflowStatus {
    // Workflow canceled before it finished (terminal)
    @SerialName("canceled")
    Canceled,

    // Internal error starting a job in the workflow (terminal)
    @SerialName("error")
    Error,

    // One or more jobs in the workflow failed (terminal)
    @SerialName("failed")
    Failed,

    // A job in the workflow failed, but others are still running or yet to be approved
    @SerialName("failing")
    Failing,

    // Workflow never started (terminal)
    @SerialName("not run")
    NotRun,

    // A job in the workflow is waiting for approval
    @SerialName("on hold")
    OnHold,

    // Workflow is in progress
    @SerialName("running")
    Running,

    // All jobs in the workflow completed successfully (terminal)
    @SerialName("success")
    Success,

    // The user who triggered the pipeline or approved an approval job does not have access to a required restricted context (terminal)
    @SerialName("unauthorized")
    Unauthorized,
    ;

    /**
     * Workflows identified as [CiResult.Failure] will be promoted to triage
     */
    fun asCiResult(): CiResult = when (this) {
        Running,
        OnHold,
        -> CiResult.Pending

        Success,
        -> CiResult.Success

        Failed,
        -> CiResult.Failure

        Canceled,
        Error,
        Failing,
        NotRun,
        Unauthorized,
        -> CiResult.Ignored
    }
}
