package com.nextchaptersoftware.ci.github.rules

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.rules.Rules
import com.nextchaptersoftware.ci.rules.adapted
import com.nextchaptersoftware.scm.github.models.GitHubCheckApp
import com.nextchaptersoftware.scm.github.models.GitHubCheckRunEvent

object GitHubCheckRunEventRule : Rules.All<GitHubCheckRunEvent>(
    GitHubCheckSuiteRule.adapted { it.checkRun.checkSuite },
    GitHubCheckRunAppRule.adapted { it.checkRun.checkSuite.app },
    GitHubStatusWaitingRule.adapted { it.checkRun.status },
    GitHubRepoRule.adapted { it.repository },
)

private object GitHubCheckRunAppRule : Rules.AllowIf<GitHubCheckApp>() {
    private val ciConfig = CIConfig.INSTANCE

    override fun GitHubCheckApp.item() = name

    override fun GitHubCheckApp.test() = ciConfig.github.checkRunAllowList?.contains(name) ?: true
}
