package com.nextchaptersoftware.ci.circleci.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.utils.asUUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CircleCiProjectTest {

    @Test
    fun `decode from V2 API json -- circleci type`() {
        val jsonV2ApiProject = """
        {
            "id": "853c75c3-6aa6-4499-b0a0-63f2e6b499c3",
            "name": "another one",
            "organization_id": "8024e68d-63e8-48bd-b244-1da7f44f70d9",
            "organization_name": "martin-ncs",
            "organization_slug": "circleci/GpnDMZ5TSRxRuzQTzM5Z4k",
            "slug": "circleci/GpnDMZ5TSRxRuzQTzM5Z4k/HTFSwYZ8yAP9Xcjt8tVB8N",
            "vcs_info": {
                "default_branch": "main",
                "provider": "CircleCI",
                "vcs_url": "//circleci.com/8024e68d-63e8-48bd-b244-1da7f44f70d9/853c75c3-6aa6-4499-b0a0-63f2e6b499c3"
            }
        }
        """.trimIndent()
        val project = jsonV2ApiProject.decode<CircleCiProject>()
        assertThat(project).isEqualTo(
            CircleCiProject(
                organizationExternalId = "8024e68d-63e8-48bd-b244-1da7f44f70d9".asUUID(),
                projectExternalId = "853c75c3-6aa6-4499-b0a0-63f2e6b499c3".asUUID(),
                organizationName = "martin-ncs",
                organizationSlug = "circleci/GpnDMZ5TSRxRuzQTzM5Z4k",
                projectName = "another one",
                projectSlug = "circleci/GpnDMZ5TSRxRuzQTzM5Z4k/HTFSwYZ8yAP9Xcjt8tVB8N",
            ),
        )
        assertThat(project.organizationUrl).isEqualTo("https://app.circleci.com/organization/circleci/GpnDMZ5TSRxRuzQTzM5Z4k".asUrl)
        assertThat(project.projectUrl).isEqualTo("https://app.circleci.com/projects/circleci/GpnDMZ5TSRxRuzQTzM5Z4k/HTFSwYZ8yAP9Xcjt8tVB8N".asUrl)
    }

    @Test
    fun `decode from V2 API json -- gh type`() {
        val jsonV2ApiProject = """
        {
            "id": "d496b342-88b8-45e8-b2bf-f511212afd18",
            "name": "canary",
            "organization_id": "869df4d6-1bff-4ef3-bc41-02b8cc724e34",
            "organization_name": "travelperk",
            "organization_slug": "gh/travelperk",
            "slug": "gh/travelperk/canary",
            "vcs_info": {
                "default_branch": "main",
                "provider": "GitHub",
                "vcs_url": "https://github.com/travelperk/canary"
            }
        }
        """.trimIndent()
        val project = jsonV2ApiProject.decode<CircleCiProject>()
        assertThat(project).isEqualTo(
            CircleCiProject(
                organizationExternalId = "869df4d6-1bff-4ef3-bc41-02b8cc724e34".asUUID(),
                projectExternalId = "d496b342-88b8-45e8-b2bf-f511212afd18".asUUID(),
                organizationName = "travelperk",
                organizationSlug = "gh/travelperk",
                projectName = "canary",
                projectSlug = "gh/travelperk/canary",
            ),
        )
        assertThat(project.organizationUrl).isEqualTo("https://app.circleci.com/organization/gh/travelperk".asUrl)
        assertThat(project.projectUrl).isEqualTo("https://app.circleci.com/projects/gh/travelperk/canary".asUrl)
    }

    @Test
    fun `decode from V1 API json -- circleci type`() {
        val jsonV1ApiProject = """
        {
          "branches": {},
          "oss": false,
          "reponame": "bens project",
          "username": "martin-ncs",
          "has_usable_key": false,
          "vcs_type": "circleci",
          "language": null,
          "vcs_url": "//circleci.com/8024e68d-63e8-48bd-b244-1da7f44f70d9/c3814bda-f8c5-40f8-b0b5-25a1c5972d71",
          "following": false,
          "default_branch": "main"
        }
        """.trimIndent()

        val project = jsonV1ApiProject.decode<CircleCiProjectMinimalV1>()
        assertThat(project).isEqualTo(
            CircleCiProjectMinimalV1(
                vcsUrl = "//circleci.com/8024e68d-63e8-48bd-b244-1da7f44f70d9/c3814bda-f8c5-40f8-b0b5-25a1c5972d71",
            ),
        )
        assertThat(project.externalOrganizationIdOrSlug).isEqualTo("8024e68d-63e8-48bd-b244-1da7f44f70d9")
        assertThat(project.externalProjectIdOrSlug).isEqualTo("c3814bda-f8c5-40f8-b0b5-25a1c5972d71")
    }

    @Test
    fun `decode from V1 API json -- gh type`() {
        val jsonV1ApiProject = """
        {
            "branches": {},
            "oss": false,
            "reponame": "backend",
            "followed": false,
            "username": "travelperk",
            "has_usable_key": false,
            "vcs_type": "github",
            "language": null,
            "vcs_url": "https://github.com/travelperk/backend",
            "default_branch": "main"
        }
        """.trimIndent()

        val project = jsonV1ApiProject.decode<CircleCiProjectMinimalV1>()
        assertThat(project).isEqualTo(
            CircleCiProjectMinimalV1(
                vcsUrl = "https://github.com/travelperk/backend",
            ),
        )
        assertThat(project.externalOrganizationIdOrSlug).isEqualTo("gh/travelperk")
        assertThat(project.externalProjectIdOrSlug).isEqualTo("gh/travelperk/backend")
    }

    @Test
    fun `decode from webhook`() {
        val jsonWebhookProject = """
        {
          "id": "8cc3fda2-01f5-497d-a609-1fc78ad2a4f8",
          "name": "test-repo",
          "slug": "circleci/GpnDMZ5TSRxRuzQTzM5Z4k/JPBB9r8KeSaKiPLExJ7Ugw"
        }
        """.trimIndent()

        val project = jsonWebhookProject.decode<CircleCiProjectMinimalWebhook>()
        assertThat(project).isEqualTo(
            CircleCiProjectMinimalWebhook(
                projectSlug = "circleci/GpnDMZ5TSRxRuzQTzM5Z4k/JPBB9r8KeSaKiPLExJ7Ugw",
            ),
        )
    }
}
