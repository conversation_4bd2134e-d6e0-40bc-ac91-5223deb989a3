{"pipeline": {"type": "pipeline", "uuid": "{ee3acdd8-479b-452d-868c-4142be7cf704}"}, "setup_commands": [{"commandType": "user", "name": "umask 000", "command": "umask 000"}, {"commandType": "user", "name": "export GIT_LFS_SKIP_SMUDGE=1", "command": "export GIT_LFS_SKIP_SMUDGE=1"}, {"commandType": "user", "name": "retry 6 git clone --branch=\"mrtn/foo-2\" https://x-token-auth:{access_token}@bitbucket.org/$BITBUCKET_REPO_FULL_NAME.git $BUILD_DIR", "command": "retry 6 git clone --branch=\"mrtn/foo-2\" https://x-token-auth:{access_token}@bitbucket.org/$BITBUCKET_REPO_FULL_NAME.git $BUILD_DIR"}, {"commandType": "user", "name": "git reset --hard ab1dab02e8a152612d531ea6720fb0a121decd1b", "command": "git reset --hard ab1dab02e8a152612d531ea6720fb0a121decd1b"}, {"commandType": "user", "name": "git config user.name bitbucket-pipelines", "command": "git config user.name bitbucket-pipelines"}, {"commandType": "user", "name": "git config user.email <EMAIL>", "command": "git config user.email <EMAIL>"}, {"commandType": "user", "name": "git config push.default current", "command": "git config push.default current"}, {"commandType": "user", "name": "CONFLICT_EXIT_CODE=3", "command": "CONFLICT_EXIT_CODE=3"}, {"commandType": "user", "name": "git merge 010793954b35 --no-edit || exit $CONFLICT_EXIT_CODE", "command": "git merge 010793954b35 --no-edit || exit $CONFLICT_EXIT_CODE"}, {"commandType": "user", "name": "git config http.${BITBUCKET_GIT_HTTP_ORIGIN}.proxy http://localhost:29418/", "command": "git config http.${BITBUCKET_GIT_HTTP_ORIGIN}.proxy http://localhost:29418/"}, {"commandType": "user", "name": "git remote set-url origin http://bitbucket.org/$BITBUCKET_REPO_FULL_NAME", "command": "git remote set-url origin http://bitbucket.org/$BITBUCKET_REPO_FULL_NAME"}, {"commandType": "user", "name": "git reflog expire --expire=all --all", "command": "git reflog expire --expire=all --all"}, {"commandType": "user", "name": "echo \".bitbucket/pipelines/generated\" >> .git/info/exclude", "command": "echo \".bitbucket/pipelines/generated\" >> .git/info/exclude"}, {"commandType": "user", "name": "chmod 777 $BUILD_DIR", "command": "chmod 777 $BUILD_DIR"}], "script_commands": [{"commandType": "user", "name": "echo 'trying once..'", "command": "echo 'trying once..'"}, {"commandType": "user", "name": "test $((1 + $RANDOM % 10)) -gt 8;", "command": "test $((1 + $RANDOM % 10)) -gt 8;"}], "teardown_commands": [{"commandType": "system", "action": "PROCESS_TEST_REPORTS", "name": "Process Test Reports", "command": "echo \"Processing test reports\""}], "image": {"name": "atlassian/default-image:latest"}, "maxTime": 120, "build_seconds_used": 10, "uuid": "{2a4900d1-d601-49ec-9cb5-67ae1ea1f63a}", "name": "Randomly might fail", "trigger": {"type": "pipeline_step_trigger_automatic"}, "state": {"name": "COMPLETED", "type": "pipeline_step_state_completed", "result": {"name": "FAILED", "type": "pipeline_step_state_completed_failed"}}, "started_on": "2025-06-02T22:52:06.802697248Z", "completed_on": "2025-06-02T22:52:17.394099557Z", "duration_in_seconds": 11, "run_number": 1, "type": "pipeline_step"}