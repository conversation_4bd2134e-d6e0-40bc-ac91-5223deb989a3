package com.nextchaptersoftware.assetservice.api

import com.nextchaptersoftware.api.AssetsApiDelegateInterface
import com.nextchaptersoftware.api.models.AssetUrl
import com.nextchaptersoftware.api.models.CompleteMultipartUploadRequest
import com.nextchaptersoftware.api.models.CreateAssetRequest
import com.nextchaptersoftware.api.models.CreateAssetResponse
import com.nextchaptersoftware.api.models.CreateMultipartUploadRequest
import com.nextchaptersoftware.api.models.CreateMultipartUploadResponse
import com.nextchaptersoftware.api.models.GetAssetResponse
import com.nextchaptersoftware.api.models.MultipartUploadPart
import com.nextchaptersoftware.api.models.ScmAssetRequest
import com.nextchaptersoftware.api.models.ScmAssetResponse
import com.nextchaptersoftware.api.models.ScmAuthorizedAsset
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.asset.AssetService
import com.nextchaptersoftware.aws.s3.S3CompletedPart
import com.nextchaptersoftware.db.models.AssetId
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.parameterMap
import com.nextchaptersoftware.models.legacyMemberId
import com.nextchaptersoftware.models.mustValidateRepoAccess
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.orgMember
import com.nextchaptersoftware.models.orgMemberId
import com.nextchaptersoftware.scm.delegates.ScmAssetsDelegate
import com.nextchaptersoftware.security.jwt.JwtUtils
import com.nextchaptersoftware.utils.asApiDateTime
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import io.ktor.http.toURI
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlinx.datetime.Instant
import kotlinx.datetime.toKotlinInstant
import org.openapitools.server.Resources
import org.openapitools.server.Resources.deleteAsset

class AssetsApiDelegateImpl(
    private val assetService: AssetService,
    private val scmAssetsDelegate: ScmAssetsDelegate,
    private val urlBuilderProvider: UrlBuilderProvider,
) : AssetsApiDelegateInterface {

    companion object {
        /**
         * Default expiry for SCM assets if the expiry cannot be derived from the authorized JWT.
         * Adds some leeway to account for clock skew, network latency, and time for the client to download the asset.
         */
        private val DEFAULT_SCM_ASSET_EXPIRY = 5.minutes.minus(20.seconds)
    }

    override suspend fun createMultipartUpload(
        context: RoutingContext,
        input: Resources.createMultipartUpload,
        body: CreateMultipartUploadRequest,
    ): CreateMultipartUploadResponse {
        val presignedParts = assetService.createMultipartUpload(
            assetId = input.assetId.let(::AssetId),
            authorId = context.orgMemberId(),
            numParts = body.numParts,
        )
        return CreateMultipartUploadResponse(
            uploadId = presignedParts.uploadId,
            parts = presignedParts.presignedParts.map {
                MultipartUploadPart(
                    presignedUrl = AssetUrl(
                        url = it.url.url.toURI(),
                        expiryAt = it.url.expiryAt.toKotlinInstant().asApiDateTime(),
                    ),
                    partNumber = it.partNum,
                )
            },
        )
    }

    override suspend fun abortMultipartUpload(
        context: RoutingContext,
        input: Resources.abortMultipartUpload,
    ) {
        assetService.abortMultipartUpload(
            assetId = input.assetId.let(::AssetId),
            authorId = context.orgMemberId(),
            uploadId = input.uploadId,
        )
    }

    override suspend fun authorizeScmAssets(
        context: RoutingContext,
        input: Resources.authorizeScmAssets,
        body: ScmAssetRequest,
    ): ScmAssetResponse {
        val authorizedAssetUrlMapping = scmAssetsDelegate.authorizeAssets(
            mustValidateRepoAccess = context.mustValidateRepoAccess,
            orgId = context.orgId,
            repoId = null,
            memberId = context.legacyMemberId(),
            assetUrls = body.assetUrls.toSet(),
        )

        return ScmAssetResponse(
            authorizedAssets = authorizedAssetUrlMapping.map { (assetUrl, authorizedAssetUrl) ->
                ScmAuthorizedAsset(
                    assetUrl = assetUrl,
                    authorizedAssetUrl = authorizedAssetUrl.asString,
                    expiresAt = getAssetUrlExpiry(authorizedAssetUrl).epochSeconds.toInt(),
                )
            },
        )
    }

    override suspend fun authorizeScmRepoAssets(
        context: RoutingContext,
        input: Resources.authorizeScmRepoAssets,
        body: ScmAssetRequest,
    ): ScmAssetResponse {
        val authorizedAssetUrlMapping = scmAssetsDelegate.authorizeAssets(
            mustValidateRepoAccess = context.mustValidateRepoAccess,
            orgId = context.orgId,
            memberId = context.legacyMemberId(),
            repoId = RepoId(input.repoId),
            assetUrls = body.assetUrls.toSet(),
        )

        return ScmAssetResponse(
            authorizedAssets = authorizedAssetUrlMapping.map { (assetUrl, authorizedAssetUrl) ->
                ScmAuthorizedAsset(
                    assetUrl = assetUrl,
                    authorizedAssetUrl = authorizedAssetUrl.asString,
                    expiresAt = getAssetUrlExpiry(authorizedAssetUrl).epochSeconds.toInt(),
                )
            },
        )
    }

    private fun getAssetUrlExpiry(authorizedAssetUrl: Url): Instant {
        return authorizedAssetUrl.parameterMap()["jwt"]
            ?.let { jwtString -> JwtUtils.getExpiryTime(jwtString) }
            ?: Instant.nowWithMicrosecondPrecision().plus(DEFAULT_SCM_ASSET_EXPIRY)
    }

    override suspend fun completeMultipartUpload(
        context: RoutingContext,
        input: Resources.completeMultipartUpload,
        body: CompleteMultipartUploadRequest,
    ) {
        assetService.completeMultipartUpload(
            assetId = input.assetId.let(::AssetId),
            authorId = context.orgMemberId(),
            uploadId = input.uploadId,
            completedParts = body.completedParts.map {
                S3CompletedPart(
                    partNum = it.partNumber,
                    eTag = it.eTag,
                )
            },
        )
    }

    override suspend fun createAsset(
        context: RoutingContext,
        input: Resources.createAsset,
        body: CreateAssetRequest,
    ): CreateAssetResponse {
        val assetAndUrl = assetService.createAsset(
            id = input.assetId.let(::AssetId),
            author = context.orgMember(),
            orgId = context.orgId,
            name = body.asset.name,
            contentType = body.asset.contentType,
            contentLength = body.asset.contentLength,
            threadId = body.asset.threadId?.let(::ThreadId),
            messageId = body.asset.messageId?.let(::MessageId),
        )

        return CreateAssetResponse(
            asset = assetAndUrl.asset.asApiModel(),
            uploadUrl = assetAndUrl.url.asApiModel(),
        )
    }

    override suspend fun getAsset(
        context: RoutingContext,
        input: Resources.getAsset,
        xUnblockedProductAgent: String?,
    ): GetAssetResponse {
        return when (val assetAndUrl = assetService.getAsset(input.assetId.let(::AssetId), input.amznCloudFrontId, xUnblockedProductAgent)) {
            null -> throw NotFoundException()

            else -> {
                GetAssetResponse(
                    asset = assetAndUrl.asset.asApiModel(),
                    downloadUrl = assetAndUrl.url.asApiModel(),
                    clientDownloadUrl = urlBuilderProvider
                        .assets()
                        .withOrg(context.orgId.value)
                        .withAsset(input.assetId)
                        .build()
                        .toURI(),
                )
            }
        }
    }

    override suspend fun deleteAsset(context: RoutingContext, input: deleteAsset) {
        assetService.deleteAsset(
            id = input.assetId.let(::AssetId),
            authorId = context.orgMemberId(),
        )
    }
}
