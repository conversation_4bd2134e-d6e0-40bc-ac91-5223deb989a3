package com.nextchaptersoftware.dataservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class DataEventJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
        get() = "Data Event Job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
