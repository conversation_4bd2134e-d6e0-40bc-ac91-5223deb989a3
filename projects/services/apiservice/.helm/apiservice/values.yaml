baseservice:
  priorityClassName: high-priority
  serviceAccount:
    name: "apiservice"
  service:
    type: NodePort
    port: 80
    targetPort: 8081
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: alb
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
      alb.ingress.kubernetes.io/security-groups: 'CloudFrontIngressOnly'
      alb.ingress.kubernetes.io/manage-backend-security-group-rules: "true"
      alb.ingress.kubernetes.io/group.order: '100'
      alb.ingress.kubernetes.io/group.name: "api-external"
      alb.ingress.kubernetes.io/healthcheck-path: "/api/__shallowcheck"
  secretProviderClass:
    create: true
    objects: |-
      - objectName: "redis-unblocked-password-1"
        objectType: "secretsmanager"
        objectAlias: "redis-password"
      - objectName: "activemq-unblocked-password-2"
        objectType: "secretsmanager"
        objectAlias: "activemq-password"
  deploymentStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 20%
      maxSurge: 20%
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: "app.kubernetes.io/name"
              operator: In
              values:
              - apiservice
          topologyKey: kubernetes.io/hostname
