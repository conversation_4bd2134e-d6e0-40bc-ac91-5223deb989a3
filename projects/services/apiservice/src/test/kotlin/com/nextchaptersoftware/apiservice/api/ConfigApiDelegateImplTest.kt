package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.ClientConfig
import com.nextchaptersoftware.api.models.FeatureSettings
import com.nextchaptersoftware.api.models.MetaConfig
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMember
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.stores.OrgMemberRoleStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.epoch
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class ConfigApiDelegateImplTest : DatabaseTestsBase() {

    private lateinit var client: UnblockedApiClient
    private lateinit var org: OrgDAO
    private lateinit var team: ScmTeamDAO
    private lateinit var memberMe: OrgMember
    private lateinit var memberOwnerAccount: OrgMember
    private val identityId = IdentityId.random()
    private val memberOwnerNoAccount = MemberId.random()
    private val orgMemberRoleStore: OrgMemberRoleStore = Stores.orgMemberRoleStore

    suspend fun setup() {
        org = makeOrg(enabledAt = Instant.epoch)
        team = makeScmTeam(
            org = org,
        )

        val identity = makeIdentity(id = identityId, person = makePerson())

        memberMe = makeMember(
            scmTeam = team,
            identity = identity,
            isCurrentMember = true,
            isPrimaryMember = true,
            providerRole = ProviderRole.Read, // not an admin SCM role
        ).orgMember.asDataModel()

        memberOwnerAccount = makeMember(
            scmTeam = team,
            identity = makeIdentity(person = makePerson()),
            isCurrentMember = true,
            isPrimaryMember = true,
            providerRole = ProviderRole.Owner,
        ).orgMember.asDataModel()

        makeMember(
            id = memberOwnerNoAccount,
            scmTeam = team,
            isCurrentMember = true,
            isPrimaryMember = true,
            providerRole = ProviderRole.Owner,
        )

        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            authContext = ApiAuthContext.Authenticated(
                identityId = identityId,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
        )
    }

    @Test
    fun getGlobalConfig() = suspendingDatabaseTest {
        setup()
        client.getGlobalConfig {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<ClientConfig>().also {
                assertThat(it.capabilities).isEmpty()
                assertThat(it.quantities).isEmpty()
            }
        }
    }

    @Test
    fun getMetaConfig() = suspendingDatabaseTest {
        setup()
        client.getMetaConfig {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<MetaConfig>().also {
                assertThat(it.serviceAddresses).isNotEmpty()
                assertThat(it.serviceAddresses).isEqualTo(
                    listOf(
                        "*************/32",
                        "***********/32",
                    ),
                )
            }
        }
    }

    @Test
    fun `feature settings`() = suspendingDatabaseTest {
        setup()

        client.getFeatureSettings(team.orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<FeatureSettings>().also {
                assertThat(it.enableRoleBasedAccessControl).isFalse
                assertThat(it.enableDataSourceAccessControl).isFalse
                assertThat(it.enableAdminOnlyAnalytics).isFalse
            }
        }

        client.patchFeatureSettings(
            team.orgId,
            FeatureSettings(
                enableRoleBasedAccessControl = true,
                enableDataSourceAccessControl = true,
                enableAdminOnlyAnalytics = true,
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.NoContent)
        }

        client.getFeatureSettings(team.orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<FeatureSettings>().also {
                assertThat(it.enableRoleBasedAccessControl).isTrue
                assertThat(it.enableDataSourceAccessControl).isTrue
                assertThat(it.enableAdminOnlyAnalytics).isTrue
            }
        }

        orgMemberRoleStore.listUnblockedAdmins(orgId = org.idValue).also {
            assertThat(it).containsExactlyInAnyOrder(
                memberMe.id,
                memberOwnerAccount.id,
            )
        }

        client.patchFeatureSettings(
            team.orgId,
            FeatureSettings(
                enableRoleBasedAccessControl = false,
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.NoContent)
        }

        client.getFeatureSettings(team.orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<FeatureSettings>().also {
                assertThat(it.enableRoleBasedAccessControl).isFalse
                assertThat(it.enableDataSourceAccessControl).isTrue
                assertThat(it.enableAdminOnlyAnalytics).isTrue
            }
        }

        orgMemberRoleStore.listUnblockedAdmins(orgId = org.idValue).also {
            assertThat(it).isEmpty()
        }

        client.patchFeatureSettings(
            team.orgId,
            FeatureSettings(
                enableAdminOnlyAnalytics = false,
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.NoContent)
        }

        client.getFeatureSettings(team.orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            body<FeatureSettings>().also {
                assertThat(it.enableRoleBasedAccessControl).isFalse
                assertThat(it.enableDataSourceAccessControl).isTrue
                assertThat(it.enableAdminOnlyAnalytics).isFalse
            }
        }
    }
}
