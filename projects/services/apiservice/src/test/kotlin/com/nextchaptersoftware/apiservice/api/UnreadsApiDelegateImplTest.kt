package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.ThreadInfo
import com.nextchaptersoftware.api.models.Unread
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.ModelBuilders.makeThreadParticipant
import com.nextchaptersoftware.db.ModelBuilders.makeThreadUnread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadUnread
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.CustomHeaders
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.currentCoroutineContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class UnreadsApiDelegateImplTest : DatabaseTestsBase() {
    private lateinit var client: UnblockedApiClient
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var thread: ThreadDAO
    private lateinit var repo: RepoDAO
    private lateinit var identity: IdentityDAO
    private lateinit var teamMember: MemberDAO
    private lateinit var threadUnread: ThreadUnread

    suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        repo = makeRepo(scmTeam = scmTeam)
        identity = makeIdentity(person = makePerson())
        teamMember = makeMember(scmTeam = scmTeam, identity = identity, isPrimaryMember = true)
        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            authContext = ApiAuthContext.Authenticated(
                identityId = identity.id.value,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
            threadUnreadStore = Stores.threadUnreadStore,
        )

        thread = makeThread(org = org, repo = repo, title = "latest")
        makeSourceMark(scmTeam = scmTeam, thread = thread).also { mark ->
            makeSourcePoint(scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
        }
        makeThreadParticipant(thread = thread, member = teamMember)
        threadUnread = makeThreadUnread(
            member = teamMember,
            thread = thread,
        ).asDataModel()
    }

    @Test
    fun getUnreads() = suspendingDatabaseTest {
        setup()
        client.getUnreads(scmTeam.orgId, listOf(repo.id.value)) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val actual = body<List<Unread>>()
            assertThat(actual).containsExactly(threadUnread.asApiModel())
            assertThat(headers[CustomHeaders.LAST_MODIFIED]).isNotNull()
        }
    }

    @Test
    fun updateThreadUnread() = suspendingDatabaseTest {
        setup()
        val message = makeMessage(thread = thread)

        client.updateThreadUnread(
            orgId = scmTeam.orgId,
            threadId = threadUnread.threadId,
            latestReadMessageId = message.idValue,
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.NoContent)
        }

        val updatedThreadUnread = suspendedTransaction {
            client.threadUnreadStore.find(this, threadUnread.threadId, checkNotNull(threadUnread.orgMemberId))
        }
        assertThat(updatedThreadUnread?.latestReadMessageId).isEqualTo(message.idValue)
    }

    @Test
    fun clearUnreads() = suspendingDatabaseTest {
        setup()

        client.getThreadsForMe(orgId = org.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val threadsResponse = body<List<ThreadInfo>>()
            assertThat(threadsResponse).isNotEmpty
            threadsResponse.forEach {
                assertThat(it.unread?.isUnread).isTrue()
            }
        }

        client.clearUnreads(orgId = scmTeam.orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.NoContent)
        }

        client.getThreadsForMe(orgId = org.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val threadsResponse = body<List<ThreadInfo>>()
            assertThat(threadsResponse).isNotEmpty
            threadsResponse.forEach {
                assertThat(it.unread?.isUnread).isFalse()
            }
        }
    }
}
