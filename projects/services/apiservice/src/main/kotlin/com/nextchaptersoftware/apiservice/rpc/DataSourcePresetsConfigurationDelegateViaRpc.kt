package com.nextchaptersoftware.apiservice.rpc

import com.nextchaptersoftware.api.integration.extension.services.DataSourcePresetsConfigurationDelegateInterface
import com.nextchaptersoftware.api.models.AvailableDataSourcePresetInstallation
import com.nextchaptersoftware.api.models.DataSourcePreset
import com.nextchaptersoftware.api.models.DataSourcePresetSummary
import com.nextchaptersoftware.api.models.UpdateDataSourcePresetRequest
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.DataSourcePresetsCalls

internal class DataSourcePresetsConfigurationDelegateViaRpc : DataSourcePresetsConfigurationDelegateInterface {
    override suspend fun getAvailableDataSourcePresetInstallations(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
    ): List<AvailableDataSourcePresetInstallation> {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.dataSourcePresetsGetAvailableDataSourcePresetInstallations(
                    params = DataSourcePresetsCalls.DataSourcePresetsGetAvailableDataSourcePresetInstallationsParams(
                        orgId = orgId,
                        orgMemberId = orgMemberId,
                    ),
                )
            }
    }

    override suspend fun getDataSourcePreset(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetId: DataSourcePresetId,
    ): DataSourcePreset {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.dataSourcePresetsGetDataSourcePreset(
                    params = DataSourcePresetsCalls.DataSourcePresetsGetDataSourcePresetParams(
                        orgId = orgId,
                        orgMemberId = orgMemberId,
                        dataSourcePresetId = dataSourcePresetId,
                    ),
                )
            }
    }

    override suspend fun getDataSourcePresets(orgId: OrgId): List<DataSourcePresetSummary> {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.dataSourcePresetsGetDataSourcePresets(
                    params = DataSourcePresetsCalls.DataSourcePresetsGetDataSourcePresetsParams(
                        orgId = orgId,
                    ),
                )
            }
    }

    override suspend fun removeDataSourcePreset(
        orgId: OrgId,
        dataSourcePresetId: DataSourcePresetId,
    ) {
        RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.dataSourcePresetsRemoveDataSourcePreset(
                    params = DataSourcePresetsCalls.DataSourcePresetsRemoveDataSourcePresetParams(
                        orgId = orgId,
                        dataSourcePresetId = dataSourcePresetId,
                    ),
                )
            }
    }

    override suspend fun updateDataSourcePreset(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetId: DataSourcePresetId,
        updateDataSourcePresetRequest: UpdateDataSourcePresetRequest,
    ): DataSourcePreset {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.dataSourcePresetsUpdateDataSourcePreset(
                    params = DataSourcePresetsCalls.DataSourcePresetsUpdateDataSourcePresetParams(
                        orgId = orgId,
                        orgMemberId = orgMemberId,
                        dataSourcePresetId = dataSourcePresetId,
                        updateDataSourcePresetRequest = updateDataSourcePresetRequest,
                    ),
                )
            }
    }
}
