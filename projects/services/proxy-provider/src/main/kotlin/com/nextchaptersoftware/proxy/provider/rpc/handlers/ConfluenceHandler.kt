package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationService
import com.nextchaptersoftware.api.models.ConfluenceConfiguration
import com.nextchaptersoftware.api.models.ConfluenceDataCenter
import com.nextchaptersoftware.api.models.ConfluenceSite
import com.nextchaptersoftware.api.models.GetConfluenceSpacesResponse
import com.nextchaptersoftware.rpc.calls.ConfluenceCalls
import com.nextchaptersoftware.rpc.calls.ConfluenceCalls.ConfluenceDataCenterUpsertParams

class ConfluenceHandler(
    private val confluenceConfigurationService: ConfluenceConfigurationService,
) : ConfluenceCalls {
    override suspend fun confluenceGetConfiguration(
        params: ConfluenceCalls.ConfluenceGetConfigurationParams,
    ): ConfluenceConfiguration {
        return confluenceConfigurationService.getConfluenceConfiguration(
            orgId = params.orgId,
            confluenceSiteId = params.confluenceSiteId,
            orgMemberId = params.orgMemberId,
        )
    }

    override suspend fun confluenceGetSpaces(
        params: ConfluenceCalls.ConfluenceGetSpacesParams,
    ): GetConfluenceSpacesResponse {
        return confluenceConfigurationService.getConfluenceSpaces(
            orgId = params.orgId,
            orgMemberId = params.orgMemberId,
            confluenceSiteId = params.confluenceSiteId,
        )
    }

    override suspend fun confluenceGetSites(
        params: ConfluenceCalls.ConfluenceGetSitesParams,
    ): List<ConfluenceSite> {
        return confluenceConfigurationService.getConfluenceSites(
            orgId = params.orgId,
        )
    }

    override suspend fun confluenceUpdateConfiguration(
        params: ConfluenceCalls.ConfluenceUpdateConfigurationParams,
    ) {
        confluenceConfigurationService.updateConfluenceConfiguration(
            orgId = params.orgId,
            confluenceSiteId = params.confluenceSiteId,
            confluenceSpaceIngestionType = params.confluenceSpaceIngestionType,
            confluenceSpaceIds = params.confluenceSpaceIds,
            orgMemberId = params.orgMemberId,
        )
    }

    override suspend fun confluenceDataCenterUpsert(
        params: ConfluenceDataCenterUpsertParams,
    ): ConfluenceDataCenter {
        return confluenceConfigurationService.upsertConfluenceDataCenter(
            installationId = params.confluenceDataCenterId,
            orgId = params.orgId,
            personId = params.personId,
            orgMemberId = params.orgMemberId,
            hostUrl = params.hostUrl,
            encryptedToken = params.encryptedToken,
        )
    }
}
