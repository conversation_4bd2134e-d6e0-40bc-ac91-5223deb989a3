package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.integration.extension.services.asana.AsanaConfigurationDelegateInterface
import com.nextchaptersoftware.api.models.AsanaConfiguration
import com.nextchaptersoftware.api.models.AsanaProjectsResponse
import com.nextchaptersoftware.api.models.AsanaWorkspace
import com.nextchaptersoftware.db.models.AsanaProjectId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId

internal class AsanaHandler(
    private val asanaConfigurationDelegate: AsanaConfigurationDelegateInterface,
) {
    suspend fun getWorkspaces(
        personId: PersonId,
    ): List<AsanaWorkspace> {
        return asanaConfigurationDelegate.getWorkspaces(
            personId = personId,
        )
    }

    suspend fun getProjects(
        orgId: OrgId,
        installationId: InstallationId,
    ): AsanaProjectsResponse {
        return asanaConfigurationDelegate.getProjects(
            orgId = orgId,
            installationId = installationId,
        )
    }

    suspend fun searchProjects(
        orgId: OrgId,
        installationId: InstallationId,
        query: String,
    ): AsanaProjectsResponse {
        return asanaConfigurationDelegate.searchProjects(
            orgId = orgId,
            installationId = installationId,
            query = query,
        )
    }

    suspend fun connectWorkspace(
        orgId: OrgId,
        personId: PersonId,
        workspaceGid: String,
        installationId: InstallationId,
    ): InstallationId {
        return asanaConfigurationDelegate.connectWorkspace(
            orgId = orgId,
            personId = personId,
            workspaceGid = workspaceGid,
            installationId = installationId,
        )
    }

    suspend fun getConfiguration(
        orgId: OrgId,
        installationId: InstallationId,
    ): AsanaConfiguration {
        return asanaConfigurationDelegate.getConfiguration(
            orgId = orgId,
            installationId = installationId,
        )
    }

    suspend fun saveConfiguration(
        orgId: OrgId,
        installationId: InstallationId,
        autoSelectNewProjects: Boolean,
        projectIds: List<AsanaProjectId>,
    ) {
        return asanaConfigurationDelegate.saveConfiguration(
            orgId = orgId,
            installationId = installationId,
            autoSelectNewProjects = autoSelectNewProjects,
            projectIds = projectIds,
        )
    }
}
