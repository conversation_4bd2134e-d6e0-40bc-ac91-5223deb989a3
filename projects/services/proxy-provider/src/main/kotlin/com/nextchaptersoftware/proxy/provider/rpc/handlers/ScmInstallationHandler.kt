package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.rpc.calls.ScmInstallationCalls
import com.nextchaptersoftware.rpc.calls.ScmInstallationCalls.ScmInstallationGetAuthorizedInstallationsParams
import com.nextchaptersoftware.rpc.calls.ScmInstallationCalls.ScmInstallationRefreshTeamResourcesParams
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.models.ScmInstallationAccount

internal class ScmInstallationHandler(
    private val scmTeamApiFactory: ScmTeamApiFactory,
    private val scmInstallService: ScmInstallationService,
) : ScmInstallationCalls {

    override suspend fun scmInstallationGetAuthorizedInstallations(
        params: ScmInstallationGetAuthorizedInstallationsParams,
    ): List<ScmInstallationAccount> {
        val identity = params.requiredIdentity()
        val scm = Scm.fromIdentity(identity)
        return scmInstallService.getAuthorizedInstallations(scm, identity)
    }

    override suspend fun scmInstallationGetScmInstallationAccount(
        params: ScmInstallationCalls.ScmInstallationGetScmInstallationAccountParams,
    ): ScmInstallationAccount {
        val scmTeam = params.requiredScmTeam()

        return scmTeamApiFactory.getApiFromTeam(scmTeam, Scm.fromTeam(scmTeam))
            .installationAccount()
    }

    override suspend fun scmInstallationRefreshTeamResources(
        params: ScmInstallationRefreshTeamResourcesParams,
    ) {
        val scmTeam = params.requiredScmTeam()
        return scmInstallService.refreshTeamResources(
            scmTeam = scmTeam,
        )
    }
}
