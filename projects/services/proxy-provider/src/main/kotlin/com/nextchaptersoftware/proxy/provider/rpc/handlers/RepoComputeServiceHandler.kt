package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.repo.RepoAccessResult
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls.ComputeRepoAccessParams
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls.ComputeRepoAccessResult

class RepoComputeServiceHandler(
    private val repoComputeService: LocalRepoComputeService,
    private val memberStore: MemberStore = Stores.memberStore,
) : RepoComputeCalls {

    override suspend fun repoAccessComputeRepoAccess(params: ComputeRepoAccessParams): ComputeRepoAccessResult {
        val scmTeam = scmTeamStore.findById(teamId = params.scmTeamId)
            ?: return ComputeRepoAccessResult.None

        val memberAndIdentity = memberStore.getOrgMemberAndIdentity(orgId = scmTeam.orgId, memberId = params.memberId)
            ?: return ComputeRepoAccessResult.None

        return repoComputeService.getOrComputeRepoAccess(
            scmTeam = scmTeam,
            scmOrgMemberAndIdentity = memberAndIdentity,
            forceCompute = params.forceCompute,
        ).asRpcRepoAccessResult
    }
}

private val RepoAccessResult.asRpcRepoAccessResult: ComputeRepoAccessResult
    get() = when (this) {
        RepoAccessResult.All -> ComputeRepoAccessResult.All
        RepoAccessResult.None -> ComputeRepoAccessResult.None
        is RepoAccessResult.Some -> ComputeRepoAccessResult.Some(allowed)
    }
