package com.nextchaptersoftware.mermaidservice

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.mermaidservice.api.DiagramsApiDelegateImpl
import com.nextchaptersoftware.mermaidservice.client.MermaidInkLocalClient
import com.nextchaptersoftware.mermaidservice.plugins.configureRouting
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import io.ktor.server.application.Application

fun Application.module(
    serviceLifecycle: ServiceLifecycle,
) {
    val mermaidInkLocalClient by lazy {
        MermaidInkLocalClient()
    }

    val mermaidConfig by lazy {
        GlobalConfig.INSTANCE.mermaid
    }

    val diagramsApiDelegateImpl by lazy {
        DiagramsApiDelegateImpl(
            authenticationSecret = mermaidConfig.hmacSecret,
            mermaidClient = mermaidInkLocalClient,
            mermaidConfig = mermaidConfig,
        )
    }
    configureRouting(
        serviceLifecycle = serviceLifecycle,
        diagramsApiDelegateImpl = diagramsApiDelegateImpl,
        mermaidClient = mermaidInkLocalClient,
    )
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureJvmMetrics()
}
