@file:Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")

package com.nextchaptersoftware.mermaidservice.api

import com.nextchaptersoftware.api.DiagramApiDelegateInterface
import com.nextchaptersoftware.compress.Base64CompressedUrlSafe
import com.nextchaptersoftware.compress.CompressionBase64UrlSafe
import com.nextchaptersoftware.config.MermaidConfig
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.mermaidservice.client.MermaidInkLocalClient
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import com.sksamuel.hoplite.Secret
import io.ktor.http.ContentDisposition
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.server.response.header
import io.ktor.server.routing.RoutingContext
import java.net.URI
import kotlinx.coroutines.withTimeout
import org.openapitools.server.Resources

private val LOGGER = mu.KotlinLogging.logger {}

class DiagramsApiDelegateImpl(
    private val authenticationSecret: Secret,
    private val mermaidClient: MermaidInkLocalClient,
    private val mermaidConfig: MermaidConfig,
) : DiagramApiDelegateInterface {

    companion object {
        private val imageType = ContentType.Image.PNG
        private const val MERMAID_INK_CALL_TIMEOUT = 4000L
    }

    private val authenticator by lazy {
        HMACAuthenticator(authenticationSecret = authenticationSecret)
    }

    private val errorImage: ByteArray by lazy {
        URI(mermaidConfig.errorImageUrl).toURL().openStream().readBytes()
    }

    override suspend fun renderDiagram(
        context: RoutingContext,
        input: Resources.renderDiagram,
    ): ByteArray {
        val encodedDiagram = Base64CompressedUrlSafe(input.encodedDiagram)

        context.call.response.header(
            name = HttpHeaders.ContentDisposition,
            value = ContentDisposition.Inline.withParameter(
                key = ContentDisposition.Parameters.FileName,
                value = "diagram.${imageType.contentSubtype}",
            ).toString(),
        )

        context.call.response.header(
            name = HttpHeaders.ContentType,
            value = imageType.toString(),
        )

        runSuspendCatching {
            authenticator.validate(signature = input.sig, payload = encodedDiagram.value, algorithm = HMACAuthenticator.Algorithm.SHA1)
        }.onFailure {
            LOGGER.error(it) { "Diagram render abuse detected" }
            return errorImage
        }

        return runCatching {
            withTimeout(MERMAID_INK_CALL_TIMEOUT) {
                mermaidClient.getDiagramImage(
                    CompressionBase64UrlSafe.decompress(encodedDiagram).urlSafeBase64Encode(),
                )
            }
        }.getOrElse {
            LOGGER.error(it) { "Error rendering diagram" }
            errorImage
        }
    }
}
