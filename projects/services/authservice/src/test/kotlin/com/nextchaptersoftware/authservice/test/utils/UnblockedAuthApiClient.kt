package com.nextchaptersoftware.authservice.test.utils

import com.nextchaptersoftware.access.NoOpRestrictedAccessService
import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.identity.IdentityAuthExchangeService
import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.authservice.module
import com.nextchaptersoftware.authservice.providers.ProviderAuthApiFactory
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.SamlIdpMetadataId
import com.nextchaptersoftware.db.utils.DatabaseContextPlugin
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.utils.CollectionsUtils.onNotEmpty
import io.ktor.client.HttpClient
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.cookie
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import io.ktor.http.contentType
import io.ktor.server.application.install
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import io.ktor.server.testing.testApplication
import java.util.UUID
import org.jetbrains.exposed.sql.Database

class UnblockedAuthApiClient(
    private val database: Database?,
    private val config: GlobalConfig = GlobalConfig.INSTANCE,
    private val providerAuthApiFactory: ProviderAuthApiFactory? = null,
    private val authToken: String? = null,
    private val restrictedAccessService: RestrictedAccessServiceInterface = NoOpRestrictedAccessService(),
    private val overrideIdentityAuthExchangeService: IdentityAuthExchangeService<in OAuthApiType>? = null,
    private val scmConfig: ScmConfig = ScmConfig.INSTANCE,
) {
    private fun authenticatedClient(block: suspend HttpClient.() -> Unit) = testApplication {
        application {
            module(
                config = config,
                scmConfig = scmConfig,
                overrideIdentityAuthExchangeService = overrideIdentityAuthExchangeService,
                overrideProviderAuthApiFactory = providerAuthApiFactory,
                overrideRestrictedAccessService = restrictedAccessService,
            )

            // Test endpoint for authentication
            routing {
                route("/api") {
                    authenticate("bearerAuth") {
                        get("/teams/{teamId}") {
                            call.respond(HttpStatusCode.OK)
                        }
                    }
                }
            }
            install(DatabaseContextPlugin) {
                database = <EMAIL>
            }
        }

        val client = createClient {
            this.followRedirects = false
            this.expectSuccess = false
            authToken?.let { token ->
                install(Auth) {
                    bearer {
                        loadTokens {
                            BearerTokens(
                                accessToken = token,
                                refreshToken = "",
                            )
                        }
                    }
                }
            }
            install(ContentNegotiation) {
                installJsonSerializer()
            }
            defaultRequest {
                contentType(ContentType.Application.Json)

                // Add SkipCORS bypass for local and CI testing. The trick is the non-standard scheme. See local.conf
                headers["Origin"] = "test://localhost"
            }
        }
        block(client)
    }

    /**
     * @see com.nextchaptersoftware.api.AuthApiDelegateInterface.loginOptionsV3
     * @see com.nextchaptersoftware.authservice.api.AuthApiDelegateImpl.loginOptionsV3
     */
    fun loginOptionsV3(
        agentType: AgentType? = null,
        clientSecret: UUID? = null,
        clientState: String? = null,
        enterpriseProviderIds: List<EnterpriseAppConfigId>? = null,
        inviteId: UUID? = null,
        manifestRedirectUrl: Url? = null,
        orgId: OrgId? = null,
        redirectUrl: Url? = null,
        ssoProviderIds: List<SamlIdpMetadataId>? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/login/optionsV3") {
            url {
                agentType?.let { parameters.append("agentType", it.enumValue) }
                clientSecret?.let { parameters.append("clientSecret", it.toString()) }
                clientState?.let { parameters.append("clientState", it) }
                enterpriseProviderIds?.onNotEmpty {
                    parameters.append("enterpriseProviderIds", enterpriseProviderIds.joinToString(",") { it.value.toString() })
                }
                manifestRedirectUrl?.let { parameters.append("manifestRedirectUrl", it.toString()) }
                redirectUrl?.let { parameters.append("redirectUrl", it.toString()) }
                ssoProviderIds?.onNotEmpty {
                    parameters.append("ssoProviderIds", ssoProviderIds.joinToString(",") { it.value.toString() })
                }
                orgId?.let { parameters.append("teamId", it.toString()) }
                inviteId?.let { parameters.append("inviteId", it.toString()) }
            }
        }
        block(response)
    }

    fun login(
        provider: Provider,
        clientSecret: UUID?,
        agentType: AgentType?,
        clientState: String?,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get {
            url {
                pathSegments = listOf("api", "login", provider.name)
                clientSecret?.let { parameters.append("clientSecret", it.toString()) }
                agentType?.let { parameters.append("agentType", it.enumValue) }
                clientState?.let { parameters.append("clientState", it) }
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.authservice.api.AuthApiDelegateImpl.exchangeAuthCodeV2
     */
    fun exchange(
        state: String,
        code: String,
        secret: UUID,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/login/exchangeV2") {
            url {
                parameters.append("state", state)
                parameters.append("code", code)
            }
            cookie(
                name = LoginService.CLIENT_SERVICE_COOKIE_NAME,
                value = secret.toString(),
            )
        }
        block(response)
    }

    fun exchangeNoCookie(
        state: String,
        code: String,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/login/exchangeV2") {
            url {
                parameters.append("state", state)
                parameters.append("code", code)
            }
        }
        block(response)
    }

    fun testAuth(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/teams/$orgId")
        block(response)
    }

    fun preAuth(
        provider: Provider,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/preauth") {
            url {
                parameters.append("provider", provider.enumValue)
            }
        }
        block(response)
    }

    fun preAuthExchange(
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/preauth/exchange")
        block(response)
    }

    fun refreshToken(
        sessionId: UUID? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/login/refresh") {
            url {
                sessionId?.also { parameters.append("sessionIdentifier", it.toString()) }
            }
        }
        block(response)
    }

    fun logout(
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = delete("/api/login")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.authservice.api.AuthInstallApiDelegateImpl.authExchange
     */
    fun authExchange(
        code: String,
        state: String,
        provider: Provider,
        block: suspend HttpResponse.() -> Unit,
    ) = authenticatedClient {
        val response = get("/api/auth/${provider.enumValue}/exchange") {
            parameter("code", code)
            parameter("state", state)
        }
        block(response)
    }
}
