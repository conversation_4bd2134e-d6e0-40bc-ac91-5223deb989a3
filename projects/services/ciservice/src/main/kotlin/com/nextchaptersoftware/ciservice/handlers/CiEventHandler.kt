package com.nextchaptersoftware.ciservice.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.ci.payloads.CiEvent
import com.nextchaptersoftware.ciservice.handlers.builds.BuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildTriageTaskHandler
import com.nextchaptersoftware.event.queue.handlers.EventHandler

class CiEventHandler(
    private val buildEventHandler: BuildEventHandler,
    private val buildJobEventHandler: BuildJobEventHandler,
    private val buildTriageTaskHandler: BuildTriageTaskHandler,
    private val ciWebhookEventHandler: CiWebhookEventHandler,
) : EventHandler {

    override suspend fun handle(event: String): Boolean {
        when (val ciEvent = event.decode<CiEvent>()) {
            is CiEvent.BuildCompletedEvent -> buildEventHandler.handle(ciEvent)
            is CiEvent.BuildJobCompletedEvent -> buildJobEventHandler.handle(ciEvent)
            is CiEvent.BuildTriageTask -> buildTriageTaskHandler.handle(ciEvent)
            is CiEvent.WebhookEvent -> ciWebhookEventHandler.handle(ciEvent)
        }
        return true
    }
}
