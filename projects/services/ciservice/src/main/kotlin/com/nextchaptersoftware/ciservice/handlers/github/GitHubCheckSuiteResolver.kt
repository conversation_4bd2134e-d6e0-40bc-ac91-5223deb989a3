package com.nextchaptersoftware.ciservice.handlers.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.cache.RedisReadThroughCache
import com.nextchaptersoftware.cache.logged
import com.nextchaptersoftware.cache.typed
import com.nextchaptersoftware.ci.github.models.asCiBuild
import com.nextchaptersoftware.ci.models.CiExecution
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.scm.github.GitHubActionsClient
import com.nextchaptersoftware.scm.github.models.GitHubCheckSuite
import io.lettuce.core.ExperimentalLettuceCoroutinesApi
import kotlin.time.Duration.Companion.minutes

internal object GitHubCheckSuiteResolver {

    suspend fun GitHubActionsClient.resolveCiBuild(
        repo: Repo,
        checkSuite: GitHubCheckSuite,
    ): CiExecution.CiBuild {
        return buildFromCheckSuiteId(repo, checkSuite)
            ?: checkSuite.asCiBuild()
    }

    @OptIn(ExperimentalLettuceCoroutinesApi::class)
    private val cache by lazy {
        RedisReadThroughCache(
            namespace = javaClass.simpleName,
            expiry = 10.minutes,
        ).typed<CiExecution.CiBuild?>(
            decode = { it.decode() },
            encode = { it.encode() },
        ).logged(
            name = "${javaClass.simpleName}.builds",
        )
    }

    private suspend fun GitHubActionsClient.buildFromCheckSuiteId(
        repo: Repo,
        checkSuite: GitHubCheckSuite,
    ): CiExecution.CiBuild? {
        val key = "${checkSuite.id}:${checkSuite.headSha}"
        return cache.getOrCompute(key) {
            workflowRunsList(
                repositoryId = repo.externalId,
                checkSuiteId = checkSuite.id.toString(),
                branch = checkSuite.headBranch,
                headSha = checkSuite.headSha,
            )
                .workflowRuns
                .firstOrNull()
                ?.asCiBuild()
        }
            ?.patchNullRunner(checkSuite = checkSuite)
            ?.patchRunState(checkSuite = checkSuite)
    }

    private fun CiExecution.CiBuild.patchNullRunner(
        checkSuite: GitHubCheckSuite,
    ): CiExecution.CiBuild {
        return when (runner) {
            null -> copy(runner = checkSuite.app.name)
            else -> this
        }
    }

    private fun CiExecution.CiBuild.patchRunState(
        checkSuite: GitHubCheckSuite,
    ): CiExecution.CiBuild {
        val webhookBuild = checkSuite.asCiBuild()
        return copy(
            pullRequestNumber = webhookBuild.pullRequestNumber,
            baseSha = webhookBuild.baseSha,
            headSha = webhookBuild.headSha,
            status = webhookBuild.status,
            result = webhookBuild.result,
            attempt = webhookBuild.attempt ?: attempt,
            startedAt = webhookBuild.startedAt ?: startedAt,
            completedAt = webhookBuild.completedAt ?: completedAt,
        )
    }
}
