package com.nextchaptersoftware.notificationservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.service.BackgroundJob
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class NotificationEventJob(
    private val eventDequeueService: EventDequeueService,
    private val jobTimeout: Duration,
) : BackgroundJob {
    override val name: String
        get() = "Notification Event Job"

    override suspend fun run() {
        withTimeout(jobTimeout) {
            runSuspendCatching {
                eventDequeueService.process()
            }.onFailure {
                LOGGER.errorAsync(it) { "Failed to process notification message" }
            }.getOrThrow()
        }
    }
}
