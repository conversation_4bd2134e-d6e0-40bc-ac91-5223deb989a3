package com.nextchaptersoftware.webhookservice.api

import com.nextchaptersoftware.api.LinearHooksApiDelegateInterface
import com.nextchaptersoftware.integration.queue.payloads.LinearWebhookHeaders
import com.nextchaptersoftware.linear.events.queue.enqueue.LinearEventEnqueueService
import com.nextchaptersoftware.linear.events.queue.payloads.LinearEvent
import com.nextchaptersoftware.log.kotlin.warnAsync
import io.ktor.server.routing.RoutingContext
import mu.KotlinLogging
import org.openapitools.server.Resources

private val LOGGER = KotlinLogging.logger {}

class LinearHooksApiDelegateImpl(
    private val linearEventEnqueueService: LinearEventEnqueueService,
) : LinearHooksApiDelegateInterface {
    override suspend fun linearEvent(
        context: RoutingContext,
        input: Resources.linearEvent,
        body: String,
    ): String {
        when (val signature = context.call.request.headers[LinearWebhookHeaders.LinearSignature.header]) {
            null -> {
                LOGGER.warnAsync { "Ignoring Linear webhook with missing signature header" }
            }

            else -> {
                linearEventEnqueueService.enqueueEvent(
                    event = LinearEvent.LinearWebhookEvent(
                        payload = body,
                        signature = signature,
                    ),
                )
            }
        }

        return "Accepted by Unblocked."
    }
}
