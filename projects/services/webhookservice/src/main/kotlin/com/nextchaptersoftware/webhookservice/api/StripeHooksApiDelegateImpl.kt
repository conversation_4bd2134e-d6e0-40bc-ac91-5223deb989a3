package com.nextchaptersoftware.webhookservice.api

import com.nextchaptersoftware.api.StripeHooksApiDelegateInterface
import com.nextchaptersoftware.stripe.webhook.services.StripeWebhookEventService
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class StripeHooksApiDelegateImpl(
    private val stripeWebhookEventService: StripeWebhookEventService,
) : StripeHooksApiDelegateInterface {
    override suspend fun stripeEvent(
        context: RoutingContext,
        input: Resources.stripeEvent,
        stripeSignature: String,
        body: String,
    ): String {
        stripeWebhookEventService.process(stripeSignature = stripeSignature, body = body)

        return "Accepted by Unblocked."
    }
}
