plugins {
    application
    kotlin("jvm")
}

application {
    mainClass.set("com.nextchaptersoftware.slackservice.ApplicationKt")
}

dependencies {
    testImplementation(testLibs.bundles.test.core)

    implementation(project(":projects:clients:client-openai", configuration = "default"))
    implementation(project(":projects:models", configuration = "default"))
    implementation(project(":projects:libs:lib-billing", configuration = "default"))
    implementation(project(":projects:libs:lib-common", configuration = "default"))
    implementation(project(":projects:libs:lib-integration-data-events", configuration = "default"))
    implementation(project(":projects:libs:lib-dsac-provider", configuration = "default"))
    implementation(project(":projects:libs:lib-log", configuration = "default"))
    implementation(project(":projects:libs:lib-log-kotlin", configuration = "default"))
    implementation(project(":projects:libs:lib-ml-completion-prompts", configuration = "default"))
    implementation(project(":projects:libs:lib-pinecone", configuration = "default"))
    implementation(project(":projects:libs:lib-rpc", configuration = "default"))
    implementation(project(":projects:libs:lib-service-bootstrap", configuration = "default"))
    implementation(project(":projects:libs:lib-slack-bot", configuration = "default"))
    implementation(project(":projects:libs:lib-slack-events", configuration = "default"))
    implementation(project(":projects:libs:lib-slack-ingestion", configuration = "default"))
    implementation(project(":projects:libs:lib-slack-webhook", configuration = "default"))
    implementation(project(":projects:libs:lib-topic-search", configuration = "default"))
    implementation(project(":projects:libs:lib-user-secret", configuration = "default"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlinx.serialization.ExperimentalSerializationApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
