package com.nextchaptersoftware.scmservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class PullRequestSummariesIngestionEventJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
        get() = "Pull Request Summaries Ingestion Event Job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
