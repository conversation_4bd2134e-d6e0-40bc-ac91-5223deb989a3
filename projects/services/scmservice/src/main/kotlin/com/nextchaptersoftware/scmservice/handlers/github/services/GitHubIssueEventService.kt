package com.nextchaptersoftware.scmservice.handlers.github.services

import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ghissues.services.GitHubIssuesController
import com.nextchaptersoftware.ghissues.services.GitHubIssuesIngestionService
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.models.GitHubIssueEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class GitHubIssueEventService(
    private val teamAndRepoProvider: TeamAndRepoProvider,
    private val gitHubIssuesController: GitHubIssuesController = GitHubIssuesController(),
    private val scmMembershipMaintenance: ScmMembershipMaintenance,
    private val gitHubIssuesIngestionService: GitHubIssuesIngestionService,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
) {
    @Suppress("LongMethod")
    suspend fun process(
        scm: Scm,
        event: GitHubIssueEvent,
    ): Unit = withLoggingContextAsync(
        "ownerExternalId" to event.ownerExternalId,
        "repoExternalId" to event.repoExternalId,
        "action" to event.action.name,
        "issueNumber" to event.issue.number,
    ) {
        if (event.issue.isPullRequest) {
            LOGGER.debugAsync { "Received GitHub issue event for pull request, ignoring" }
            return@withLoggingContextAsync
        }

        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = scm,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: return@withLoggingContextAsync

        val teamId = scmTeam.idValue
        val orgId = scmTeamStore.getOrgId(teamId = teamId)
        val repoId = repo.id
        val issue = event.issue.asScmIssue

        if (!gitHubIssuesController.enabled(teamId)) {
            LOGGER.debugAsync { "Team not enabled for GitHub issues, ignoring" }
            return@withLoggingContextAsync
        }

        when (event.action) {
            GitHubIssueEvent.Action.Deleted,
            -> {
                gitHubIssuesIngestionService.delete(
                    orgId = orgId,
                    repoId = repoId,
                    issueNumber = issue.number,
                )
            }

            GitHubIssueEvent.Action.Closed,
            GitHubIssueEvent.Action.Opened,
            GitHubIssueEvent.Action.Reopened,
            GitHubIssueEvent.Action.Edited,
            -> {
                val scmMemberProvider = scmMembershipMaintenance.addMembers(
                    scmTeam = scmTeam.asDataModel(),
                    scmUsers = listOfNotNull(issue.user),
                )

                gitHubIssuesIngestionService.ingest(
                    orgId = orgId,
                    repoId = repoId,
                    issue = issue,
                    scmMemberProvider = scmMemberProvider,
                )
            }

            GitHubIssueEvent.Action.Assigned,
            GitHubIssueEvent.Action.Demilestoned,
            GitHubIssueEvent.Action.Labeled,
            GitHubIssueEvent.Action.Locked,
            GitHubIssueEvent.Action.Milestoned,
            GitHubIssueEvent.Action.Pinned,
            GitHubIssueEvent.Action.Transferred,
            GitHubIssueEvent.Action.Typed,
            GitHubIssueEvent.Action.Unassigned,
            GitHubIssueEvent.Action.Unlabeled,
            GitHubIssueEvent.Action.Unlocked,
            GitHubIssueEvent.Action.Unpinned,
            -> doNothing()

            GitHubIssueEvent.Action.Unknown,
            -> LOGGER.debugAsync("action" to event._action) { "Unknown GitHub issue event action" }
        }
    }
}
