package com.nextchaptersoftware.scmservice.handlers.github.services

import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.pr.ingestion.PullRequestService
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestCommentThreadProvider
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestIngestionServiceProvider
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestArchiveEventService
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewCommentEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.UnblockedPRCommentSignature.hasSignature
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

// FIXME richie move to client-scm
class GitHubPullRequestReviewCommentEventService(
    private val teamAndRepoProvider: TeamAndRepoProvider,
    private val scmMembershipMaintenance: ScmMembershipMaintenance,
    private val pullRequestService: PullRequestService,
    private val prCommentThreadProvider: PullRequestCommentThreadProvider,
    private val prIngestionServiceProvider: PullRequestIngestionServiceProvider,
    private val pullRequestArchiveEventService: PullRequestArchiveEventService,
) {
    @Suppress("LongMethod")
    suspend fun process(
        scm: Scm,
        event: GitHubPullRequestReviewCommentEvent,
    ): Unit = withLoggingContextAsync(
        "ownerExternalId" to event.ownerExternalId,
        "repoExternalId" to event.repoExternalId,
        "commentId" to event.commentId,
    ) {
        val (scmTeamDAO, repo) = teamAndRepoProvider.get(
            scm = scm,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: return@withLoggingContextAsync

        val scmTeam = scmTeamDAO.asDataModel()

        val users = listOfNotNull(event.pullRequest.user, event.comment.user)
        val scmMemberProvider = scmMembershipMaintenance.addMembers(scmTeam = scmTeam, scmUsers = users.map { it.asScmUser })

        val pullRequest = pullRequestService.upsert(
            scm = scm,
            orgId = scmTeam.orgId,
            teamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = event.pullRequest.asScmPullRequest,
            prCreatorId = scmMemberProvider.getMemberId(
                scmUser = event.pullRequest.user.required().asScmUser,
            ),
        ) ?: return@withLoggingContextAsync

        val service = prIngestionServiceProvider.get(
            scmTeam = scmTeamDAO,
            repo = repo,
            pullRequest = pullRequest,
            scmMemberProvider = scmMemberProvider,
        )

        when (event.action) {
            GitHubPullRequestReviewCommentEvent.Action.Created -> {
                when (event.comment.inReplyToId) {
                    null -> {
                        val file = prCommentThreadProvider.getFile(
                            scmTeam = scmTeam,
                            repo = repo,
                            prNumber = event.pullRequest.number,
                            path = event.comment.path,
                        )

                        if (file == null) {
                            LOGGER.debugAsync { "Could not get file, not creating thread" }
                            return@withLoggingContextAsync
                        }

                        service.createThread(scm = scm, comment = event.comment.asScmPrComment, file = file)
                    }

                    else -> if (!event.comment.wasCreatedFromUnblocked) {
                        service.createReplyMessage(scm = scm, comment = event.comment.asScmPrComment)
                    }
                }
            }

            GitHubPullRequestReviewCommentEvent.Action.Edited -> {
                service.updateMessage(scm = scm, comment = event.comment.asScmPrComment)
            }

            GitHubPullRequestReviewCommentEvent.Action.Deleted -> {
                service.deleteMessage(comment = event.comment.asScmPrComment)
            }
        }

        pullRequestArchiveEventService.sendEvent(teamId = scmTeam.id, repoId = repo.id, pullRequestNumber = pullRequest.number)
    }
}

private val GitHubPullRequestReviewComment.wasCreatedFromUnblocked: Boolean
    get() = body.hasSignature
