package com.nextchaptersoftware.scmservice.jobs

import com.nextchaptersoftware.db.stores.PullRequestIngestionStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.service.BackgroundJob

/**
 * A job that will backfill the pull request ingestion model on repos that need it.
 */
class PullRequestIngestionBackfillJob(
    private val pullRequestIngestionStore: PullRequestIngestionStore = Stores.pullRequestIngestionStore,
) : BackgroundJob {

    override val name: String
        get() = javaClass.simpleName

    override suspend fun run() {
        pullRequestIngestionStore.backfillMissingIngestions()
    }
}
