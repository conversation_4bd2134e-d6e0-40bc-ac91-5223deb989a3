package com.nextchaptersoftware.scmservice.handlers.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.scm.github.GitHubAppApi
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestEvent
import com.nextchaptersoftware.scmservice.handlers.ScmHandler
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubPullRequestEventService

class GitHubPullRequestHandler(
    private val service: GitHubPullRequestEventService,
) : <PERSON>m<PERSON><PERSON><PERSON> {
    override suspend fun handle(headers: List<Pair<String, String>>, body: String, appApi: GitHubAppApi) {
        val payload = body.decode<GitHubPullRequestEvent>()
        service.process(appApi.scm, payload)
    }
}
