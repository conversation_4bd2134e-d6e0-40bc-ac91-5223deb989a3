package com.nextchaptersoftware.ingest.asana.jobs

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.asana.api.models.HumanReadableAsanaTask
import com.nextchaptersoftware.db.models.AsanaTask
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.AsanaTaskStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.models.EmbeddingMetadata
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.rapid.services.RapidServiceProvider
import com.nextchaptersoftware.rapid.types.RapidId
import com.nextchaptersoftware.rapid.types.RapidTenant
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.service.BackgroundJob
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlin.time.Duration.Companion.seconds
import kotlin.time.measureTimedValue
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class AsanaDataEmbeddingJob(
    private val embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    private val rapidServiceProvider: RapidServiceProvider,
    private val asanaTaskStore: AsanaTaskStore = Stores.asanaTaskStore,
) : BackgroundJob {

    override val name: String
        get() = javaClass.simpleName

    private val lockProvider by lazy {
        LockProvider(type = LockType.AsanaEmbedding)
    }

    /**
     * Embeds Asana tasks for one installation at random.
     */
    override suspend fun run() {
        asanaTaskStore.getNextTasksForEmbedding().forEach { (orgId, tasks) ->
            val installationId = tasks.firstOrNull()?.installationId ?: return@forEach

            lockProvider.create(installationId.value).also { lock ->
                if (lock.acquire()) {
                    try {
                        withLoggingContextAsync("orgId" to orgId, "installationId" to installationId) {
                            embedInstallationTasks(orgId, installationId, tasks)
                        }
                    } finally {
                        lock.release()
                    }
                    return
                }
            }
        }
    }

    private suspend fun embedInstallationTasks(orgId: OrgId, installationId: InstallationId, tasks: List<AsanaTask>) {
        // Embed tasks
        measureTimedValue {
            tasks.forEach { task ->
                check(task.installationId == installationId) { "Installation ID mismatch" }
                runSuspendCatching {
                    withTimeout(30.seconds) {
                        embedTask(orgId, task)
                    }
                }.onFailure {
                    LOGGER.errorAsync(t = it, "taskId" to task.id) { "Failed to embed Asana task" }
                }
            }
        }.also {
            LOGGER.debugAsync(
                "durationMs" to it.duration.inWholeMilliseconds,
                "taskCount" to tasks.size,
            ) {
                "Embedded Asana tasks for installation"
            }
        }
    }

    private suspend fun embedTask(orgId: OrgId, task: AsanaTask) {
        val tenant = RapidTenant(orgId.value)
        val id = RapidId(task.id.value)

        val content = rapidServiceProvider.queryService.getItem(tenant = tenant, id = id)?.content
            .required { "Missing Rapid item for task" }
            .value
            .decode<HumanReadableAsanaTask>()

        val metadata = EmbeddingMetadata(
            documentGroup = asanaTaskStore.generateGroupId(task.installationId, task.id),
            documentInstallation = task.installationId.value,
            documentOrg = orgId.value,
            documentSource = Provider.Asana,
            documentType = DocumentType.Documentation,
            externalUrl = task.permaLink,
            insightType = InsightType.Issue,
            sourceDocument = task.id.value,
            timestamp = task.createdAt,
            title = task.name,
        )

        // Send the embedding request
        embeddingEventEnqueueService.enqueueEmbedDocumentEvent(
            orgId = orgId,
            installationId = task.installationId,
            sourceDocumentId = task.id.value,
            metadata = metadata,
            content = content.asMarkdown(),
        )
        // Mark the task as embedded
        asanaTaskStore.completeEmbedding(id = task.id)
    }
}
