package com.nextchaptersoftware.ingest.asana

import com.nextchaptersoftware.db.health.PostgresHealthChecker
import com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder

fun main() {
    ServiceBootstrapBuilder.bootstrap {
        withHealthCheckers(listOf(PostgresHealthChecker()))
        withLogs()
        withHoneycomb()
        withDatabase()
    }.startHttpServer { serviceLifecycle ->
        module(
            serviceLifecycle = serviceLifecycle,
        )
    }
}
