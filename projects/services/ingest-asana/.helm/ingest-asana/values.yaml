baseservice:
  serviceAccount:
    name: "ingest-asana"
  service:
    type: NodePort
    port: 80
    targetPort: 8081
  secretProviderClass:
    create: true
    objects: |-
      - objectName: "redis-unblocked-password-1"
        objectType: "secretsmanager"
        objectAlias: "redis-password"
      - objectName: "activemq-unblocked-password-2"
        objectType: "secretsmanager"
        objectAlias: "activemq-password"
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchExpressions:
                - key: "app.kubernetes.io/name"
                  operator: In
                  values:
                    - ingest-asana
            topologyKey: kubernetes.io/hostname
