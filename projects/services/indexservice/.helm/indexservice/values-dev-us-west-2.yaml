baseservice:
  resources:
    limits:
      memory: 2048Mi
    requests:
      cpu: 500m
      memory: 1024Mi
  service:
    environment: "dev"
    baseUrl: "dev.getunblocked.com"
    envFrom:
      secretRefs:
        - unblocked-service-secrets-env
        - unblocked-content-service-secrets-env
        - unblocked-document-service-secrets-env
    env:
      - name: "REDIS_PASSWORD_FILE_PATH"
        value: "/secrets/redis-password"
      - name: "ACTIVEMQ_PASSWORD_FILE_PATH"
        value: "/secrets/activemq-password"
  kedaautoscaling:
    enabled: true
    minReplicaCount: 1
    maxReplicaCount: 3
    triggers:
      cloudwatch:
        - enabled: true
          expression: SELECT AVG(QueueSize) FROM SCHEMA("AWS/AmazonMQ", Broker,Queue) WHERE Queue = 'search_indexing_events'
          metricCollectionTime: "60"
          metricStat: "Average"
          metricStatPeriod: "30"
          metricEndTimeOffset: "20"
          targetMetricValue: "200.0"
          awsRegion: "us-west-2"
