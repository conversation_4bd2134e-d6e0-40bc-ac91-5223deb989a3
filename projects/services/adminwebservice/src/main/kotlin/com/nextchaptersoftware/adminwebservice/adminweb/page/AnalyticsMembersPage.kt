package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderTooltipFooter
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderZoomControls
import com.nextchaptersoftware.adminwebservice.adminweb.chart.Chart.renderChart
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartConfig
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartInteraction
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartLegend
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartOptions
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartPlugins
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartScale
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartType
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartZoom
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartData
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartDataPoint
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartDataset
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartScales
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItemInput
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.alignToStartOfMonth
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.alignToStartOfWeek
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.services.LoginAttemptComparisonReport
import com.nextchaptersoftware.api.services.SessionEventService
import com.nextchaptersoftware.api.services.SessionFlow
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ActivityType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.PersonSessionModel
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.SessionActionModel
import com.nextchaptersoftware.db.models.UserEngagementModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.AnyOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SessionEventStore
import com.nextchaptersoftware.db.stores.SessionEventStore.Companion.BEFORE_PERSON_CREATED_CLAUSE
import com.nextchaptersoftware.db.stores.Stores.samlIdpMetadataStore
import com.nextchaptersoftware.db.stores.UserEngagementStore.Companion.reportingDateAlias
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.utils.ReportingUtils.INITIAL_REPORTING_DATETIME
import com.nextchaptersoftware.utils.ReportingUtils.REPORTING_TIMEZONE
import com.nextchaptersoftware.utils.asPSTDayMonthYear
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.ContentDisposition
import io.ktor.http.HttpHeaders
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.header
import io.ktor.server.response.respondFile
import io.ktor.server.routing.RoutingContext
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.toInstant
import kotlinx.html.FlowContent
import kotlinx.html.InputType
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h5
import kotlinx.html.id
import kotlinx.html.p
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.ColumnSet
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.notInList

object AnalyticsMembersPage {

    suspend fun getMemberJoinedTimestamps(orgId: OrgId): List<Instant> {
        val installationsIds = samlIdpMetadataStore.findByOrgId(orgId)?.let { saml ->
            when {
                saml.isEnforced -> setOf(saml.installation)
                else -> null
            }
        }

        return suspendedTransaction {
            OrgModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgMemberModel,
                    otherColumn = OrgMemberModel.org,
                    onColumn = OrgModel.id,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = OrgMemberModel.id,
                ) {
                    MemberStore.IS_PRIMARY_CURRENT_MEMBER
                }
                .innerJoinInstallations(installationsIds)
                .join(
                    joinType = JoinType.INNER,
                    otherTable = PersonModel,
                    otherColumn = PersonModel.id,
                    onColumn = OrgMemberModel.person,
                )
                .select(PersonModel.id, PersonModel.createdAt)
                .where { OrgModel.id eq orgId }
                .withDistinct(true)
                .groupBy(PersonModel.id)
                .associateBy({ it[PersonModel.id].value }) { row ->
                    row[PersonModel.createdAt]
                }
        }.values.toList()
    }

    private fun ColumnSet.innerJoinInstallations(
        installationIds: Set<InstallationId>? = null,
    ): ColumnSet {
        if (installationIds == null) {
            return this
        }
        return join(
            joinType = JoinType.INNER,
            otherTable = InstallationModel,
            otherColumn = InstallationModel.id,
            onColumn = MemberModel.installation,
        ) {
            InstallationModel.id inList installationIds
        }
    }

    private suspend fun getDistinctMemberTimeSeriesByProductAgent(
        where: Op<Boolean>,
    ): Map<ProductAgentType, List<Pair<LocalDate, OrgMemberId>>> {
        return suspendedTransaction {
            UserEngagementModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.org,
                    onColumn = UserEngagementModel.orgId,
                ) {
                    AllOp(
                        ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE,
                        ScmTeamModel.memberCount greater 1,
                    )
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgModel,
                    otherColumn = OrgModel.id,
                    onColumn = UserEngagementModel.orgId,
                ) {
                    OrgStore.PROCESSING_COMPLETE
                }
                .select(UserEngagementModel.productAgent, reportingDateAlias, UserEngagementModel.orgMemberId)
                .whereAll(
                    where,
                    UserEngagementModel.createdAt.greaterEq(INITIAL_REPORTING_DATETIME),
                    UserEngagementModel.activityType inList ActivityType.engagementActivities,
                )
                .groupBy(UserEngagementModel.productAgent, reportingDateAlias, UserEngagementModel.orgMemberId)
                .orderBy(reportingDateAlias, SortOrder.ASC)
                .groupBy({ it[UserEngagementModel.productAgent] }) { row ->
                    Pair(
                        row[reportingDateAlias],
                        row[UserEngagementModel.orgMemberId],
                    )
                }
        }
    }

    private suspend fun getTimeSeriesSessionActionLoginEvents(): Map<String, List<Pair<LocalDate, UUID>>> {
        val actionIds = listOf("signIn", "getStarted", "bookADemo")
        return suspendedTransaction {
            SessionActionModel.join(
                otherTable = PersonSessionModel,
                joinType = JoinType.LEFT,
                onColumn = SessionActionModel.sessionId,
                otherColumn = PersonSessionModel.sessionId,
            )
                .join(otherTable = PersonModel, joinType = JoinType.LEFT, onColumn = PersonSessionModel.person, otherColumn = PersonModel.id)
                .select(SessionActionModel.actionId, SessionEventStore.reportingDateAlias, SessionActionModel.sessionId)
                .whereAll(
                    SessionActionModel.actionId inList actionIds,
                    AnyOp(
                        PersonSessionModel.sessionId.isNull(),
                        BEFORE_PERSON_CREATED_CLAUSE,
                    ),
                )
                .groupBy(SessionActionModel.actionId, SessionEventStore.reportingDateAlias, SessionActionModel.sessionId)
                .orderBy(SessionEventStore.reportingDateAlias, SortOrder.ASC)
                .groupBy({ it[SessionActionModel.actionId] }) { row ->
                    Pair(
                        row[SessionEventStore.reportingDateAlias],
                        row[SessionActionModel.sessionId],
                    )
                }
        }
    }

    /**
     * Get the distinct members who have used each product agent daily.
     */
    suspend fun getDistinctMemberTimeSeriesByProductAgentForOrg(
        orgId: OrgId,
    ): Map<ProductAgentType, List<Pair<LocalDate, OrgMemberId>>> {
        return getDistinctMemberTimeSeriesByProductAgent(
            OrgModel.id eq orgId,
        )
    }

    private suspend fun getDistinctMemberTimeSeriesByProductAgentAllOrgs(
        insiderService: InsiderServiceInterface,
    ): Map<ProductAgentType, List<Pair<LocalDate, OrgMemberId>>> {
        return getDistinctMemberTimeSeriesByProductAgent(
            OrgModel.id notInList insiderService.insiderOrgs,
        )
    }

    suspend fun RoutingContext.downloadSessionActions(
        sessionEventService: SessionEventService = SessionEventService(),
    ) {
        val receivedParameters = call.receiveParameters()
        val startDate = receivedParameters["startDate"]?.let {
            val localDateTime = LocalDateTime.parse(it)
            localDateTime.toInstant(TimeZone.UTC)
        } ?: error("startDate is required")
        val endDate = receivedParameters["endDate"]?.let {
            val localDateTime = LocalDateTime.parse(it)
            localDateTime.toInstant(TimeZone.UTC)
        } ?: error("endDate is required")

        val data = sessionEventService.getGetStartedSessionFlowsCSV(startDate..<endDate)
        call.response.header(
            HttpHeaders.ContentDisposition,
            ContentDisposition.Attachment.withParameter(ContentDisposition.Parameters.FileName, "sessionActions.csv")
                .toString(),
        )
        val tempFile = kotlin.io.path.createTempFile("sessionActions", ".csv").toFile()
        tempFile.writeText(data)
        call.respondFile(tempFile)
    }

    suspend fun RoutingContext.renderAnalyticsMembersPage(
        page: AdminPage,
        insiderService: InsiderService = InsiderService(),
        sessionEventService: SessionEventService = SessionEventService(),

        ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val distinctMemberTimeSeriesByProductAgent = getDistinctMemberTimeSeriesByProductAgentAllOrgs(insiderService)
        val today = Instant.nowWithMicrosecondPrecision()

        val loginAttemptReport = sessionEventService.generateLoginAttemptsComparisonReport(today.minus(30.days)..<today)
        val sessionFlows = sessionEventService.getSessionFlows(today.minus(7.days)..<today)
        val referralsCount = sessionEventService.getReferralsCount()

        val loginActionsMonthlyData = getTimeSeriesSessionActionLoginEvents()
        val path = call.request.path()

        val sessionActions = listOf(
            MenuItem(
                href = "$path/allSessionActions",
                label = "Download Get Started Session Actions",
                description = "Downloads (getStarted,triggerLogin,bookADemo,PersonAssociation) action flows as a CSV",
                inputs = listOf(
                    MenuItemInput.TextInput(
                        label = "Date",
                        name = "startDate",
                        inputType = InputType.dateTimeLocal,
                    ),
                    MenuItemInput.TextInput(
                        label = "Date",
                        name = "endDate",
                        inputType = InputType.dateTimeLocal,
                    ),
                ),
            ),
        )

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 { +page.label }
                    renderTooltipFooter()
                    renderZoomControls()
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAgentUsageChartWeekly(distinctMemberTimeSeriesByProductAgent)
                    }
                    div(classes = "col-6") {
                        renderAgentUsageChartMonthly(distinctMemberTimeSeriesByProductAgent)
                    }
                }

                div(classes = "row") {
                    renderReferralsCount(referralsCount)
                }

                div(classes = "row") {
                    renderSessionActionLoginEventsMonthly(loginActionsMonthlyData)
                }

                div(classes = "row") {
                    renderSessionFlows(sessionFlows)
                }

                div(classes = "row") {
                    renderLoginAttempts(loginAttemptReport, sessionActions)
                }
            }
        }
    }

    fun FlowContent.renderMemberGrowthChart(joinTimestamps: List<Instant>) {
        val now = Instant.nowWithMicrosecondPrecision()

        val datasets = joinTimestamps.let {
            var cumulative = 0L
            TimeSeriesChartDataset(
                label = "Members",
                data = joinTimestamps.sorted().map {
                    TimeSeriesChartDataPoint(x = it.toEpochMilliseconds(), y = ++cumulative)
                } + TimeSeriesChartDataPoint(x = now.toEpochMilliseconds(), y = cumulative),
            )
        }

        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = TimeSeriesChartScales<ChartScale<Double>, ChartScale<Double>>(
                x = ChartScale(
                    type = ChartScale.Type.time,
                ),
                y = ChartScale(
                    type = ChartScale.Type.linear,
                    title = ChartScale.ScaleTitle(display = true, text = "Members With Accounts"),
                ),
            ),
            plugins = ChartPlugins(
                legend = ChartLegend(position = ChartLegend.Position.right),
                zoom = ChartZoom(zoom = ChartZoom.defaultDragZoom),
            ),
        )

        val config = ChartConfig(
            type = ChartType.line,
            data = TimeSeriesChartData(datasets = listOf(datasets)),
            options = options,
        )

        h5(classes = "mt-5") { +"Cumulative Member Growth" }
        p(classes = "text-muted") { +"Growth in the number of members with Unblocked accounts over time." }

        renderChart(config)
    }

    fun FlowContent.renderAgentUsageChartMonthly(
        distinctMemberTimeSeriesByProductAgent: Map<ProductAgentType, List<Pair<LocalDate, OrgMemberId>>>,
    ) {
        val datasets = distinctMemberTimeSeriesByProductAgent
            .mapValues { (_, membersByDate) ->
                membersByDate
                    .groupBy({ it.first.alignToStartOfMonth().atStartOfDayIn(REPORTING_TIMEZONE) }) { it.second }
                    .mapValues { (_, teamMemberIds) -> teamMemberIds.distinct().size.toLong() }
                    .toList()
            }
            .map { (agent, membersByDate) ->
                TimeSeriesChartDataset(
                    label = agent.name,
                    data = membersByDate.map { (date, distinctMembers) ->
                        TimeSeriesChartDataPoint(x = date.toEpochMilliseconds(), y = distinctMembers)
                    },
                )
            }

        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = TimeSeriesChartScales<ChartScale<Double>, ChartScale<Double>>(
                x = ChartScale(
                    type = ChartScale.Type.time,
                    time = ChartScale.TimeOptions(minUnit = ChartScale.TimeUnit.month),
                    stacked = true,
                ),
                y = ChartScale(
                    type = ChartScale.Type.linear,
                    title = ChartScale.ScaleTitle(display = true, text = "Distinct Members"),
                    stacked = true,
                ),
            ),
            plugins = ChartPlugins(
                legend = ChartLegend(position = ChartLegend.Position.right),
                zoom = ChartZoom(zoom = ChartZoom.defaultDragZoom),
            ),
        )

        val config = ChartConfig(
            type = ChartType.bar,
            data = TimeSeriesChartData(datasets = datasets),
            options = options,
        )

        h5(classes = "mt-5") { +"Client Usage Monthly" }
        p(classes = "text-muted") { +"Product agents used by active members each month." }

        renderChart(config)
    }

    fun FlowContent.renderAgentUsageChartWeekly(
        distinctMemberTimeSeriesByProductAgent: Map<ProductAgentType, List<Pair<LocalDate, OrgMemberId>>>,
    ) {
        val datasets = distinctMemberTimeSeriesByProductAgent
            .mapValues { (_, membersByDate) ->
                membersByDate
                    .groupBy({ it.first.alignToStartOfWeek().atStartOfDayIn(REPORTING_TIMEZONE) }) { it.second }
                    .mapValues { (_, teamMemberIds) -> teamMemberIds.distinct().size.toLong() }
                    .toList()
            }
            .map { (agent, membersByDate) ->
                TimeSeriesChartDataset(
                    label = agent.name,
                    data = membersByDate.map { (date, distinctMembers) ->
                        TimeSeriesChartDataPoint(x = date.toEpochMilliseconds(), y = distinctMembers)
                    },
                )
            }

        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = TimeSeriesChartScales<ChartScale<Double>, ChartScale<Double>>(
                x = ChartScale(
                    type = ChartScale.Type.time,
                    time = ChartScale.TimeOptions(minUnit = ChartScale.TimeUnit.day),
                    stacked = true,
                ),
                y = ChartScale(
                    type = ChartScale.Type.linear,
                    title = ChartScale.ScaleTitle(display = true, text = "Distinct Members"),
                    stacked = true,
                ),
            ),
            plugins = ChartPlugins(
                legend = ChartLegend(position = ChartLegend.Position.right),
                zoom = ChartZoom(zoom = ChartZoom.defaultDragZoom),
            ),
        )

        val config = ChartConfig(
            type = ChartType.bar,
            data = TimeSeriesChartData(datasets = datasets),
            options = options,
        )

        h5(classes = "mt-5") { +"Client Usage Weekly" }
        p(classes = "text-muted") { +"Product agents used by active members each week." }

        renderChart(config)
    }

    private fun FlowContent.renderSessionActionLoginEventsMonthly(loginEvents: Map<String, List<Pair<LocalDate, UUID>>>) {
        val datasets = loginEvents
            .mapValues { (_, actionsByDate) ->
                actionsByDate
                    .groupBy({ it.first.alignToStartOfMonth().atStartOfDayIn(REPORTING_TIMEZONE) }) { it.second }
                    .mapValues { (_, sessionIds) -> sessionIds.distinct().size.toLong() }
                    .toList()
            }
            .map { (actionId, membersByDate) ->
                TimeSeriesChartDataset(
                    label = actionId,
                    data = membersByDate.map { (date, distinctMembers) ->
                        TimeSeriesChartDataPoint(x = date.toEpochMilliseconds(), y = distinctMembers)
                    },
                )
            }

        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = TimeSeriesChartScales<ChartScale<Double>, ChartScale<Double>>(
                x = ChartScale(
                    type = ChartScale.Type.time,
                    time = ChartScale.TimeOptions(minUnit = ChartScale.TimeUnit.month),
                    stacked = true,
                ),
                y = ChartScale(
                    type = ChartScale.Type.linear,
                    title = ChartScale.ScaleTitle(display = true, text = "Distinct Sessions"),
                    stacked = true,
                ),
            ),
            plugins = ChartPlugins(
                legend = ChartLegend(position = ChartLegend.Position.right),
                zoom = ChartZoom(zoom = ChartZoom.defaultDragZoom),
            ),
        )

        val config = ChartConfig(
            type = ChartType.bar,
            data = TimeSeriesChartData(datasets = datasets),
            options = options,
        )

        h5(classes = "mt-5") { +"Login Actions monthly" }
        p(classes = "text-muted") { +"Login actions for for users who didn't have an account at the time" }

        renderChart(config)
    }

    private fun FlowContent.renderSessionFlows(sessionFlows: List<SessionFlow>) {
        h5(classes = "mt-5") { +"Session Flows" }
        p(classes = "text-muted") { +"Session Flows for users across the last 7 days" }
        table(classes = "table table-sm table-hover") {
            thead {
                tr {
                    th { +"Events" }
                    th { +"Number of Users" }
                }
            }
            tbody {
                sessionFlows.forEach { report ->
                    tr {
                        td { +report.events.joinToString("->") }
                        td { +report.count.toString() }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderReferralsCount(referralsCount: List<SessionEventStore.SessionReferralCount>) {
        h5(classes = "mt-5") { +"Campaign Referrals" }
        p(classes = "text-muted") { +"Number of users who have signed up after seeing a campaign" }
        table(classes = "table table-sm table-hover") {
            thead {
                tr {
                    th { +"Campaign" }
                    th { +"Number of Users" }
                }
            }
            tbody {
                referralsCount.forEach { report ->
                    tr {
                        td { +report.campaignName }
                        td { +report.count.toString() }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderLoginAttempts(report: LoginAttemptComparisonReport, actions: List<MenuItem>) {
        h5(classes = "mt-5") { +"Aggregated Login Attempts" }
        p(classes = "text-muted") { +"User Login Attempts for last 30 days" }
        table(classes = "table table-sm table-hover") {
            thead {
                tr {
                    th { +"Date" }
                    th { +"User Login Attempts" }
                    th { +"Abandoned Users" }
                    th { +"Conversion Percentage" }
                }
            }
            tbody {
                report.aggregatedItems.forEach { report ->
                    tr {
                        td { +report.reportingDate.asPSTDayMonthYear }
                        td { +report.loginAttempts.toString() }
                        td { +report.abandonedAttempts.toString() }
                        td { +("${report.conversionPercentage}%") }
                    }
                }
            }
        }

        div(classes = "row mt-5") {
            div(classes = "col-10") {
                h5 { +"Detailed Login Attempts" }
                p(classes = "text-muted") { +"User Login Attempts for last 30 days" }
            }
            div(classes = "col-2") {
                renderActionMenu(actions, "Session Actions")
            }
        }

        table(classes = "table table-sm table-hover") {
            thead {
                tr {
                    th { +"Date" }
                    th { +"Page" }
                    th { +"Event" }
                    th { +"User Login Attempts" }
                    th { +"Abandoned Users" }
                    th { +"Conversion Percentage" }
                }
            }
            tbody {
                report.reportItems.forEach { report ->
                    tr {
                        td { +report.reportingDate.asPSTDayMonthYear }
                        td { +report.canonicalUrl }
                        td { +report.actionId }
                        td { +report.loginAttempts.toString() }
                        td { +report.abandonedAttempts.toString() }
                        td { +("${report.conversionPercentage}%") }
                    }
                }
            }
        }
    }
}
