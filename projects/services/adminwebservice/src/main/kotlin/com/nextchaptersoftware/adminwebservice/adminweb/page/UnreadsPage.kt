package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.orgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.component.ActionButton
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.actionButton
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.models.ThreadUnread
import com.nextchaptersoftware.db.models.ThreadUnreadDAO
import com.nextchaptersoftware.db.models.ThreadUnreadId
import com.nextchaptersoftware.db.models.ThreadUnreadModel
import com.nextchaptersoftware.db.models.UnreadStatus
import com.nextchaptersoftware.db.models.toIdentity
import com.nextchaptersoftware.db.models.toThreadUnread
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.ThScope
import kotlinx.html.h1
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.selectAll

object UnreadsPage {

    suspend fun RoutingContext.renderUnreadsPage(page: AdminPage) {
        val breadcrumb = call.makeBreadcrumb()

        val org = call.parameters.org()
        val threadId = call.parameters.optionalId("threadId", ::ThreadId)
        val memberId = call.parameters.optionalId("memberId", ::MemberId)

        val unreads = suspendedTransaction {
            ThreadUnreadModel
                .join(
                    otherTable = ThreadModel,
                    otherColumn = ThreadModel.id,
                    onColumn = ThreadUnreadModel.thread,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = ThreadUnreadModel.orgMember,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                    joinType = JoinType.INNER,
                )
                .selectAll()
                .whereAll(
                    ThreadModel.org eq org.id,
                    threadId?.let { ThreadUnreadModel.thread eq threadId },
                    memberId?.let { MemberModel.id eq it },
                ).map {
                    Pair(
                        it.toThreadUnread(),
                        it.toIdentity(),
                    )
                }
        }
        val adminIdentity = call.getAdminIdentity()
        val path = call.request.path()

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                h1 { +page.label }
                renderUnreads(unreads, path)
            }
        }
    }

    private fun FlowContent.renderUnreads(unreads: List<Pair<ThreadUnread, Identity>>, path: String) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Team Member" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Modified" }
                    th(scope = ThScope.col) { +"Unread Status" }
                    th(scope = ThScope.col) { +"Actions" }
                }
            }
            tbody(classes = "table-dark") {
                unreads.forEach { (unread, member) ->
                    tr {
                        td { profile(member) }
                        td { timeAgo(unread.createdAt) }
                        td { timeAgo(unread.modifiedAt) }
                        td {
                            badge(
                                bootstrapStyle = when (unread.status) {
                                    UnreadStatus.AllMessagesRead -> BootstrapStyle.Success
                                    UnreadStatus.SomeMessagesRead -> BootstrapStyle.Warning
                                    UnreadStatus.NoMessagesRead -> BootstrapStyle.Danger
                                },
                            ) { +unread.status.name }
                        }
                        td {
                            actionButton(
                                ActionButton(
                                    label = "Delete",
                                    href = "$path/${unread.id}/delete",
                                    style = BootstrapStyle.Danger,
                                    method = FormMethod.post,
                                ),
                            )
                        }
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.deleteThreadUnread() {
        val orgId = call.parameters.orgId()
        val unreadId = call.parameters.requiredId("unreadId", ::ThreadUnreadId)

        suspendedTransaction {
            val threadUnread = ThreadUnreadDAO[unreadId]
            require(threadUnread.thread.org.id.value == orgId) { "Thread unread does not belong to org" }
            threadUnread.delete()
        }

        call.respondRedirect(
            call.request.path()
                .replaceAfterLast("/", "").trimEnd('/')
                .replaceAfterLast("/", "").trimEnd('/'),
        )
    }
}
