package com.nextchaptersoftware.adminwebservice.adminweb.component

import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import java.util.UUID
import kotlinx.html.FlowContent
import kotlinx.html.ScriptType
import kotlinx.html.code
import kotlinx.html.details
import kotlinx.html.div
import kotlinx.html.id
import kotlinx.html.img
import kotlinx.html.onClick
import kotlinx.html.script
import kotlinx.html.style
import kotlinx.html.summary
import kotlinx.html.title
import kotlinx.html.unsafe

object CodePreview {

    private const val MAX_PREVIEW_LINES: Int = 4

    private fun FlowContent.addCopyFunction(contentId: UUID) {
        script(type = ScriptType.textJavaScript) {
            unsafe {
                +"""
                function copyCodeToClipboard(codeBlockId) {
                    // Get the code block element by its ID
                    var codeBlock = document.getElementById(codeBlockId);

                    if (!codeBlock) {
                        return;
                    }

                    // Use the Clipboard API to copy the text
                    navigator.clipboard.writeText(codeBlock.textContent).then(function() {
                        console.log('Text copied to clipboard');
                    }).catch(function(err) {
                        console.error('Failed to copy text: ', err);
                    });
                }
                """.trimIndent()
            }
        }
        img(classes = "mx-2", src = "$WEB_ROOT/images/copy.svg") {
            onClick = "copyCodeToClipboard('$contentId')"
            style = "cursor: pointer;"
            alt = "Copy Content"
            title = "Copy Content"
            width = "20px"
        }
    }

    fun FlowContent.renderCodePreview(content: String) {
        val lines: List<String> = content.split("\n").dropWhile { it.isBlank() }
        val blockId = UUID.randomUUID()

        div {
            style = "display:none;"
            id = blockId.toString()
            +lines.joinToString("\n")
        }

        when {
            lines.size <= MAX_PREVIEW_LINES -> {
                code {
                    +lines.joinToString("\n")
                }
            }

            else -> {
                details {
                    summary {
                        code {
                            +lines.take(MAX_PREVIEW_LINES).joinToString("\n")
                        }
                    }
                    code {
                        +lines.drop(MAX_PREVIEW_LINES).joinToString("\n")
                    }
                }
            }
        }

        addCopyFunction(contentId = blockId)
    }
}
