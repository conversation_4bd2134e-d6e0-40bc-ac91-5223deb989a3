package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.FilePoint
import com.nextchaptersoftware.db.models.Point
import com.nextchaptersoftware.db.models.SourceMark
import com.nextchaptersoftware.db.models.SourceMarkDAO
import com.nextchaptersoftware.db.models.SourceMarkId
import com.nextchaptersoftware.db.models.SourcePoint
import com.nextchaptersoftware.db.models.SourcePointDAO
import com.nextchaptersoftware.db.models.SourcePointModel
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.ScriptType
import kotlinx.html.ThScope
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.img
import kotlinx.html.onClick
import kotlinx.html.pre
import kotlinx.html.script
import kotlinx.html.span
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.title
import kotlinx.html.tr
import kotlinx.html.unsafe
import org.jetbrains.exposed.sql.SortOrder

object SourceMarkPage {
    suspend fun RoutingContext.renderSourceMarkPage(page: AdminPage) {
        val breadcrumb = call.makeBreadcrumb()
        val org = call.parameters.org()

        val sourcemarkId = call.parameters.requiredId("sourcemarkId", ::SourceMarkId)

        val mark = suspendedTransaction {
            SourceMarkDAO[sourcemarkId].asDataModel()
        }

        val points = suspendedTransaction {
            SourcePointDAO
                .find { SourcePointModel.mark eq mark.id }
                .orderBy(SourcePointModel.createdAt to SortOrder.ASC)
                .map { it.asDataModel() }
        }
        val adminIdentity = call.getAdminIdentity()

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                val isFileMark = points.firstOrNull() is FilePoint

                h1 {
                    when {
                        isFileMark -> +"Filemark"
                        else -> +"Sourcemark"
                    }
                }
                renderMark(mark)

                h3(classes = "mt-5") {
                    when {
                        isFileMark -> +"Filepoints"
                        else -> +"Sourcepoints"
                    }
                }
                renderPoints(points)
            }
        }
    }

    fun FlowContent.renderMark(mark: SourceMark) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("ID", mark.id)
                property("Created", mark.createdAt)
                property("Modified", mark.modifiedAt)
                property("Traits") {
                    if (mark.isDeleted) {
                        badge(bootstrapStyle = BootstrapStyle.Danger) { +"Deleted" }
                    }
                    if (mark.isArchived) {
                        badge(bootstrapStyle = BootstrapStyle.Warning) { +"Archived" }
                    }
                }
            }
        }
    }

    @Suppress("LongMethod")
    fun FlowContent.renderPoints(points: List<Point>) {
        table(classes = "table align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"#" }
                    th(scope = ThScope.col) { +"Traits" }
                    th(scope = ThScope.col) { +"File Path" }
                    th(scope = ThScope.col) { +"Lines" }
                    th(scope = ThScope.col) { +"Columns" }
                    th(scope = ThScope.col) { +"Commit Hash" }
                    th(scope = ThScope.col) { +"File Hash" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col) { +"Copy" }
                }
            }
            tbody(classes = "table-dark") {
                points.forEachIndexed { index, point ->
                    tr {
                        td { +index.toString() }
                        td {
                            if (point.isOriginal) {
                                badge(bootstrapStyle = BootstrapStyle.Info) { +"Original" }
                            }
                            if (point is SourcePoint && point.isUntrusted) {
                                badge(bootstrapStyle = BootstrapStyle.Warning) { +"Untrusted" }
                            }
                            point.stopPropagation?.let { reason ->
                                badge(bootstrapStyle = BootstrapStyle.Danger) { +reason.label }
                            }
                        }
                        td {
                            code {
                                style = "word-wrap: anywhere;"
                                +point.filePath
                            }
                        }
                        when (point) {
                            is FilePoint -> {
                                td { +"-" }
                                td { +"-" }
                            }

                            is SourcePoint -> {
                                td { +point.lineRange.toString() }
                                td { +"${point.columnStart}..${point.columnEnd}" }
                            }
                        }
                        td { code { +point.commitHash.asShortString() } }
                        td { code { +point.fileHash.asShortString() } }
                        td { timeAgo(point.createdAt) }
                        when (point) {
                            is FilePoint -> td { }
                            is SourcePoint -> td { copyPoint(point, index) }
                        }
                    }
                    if (point is SourcePoint && point.isOriginal) {
                        tr {
                            td(classes = "px-4") {
                                colSpan = "9"
                                tryRenderSnippet(point)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun FlowContent.copyPoint(point: SourcePoint, index: Int) {
        val copyFunctionName = "copy$index"

        script(type = ScriptType.textJavaScript) {
            val gitCommand = """
        git show ${point.commitHash}:'${point.filePath}' | head -${point.lineRange.last} | tail -${point.lineRange.length}
        """.trimIndent()

            unsafe {
                +"""
            const $copyFunctionName = () => navigator.clipboard.writeText(`$gitCommand`);
            """.trimIndent()
            }
        }

        img(classes = "mx-2", src = "$WEB_ROOT/images/copy.svg") {
            onClick = "$copyFunctionName()"
            style = "cursor: pointer;"
            alt = "Copy Git command"
            title = "Copy Git command"
            width = "20px"
        }
    }

    val IntRange.length
        get() = last - first + 1

    private fun FlowContent.tryRenderSnippet(point: SourcePoint) {
        runSuspendCatching {
            if (point.snippet?.isEmpty == false) {
                renderSnippet(point)
            } else {
                span(classes = "text-danger") {
                    +"Error: Snippet is empty."
                }
            }
        }.onFailure { e ->
            span(classes = "text-danger") {
                +("Error: Snippet is corrupt: ${e.toString().ifEmpty { "Unknown error." }}")
            }
        }
    }

    private fun FlowContent.renderSnippet(point: SourcePoint) {
        point.snippet?.also { snippet ->
            val preStyle = "white-space: pre-wrap;"
            var lineNum = snippet.firstLine

            pre(classes = "m-0") {
                style = preStyle
                point.snippetPreContext.forEach { line ->
                    +("%4d".format(lineNum++) + " |$line\n")
                }
            }
            pre(classes = "m-0 bg-primary") {
                style = preStyle
                point.snippetExactContext.forEach { line ->
                    +("%4d".format(lineNum++) + " |$line\n")
                }
            }
            pre(classes = "m-0") {
                style = preStyle
                point.snippetPostContext.forEach { line ->
                    +("%4d".format(lineNum++) + " |$line\n")
                }
            }
        }
    }
}
