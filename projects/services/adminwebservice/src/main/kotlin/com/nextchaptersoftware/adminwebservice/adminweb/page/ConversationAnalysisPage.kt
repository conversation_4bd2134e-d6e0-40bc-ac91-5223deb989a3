package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrg
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.distributionProgressBar
import com.nextchaptersoftware.adminwebservice.adminweb.component.modalActionButton
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.conversationanalysis.ConversationAnalysisService
import com.nextchaptersoftware.db.models.ConversationAnalysisId
import com.nextchaptersoftware.db.stores.ConversationAnalysisAggregate
import com.nextchaptersoftware.db.stores.ConversationAnalysisStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.AdminUrlBuilder
import com.nextchaptersoftware.utils.asLocalYearMonthDay
import com.nextchaptersoftware.utils.ensureTrailingSlash
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.span
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object ConversationAnalysisPage {
    private const val MAX_ANALYSIS_COUNT = 200

    suspend fun RoutingContext.renderConversationAnalysisPage(
        page: AdminPage,
        store: ConversationAnalysisStore = Stores.conversationAnalysisStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val org = call.parameters.optionalOrg()
        val path = call.request.path()

        val analyses = store.find(
            orgId = org?.id,
            fromDate = null,
            toDate = null,
        ).take(MAX_ANALYSIS_COUNT)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                h1 { +page.label }
                renderAnalysisTable(analyses, path)
            }
        }
    }

    @Suppress("MagicNumber")
    private fun FlowContent.renderAnalysisTable(analyses: List<ConversationAnalysisAggregate>, path: String) {
        div(classes = "container-fluid") {
            div(classes = "row") {
                div(classes = "col-12 table-responsive") {
                    table(classes = "table table-responsive align-middle") {
                        thead {
                            tr {
                                th { +"ID" }
                                th { +"Type" }
                                th { +"Org" }
                                th { +"Created" }
                                th { +"From Date" }
                                th { +"To Date" }
                                th { +"State" }
                                th { +"Progress" }
                                th { +"Actions" }
                            }
                        }
                        tbody(classes = "table-dark") {
                            analyses.forEach { analysis ->
                                tr {
                                    attributes["onclick"] = onClickAction(path.ensureTrailingSlash + analysis.analysis.id.toString())
                                    style = "cursor: pointer;"

                                    td {
                                        when {
                                            analysis.analysis.name.isNullOrBlank() -> span("text-muted small") { +analysis.analysis.id.toString() }
                                            else -> +"${analysis.analysis.name}"
                                        }
                                    }
                                    td {
                                        asBadge(analysis.analysis.analysisType)
                                    }
                                    td {
                                        analysis.org?.let {
                                            profile(it)
                                        } ?: +"Cross Org"
                                    }
                                    td { timeAgo(analysis.analysis.createdAt) }
                                    td { +"${analysis.analysis.fromDate?.asLocalYearMonthDay ?: +"-"}" }
                                    td { +"${analysis.analysis.toDate?.asLocalYearMonthDay ?: +"-"}" }
                                    td {
                                        asBadge(analysis.executionState.state)
                                    }
                                    td {
                                        val percentage = if (analysis.analysis.totalConversationsCount > 0) {
                                            (analysis.executionState.completed * 100) / analysis.analysis.totalConversationsCount
                                        } else {
                                            0
                                        }
                                        +"$percentage% (${analysis.executionState.completed}/${analysis.analysis.totalConversationsCount})"
                                        span {
                                            distributionProgressBar(
                                                total = analysis.analysis.totalConversationsCount,
                                                success = analysis.executionState.completed,
                                                failure = analysis.executionState.failed,
                                            )
                                        }
                                    }
                                    td {
                                        modalActionButton(
                                            id = "${analysis.analysis.id}-delete",
                                            showButtonHelp = false,
                                            bootstrapStyle = BootstrapStyle.Danger,
                                            item = MenuItem(
                                                label = "Delete",
                                                description = "Delete this analysis and its results",

                                                href = "$path/${analysis.analysis.id}/delete",
                                            ),
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.deleteAnalysis(conversationAnalysisService: ConversationAnalysisService) {
        val analysisId = call.parameters.requiredId("analysisId")
        conversationAnalysisService.deleteAnalysis(analysisId.let(::ConversationAnalysisId))

        // Construct the redirect URL based on whether we have an orgId
        val redirectPath = AdminUrlBuilder()
            .also { builder ->
                call.parameters.optionalOrgId()?.also { orgId ->
                    builder.withOrg(orgId.value).withMachineLearning()
                } ?: run {
                    builder.withPath("analytics")
                }
            }
            .withPath("conversationAnalysis")
            .build()
            .toString()

        call.respondRedirect(redirectPath)
    }
}
