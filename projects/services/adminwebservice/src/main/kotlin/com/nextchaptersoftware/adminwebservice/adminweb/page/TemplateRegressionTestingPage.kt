package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminNavigationTab
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.ActionButton
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.actionButton
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceRuntimeConfiguration
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.RegressionTestId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.search.semantic.services.eval.InflatedRegressionTest
import com.nextchaptersoftware.search.semantic.services.eval.PassState
import com.nextchaptersoftware.search.semantic.services.eval.RegressionTestService
import com.nextchaptersoftware.search.semantic.services.eval.durationDriftPass
import com.nextchaptersoftware.search.semantic.services.eval.evalScoreDriftPass
import com.nextchaptersoftware.search.semantic.services.eval.similarityScorePass
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.button
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h2
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object TemplateRegressionTestingPage {
    @Suppress("MagicNumber")
    data class RegressionTestRun(
        val scmTeam: ScmTeam,
        val template: MLInferenceTemplate,
        val runConfiguration: MLInferenceRuntimeConfiguration,
        val results: List<ValidationResultPair>,
    ) {
        val score: Double
            get() {
                val completedScores = results.mapNotNull { it.result?.validationDistance }
                if (completedScores.isEmpty()) return 0.0
                return completedScores.sumOf { it } / completedScores.size
            }

        val secondsDelta: Long
            get() {
                val completedDurationDeltas = results.mapNotNull {
                    it.result?.runDuration?.let { runDuration ->
                        it.validationRecord?.runDuration?.let { validationDuration ->
                            runDuration.inWholeSeconds - validationDuration.inWholeSeconds
                        }
                    }
                }
                if (completedDurationDeltas.isEmpty()) return 0
                return completedDurationDeltas.sumOf { it } / completedDurationDeltas.size
            }

        val state: RegressionTestRunState
            get() {
                return when {
                    runConfiguration.validationRecord == null -> RegressionTestRunState.Invalid

                    results.mapNotNull { it.result }.size == results.size -> {
                        RegressionTestRunState.fromScore(score, secondsDelta)
                    }

                    else -> RegressionTestRunState.InProgress
                }
            }

        val progress: Double
            get() {
                val completed = results.mapNotNull { it.result }.toSet()
                return completed.size.toDouble() / results.size.toDouble()
            }
    }

    data class ValidationResultPair(
        val validationRecord: MLInference?,
        val result: MLInference?,
    )

    enum class RegressionTestRunState {
        Pass,
        Fail,
        InProgress,
        Invalid,
        ;

        companion object {
            @Suppress("MagicNumber")
            fun fromScore(score: Double?, secondsDelta: Long) = score?.let {
                if (it >= 0.95 && secondsDelta <= 5) {
                    Pass
                } else {
                    Fail
                }
            } ?: InProgress
        }
    }

    suspend fun RoutingContext.renderTemplateRegressionTestingPage(
        page: AdminPage,
        templateService: MLInferenceTemplateService,
        regressionTestService: RegressionTestService,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val adminIdentity = call.getAdminIdentity()
        val path = call.request.path()

        val templateId = call.parameters.requiredId("templateId", ::MLInferenceTemplateId)

        val template = templateService.get(templateId)

        val tests = regressionTestService.findByTemplate(templateId).map {
            regressionTestService.inflateFromTest(it.id)
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +"${page.label} - ${template.name}" }

                renderRunActions(
                    path = path,
                )

                renderTests(
                    templateId = templateId,
                    tests = tests,
                )
            }
        }
    }

    private fun FlowContent.renderRunActions(
        path: String,
    ) {
        form(
            classes = "d-flex flex-column form-group",
            action = "$path/runRegressionTest",
            method = FormMethod.post,
        ) {
            div(classes = "d-flex p-2 justify-content-end") {
                button(classes = "btn btn-primary ml-auto", type = ButtonType.submit) {
                    +"Run Regression Test"
                }
            }
        }
    }

    private fun FlowContent.renderTests(
        templateId: MLInferenceTemplateId,
        tests: List<InflatedRegressionTest>,
    ) {
        h2(classes = "mt-5") { +"Tests" }
        table(classes = "table table-striped table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Pass/Fail" }
                    th(scope = ThScope.col) { +"Date" }
                    th(scope = ThScope.col) { +"Status" }
                    th(scope = ThScope.col) { +"Avg Score Improvement" }
                    th(scope = ThScope.col) { +"Avg Duration Improvement" }
                    th(scope = ThScope.col) { +"Avg Similarity" }
                    th(scope = ThScope.col, classes = "noSort") { +"Actions" }
                }
            }
            tbody(classes = "table-darK") {
                tests.forEach {
                    renderTest(
                        templateId = templateId,
                        test = it,
                    )
                }
            }
        }
    }

    @Suppress("CyclomaticComplexMethod")
    private fun TBODY.renderTest(
        templateId: MLInferenceTemplateId,
        test: InflatedRegressionTest,
    ) {
        tr {
            attributes["onclick"] = "window.location='${AdminNavigationTab.MLTemplates.route}/$templateId/regression-tests/${test.testId}';"
            style = "cursor: pointer;"

            val passBadge = when (test.pass) {
                PassState.Pending -> null
                PassState.Passed -> true
                is PassState.Failed -> false
            }

            td {
                asBadge(
                    bool = passBadge,
                    trueText = "Pass",
                    falseText = "Fail",
                    unknownText = "Pending",
                    unknownStyle = BootstrapStyle.Warning,
                )
                if (test.pass is PassState.Failed) {
                    div(classes = "small text-muted d-none d-xl-block") {
                        +(test.pass as PassState.Failed).reason
                    }
                }
            }
            td { timeAgo(test.createdAt) }
            td {
                asBadge(
                    test = test,
                )
            }

            val averageEvalScoreDrift = test.averageEvalScoreDrift
            val evalScoreCellClass = if (averageEvalScoreDrift == 0.0) {
                ""
            } else {
                when (evalScoreDriftPass(averageEvalScoreDrift)) {
                    true -> if (averageEvalScoreDrift > 0.0) {
                        "table-success"
                    } else {
                        "table-warning"
                    }

                    false -> "table-danger"
                }
            }
            val averageDurationDrift = test.averageDurationDrift
            val averageDurationCellClass = if (averageDurationDrift == 0.0) {
                ""
            } else {
                when (durationDriftPass(averageDurationDrift)) {
                    true -> if (averageDurationDrift > 0.0) {
                        "table-success"
                    } else {
                        "table-warning"
                    }

                    false -> "table-danger"
                }
            }
            val averageSimilarityScore = test.averageSimilarityScore
            val averageSimilarityCellClass = if (averageSimilarityScore == 0.0) {
                ""
            } else {
                when (similarityScorePass(averageSimilarityScore)) {
                    true -> "table-success"
                    false -> "table-danger"
                }
            }

            td(classes = evalScoreCellClass) {
                +averageEvalScoreDrift.toString()
            }
            td(classes = averageDurationCellClass) {
                +averageDurationDrift.toString()
            }
            td(classes = averageSimilarityCellClass) {
                +averageSimilarityScore.toString()
            }
            td {
                div(classes = "list-group list-group-horizontal") {
                    actionButton(
                        ActionButton(
                            label = "Delete",
                            href = "${AdminNavigationTab.MLTemplates.route}/$templateId/regression-tests/${test.testId}/delete",
                            style = BootstrapStyle.Danger,
                            method = FormMethod.get,
                        ),
                    )
                }
            }
        }
    }

    suspend fun RoutingContext.runRegressionTest(
        regressionTestService: RegressionTestService,
    ) {
        val templateId = call.parameters.requiredId("templateId", ::MLInferenceTemplateId)

        regressionTestService.createAndRunRegressionTest(templateId)
    }

    suspend fun RoutingContext.deleteRegressionTest(
        regressionTestService: RegressionTestService,
    ) {
        val templateId = call.parameters.requiredId("templateId")
        val testId = call.parameters.requiredId("testId", ::RegressionTestId)

        regressionTestService.deleteRegressionTest(testId)

        call.respondRedirect("${AdminNavigationTab.MLTemplates.route}/$templateId/regression-tests")
    }
}
