package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.asProgressBar
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.models.BulkEngagementMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.utils.IngestionAlert
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ClientVersion
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.UserEngagementModel
import com.nextchaptersoftware.db.models.toPerson
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.db.stores.UserEngagementStore
import com.nextchaptersoftware.ingestion.redis.IngestionDisablementService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.utils.epoch
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import kotlinx.html.FlowContent
import kotlinx.html.InputAutoComplete
import kotlinx.html.InputType
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.id
import kotlinx.html.input
import kotlinx.html.label
import kotlinx.html.p
import kotlinx.html.role
import kotlinx.html.span
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.title
import kotlinx.html.tr
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.max
import org.jetbrains.exposed.sql.selectAll

object PeoplePage {

    enum class PeopleView(val label: String, val description: String) {
        Active(
            label = "Active People",
            description = "People that have been active for at least one day in the last 30 days.",
        ),
        New(
            label = "New People",
            description = "People that have signed up in the last 7 days.",
        ),
        External(
            label = "External People",
            description = "All external people.",
        ),
        Internal(
            label = "Internal People",
            description = "All internal people.",
        ),
    }

    private const val MAX_PEOPLE = 2000

    private suspend fun getPeople(
        peopleView: PeopleView,
        insiderService: InsiderServiceInterface,
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
    ): List<Person> {
        return suspendedTransaction {
            when (peopleView) {
                PeopleView.Active -> {
                    val ids = userEngagementStore.getActivePeople(
                        trx = this,
                        since = Instant.nowWithMicrosecondPrecision().minus(30.days),
                        limit = MAX_PEOPLE,
                    ).filterNot { insiderService.isInsiderPerson(personId = it) }
                    PersonDAO.forIds(ids).map { it.asDataModel() }
                }

                PeopleView.New ->
                    PersonModel
                        .selectAll()
                        .where { PersonModel.createdAt greaterEq Instant.nowWithMicrosecondPrecision().minus(7.days) }
                        .limit(MAX_PEOPLE)
                        .map { it.toPerson() }
                        .filterNot { insiderService.isInsiderPerson(personId = it.id) }

                PeopleView.External -> {
                    val ids = PersonModel
                        .select(PersonModel.id)
                        .map { it[PersonModel.id].value }
                        .filterNot { insiderService.isInsiderPerson(personId = it) }
                    PersonDAO.forIds(ids).map { it.asDataModel() }
                }

                PeopleView.Internal -> {
                    val ids = PersonModel
                        .select(PersonModel.id)
                        .map { it[PersonModel.id].value }
                        .filter { insiderService.isInsiderPerson(personId = it) }
                    PersonDAO.forIds(ids).map { it.asDataModel() }
                }
            }
        }.sortedByDescending { it.createdAt }
    }

    suspend fun RoutingContext.renderPeoplePage(
        page: AdminPage,
        peopleView: PeopleView,
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
        insiderService: InsiderServiceInterface,
        ingestionDisablementService: IngestionDisablementService,
        personStore: PersonStore = Stores.personStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val people = getPeople(peopleView, insiderService, userEngagementStore)
        val orgIdsByPerson = personStore.getOrgsForPeople(people.map { it.id })
        val orgCache = orgStore.hydrateOrgs(orgIdsByPerson.values.flatten().distinct()).associateBy { it.id }

        val providersByPerson = personStore.getIdentityProvidersForPeople(personIds = people.map { it.id })
        val clientVersions = Stores.clientVersionStore.findByPersonIds(personIds = people.map { it.id })
        val adminIdentity = call.getAdminIdentity()
        val additionalAlerts = IngestionAlert.get(ingestionDisabled = ingestionDisablementService.isDisabled())
        val lastActiveAts = lastActiveAts(people)

        val engagement = when (peopleView) {
            PeopleView.Active, PeopleView.Internal -> engagementMetrics(userEngagementStore, people)
            PeopleView.New, PeopleView.External -> null
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, additionalAlerts = additionalAlerts) }
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 {
                        style = "margin-right: auto;"
                        +peopleView.label
                    }
                    renderPeopleButtons(peopleView)
                }
                p(classes = "mb-5") { +peopleView.description }
                renderPeople(people, lastActiveAts, orgIdsByPerson, orgCache, providersByPerson, clientVersions, engagement)
            }
        }
    }

    private fun FlowContent.renderPeopleButtons(peopleView: PeopleView) {
        div(classes = "btn-group py-3") {
            role = "group"
            PeoplePage.PeopleView.entries.forEach { view ->
                input(type = InputType.radio, classes = "btn-check", name = "btnradio") {
                    val name = view.name
                    id = "btnradio$name"
                    autoComplete = InputAutoComplete.off
                    checked = peopleView == view
                    label(classes = "btn btn-outline-primary") {
                        attributes["for"] = "btnradio$name"
                        attributes["onclick"] = onClickAction(name)
                        style = "width: 100px;"
                        +name
                    }
                }
            }
        }
    }

    fun FlowContent.renderPeople(
        people: List<Person>,
        lastActiveAts: Map<PersonId, Instant?>,
        orgsByPerson: Map<PersonId, List<OrgId>>? = null,
        orgCache: Map<OrgId, Org>? = null,
        providersByPerson: Map<PersonId, List<Provider>>? = null,
        clientVersions: Map<PersonId, List<ClientVersion>>? = null,
        engagement: BulkEngagementMetrics<PersonId>? = null,
    ) {
        table(classes = "table table-hover align-middle searchable mt-5") {
            thead {
                tr {
                    th(scope = ThScope.col, classes = "noSort noSearch") { }
                    th(scope = ThScope.col) { +"Person" }
                    th(scope = ThScope.col) { +"Name / Primary Email" }
                    orgsByPerson?.also {
                        th(scope = ThScope.col) { +"Orgs" }
                    }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") {
                        +"Last Active"
                        span {
                            title = "The last time the user viewed content in the Desktop app or Dashboard, " +
                                    "or viewed the tutorial, a thread, a PR, the sidebar, or insights panel in an IDE."
                            +" ℹ️"
                        }
                    }
                    engagement?.also {
                        th(scope = ThScope.col, classes = "noSearch") { +"Active N/7" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Active N/30" }
                    }
                    clientVersions?.also {
                        th(scope = ThScope.col) { +"Versions" }
                    }
                }
            }
            tbody(classes = "table-dark") {
                people
                    .sortedWith { a, b -> comparePeople(lastActiveAts, a, b) }
                    .forEach { person ->
                        renderOnePerson(
                            person,
                            lastActiveAts[person.id],
                            orgsByPerson?.let {
                                val orgIds = orgsByPerson[person.id].orEmpty()
                                orgIds.mapNotNull { orgCache?.get(it) }.sortedBy { it.displayName }
                            },
                            providersByPerson?.get(person.id)?.sortedBy { it.displayName },
                            clientVersions,
                            engagement,
                        )
                    }
            }
        }
    }

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    private fun TBODY.renderOnePerson(
        person: Person,
        lastActiveAt: Instant?,
        orgs: List<Org>? = null,
        providers: List<Provider>? = null,
        clientVersions: Map<PersonId, List<ClientVersion>>? = null,
        engagement: BulkEngagementMetrics<PersonId>? = null,
    ) {
        tr {
            attributes["onclick"] = onClickAction("$WEB_ROOT/people/${person.id}")
            style = "cursor: pointer;"

            td(classes = "index") { }
            td {
                avatar(person)
                providers?.also {
                    providers.filter { it.isScmProvider }.mapIndexed { idx, provider ->
                        asBadge(
                            provider = provider,
                            small = true,
                            withStyle = when (idx) {
                                0 -> " vertical-align: bottom; margin-bottom: -5px; margin-left: -13px;"
                                else -> " vertical-align: bottom; margin-bottom: -5px;"
                            },
                        )
                    }
                }
            }
            td {
                +person.customDisplayName
                div(classes = "small text-muted") {
                    +person.primaryEmail.value
                }
            }
            orgs?.also {
                td {
                    orgs.firstOrNull()?.also { team ->
                        avatar(team)
                        if (orgs.count() == 1) {
                            div(classes = "small text-muted d-none d-lg-inline-block") {
                                +team.displayName
                            }
                        }
                        orgs.drop(1).map { otherTeam ->
                            span { avatar(otherTeam) }
                        }
                    } ?: "-"
                }
            }
            td { timeAgo(person.createdAt) }
            td { timeAgo(lastActiveAt) }
            engagement?.also {
                td {
                    engagement.engagement7day[person.id]?.let { asProgressBar(it) } ?: +"-"
                }
                td {
                    engagement.engagement30day[person.id]?.let { asProgressBar(it) } ?: +"-"
                }
            }
            clientVersions?.also {
                td {
                    clientVersions[person.id]?.sortedBy { it.productAgent.name }?.map(::asBadge)
                }
            }
        }
    }

    private fun comparePeople(lastActiveAts: Map<PersonId, Instant?>, a: Person, b: Person): Int {
        // last active
        val aLastActive = lastActiveAts[a.id]?.epochSeconds ?: Instant.epoch.epochSeconds
        val bLastActive = lastActiveAts[b.id]?.epochSeconds ?: Instant.epoch.epochSeconds
        if (aLastActive != bLastActive) {
            return (bLastActive - aLastActive).toInt()
        }

        // created at
        return (b.createdAt.epochSeconds - a.createdAt.epochSeconds).toInt()
    }

    suspend fun lastActiveAts(people: List<Person>): Map<PersonId, Instant> {
        return suspendedTransaction {
            PersonModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = UserEngagementModel,
                    otherColumn = UserEngagementModel.personId,
                    onColumn = PersonModel.id,
                ) {
                    UserEngagementStore.ENGAGED_ACTIVITY_CLAUSE
                }
                .select(PersonModel.id, UserEngagementModel.createdAt.max())
                .where { PersonModel.id inList people.map { it.id } }
                .groupBy(PersonModel.id)
                .associateBy({ it[PersonModel.id].value }) { it[UserEngagementModel.createdAt.max()] ?: Instant.epoch }
        }
    }

    @Suppress("MagicNumber")
    private suspend fun engagementMetrics(
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
        people: List<Person>,
    ): BulkEngagementMetrics<PersonId> {
        return BulkEngagementMetrics(
            engagement7day = people
                .filter { person -> person.createdAt < Instant.nowWithMicrosecondPrecision().minus(7.days) }
                .let { userEngagementStore.engagementScoreByPerson(it.map(Person::id), 7) },
            engagement30day = people
                .filter { person -> person.createdAt < Instant.nowWithMicrosecondPrecision().minus(30.days) }
                .let { userEngagementStore.engagementScoreByPerson(it.map(Person::id), 30) },
        )
    }
}
