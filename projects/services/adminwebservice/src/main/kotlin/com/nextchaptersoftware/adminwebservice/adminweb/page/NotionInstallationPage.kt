package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.NotionIngestion
import com.nextchaptersoftware.db.models.NotionObjectType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.IngestionStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.NotionObjectStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgress
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgressServiceProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.ingestion.queue.enqueue.NoopNotionIngestionEventEnqueueService
import com.nextchaptersoftware.notion.ingestion.redis.cache.NotionIngestionCache
import com.nextchaptersoftware.notion.ingestion.services.NoopNotionLock
import com.nextchaptersoftware.notion.ingestion.services.NotionContentService
import com.nextchaptersoftware.utils.asUUID
import io.ktor.http.ContentType
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.withTimeout
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.TextAreaWrap
import kotlinx.html.a
import kotlinx.html.button
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.stream.appendHTML
import kotlinx.html.textArea

object NotionInstallationPage {

    suspend fun RoutingContext.renderNotionInstallationPage(
        page: AdminPage,
        ingestionProgressServiceProvider: IngestionProgressServiceProvider,
        installationStore: InstallationStore = Stores.installationStore,
        ingestionStore: IngestionStore = Stores.ingestionStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val adminIdentity = call.getAdminIdentity()
        val org = call.parameters.org()
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)

        val installation = installationStore.findById(installationId = installationId)
            ?: throw IllegalArgumentException("Could not find installation")

        val ingestion = suspendedTransaction {
            ingestionStore.find(
                installationId = installation.id,
                provider = Provider.Notion,
            ) as? NotionIngestion
        }

        val ingestionProgressService = ingestionProgressServiceProvider.get(
            installationId = installation.id,
            orgId = installation.orgId,
        )
        val progress = ingestionProgressService.progress()

        val path = call.request.path()

        val actions = buildList {
            addAll(
                listOf(
                    MenuItem(
                        href = "$path/reingestNotion",
                        label = "Reingest Notion installation",
                        description = "Reingest all docs",
                    ),
                    MenuItem(
                        href = "$path/clearNotionNextCursor",
                        label = "Clear Notion Next Cursor",
                        description = "Reingest all docs modified since \"Last Synced\"",
                    ),
                ),
            )
        }

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            actionMenu { renderActionMenu(actions, sort = false) }
            content {
                h1 { +page.label }
                render(installation, ingestion, progress, path)
            }
        }
    }

    @Suppress("LongMethod")
    private fun FlowContent.render(
        installation: Installation,
        ingestion: NotionIngestion?,
        progress: IngestionProgress,
        path: String,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Name", installation.displayName)
                property("External ID", installation.installationExternalId)
                property("Created", installation.createdAt)
                property("HTML Url", installation.htmlUrl)
                property("Last Synced", ingestion?.lastSynced)
                property("Next Cursor", ingestion?.nextCursor)
                property("Progress", progress.toString())
                property("API Token") {
                    div {
                        attributes["hx-get"] = "$path/getNotionApiToken"
                        attributes["hx-trigger"] = "click"
                        attributes["hx-swap"] = "outerHTML"
                        +"*** Click to reveal ***"
                    }
                }
                property("Search Pages", description = "Search Notion pages by title") {
                    form(action = "$path/searchNotionPages") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "query"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Search"
                        }
                    }
                }
                property("Search Databases", description = "Search Notion databases by title") {
                    form(action = "$path/searchNotionDatabases") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "query"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Search"
                        }
                    }
                }
                property("Database", description = "Get a database by ID") {
                    form(action = "$path/getNotionDatabase") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "databaseId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
                property("Database Children", description = "Query a database by ID") {
                    form(action = "$path/queryNotionDatabase") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "databaseId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
                property("Page", description = "Get a page by ID") {
                    form(action = "$path/getNotionPage") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "pageId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
                property("Block", description = "Get block by block ID or page ID") {
                    form(action = "$path/getBlock") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "blockId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
                property("Block Children", description = "Get block children by block ID or page ID") {
                    form(action = "$path/getBlockChildren") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "blockId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
                property("User", description = "Get the Notion user that connected this installation") {
                    a(classes = "btn btn-primary my-0 mx-1", href = "$path/getNotionUser") {
                        +"Get Notion User"
                    }
                }
                property("Ingest Page", description = "Ingest a page by ID") {
                    form(action = "$path/ingestNotionPage") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "pageId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Ingest"
                        }
                    }
                }
                property("Get Block and Descendants", description = "Get a block and all its descendants by ID") {
                    form(action = "$path/getBlockAndChildren") {
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "blockId"
                            +""
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Get"
                        }
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.reingestNotion(
        notionIngestionCache: NotionIngestionCache,
        ingestionStore: IngestionStore = Stores.ingestionStore,
    ) {
        val org = call.parameters.org()
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)

        ingestionStore.setLastSynced(installationId = installationId, lastSynced = null)
        ingestionStore.setNotionNextCursor(installationId = installationId, nextCursor = null)
        notionIngestionCache.clear(
            orgId = org.id,
            installationId = installationId,
        )
    }

    suspend fun RoutingContext.clearNotionNextCursor(
        ingestionStore: IngestionStore = Stores.ingestionStore,
        installationStore: InstallationStore = Stores.installationStore,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        requireNotNull(installationStore.findById(installationId = installationId))

        ingestionStore.setNotionNextCursor(installationId = installationId, nextCursor = null)
    }

    suspend fun RoutingContext.searchNotionPages(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val query = call.request.queryParameters["query"]
        val nextCursor = call.request.queryParameters["next_cursor"]

        call.respondText {
            apiProvider.searchApi.searchPages(
                installationId = installationId,
                identityId = identityId,
                query = query,
                startCursor = nextCursor,
            ).encode()
        }
    }

    suspend fun RoutingContext.searchNotionDatabases(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val query = call.request.queryParameters["query"]
        val nextCursor = call.request.queryParameters["next_cursor"]

        call.respondText {
            apiProvider.searchApi.searchDatabases(
                installationId = installationId,
                identityId = identityId,
                query = query,
                startCursor = nextCursor,
            ).encode()
        }
    }

    suspend fun RoutingContext.getNotionPage(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = call.request.queryParameters.optionalId("identityId", ::IdentityId)
            ?: apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val pageId = requireNotNull(call.request.queryParameters["pageId"])

        call.respondText {
            apiProvider.pagesApi.getPage(installationId = installationId, identityId = identityId, pageId = pageId).encode()
        }
    }

    suspend fun RoutingContext.getNotionDatabase(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val databaseId = requireNotNull(call.request.queryParameters["databaseId"])

        call.respondText {
            apiProvider.databasesApi.getDatabase(installationId = installationId, identityId = identityId, databaseId = databaseId).encode()
        }
    }

    suspend fun RoutingContext.queryNotionDatabase(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val databaseId = requireNotNull(call.request.queryParameters["databaseId"])

        call.respondText {
            apiProvider.databasesApi.queryDatabase(installationId = installationId, identityId = identityId, databaseId = databaseId).encode()
        }
    }

    suspend fun RoutingContext.getBlock(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val blockId = requireNotNull(call.request.queryParameters["blockId"])

        call.respondText {
            apiProvider.blocksApi.getBlock(installationId = installationId, identityId = identityId, blockId = blockId).encode()
        }
    }

    suspend fun RoutingContext.getBlockChildren(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val blockId = requireNotNull(call.request.queryParameters["blockId"])

        call.respondText {
            apiProvider.blocksApi.getBlockChildren(installationId = installationId, identityId = identityId, blockId = blockId).encode()
        }
    }

    suspend fun RoutingContext.getNotionUser(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id

        call.respondText {
            apiProvider.userApi.getBotUser(installationId = installationId, identityId = identityId).encode()
        }
    }

    suspend fun RoutingContext.ingestNotionPage(
        cache: NotionIngestionCache,
        notionObjectStore: NotionObjectStore = Stores.notionObjectStore,
    ) {
        val org = call.parameters.org()

        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)

        val pageId = requireNotNull(call.request.queryParameters["pageId"]).asUUID()

        cache.delete(orgId = org.id, installationId = installationId, pageId = pageId)

        notionObjectStore.markForIngestion(installationId = installationId, externalNotionId = pageId, type = NotionObjectType.Page, priority = 1000)

        call.respondRedirect(
            call.request.path().replaceAfterLast("/", "").trimEnd('/'),
        )
    }

    suspend fun RoutingContext.getBlockAndChildren(
        apiProvider: NotionApiProvider,
    ) {
        val org = call.parameters.org()
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id
        val blockId = requireNotNull(call.request.queryParameters["blockId"]).asUUID()

        val result = runSuspendCatching {
            withTimeout(5.seconds) {
                NotionContentService(
                    apiProvider = apiProvider,
                    enqueueService = NoopNotionIngestionEventEnqueueService(),
                ).getContents(
                    orgId = org.id,
                    installationId = installationId,
                    identityId = identityId,
                    blockId = blockId,
                    lock = NoopNotionLock(),
                )
            }
        }.getOrNull()

        call.respondText {
            result?.map { it.withoutContent() }?.encode() ?: "timed out"
        }
    }

    suspend fun RoutingContext.getNotionApiToken(
        apiProvider: NotionApiProvider,
    ) {
        val installationId = call.parameters.requiredId("notionInstallationId", ::InstallationId)
        val identityId = apiProvider.oauthTokenProvider.getDefaultConnectedIdentity(installationId = installationId).id

        val apiToken = apiProvider.oauthTokenProvider.getOAuthTokens(installationId = installationId, identityId = identityId).accessToken
        val htmlFragment = buildString {
            appendHTML().div {
                +apiToken.value
            }
        }

        call.respondText(htmlFragment, ContentType.Text.Html)
    }
}
