package com.nextchaptersoftware.adminwebservice.adminweb.component

object Click {
    fun onClickAction(action: String) = """
        event.stopPropagation();
        event.preventDefault();
        if (event.metaKey || event.shiftKey) {
            window.open('$action', '_blank').focus();
        } else {
            window.location = '$action';
        }
    """.trimIndent()

    fun onClickStopPropagation() = """
        event.stopPropagation()
    """.trimIndent()
}
