package com.nextchaptersoftware.adminwebservice.jobs

import com.nextchaptersoftware.db.stores.Cohort
import com.nextchaptersoftware.service.BackgroundJob
import com.nextchaptersoftware.userengagement.UserEngagementService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant

class UserEngagementMetricsJob(
    private val userEngagementService: UserEngagementService,
) : BackgroundJob {
    override val name: String
        get() = "User activity aggregation job"

    override suspend fun run() {
        val now = Instant.nowWithMicrosecondPrecision()
        Cohort.entries.forEach { cohort ->
            userEngagementService.calculateUserEngagementMetrics(priorTo = now, cohort = cohort)
        }
    }
}
