package com.nextchaptersoftware.adminwebservice.adminweb

import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.db.models.ActivityType
import com.nextchaptersoftware.db.models.AggregateUserEngagement
import com.nextchaptersoftware.utils.ReportingUtils.asReportingDateTime
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.safeTake
import java.time.DayOfWeek
import java.time.temporal.TemporalAdjusters
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toKotlinLocalDate
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.span
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.tr

object AdminActivity {

    @Suppress("MagicNumber")
    fun FlowContent.renderActivity(activity: List<AggregateUserEngagement>) {
        table(classes = "table table-hover align-middle") {
            tbody(classes = "table-dark") {
                val today = Instant.nowWithMicrosecondPrecision().asReportingDateTime.date

                val startOfWeek = today
                    .toJavaLocalDate()
                    .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                    .toKotlinLocalDate()

                val startOfMonth = today
                    .toJavaLocalDate()
                    .with(TemporalAdjusters.firstDayOfMonth())
                    .toKotlinLocalDate()

                val groupedActivity: Map<String, List<AggregateUserEngagement>> = activity.groupBy {
                    when {
                        it.date > today -> "TODAY"
                        it.date > startOfWeek -> "THIS WEEK"
                        it.date > startOfMonth -> "THIS MONTH"
                        else -> it.date.year.toString() + " " + it.date.month.name.safeTake(3)
                    }
                }
                groupedActivity.forEach { (period, periodActivity) ->
                    tr {
                        td(classes = "small bg-${BootstrapStyle.Primary.tag}") {
                            colSpan = "3"
                            div(classes = "d-flex justify-content-between") {
                                span { +period }
                                val engagedDays = periodActivity.distinctBy { it.date }.count()
                                when (engagedDays) {
                                    1 -> span { +"$engagedDays day engaged" }
                                    else -> span { +"$engagedDays days engaged" }
                                }
                            }
                        }
                    }
                    periodActivity.forEach {
                        tr {
                            td { +it.date.toString() }
                            td { +it.productAgent.name }
                            td {
                                +it.activityType.asString
                                if (it.count > 1) {
                                    badge(classes = "small mx-2", bootstrapStyle = BootstrapStyle.Info) { +it.count.toString() }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private val ActivityType.asString: String
        get() = when (this) {
            ActivityType.ContentViewed -> "Viewed a list questions, PR comments, PRs"
            ActivityType.IntegrationChange -> "Updated integrations"
            ActivityType.MessageCreated -> "Asked follow up question, or replied to PR comment"
            ActivityType.PullRequestViewed -> "Viewed a pull request"
            ActivityType.Search -> "Searched"
            ActivityType.MemberDescriptionChange -> "Updated expert description"
            ActivityType.ThreadCreated -> "Asked a question"
            ActivityType.ThreadViewed -> "Viewed a question or PR thread"
            ActivityType.TopicExpertiseChange -> "Updated topic expertise"
            ActivityType.IdeSidebarViewed -> "IDE explore panel is open"
            ActivityType.IdeSidebarPanelOpened -> "Opened IDE explore panel"
            ActivityType.IdeSidebarPanelClosed -> "Closed IDE explore panel"
            ActivityType.IdeInsightsViewed -> "IDE insights panel is open"
            ActivityType.IdeInsightsPanelOpened -> "Opened IDE insights panel"
            ActivityType.IdeInsightsPanelClosed -> "Closed IDE insights panel"
            ActivityType.VisitedProcessingComplete -> "Navigated to web from processing complete email"
            ActivityType.TeamUsageViewed -> "Viewed the Usage Overview page"
            ActivityType.IdeThreadViewedFromGutterIcon -> "Viewed a question or PR thread from the IDE gutter icon"
            ActivityType.IdeThreadViewedFromMyQuestions -> "Viewed a question from the IDE My Questions panel"
            ActivityType.IdeThreadViewedFromPRExplorerInsights -> "Viewed a PR from the IDE PR Explorer Insights panel"
            ActivityType.IdeThreadViewedFromQAExplorerInsights -> "Viewed a question from the IDE QA Explorer Insights panel"
            ActivityType.AnswerPreferencesTooltipViewed -> "Opened Answer Preferences Tooltip"
        }
}
