package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminBarCharts.renderBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminBarCharts.renderTimeSeriesChart
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminBarCharts.transformLatenciesToFrequency
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderTooltipFooter
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderZoomControls
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.syncZoomHandlers
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminHeatmapCharts.renderHeatmapChart
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartScale
import com.nextchaptersoftware.adminwebservice.adminweb.chart.ChartType
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartDataPoint
import com.nextchaptersoftware.adminwebservice.adminweb.chart.TimeSeriesChartDataset
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.alignToStartOfMonth
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.alignToStartOfWeek
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.MLInferenceModel
import com.nextchaptersoftware.db.models.MessageFeedbackModel
import com.nextchaptersoftware.db.models.MessageModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.SlackAutoAnswerModel
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.utils.ReportingUtils.INITIAL_REPORTING_DATETIME
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import kotlin.time.DurationUnit
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h5
import kotlinx.html.id
import kotlinx.html.p
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.notInList
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.kotlin.datetime.date

object AnalyticsAnswersPage {

    /**
     * Default sampling interval for decimating data.
     */
    private const val DEFAULT_SAMPLING_INTERVAL = 10

    suspend fun RoutingContext.renderAnalyticsAnswersPage(
        page: AdminPage,
        insiderService: InsiderService = InsiderService(),
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val waitLatenciesByDate = getWaitLatencies(insiderService)

        val completionLatenciesByDate = getCompletionLatencies(insiderService)

        val answersByDate = getAnswers(insiderService)
        val feedbackByDate = getFeedback(insiderService, null)
        val slackAnswersByDate = getSlackAnswers(insiderService)
        val slackNonAutoAnswersByDate = slackAnswersByDate.map {
            Pair(it.first, it.second)
        }
        val slackAutoAnswersByDate = slackAnswersByDate.map {
            Pair(it.first, it.third)
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 { +page.label }
                    renderTooltipFooter()
                    renderZoomControls()
                    syncZoomHandlers()
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerCountWeeklyBarChart(answersByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerCountMonthlyBarChart(answersByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderSlackWeeklyBarChart(slackNonAutoAnswersByDate, slackAutoAnswersByDate)
                    }
                    div(classes = "col-6") {
                        renderSlackMonthlyBarChart(slackNonAutoAnswersByDate, slackAutoAnswersByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderFeedbackCountWeeklyBarChart(feedbackByDate)
                    }
                    div(classes = "col-6") {
                        renderFeedbackCountMonthlyBarChart(feedbackByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerWaitTimeHeatmap(waitLatenciesByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerWaitTimeHistogram(waitLatenciesByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerCompleteTimeHeatmap(completionLatenciesByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerCompleteTimeHistogram(completionLatenciesByDate)
                    }
                }
            }
        }
    }

    fun FlowContent.renderAnswerWaitTimeHeatmap(waitLatenciesByDate: List<Pair<Instant, Duration>>) {
        h5(classes = "mt-5") { +"Answer Wait Time Heatmap" }
        p(classes = "text-muted") { +"Time that the user has to wait until the first character of the answer is rendered." }
        renderHeatmapChart(label = "Wait Time", durationByDate = waitLatenciesByDate)
    }

    fun FlowContent.renderAnswerCompleteTimeHeatmap(completionLatenciesByDate: List<Pair<Instant, Duration>>) {
        h5(classes = "mt-5") { +"Answer Complete Time Heatmap" }
        p(classes = "text-muted") { +"Time taken to complete answer." }
        renderHeatmapChart(label = "Complete Time", completionLatenciesByDate)
    }

    fun FlowContent.renderAnswerWaitTimeHistogram(waitLatenciesByDate: List<Pair<Instant, Duration>>) {
        h5(classes = "mt-5") { +"Answer Wait Time Histogram" }
        p(classes = "text-muted") { +"Histogram of answers by wait time." }
        renderBarChart(
            datasets = transformLatenciesToFrequency(
                latencies = mapOf("Wait Time" to waitLatenciesByDate),
                durationUnit = DurationUnit.SECONDS,
            ),
            xAxisTitle = "Duration (s)",
            yAxisTitle = "Relative Frequency (%)",
        )
    }

    fun FlowContent.renderAnswerCompleteTimeHistogram(completionLatenciesByDate: List<Pair<Instant, Duration>>) {
        h5(classes = "mt-5") { +"Answer Complete Time Histogram" }
        p(classes = "text-muted") { +"Histogram of answers by complete time." }
        renderBarChart(
            datasets = transformLatenciesToFrequency(
                latencies = mapOf("Complete Time" to completionLatenciesByDate),
                durationUnit = DurationUnit.SECONDS,
            ),
            xAxisTitle = "Duration (s)",
            yAxisTitle = "Relative Frequency (%)",
        )
    }

    fun FlowContent.renderAnswerCountWeeklyBarChart(answersByDateByAgent: Map<ProductAgentType?, List<Pair<LocalDate, Long>>>) {
        h5(classes = "mt-5") { +"Answer Count Weekly" }
        p(classes = "text-muted") { +"Number of answers generated each week." }
        renderTimeBarChart(
            datasets = answersByDateByAgent.map { (productAgent, answersByDate) ->
                TimeSeriesChartDataset(
                    label = (productAgent ?: ProductAgentType.Unknown).name,
                    data = answersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfWeek().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                )
            },
            minTimeUnit = ChartScale.TimeUnit.week,
            yAxisTitle = "Answers",
        )
    }

    fun FlowContent.renderAnswerCountMonthlyBarChart(answersByDateByAgent: Map<ProductAgentType?, List<Pair<LocalDate, Long>>>) {
        h5(classes = "mt-5") { +"Answer Count Monthly" }
        p(classes = "text-muted") { +"Number of answers generated every month." }
        renderTimeBarChart(
            datasets = answersByDateByAgent.map { (productAgent, answersByDate) ->
                TimeSeriesChartDataset(
                    label = (productAgent ?: ProductAgentType.Unknown).name,
                    data = answersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfMonth().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                )
            },
            minTimeUnit = ChartScale.TimeUnit.month,
            yAxisTitle = "Answers",
        )
    }

    fun FlowContent.renderSlackWeeklyBarChart(
        slackNonAutoAnswersByDate: List<Pair<LocalDate, Long>>,
        slackAutoAnswersByDate: List<Pair<LocalDate, Long>>,
    ) {
        h5(classes = "mt-5") { +"Slack Answer Count Weekly" }
        p(classes = "text-muted") { +"Number of answers generated each week." }
        renderTimeBarChart(
            datasets = listOf(
                TimeSeriesChartDataset(
                    label = "Auto Answer",
                    data = slackAutoAnswersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfWeek().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                ),
                TimeSeriesChartDataset(
                    label = "Non-Auto Answer",
                    data = slackNonAutoAnswersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfWeek().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                ),
            ),
            minTimeUnit = ChartScale.TimeUnit.week,
            yAxisTitle = "Answers",
        )
    }

    fun FlowContent.renderSlackMonthlyBarChart(
        slackNonAutoAnswersByDate: List<Pair<LocalDate, Long>>,
        slackAutoAnswersByDate: List<Pair<LocalDate, Long>>,
    ) {
        h5(classes = "mt-5") { +"Slack Answer Count Monthly" }
        p(classes = "text-muted") { +"Number of answers generated each month." }
        renderTimeBarChart(
            datasets = listOf(
                TimeSeriesChartDataset(
                    label = "Auto Answer",
                    data = slackAutoAnswersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfMonth().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                ),
                TimeSeriesChartDataset(
                    label = "Non-Auto Answer",
                    data = slackNonAutoAnswersByDate
                        .groupBy({ (date, _) -> date.alignToStartOfMonth().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                ),
            ),
            minTimeUnit = ChartScale.TimeUnit.month,
            yAxisTitle = "Answers",
        )
    }

    fun FlowContent.renderFeedbackCountWeeklyBarChart(feedbackByDateBySentiment: Map<FeedbackType, List<Pair<LocalDate, Long>>>) {
        h5(classes = "mt-5") { +"Feedback Count Weekly" }
        p(classes = "text-muted") { +"Feedback received each week." }
        renderTimeBarChart(
            datasets = feedbackByDateBySentiment.map { (feedbackType, feedbackByDate) ->
                TimeSeriesChartDataset(
                    label = feedbackType.name,
                    data = feedbackByDate
                        .groupBy({ (date, _) -> date.alignToStartOfWeek().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                )
            },
            minTimeUnit = ChartScale.TimeUnit.week,
            yAxisTitle = "Feedback Count",
        )
    }

    fun FlowContent.renderFeedbackCountMonthlyBarChart(feedbackByDateBySentiment: Map<FeedbackType, List<Pair<LocalDate, Long>>>) {
        h5(classes = "mt-5") { +"Feedback Count Monthly" }
        p(classes = "text-muted") { +"Feedback received each month." }
        renderTimeBarChart(
            datasets = feedbackByDateBySentiment.map { (feedbackType, feedbackByDate) ->
                TimeSeriesChartDataset(
                    label = feedbackType.name,
                    data = feedbackByDate
                        .groupBy({ (date, _) -> date.alignToStartOfMonth().atStartOfDayIn(TimeZone.currentSystemDefault()) }) { it.second }
                        .mapValues { (_, v) -> v.sum() }
                        .map { (instant, count) ->
                            TimeSeriesChartDataPoint(x = instant.toEpochMilliseconds(), y = count)
                        },
                )
            },
            minTimeUnit = ChartScale.TimeUnit.month,
            yAxisTitle = "Feedback Count",
        )
    }

    private fun FlowContent.renderTimeBarChart(
        datasets: List<TimeSeriesChartDataset<Long, Long>>,
        minTimeUnit: ChartScale.TimeUnit,
        yAxisTitle: String? = null,
    ) {
        renderTimeSeriesChart(
            datasets = datasets,
            minTimeUnit = minTimeUnit,
            yAxisTitle = yAxisTitle,
            chartType = ChartType.bar,
        )
    }

    suspend fun getWaitLatenciesByOrg(
        orgId: OrgId,
        ceiling: Duration = 60.seconds,
    ): List<Pair<Instant, Duration>> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    otherTable = MessageModel,
                    otherColumn = MLInferenceModel.botMessage,
                    onColumn = MessageModel.id,
                    joinType = JoinType.INNER,
                )
                .select(MessageModel.createdAt, MLInferenceModel.messageFirstGeneratedAt)
                .whereAll(
                    MLInferenceModel.org eq orgId,
                    MLInferenceModel.messageFirstGeneratedAt.isNotNull(),
                )
                .orderBy(MessageModel.createdAt)
                .mapNotNull {
                    val createdAt = it[MessageModel.createdAt]
                    val messageFirstGeneratedAt = it[MLInferenceModel.messageFirstGeneratedAt]
                    messageFirstGeneratedAt?.let { Pair(createdAt, messageFirstGeneratedAt - createdAt) }
                }
        }.map { (date, duration) ->
            date to duration.coerceAtMost(ceiling)
        }
    }

    private suspend fun getWaitLatencies(
        insiderService: InsiderServiceInterface = NoOpInsiderService(),
        samplingInterval: Int = DEFAULT_SAMPLING_INTERVAL,
        ceiling: Duration = 60.seconds,
    ): List<Pair<Instant, Duration>> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    otherTable = MessageModel,
                    otherColumn = MLInferenceModel.botMessage,
                    onColumn = MessageModel.id,
                    joinType = JoinType.INNER,
                )
                .select(MessageModel.createdAt, MLInferenceModel.messageFirstGeneratedAt)
                .whereAll(
                    MLInferenceModel.org notInList insiderService.insiderOrgs,
                    MLInferenceModel.messageFirstGeneratedAt.isNotNull(),
                )
                .orderBy(MessageModel.createdAt)
                .mapNotNull {
                    val createdAt = it[MessageModel.createdAt]
                    val messageFirstGeneratedAt = it[MLInferenceModel.messageFirstGeneratedAt]
                    messageFirstGeneratedAt?.let { Pair(createdAt, messageFirstGeneratedAt - createdAt) }
                }
        }.map { (date, duration) ->
            date to duration.coerceAtMost(ceiling)
        }
        .filterIndexed { index, _ -> index % samplingInterval == 0 }
    }

    suspend fun getCompletionLatenciesByOrg(
        orgId: OrgId,
        ceiling: Duration = 180.seconds,
    ): List<Pair<Instant, Duration>> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    otherTable = MessageModel,
                    otherColumn = MLInferenceModel.botMessage,
                    onColumn = MessageModel.id,
                    joinType = JoinType.INNER,
                )
                .select(MessageModel.createdAt, MLInferenceModel.runDurationMillis)
                .whereAll(
                    MLInferenceModel.org eq orgId,
                    MLInferenceModel.runDurationMillis.isNotNull(),
                )
                .orderBy(MessageModel.createdAt)
                .mapNotNull {
                    val createdAt = it[MessageModel.createdAt]
                    val duration = it[MLInferenceModel.runDurationMillis]?.milliseconds
                    duration?.let { Pair(createdAt, duration) }
                }
        }.map { (date, duration) ->
            date to duration.coerceAtMost(ceiling)
        }
    }

    private suspend fun getCompletionLatencies(
        insiderService: InsiderServiceInterface = NoOpInsiderService(),
        samplingInterval: Int = DEFAULT_SAMPLING_INTERVAL,
        ceiling: Duration = 180.seconds,
    ): List<Pair<Instant, Duration>> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    otherTable = MessageModel,
                    otherColumn = MLInferenceModel.botMessage,
                    onColumn = MessageModel.id,
                    joinType = JoinType.INNER,
                )
                .select(MessageModel.createdAt, MLInferenceModel.runDurationMillis)
                .whereAll(
                    MLInferenceModel.org notInList insiderService.insiderOrgs,
                    MLInferenceModel.runDurationMillis.isNotNull(),
                )
                .orderBy(MessageModel.createdAt)
                .mapNotNull {
                    val createdAt = it[MessageModel.createdAt]
                    val duration = it[MLInferenceModel.runDurationMillis]?.milliseconds
                    duration?.let { Pair(createdAt, duration) }
                }
        }.map { (date, duration) ->
            date to duration.coerceAtMost(ceiling)
        }
        .filterIndexed { index, _ -> index % samplingInterval == 0 }
    }

    suspend fun getAnswersByOrg(
        orgId: OrgId,
    ): Map<ProductAgentType?, List<Pair<LocalDate, Long>>> {
        return suspendedTransaction {
            MLInferenceModel
                .select(
                    MLInferenceModel.createdAt.date(),
                    MLInferenceModel.id.count(),
                    MLInferenceModel.productAgent,
                )
                .whereAll(
                    MLInferenceModel.org eq orgId,
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.createdAt.greaterEq(INITIAL_REPORTING_DATETIME),
                )
                .groupBy(MLInferenceModel.createdAt.date(), MLInferenceModel.productAgent)
                .orderBy(MLInferenceModel.createdAt.date())
                .groupBy({ it[MLInferenceModel.productAgent] }) {
                    Pair(
                        it[MLInferenceModel.createdAt.date()],
                        it[MLInferenceModel.id.count()],
                    )
                }
        }
    }

    // FIXME: mrtn, remove once OrgAnalyticsPage migration is complete
    suspend fun getAnswers(
        insiderService: InsiderServiceInterface = NoOpInsiderService(),
    ): Map<ProductAgentType?, List<Pair<LocalDate, Long>>> {
        return suspendedTransaction {
            MLInferenceModel
                .select(
                    MLInferenceModel.createdAt.date(),
                    MLInferenceModel.id.count(),
                    MLInferenceModel.productAgent,
                )
                .whereAll(
                    MLInferenceModel.org notInList insiderService.insiderOrgs,
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.createdAt.greaterEq(INITIAL_REPORTING_DATETIME),
                )
                .groupBy(MLInferenceModel.createdAt.date(), MLInferenceModel.productAgent)
                .orderBy(MLInferenceModel.createdAt.date())
                .groupBy({ it[MLInferenceModel.productAgent] }) {
                    Pair(
                        it[MLInferenceModel.createdAt.date()],
                        it[MLInferenceModel.id.count()],
                    )
                }
        }
    }

    suspend fun getSlackAnswers(
        insiderService: InsiderServiceInterface = NoOpInsiderService(),
        orgId: OrgId? = null,
    ): List<Triple<LocalDate, Long, Long>> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    otherTable = SlackAutoAnswerModel,
                    otherColumn = SlackAutoAnswerModel.inference,
                    onColumn = MLInferenceModel.id,
                    joinType = JoinType.LEFT,
                )
                .select(
                    MLInferenceModel.createdAt.date(),
                    MLInferenceModel.id.count(),
                    SlackAutoAnswerModel.id.count(),
                )
                .whereAll(
                    orgId?.let {
                        MLInferenceModel.org eq orgId
                    } ?: (MLInferenceModel.org notInList insiderService.insiderOrgs),
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.createdAt.greaterEq(INITIAL_REPORTING_DATETIME),
                    MLInferenceModel.productAgent eq ProductAgentType.Slack,
                )
                .groupBy(MLInferenceModel.createdAt.date())
                .orderBy(MLInferenceModel.createdAt.date())
                .map {
                    val totalSlackAnswerCount = it[MLInferenceModel.id.count()]
                    val autoAnswerCount = it[SlackAutoAnswerModel.id.count()]
                    val nonAutoAnswerCount = totalSlackAnswerCount - autoAnswerCount

                    Triple(
                        it[MLInferenceModel.createdAt.date()],
                        nonAutoAnswerCount,
                        autoAnswerCount,
                    )
                }
        }
    }

    suspend fun getFeedback(
        insiderService: InsiderServiceInterface = NoOpInsiderService(),
        orgId: OrgId?,
    ): Map<FeedbackType, List<Pair<LocalDate, Long>>> {
        return suspendedTransaction {
            MessageFeedbackModel
                .join(
                    otherTable = ThreadModel,
                    joinType = JoinType.INNER,
                    onColumn = MessageFeedbackModel.thread,
                    otherColumn = ThreadModel.id,
                ) {
                    when (orgId) {
                        null -> ThreadModel.org notInList insiderService.insiderOrgs
                        else -> ThreadModel.org eq orgId
                    }
                }
                .select(MessageFeedbackModel.createdAt.date(), MessageFeedbackModel.id.count(), MessageFeedbackModel.feedbackType)
                .whereAll(
                    MessageFeedbackModel.feedbackType.isNotNull(),
                    MessageFeedbackModel.createdAt.greaterEq(INITIAL_REPORTING_DATETIME),
                )
                .groupBy(MessageFeedbackModel.createdAt.date(), MessageFeedbackModel.feedbackType)
                .orderBy(MessageFeedbackModel.createdAt.date())
                .groupBy({ checkNotNull(it[MessageFeedbackModel.feedbackType]) }) {
                    Pair(
                        it[MessageFeedbackModel.createdAt.date()],
                        it[MessageFeedbackModel.id.count()],
                    )
                }
        }
    }
}
