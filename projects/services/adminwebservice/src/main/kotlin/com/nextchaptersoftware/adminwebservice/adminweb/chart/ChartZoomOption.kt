package com.nextchaptersoftware.adminwebservice.adminweb.chart

import kotlin.time.Duration
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.button

data class ChartZoomOption(
    val duration: Duration,
    val label: String,
) {
    fun renderButtonControl(flowContent: FlowContent) {
        flowContent.button(classes = "btn btn-sm btn-outline-light my-0 mx-1", type = ButtonType.button) {
            attributes["onclick"] = """
                const xMin = new Date() - ${duration.inWholeMilliseconds};
                plotUtils.sharedZoomHandler(xMin, undefined);
            """.trimIndent()
            +label
        }
    }
}
