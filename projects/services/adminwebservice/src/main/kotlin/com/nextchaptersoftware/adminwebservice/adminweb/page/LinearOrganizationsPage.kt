package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.LinearOrganization
import com.nextchaptersoftware.db.models.LinearOrganizationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationModel
import com.nextchaptersoftware.db.models.Provider
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.a
import kotlinx.html.h1
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.and

object LinearOrganizationsPage {
    suspend fun RoutingContext.renderLinearOrganizationsPage(page: AdminPage) {
        val breadcrumb = call.makeBreadcrumb()
        val org = call.parameters.org()

        val path = call.request.path()
        val installations = Database.suspendedTransaction {
            InstallationDAO
                .find {
                    (InstallationModel.org eq org.id) and (InstallationModel.provider eq Provider.Linear)
                }
                .map { it.asDataModel() }
        }
        val linearOrganizations = Database.suspendedTransaction {
            LinearOrganizationDAO
                .find { LinearOrganizationModel.installation inList installations.map { it.id } }
                .map { it.asDataModel() }
        }
        val adminIdentity = call.getAdminIdentity()

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                h1 { +page.label }
                renderLinearOrganizations(installations, linearOrganizations, path)
            }
        }
    }

    private fun FlowContent.renderLinearOrganizations(
        installations: List<Installation>,
        linearOrganizations: List<LinearOrganization>,
        path: String,
    ) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Organization" }
                    th(scope = ThScope.col) { +"Name" }
                    th(scope = ThScope.col) { +"Linear Organization Id" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Modified" }
                    th(scope = ThScope.col, classes = "noSort") { +"View" }
                }
            }
            tbody(classes = "table-dark") {
                linearOrganizations.sortedByDescending { it.createdAt }.forEach { org ->
                    renderLinearOrganization(installations.first { it.installationExternalId == org.linearOrganizationId }, org, path)
                }
            }
        }
    }

    private fun TBODY.renderLinearOrganization(
        installation: Installation,
        linearOrganization: LinearOrganization,
        path: String,
    ) {
        tr {
            attributes["onclick"] = onClickAction("$path/${installation.id}")
            style = "cursor: pointer;"

            td {
                avatar(linearOrganization)
            }
            td { +linearOrganization.name }
            td { +linearOrganization.linearOrganizationId }
            td { timeAgo(linearOrganization.createdAt) }
            td { timeAgo(linearOrganization.modifiedAt) }
            td {
                a(
                    classes = "btn btn-outline-info btn-sm mx-1",
                    href = "$path/${installation.id}/linearTeams",
                ) {
                    +"Linear Teams"
                }
                a(
                    classes = "btn btn-outline-info btn-sm mx-1",
                    href = "$path/${installation.id}/linearIngestions",
                ) {
                    +"Linear Ingestions"
                }
            }
        }
    }
}
