package com.nextchaptersoftware.api.integration.installation.progress

import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.test.utils.TestData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource
import org.junit.jupiter.params.provider.EnumSource

class ResourceTypesTest {

    class ProviderToResourceTypePairs : TestData<Pair<Provider, ResourceType>>(
        Pair(Provider.Asana, ResourceTypes.Workspace),
        Pair(Provider.Bitbucket, ResourceTypes.Repository),
        Pair(Provider.BitbucketDataCenter, ResourceTypes.Repository),
        Pair(Provider.Confluence, ResourceTypes.Space),
        Pair(Provider.ConfluenceDataCenter, ResourceTypes.Space),
        Pair(Provider.GitHub, ResourceTypes.Repository),
        Pair(Provider.GitHubEnterprise, ResourceTypes.Repository),
        Pair(Provider.GitLab, ResourceTypes.Repository),
        Pair(Provider.GitLabSelfHosted, ResourceTypes.Repository),
        Pair(Provider.GitLabSelfHosted, ResourceTypes.Repository),
        Pair(Provider.GoogleDrive, ResourceTypes.Drive),
        Pair(Provider.Jira, ResourceTypes.Site),
        Pair(Provider.JiraDataCenter, ResourceTypes.Site),
        Pair(Provider.Linear, ResourceTypes.Team),
        Pair(Provider.Web, ResourceTypes.WebSite),
        Pair(Provider.Notion, ResourceTypes.Page),
        Pair(Provider.Slack, ResourceTypes.Channel),
        Pair(Provider.StackOverflowTeams, ResourceTypes.Team),
        Pair(Provider.Coda, ResourceTypes.Folder),
    )

    @ParameterizedTest
    @ArgumentsSource(ProviderToResourceTypePairs::class)
    fun `of -- Provider with known resource type`(input: Pair<Provider, ResourceType>) {
        assertThat(
            ResourceTypes.of(input.first),
        ).isEqualTo(
            input.second,
        )
    }

    @ParameterizedTest
    @EnumSource(Provider::class)
    fun `of -- Providers are all supported`(provider: Provider) {
        assertThat(
            ResourceTypes.of(provider),
        ).isInstanceOf(
            ResourceType::class.java,
        )
    }
}
