// DecryptAESByteArrayKt
package com.nextchaptersoftware.scripts.security

import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.types.toCiphertext
import com.nextchaptersoftware.user.secret.UserSecretService
import kotlinx.cli.ArgParser
import kotlinx.cli.ArgType
import kotlinx.cli.required

fun main(args: Array<String>) {
    val parser = ArgParser("decrypt-aes-byte-array")

    val optKey by parser.option(
        ArgType.String,
        shortName = "k",
        fullName = "key",
        description = "The AES key",
    ).required()

    val optValue by parser.option(
        ArgType.String,
        shortName = "v",
        fullName = "value",
        description = "The encrypted array byte",
    ).required()

    parser.parse(args)

    decryptAesByteArray(
        key = optKey,
        value = optValue,
    ).let(::println)
}

private fun decryptAesByteArray(
    key: String,
    value: String,
): String {
    val userSecretService = loadUserSecretService(key)
    val ciphertext = parseByteArray(value).toCiphertext()
    return userSecretService.decrypt(ciphertext).value
}

private fun loadUserSecretService(
    key: String,
): UserSecretService {
    val aes = AESCryptoSystem.importKey(key)
    return UserSecretService(
        encryption = AESCryptoSystem.AESEncryption(aes),
        decryption = AESCryptoSystem.AESDecryption(aes),
    )
}

@Suppress("MagicNumber")
private fun parseByteArray(
    value: String,
): ByteArray = value
    .removePrefix("0x")
    .chunked(2)
    .map { it.toInt(16).toByte() }
    .toByteArray()
