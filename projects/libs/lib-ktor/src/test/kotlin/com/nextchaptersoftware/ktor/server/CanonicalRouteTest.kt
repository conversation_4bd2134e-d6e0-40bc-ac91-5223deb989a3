package com.nextchaptersoftware.ktor.server

import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class CanonicalRouteTest {

    @Test
    fun fromPath() {
        CanonicalRoute.fromPath("/api/assets/${UUID.randomUUID()}/${UUID.randomUUID()}").also {
            assertThat(it.value).isEqualTo("/api/assets/:id/:id")
        }
        CanonicalRoute.fromPath("/api/foo/bar").also {
            assertThat(it.value).isEqualTo("/api/foo/bar")
        }
        CanonicalRoute.fromPath("/").also {
            assertThat(it.value).isEqualTo("/")
        }
        CanonicalRoute.fromPath("").also {
            assertThat(it.value).isEqualTo("")
        }
    }

    @Test
    fun fromNumericPath() {
        CanonicalRoute.fromPath("/api/assets/40758138/768/pie").also {
            assertThat(it.value).isEqualTo("/api/assets/:id/:id/pie")
        }
        CanonicalRoute.fromPath("/api/assets/768test").also {
            assertThat(it.value).isEqualTo("/api/assets/768test")
        }
    }

    @Test
    fun fromGUIDPath() {
        CanonicalRoute.fromPath("/api/assets/629e8728947454b496e32707/pie").also {
            assertThat(it.value).isEqualTo("/api/assets/:id/pie")
        }
        CanonicalRoute.fromPath("/api/assets/629e8728947454b496e").also {
            assertThat(it.value).isEqualTo("/api/assets/629e8728947454b496e")
        }
    }

    @Test
    fun fromDiffPath() {
        CanonicalRoute.fromPath("/api/diff/blah/blah:5e3f392291b1..2eff32e5c737").also {
            assertThat(it.value).isEqualTo("/api/diff/blah/:id")
        }
    }
}
