@file:Suppress("MagicNumber")

package com.nextchaptersoftware.ktor.utils

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import io.ktor.client.call.body
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode

private val LOGGER = mu.KotlinLogging.logger {}

object HttpExtensions {

    val HttpStatusCode.isClientError: Boolean
        get() = value in (400..499)

    val HttpStatusCode.isServerError: Boolean
        get() = value in (500..599)

    suspend inline fun <reified T> HttpResponse.logCatchingDeserialize(): T {
        return runSuspendCatching {
            body<T>()
        }.onFailure { exception ->
            __loggerHack?.errorSensitiveAsync(
                exception,
                "dataType" to T::class.simpleName,
                sensitiveFields = mapOf(
                    "rawResponse" to bodyAsText(),
                ),
            ) {
                "Response deserialization failed"
            }
        }.getOrThrow()
    }

    // Nullable required to mocks
    @Suppress("ObjectPropertyNaming", "VariableNaming", "ktlint:standard:backing-property-naming")
    val __loggerHack: mu.KLogger? = LOGGER
}
