package com.nextchaptersoftware.slack.events.queue.enqueue

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.slack.events.queue.payloads.SlackEvent

class SlackEventEnqueueService(
    private val eventEnqueueService: EventEnqueueService,
) {
    fun enqueueEvent(
        event: SlackEvent,
        priority: MessagePriority = MessagePriority.DEFAULT,
    ) {
        eventEnqueueService.enqueueEvent(
            body = event.encode(),
            priority = priority,
        )
    }
}
