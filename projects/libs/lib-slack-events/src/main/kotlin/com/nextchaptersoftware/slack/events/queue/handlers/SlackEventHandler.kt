package com.nextchaptersoftware.slack.events.queue.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.slack.events.queue.payloads.SlackEvent
import com.nextchaptersoftware.slack.events.queue.payloads.SlackTeamEvent
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackEventHandler(
    private val slackChannelMembersIngestionEventHandler: SlackChannelMembersIngestionEventHandler,
    private val slackChannelsIngestionEventHandler: SlackChannelsIngestionEventHandler,
    private val slackTeamIngestionEventHandler: SlackTeamIngestionEventHandler,
    private val slackIngestionEventHandler: SlackIngestionEventHandler,
) : EventHandler {
    override suspend fun handle(event: String): Boolean {
        val slackEvent: SlackEvent = runSuspendCatching {
            event.decode<SlackEvent>()
        }.getOrElse {
            LOGGER.errorAsync(it) { "Failed to deserialize SlackEventPayload" }
            return@handle false
        }

        return when (slackEvent) {
            is SlackTeamEvent.SlackChannelMembersIngestionEvent -> slackChannelMembersIngestionEventHandler.handle(slackEvent)
            is SlackTeamEvent.SlackChannelsIngestionEvent -> slackChannelsIngestionEventHandler.handle(slackEvent)
            is SlackTeamEvent.SlackTeamIngestionEvent -> slackTeamIngestionEventHandler.handle(slackEvent)
            is SlackTeamEvent.SlackIngestionEvent -> slackIngestionEventHandler.handle(slackEvent)
        }
    }
}
