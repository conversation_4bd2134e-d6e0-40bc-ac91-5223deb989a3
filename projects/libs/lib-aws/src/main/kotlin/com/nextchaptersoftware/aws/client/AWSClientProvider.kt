package com.nextchaptersoftware.aws.client

import com.nextchaptersoftware.aws.client.AWSClientProviderEndpoint.LocalStack
import com.nextchaptersoftware.aws.extensions.RegionExtensions.isLocalRegion
import com.nextchaptersoftware.trace.aws.openTelemetryOverrideConfiguration
import java.net.URI
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.bedrockagentruntime.BedrockAgentRuntimeClient
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeAsyncClient
import software.amazon.awssdk.services.bedrockruntime.BedrockRuntimeClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.sagemakerruntime.SageMakerRuntimeClient
import software.amazon.awssdk.services.sfn.SfnClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sts.StsClient

class AWSClientProvider(
    val region: Region,
    private val endpoint: AWSClientProviderEndpoint? = null,
    private val credentialsProvider: AwsCredentialsProvider = DefaultCredentialsProvider.create(),
) {
    companion object {
        fun from(region: Region): AWSClientProvider {
            // To support localstack and local testing
            if (region.isLocalRegion) {
                return AWSClientProvider(
                    region = Region.US_EAST_1,
                    endpoint = LocalStack,
                    // Safety measure to prevent tests from targeting AWS Dev
                    credentialsProvider = AwsBasicCredentials.create("accessKeyID", "secretAccessKey")
                        .let { StaticCredentialsProvider.create(it) },
                )
            }
            return AWSClientProvider(region)
        }
    }

    val bedrockRuntimeClient: BedrockRuntimeClient by lazy {
        endpoint?.let {
            BedrockRuntimeClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: BedrockRuntimeClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val bedrockRuntimeAsyncClient: BedrockRuntimeAsyncClient by lazy {
        endpoint?.let {
            BedrockRuntimeAsyncClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: BedrockRuntimeAsyncClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val bedrockAgentRuntimeClient: BedrockAgentRuntimeClient by lazy {
        endpoint?.let {
            BedrockAgentRuntimeClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: BedrockAgentRuntimeClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val dynamoClient: DynamoDbClient by lazy {
        endpoint?.let {
            DynamoDbClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: DynamoDbClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val lambdaClient: LambdaClient by lazy {
        endpoint?.let {
            LambdaClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: LambdaClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val s3Presigner: S3Presigner by lazy {
        endpoint?.let {
            S3Presigner.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .build()
        } ?: S3Presigner
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .build()
    }

    val s3Client: S3Client by lazy {
        endpoint?.let {
            S3Client.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .forcePathStyle(endpoint.forcePathStyle)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: S3Client
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration().build()
    }

    val sageMakerRuntimeClient: SageMakerRuntimeClient by lazy {
        endpoint?.let {
            SageMakerRuntimeClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: SageMakerRuntimeClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val sqsClient: SqsClient by lazy {
        endpoint?.let {
            SqsClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .openTelemetryOverrideConfiguration()
                .build()
        } ?: SqsClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .openTelemetryOverrideConfiguration()
            .build()
    }

    val sfnClient: SfnClient by lazy {
        endpoint?.let {
            SfnClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .build()
        } ?: SfnClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .build()
    }

    // New STS Client
    val stsClient: StsClient by lazy {
        endpoint?.let {
            StsClient.builder().region(region)
                .endpointOverride(URI.create(endpoint.endpoint))
                .credentialsProvider(credentialsProvider)
                .build()
        } ?: StsClient
            .builder()
            .region(region)
            .credentialsProvider(credentialsProvider)
            .build()
    }
}
