package com.nextchaptersoftware.trace.ktor.extractor

import io.opentelemetry.api.common.Attributes
import io.opentelemetry.api.trace.SpanContext
import io.opentelemetry.context.Context
import io.opentelemetry.instrumentation.api.instrumenter.SpanLinksBuilder

interface SpanLinksBuilder {
    fun addLink(spanContext: SpanContext?): SpanLinksBuilder?

    fun addLink(spanContext: SpanContext, attributes: Attributes): SpanLinksBuilder
}

fun interface SpanLinksExtractor<REQUEST> {
    fun extract(spanLinks: SpanLinksBuilder, parentContext: Context, request: REQUEST)
}
