package com.nextchaptersoftware.notion.ingestion.queue.handlers

import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ingestion.common.services.InstallationRateLimitService
import com.nextchaptersoftware.integration.queue.enqueue.IntegrationEventUtils.randomDelay
import com.nextchaptersoftware.ktor.utils.HttpResponseExtensions.isTooManyRequestsException
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.notion.events.queue.enqueue.NotionEventEnqueueService
import com.nextchaptersoftware.notion.events.queue.payloads.NotionIngestionEvent
import com.nextchaptersoftware.notion.ingestion.services.NotionObjectIngestionService
import com.nextchaptersoftware.notion.ingestion.services.StandardNotionLock
import com.nextchaptersoftware.redis.lock.LockProvider
import kotlin.time.Duration.Companion.minutes
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class NotionPageIngestionEventHandler(
    private val notionEventEnqueueService: NotionEventEnqueueService,
    private val notionObjectIngestionService: NotionObjectIngestionService,
    private val lockProvider: LockProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val installationRateLimitService: InstallationRateLimitService = InstallationRateLimitService(),
) : TypedEventHandler<NotionIngestionEvent.NotionPageIngestionEvent> {
    @Suppress("TooGenericExceptionCaught", "InstanceOfCheckForException")
    override suspend fun handle(event: NotionIngestionEvent.NotionPageIngestionEvent): Boolean = withLoggingContextAsync(
        "orgId" to event.orgId,
        "installationId" to event.installationId,
        "pageId" to event.page.id,
    ) {
        val installation = installationStore.findById(installationId = event.installationId) ?: run {
            LOGGER.debugAsync { "Installation not found" }
            return@withLoggingContextAsync true
        }
        require(event.orgId == installation.orgId) { "OrgId mismatch" }

        val lock = lockProvider.create(id = installation.id.value)

        when (!installationRateLimitService.shouldBackoffDueToRateLimiting(installation.id.value) && lock.acquire()) {
            true -> try {
                notionObjectIngestionService.ingestPage(
                    installation = installation,
                    identityId = event.identityId,
                    page = event.page,
                    lock = StandardNotionLock(lock = lock),
                )
            } catch (t: Throwable) {
                when (t.isTooManyRequestsException()) {
                    true -> {
                        LOGGER.debugAsync(t) { "Backing off" }
                        installationRateLimitService.setShouldBackoffDueToRateLimiting(installation.id.value)
                    }

                    else -> {
                        LOGGER.errorAsync(t) { "Failed to ingest Notion page" }
                    }
                }

                throw t // Re-throw to retry the event later
            } finally {
                lock.release()
            }

            else -> { // Re-enqueue to try again later
                notionEventEnqueueService.enqueueIngestionEvent(
                    event = event,
                    withDelay = randomDelay(maxSeconds = 10.minutes.inWholeSeconds.toInt()),
                )
            }
        }

        return@withLoggingContextAsync true
    }
}
