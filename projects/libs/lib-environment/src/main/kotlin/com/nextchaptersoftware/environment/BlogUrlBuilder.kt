package com.nextchaptersoftware.environment

import com.nextchaptersoftware.config.GlobalConfig

class BlogUrlBuilder(
    config: GlobalConfig = GlobalConfig.INSTANCE,
) : BaseUrlBuilder(
    hostName = config.blog.hostName,
    enableTls = config.blog.enableTLS,
    basePathSegments = config.blog.basePath?.let { listOf(it) },
) {
    fun withSlackAppUpdate() = apply {
        addPathSegments("slack-dms-and-private-channels")
    }
}
