package com.nextchaptersoftware.environment

import com.nextchaptersoftware.config.GlobalConfig

class SlackUrlBuilder(
    private val config: GlobalConfig = GlobalConfig.INSTANCE,
) : BaseUrlBuilder(
    hostName = config.providers.slack.appHostName,
    enableTls = true,
) {

    fun withApp(): SlackUrlBuilder {
        this.addPathSegments("apps", config.providers.slack.appId)
        return this
    }
}
