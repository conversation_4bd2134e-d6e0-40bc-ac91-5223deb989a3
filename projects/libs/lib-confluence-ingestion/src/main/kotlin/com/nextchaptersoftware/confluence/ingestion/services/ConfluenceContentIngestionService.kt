package com.nextchaptersoftware.confluence.ingestion.services

import com.nextchaptersoftware.confluence.data.services.ConfluenceContentPersistenceService
import com.nextchaptersoftware.confluence.models.BlogPost
import com.nextchaptersoftware.confluence.models.DataCenterContent
import com.nextchaptersoftware.confluence.models.Page
import com.nextchaptersoftware.db.models.ConfluenceContentId
import com.nextchaptersoftware.db.models.ConfluenceContentType
import com.nextchaptersoftware.db.models.ConfluenceSite
import com.nextchaptersoftware.db.models.ConfluenceSpace
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.stores.ConfluenceContentStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.utils.epoch
import io.ktor.http.Url
import kotlinx.datetime.Instant
import kotlinx.serialization.json.JsonObject

class ConfluenceContentIngestionService(
    private val confluenceContentPersistenceService: ConfluenceContentPersistenceService,
    private val confluenceContentStore: ConfluenceContentStore = Stores.confluenceContentStore,
) {
    suspend fun ingest(
        installation: Installation,
        site: ConfluenceSite,
        space: ConfluenceSpace,
        json: JsonObject,
        page: Page,
    ) {
        require(space.spaceId == page.spaceId) { "Confluence space ID mismatch" }

        val id = ConfluenceContentId(value = page.uuid(confluenceSiteId = site.id, confluenceSpaceId = space.id))
        val createdAt = page.createdAt ?: Instant.epoch
        val updatedAt = page.version.createdAt ?: createdAt
        val url = page.links.externalUrl(site.baseUrl)

        ingest(
            installation = installation,
            site = site,
            space = space,
            contentId = id,
            type = ConfluenceContentType.Page,
            externalId = page.id,
            url = url,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isRestricted = false, // TODO
            content = json,
        )
    }

    suspend fun ingest(
        installation: Installation,
        site: ConfluenceSite,
        space: ConfluenceSpace,
        json: JsonObject,
        blogPost: BlogPost,
    ) {
        require(space.spaceId == blogPost.spaceId) { "Confluence space ID mismatch" }

        val id = ConfluenceContentId(value = blogPost.uuid(confluenceSiteId = site.id, confluenceSpaceId = space.id))
        val createdAt = blogPost.createdAt ?: Instant.epoch
        val updatedAt = blogPost.version.createdAt ?: createdAt
        val url = blogPost.links.externalUrl(site.baseUrl)

        ingest(
            installation = installation,
            site = site,
            space = space,
            contentId = id,
            type = ConfluenceContentType.BlogPost,
            externalId = blogPost.id,
            url = url,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isRestricted = false, // TODO
            content = json,
        )
    }

    suspend fun ingest(
        installation: Installation,
        site: ConfluenceSite,
        space: ConfluenceSpace,
        json: JsonObject,
        content: DataCenterContent,
    ) {
        require(space.spaceId == content.space.id.toString()) { "Confluence space ID mismatch" }

        val type = when {
            content.isPage -> ConfluenceContentType.DataCenterPage
            content.isBlogPost -> ConfluenceContentType.DataCenterBlogPost
            else -> error("Unsupported content type: ${content::class.simpleName}")
        }

        val id = ConfluenceContentId(value = content.uuid(confluenceSiteId = site.id, confluenceSpaceId = space.id))
        val createdAt = content.history.createdDate
        val updatedAt = content.history.lastUpdated.`when`
        val url = content.links.externalUrl(site.baseUrl)

        ingest(
            installation = installation,
            site = site,
            space = space,
            contentId = id,
            type = type,
            externalId = content.id,
            url = url,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isRestricted = content.isRestricted,
            content = json,
        )
    }

    private suspend fun ingest(
        installation: Installation,
        site: ConfluenceSite,
        space: ConfluenceSpace,
        contentId: ConfluenceContentId,
        type: ConfluenceContentType,
        externalId: String,
        url: Url,
        createdAt: Instant,
        updatedAt: Instant,
        isRestricted: Boolean,
        content: JsonObject,
    ) {
        require(installation.id == site.installationId) { "Installation ID mismatch" }
        require(site.id == space.confluenceSiteId) { "Confluence site ID mismatch" }

        when (confluenceContentStore.updatedAt(id = contentId) == updatedAt) {
            true -> {
                // Touch the row so that we know we've seen it recently,
                // but don't ingest because we have the latest version
                confluenceContentStore.touchModifiedAt(id = contentId, url = url)
            }

            else -> {
                confluenceContentPersistenceService.persist(
                    orgId = installation.orgId,
                    installationId = installation.id,
                    confluenceSpaceId = space.id,
                    confluenceContentId = contentId,
                    content = content,
                )

                confluenceContentStore.upsert(
                    id = contentId,
                    installationId = installation.id,
                    spaceId = space.id,
                    type = type,
                    externalId = externalId,
                    url = url,
                    createdAt = createdAt,
                    updatedAt = updatedAt,
                    isRestricted = isRestricted,
                    needsExtraction = true,
                    needsEmbedding = true,
                )
            }
        }
    }
}
