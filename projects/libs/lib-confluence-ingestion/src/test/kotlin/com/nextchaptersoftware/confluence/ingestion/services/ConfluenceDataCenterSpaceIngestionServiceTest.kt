package com.nextchaptersoftware.confluence.ingestion.services

import com.nextchaptersoftware.confluence.models.DataCenterSpace
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.BaseInstallation
import com.nextchaptersoftware.db.models.ConfluenceDataCenterInstallation
import com.nextchaptersoftware.db.models.ConfluenceSpacePermissionEntityType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.ConfluenceSpacePermissionStore
import com.nextchaptersoftware.db.stores.ConfluenceSpaceStore
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify

class ConfluenceDataCenterSpaceIngestionServiceTest {
    private val spaceStore = mock<ConfluenceSpaceStore>()
    private val permissionStore = mock<ConfluenceSpacePermissionStore>()

    private val service = ConfluenceDataCenterSpaceIngestionService(
        apiProvider = mock(),
        confluenceSpaceStore = spaceStore,
        confluenceSpacePermissionStore = permissionStore,
    )

    private val installation = ConfluenceDataCenterInstallation(
        baseInstallation = MockDataClasses.installation(provider = Provider.ConfluenceDataCenter) as BaseInstallation,
        accessToken = Ciphertext("accessToken".toByteArray()),
    )

    private val site = MockDataClasses.confluenceSite(
        installationId = installation.id,
    )

    private val spaceKey = "SK"
    private val dataCenterSpace = DataCenterSpace(
        id = 1,
        name = "Space Name",
        key = spaceKey,
        status = "current",
        type = "global",
        permissions = DataCenterSpace.Permissions(
            data = listOf(
                DataCenterSpace.Permission(
                    operation = DataCenterSpace.Permission.Operation(targetType = "space", operationKey = "read"),
                    subject = DataCenterSpace.Permission.Subject(type = "user", userKey = "user1"),
                    spaceKey = spaceKey,
                ),
                DataCenterSpace.Permission(
                    operation = DataCenterSpace.Permission.Operation(targetType = "space", operationKey = "read"),
                    subject = DataCenterSpace.Permission.Subject(type = "group", name = "group1"),
                    spaceKey = spaceKey,
                ),
                DataCenterSpace.Permission(
                    operation = DataCenterSpace.Permission.Operation(targetType = "space", operationKey = "read"),
                    subject = DataCenterSpace.Permission.Subject(type = "anonymous"),
                    spaceKey = spaceKey,
                ),
            ),
        ),
    )

    private val space = MockDataClasses.confluenceSpace(spaceKey = spaceKey, spaceId = dataCenterSpace.id.toString())

    private val confluenceSpacePermissionA = MockDataClasses.confluenceSpacePermission(
        confluenceSpaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.User, entityId = "user1",
    )

    private val confluenceSpacePermissionB = MockDataClasses.confluenceSpacePermission(
        confluenceSpaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Group, entityId = "group1",
    )

    private val confluenceSpacePermissionC = MockDataClasses.confluenceSpacePermission(
        confluenceSpaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Anonymous, entityId = "",
    )

    private val confluenceSpacePermissionD = MockDataClasses.confluenceSpacePermission(
        confluenceSpaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Group, entityId = "otherGroup",
    )

    val confluenceSpacePermissions = listOf(
        confluenceSpacePermissionA,
        confluenceSpacePermissionB,
        confluenceSpacePermissionC,
        confluenceSpacePermissionD,
    )

    @Test
    fun ingestSpacePermissions() = runTest {
        `when`(spaceStore.findBySpaceId(confluenceSiteId = eq(site.id), spaceId = eq(dataCenterSpace.id.toString()))).thenReturn(space)
        `when`(permissionStore.list(spaceIds = listOf(space.id))).thenReturn(confluenceSpacePermissions)

        service.ingestSpacePermissions(
            installation = installation,
            site = site,
            dataCenterSpace = dataCenterSpace,
        )

        verify(permissionStore, times(1))
            .upsert(spaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.User, entityId = "user1")

        verify(permissionStore, times(1))
            .upsert(spaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Group, entityId = "group1")

        verify(permissionStore, times(1))
            .upsert(spaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Anonymous, entityId = "")

        verify(permissionStore, times(1))
            .delete(ids = listOf(confluenceSpacePermissionD.id))
    }

    @Test
    fun `ingestSpacePermissions -- deletes anonymous`() = runTest {
        `when`(spaceStore.findBySpaceId(eq(site.id), eq(dataCenterSpace.id.toString()))).thenReturn(space)
        `when`(permissionStore.list(spaceIds = listOf(space.id))).thenReturn(confluenceSpacePermissions.minus(confluenceSpacePermissionD))

        service.ingestSpacePermissions(
            installation = installation,
            site = site,
            dataCenterSpace = dataCenterSpace.copy(
                permissions = dataCenterSpace.permissions.required().copy(
                    data = dataCenterSpace.permissions.required().data.filter { it.subject.type != "anonymous" },
                ),
            ),
        )

        verify(permissionStore, times(1))
            .upsert(spaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.User, entityId = "user1")

        verify(permissionStore, times(1))
            .upsert(spaceId = space.id, entityType = ConfluenceSpacePermissionEntityType.Group, entityId = "group1")

        verify(permissionStore, times(1))
            .delete(ids = listOf(confluenceSpacePermissionC.id))
    }
}
