package com.nextchaptersoftware.maintenance.events.queue.payloads

import com.nextchaptersoftware.db.models.OrgId
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class MaintenanceEvent {
    @SerialName("DSACOrgSettingUpdatedEvent")
    @Serializable
    data class DSACOrgSettingUpdatedEvent(
        val orgId: OrgId,
    ) : MaintenanceEvent()
}
