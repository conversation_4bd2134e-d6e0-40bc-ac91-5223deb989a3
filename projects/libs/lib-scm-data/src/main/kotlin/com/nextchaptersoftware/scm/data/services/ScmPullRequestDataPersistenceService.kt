package com.nextchaptersoftware.scm.data.services

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.data.store.ScmPullRequestDataStore
import com.nextchaptersoftware.scm.models.ScmPullRequest
import io.ktor.http.ContentType
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ScmPullRequestDataPersistenceService(
    private val scmPullRequestDataStore: ScmPullRequestDataStore,
) {
    suspend fun persist(
        orgId: OrgId,
        repoId: RepoId,
        pullRequest: ScmPullRequest,
    ) = withLoggingContextAsync(
        "teamId" to orgId,
        "repoId" to repoId,
        "pullRequestNumber" to pullRequest.number,
    ) {
        runSuspendCatching {
            scmPullRequestDataStore.createData(
                orgId = orgId,
                repoId = repoId,
                pullRequest = pullRequest,
                contentType = ContentType.Application.Json,
            )
        }.onFailure {
            LOGGER.errorAsync(it) { "Failed to persist scm data" }
        }.onSuccess {
            LOGGER.traceAsync { "Finished scm data persistence" }
        }
    }
}
