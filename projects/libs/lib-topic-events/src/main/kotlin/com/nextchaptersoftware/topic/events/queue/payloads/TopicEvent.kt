package com.nextchaptersoftware.topic.events.queue.payloads

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.TopicId
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * WARNING:
 * https://github.com/Kotlin/kotlinx.serialization/blob/master/docs/polymorphism.md
 * Changes to the SerialName must be reflected in infra config files
 * dev-us-west-2.json, prod-us-west-2.json as we're using polymorphic serialization
 */
@Serializable
sealed class TopicEvent {
    abstract val orgId: OrgId

    @SerialName("TopicInsightMappingEvent")
    @Serializable
    data class TopicInsightMappingEvent(
        @SerialName("orgId")
        override val orgId: OrgId,
        @Contextual
        val insightId: UUID,
        val topicId: TopicId,
    ) : TopicEvent()

    @SerialName("TopicInsightMappingsEvent")
    @Serializable
    data class TopicInsightMappingsEvent(
        @SerialName("orgId")
        override val orgId: OrgId,
        @Contextual
        val insightId: UUID,
    ) : TopicEvent()

    @SerialName("TopicMappingEvent")
    @Serializable
    data class TopicMappingEvent(
        @SerialName("orgId")
        override val orgId: OrgId,
        val topicId: TopicId,
        val updateExperts: Boolean = false,
    ) : TopicEvent()
}
