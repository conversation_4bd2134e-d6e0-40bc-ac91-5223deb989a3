package com.nextchaptersoftware.access

import com.nextchaptersoftware.db.models.Provider

interface RestrictedAccessServiceInterface {
    fun allowOrg(provider: Provider, externalId: String): Boolean

    fun allowUser(provider: Provider, externalId: String): Boolean

    fun allowHost(hostname: String): Boolean

    fun checkOrgAccess(provider: Provider, externalId: String, enterpriseAuthority: String?) {
        enterpriseAuthority?.also {
            return checkHostAccess(enterpriseAuthority)
        }
        if (!allowOrg(provider, externalId)) {
            throw RestrictedAccessException("Organization Access denied: '$provider/$externalId'")
        }
    }

    fun checkUserAccess(provider: Provider, externalId: String, enterpriseAuthority: String?) {
        enterpriseAuthority?.also {
            return checkHostAccess(enterpriseAuthority)
        }
        if (!allowUser(provider, externalId)) {
            throw RestrictedAccessException("User Access denied: '$provider/$externalId'")
        }
    }

    fun checkHostAccess(hostnameWithPort: String) {
        hostnameWithPort.split(":")[0].also { hostname ->
            if (!allowHost(hostname)) {
                throw RestrictedAccessException("Host Access denied: '$hostname'")
            }
        }
    }
}
