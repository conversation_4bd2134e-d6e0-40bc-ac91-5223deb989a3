package com.nextchaptersoftware.access

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RestrictedAccessServiceFactoryTest {

    @Test
    fun `creates no-op service from allow all config`() {
        val restrictedAccessService = RestrictedAccessConfig(
            allowedAll = true,
            allowedAllOrgs = false,
            allowedAllUsers = false,
            allowedAllHosts = false,
            allowedProviderOrgs = emptyMap(),
            allowedProviderUsers = emptyMap(),
            allowedProviderHosts = emptySet(),
        ).let { config ->
            RestrictedAccessServiceFactory.fromConfig(config)
        }

        assertThat(restrictedAccessService).isInstanceOf(NoOpRestrictedAccessService::class.java)
    }

    @Test
    fun `creates real service from non-allow all config`() {
        val restrictedAccessService = RestrictedAccessConfig(
            allowedAll = false,
            allowedAllOrgs = false,
            allowedAllUsers = false,
            allowedAllHosts = false,
            allowedProviderOrgs = emptyMap(),
            allowedProviderUsers = emptyMap(),
            allowedProviderHosts = emptySet(),
        ).let { config ->
            RestrictedAccessServiceFactory.fromConfig(config)
        }

        assertThat(restrictedAccessService).isInstanceOf(RestrictedAccessService::class.java)
    }
}
