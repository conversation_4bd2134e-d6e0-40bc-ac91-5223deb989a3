package com.nextchaptersoftware.markdown

import com.nextchaptersoftware.common.model.Message
import com.nextchaptersoftware.common.model.TableBlockKt.tableBody
import com.nextchaptersoftware.common.model.TableBlockKt.tableCell
import com.nextchaptersoftware.common.model.TableBlockKt.tableHead
import com.nextchaptersoftware.common.model.TableBlockKt.tableRow
import com.nextchaptersoftware.common.model.block
import com.nextchaptersoftware.common.model.codeBlock
import com.nextchaptersoftware.common.model.formattedText
import com.nextchaptersoftware.common.model.horizontalLineBlock
import com.nextchaptersoftware.common.model.inlineElement
import com.nextchaptersoftware.common.model.link
import com.nextchaptersoftware.common.model.listBlock
import com.nextchaptersoftware.common.model.messageBody
import com.nextchaptersoftware.common.model.paragraphBlock
import com.nextchaptersoftware.common.model.quoteBlock
import com.nextchaptersoftware.common.model.tableBlock
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.markdown.MessageBodyConverter.asHtml
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.test.utils.TestUtils
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class MessageBodyConverterTest {
    private val blockA = block {
        paragraph = paragraphBlock {
            elements.add(
                inlineElement {
                    text = formattedText {
                        this.text = "This is a message"
                    }
                },
            )
        }
    }

    private val blockB = block {
        paragraph = paragraphBlock {
            elements.add(
                inlineElement {
                    text = formattedText {
                        text = "This is another message"
                    }
                },
            )
        }
    }

    private fun createMessageBody(vararg blocks: Message.Block) = messageBody {
        this.blocks += blocks.toList()
        version = MarkdownConverter.MESSAGE_PROTO_VERSION
    }

    private val listBlock = listBlock {
        this.style = Message.ListBlock.Style.ORDERED
        this.items += listOf(
            Message.ListBlock.ListBlockItem.newBuilder().let {
                it.addBlocks(blockA)
                it.build()
            },
            Message.ListBlock.ListBlockItem.newBuilder().let {
                it.addBlocks(blockB)
                it.build()
            },
        )
    }

    @Test
    fun code() {
        val codeBlock = block {
            code = codeBlock {
                text = """
                    message MessageBody {
                      repeated Block blocks = 1;
                      required string version = 2;
                    }
                """.trimIndent()
            }
        }
        val result = createMessageBody(codeBlock).asMarkdown()
        assertThat(result).isEqualTo(
            "```\nmessage MessageBody {\n  repeated Block blocks = 1;\n  required string version = 2;\n}\n```",
        )
    }

    @Test
    fun orderedList() {
        val result = createMessageBody(
            Message.Block.newBuilder().let { builder ->
                builder.list = listBlock
                builder.build()
            },
        ).asMarkdown()

        assertThat(result).isEqualTo(
            """
                1) This is a message
                2) This is another message
            """.trimIndent(),
        )
    }

    @Test
    fun orderedListWithStartNumber() {
        val result = createMessageBody(
            Message.Block.newBuilder().let { builder ->
                builder.list = listBlock.toBuilder().let {
                    it.startNumber = 3
                    it.build()
                }
                builder.build()
            },
        ).asMarkdown()

        assertThat(result).isEqualTo(
            """
                3) This is a message
                4) This is another message
            """.trimIndent(),
        )
    }

    @Test
    fun unorderedList() {
        val result = createMessageBody(
            Message.Block.newBuilder().let { builder ->
                builder.list = listBlock.toBuilder().let {
                    it.style = Message.ListBlock.Style.UNORDERED
                    it.build()
                }
                builder.build()
            },
        ).asMarkdown()

        assertThat(result).isEqualTo(
            """
                - This is a message
                - This is another message
            """.trimIndent(),
        )
    }

    @Test
    fun line() {
        val lineBlock = block {
            line = horizontalLineBlock {}
        }
        val result = createMessageBody(blockA, lineBlock, blockB).asMarkdown()
        assertThat(result).isEqualTo(
            """
                This is a message

                ---

                This is another message
            """.trimIndent(),
        )
    }

    @Test
    fun quote() {
        val quoteBlock = block {
            quote = quoteBlock {
                blocks += listOf(blockA, blockB)
            }
        }
        val result = createMessageBody(quoteBlock).asMarkdown()
        assertThat(result).isEqualTo(
            """
                > This is a message
                > This is another message
            """.trimIndent(),
        )
    }

    @Test
    fun paragraph() {
        val block = block {
            paragraph = paragraphBlock {
                elements.add(
                    inlineElement {
                        text = formattedText {
                            text = "This is a message"
                            isItalic = true
                            isBold = true
                        }
                    },
                )
                elements.add(inlineElement { text = formattedText { text = " " } })
                elements.add(
                    inlineElement {
                        link = link {
                            textItems.add(formattedText { text = "Google" })
                            url = "https://www.google.com"
                        }
                    },
                )
            }
        }
        val result = createMessageBody(block).asMarkdown()
        assertThat(result).isEqualTo("**_This is a message_** [Google](https://www.google.com)")
    }

    @Test
    fun markdownToMessageBodyToMarkdown() {
        val markdown = TestUtils.getResource(this, "/markdown/Markdown.md")
        val messageBody = markdown.asMessageBody()
        val markdownFromMessageBody = messageBody.asMarkdown()
        assertThat(markdownFromMessageBody).isNotBlank
    }

    @Test
    fun htmlParagraph() {
        val block = block {
            paragraph = paragraphBlock {
                elements.add(
                    inlineElement {
                        text = formattedText {
                            text = "This is a message"
                            isBold = true
                        }
                    },
                )
                elements.add(inlineElement { text = formattedText { text = " " } })
                elements.add(
                    inlineElement {
                        text = formattedText {
                            text = "This is another message"
                            isItalic = true
                        }
                    },
                )
            }
        }

        val result = createMessageBody(block).asHtml()
        assertThat(result).isEqualTo("<p><strong>This is a message</strong> <em>This is another message</em></p>\n")
    }

    @Test
    fun htmlLink() {
        val block = block {
            paragraph = paragraphBlock {
                elements.add(
                    inlineElement {
                        text = formattedText {
                            text = "This is a message"
                            isItalic = true
                            isBold = true
                        }
                    },
                )
                elements.add(inlineElement { text = formattedText { text = " " } })
                elements.add(
                    inlineElement {
                        link = link {
                            textItems.add(formattedText { text = "Google" })
                            url = "https://www.google.com"
                        }
                    },
                )
            }
        }

        val result = createMessageBody(block).asHtml()
        assertThat(result).isEqualTo("<p><strong><em>This is a message</em></strong> <a href=\"https://www.google.com\">Google</a></p>\n")
    }

    @Test
    fun table() {
        val headRow = tableRow {
            cells.add(
                tableCell {
                    width = 10
                    alignment = Message.TableBlock.TableCell.Alignment.LEFT
                    contents.add(
                        inlineElement {
                            text = formattedText { text = "Header 1" }
                        },
                    )
                },
            )
            cells.add(
                tableCell {
                    width = 20
                    alignment = Message.TableBlock.TableCell.Alignment.CENTER
                    contents.add(
                        inlineElement {
                            text = formattedText { text = "Header 2" }
                        },
                    )
                },
            )
            cells.add(
                tableCell {
                    width = 15
                    alignment = Message.TableBlock.TableCell.Alignment.RIGHT
                    contents.add(
                        inlineElement {
                            text = formattedText { text = "Header 3" }
                        },
                    )
                },
            )
        }

        val bodyRows = listOf(
            tableRow {
                cells.add(
                    tableCell {
                        width = 10
                        alignment = Message.TableBlock.TableCell.Alignment.LEFT
                        contents.add(
                            inlineElement {
                                text = formattedText { text = "Cell 1" }
                            },
                        )
                    },
                )
                cells.add(
                    tableCell {
                        width = 20
                        alignment = Message.TableBlock.TableCell.Alignment.CENTER
                        contents.add(
                            inlineElement {
                                text = formattedText { text = "Cell 2" }
                            },
                        )
                    },
                )
                cells.add(
                    tableCell {
                        width = 15
                        alignment = Message.TableBlock.TableCell.Alignment.RIGHT
                        contents.add(
                            inlineElement {
                                text = formattedText { text = "Cell 3" }
                            },
                        )
                    },
                )
            },
        )

        val tableBlock = tableBlock {
            head = tableHead {
                row = headRow
            }
            body = tableBody {
                rows.addAll(bodyRows)
            }
        }

        val block = block {
            table = tableBlock
        }

        val messageBody = createMessageBody(block)
        val result = messageBody.asMarkdown()
        assertThat(result).isEqualTo(
            """
                Header 1 | Header 2 | Header 3
                ---------- | :------------------: | --------------:
                Cell 1 | Cell 2 | Cell 3
            """.trimIndent(),
        )

        assertThat(result.asMessageBody()).isEqualTo(messageBody)
    }
}
