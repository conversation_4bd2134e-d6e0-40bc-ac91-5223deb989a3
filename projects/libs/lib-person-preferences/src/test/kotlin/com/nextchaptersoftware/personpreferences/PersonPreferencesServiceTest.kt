package com.nextchaptersoftware.personpreferences

import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.interceptors.explain.SlowQueryConfig
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PersonPreferencesServiceTest : DatabaseTestsBase() {
    private val personPreferencesMetricsStore = Stores.personPreferencesMetricsStore
    private val personPreferencesStore = Stores.personPreferencesStore
    private val service = PersonPreferencesService()

    @Test
    fun `calculatePersonPreferencesMetrics -- no preferences`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(false)
        },
    ) {
        makePerson()

        service.calculatePersonPreferencesMetrics()

        val result = personPreferencesMetricsStore.latest() ?: error("No PersonPreferencesMetricsDAO found")
        assertThat(result.countUsers).isEqualTo(0)
        assertThat(result.countIncognitoMode).isEqualTo(0)

        service.calculatePersonPreferencesMetrics() // Run again to ensure no duplicates
        assertThat(personPreferencesMetricsStore.latest()?.asDataModel()).isEqualTo(result.asDataModel())
    }

    @Test
    fun `calculatePersonPreferencesMetrics -- incognito mode not enabled `() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(false)
        },
    ) {
        val person = makePerson()
        personPreferencesStore.upsert(personId = person.idValue, incognitoMode = false)

        service.calculatePersonPreferencesMetrics()

        val result = personPreferencesMetricsStore.latest() ?: error("No PersonPreferencesMetricsDAO found")
        assertThat(result.countUsers).isEqualTo(1)
        assertThat(result.countIncognitoMode).isEqualTo(0)
    }

    @Test
    fun `calculatePersonPreferencesMetrics -- incognito mode enabled `() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(false)
        },
    ) {
        val person = makePerson()
        personPreferencesStore.upsert(personId = person.idValue, incognitoMode = true)

        service.calculatePersonPreferencesMetrics()

        val result = personPreferencesMetricsStore.latest() ?: error("No PersonPreferencesMetricsDAO found")
        assertThat(result.countUsers).isEqualTo(1)
        assertThat(result.countIncognitoMode).isEqualTo(1)
    }
}
