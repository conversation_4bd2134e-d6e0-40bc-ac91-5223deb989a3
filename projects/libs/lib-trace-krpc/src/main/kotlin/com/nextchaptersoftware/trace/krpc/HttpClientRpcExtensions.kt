package com.nextchaptersoftware.trace.krpc

import com.nextchaptersoftware.log.kotlin.traceSync
import com.nextchaptersoftware.trace.ktor.http.headers.KtorHttpHeadersSetter
import io.ktor.client.HttpClient
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.url
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.trace.Span
import io.opentelemetry.context.Context
import kotlinx.rpc.krpc.ktor.client.KtorRpcClient
import kotlinx.rpc.krpc.ktor.client.rpc
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

object HttpClientRpcExtensions {
    suspend fun HttpClient.instrumentedRPC(
        urlString: String? = null,
        block: HttpRequestBuilder.() -> Unit = {},
    ): KtorRpcClient {
        return this.rpc {
            urlString?.also { url(it) }
            val context = Context.current()
            LOGGER.traceSync(
                "traceId" to Span.current().spanContext.traceId,
                "spanId" to Span.current().spanContext.spanId,
            ) {
                "Injecting openTelemetry context for RPC call"
            }

            GlobalOpenTelemetry.get().propagators.textMapPropagator.inject(context, this, KtorHttpHeadersSetter)
            block()
        }
    }
}
