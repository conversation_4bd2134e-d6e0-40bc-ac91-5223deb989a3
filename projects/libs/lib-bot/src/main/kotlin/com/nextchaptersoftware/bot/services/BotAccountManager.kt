package com.nextchaptersoftware.bot.services

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMember
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toIdentity
import com.nextchaptersoftware.db.models.toMember
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.DashboardUrlBuilder
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.maintenance.OrgMemberMaintenance
import com.nextchaptersoftware.maintenance.UnblockedInstallationMaintenance
import com.nextchaptersoftware.scm.Scm
import java.util.UUID
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.upsertReturning

interface IBotAccountManager<E, M> {
    /**
     * Use this to refresh the bot profiles whenever we change those profile properties (eg: avatar).
     * Intended to be invoked from admin web.
     */
    suspend fun upsertIdentities()

    suspend fun getOrCreateMember(entity: E): Member

    suspend fun getMemberId(entityId: M): MemberId?
}

abstract class BaseBotAccountManager<E, M>(
    private val orgMemberMaintenance: OrgMemberMaintenance,
) : IBotAccountManager<E, M> {
    protected abstract suspend fun getMember(entity: E): Member?

    protected abstract suspend fun upsertMember(entity: E): Member

    override suspend fun getOrCreateMember(entity: E): Member {
        return getMember(entity) ?: upsertMember(entity)
    }

    protected suspend fun upsertOrgMember(
        orgId: OrgId,
        identityId: IdentityId,
    ): OrgMember {
        return suspendedTransaction {
            orgMemberMaintenance.upsertOrgMember(
                trx = this,
                id = null,
                orgId = orgId,
                identityId = identityId,
                personId = null,
                unblockedRole = null,
            )
        }
    }
}

internal class BotAccountManager(
    private val urlBuilderProvider: UrlBuilderProvider,
    private val appConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    orgMemberMaintenance: OrgMemberMaintenance = OrgMemberMaintenance(),
) : BaseBotAccountManager<ScmTeam, ScmTeamId>(orgMemberMaintenance = orgMemberMaintenance) {

    /**
     * Deterministic way to look up the bot identity ID, without having to make a DB query.
     */
    internal fun lookupBotIdentityId(item: Scm): IdentityId {
        return UUID.nameUUIDFromBytes("${item.uniqueSignature} Bot".toByteArray()).let(::IdentityId)
    }

    override suspend fun upsertIdentities() {
        listOf(Scm.GitHub, Scm.GitLab, Scm.Bitbucket).forEach { scm ->
            upsertIdentity(scm = scm)
        }
        appConfigStore.list().forEach { appConfig ->
            upsertIdentity(scm = Scm.GitHubEnterprise(appConfig.id))
        }
    }

    private suspend fun upsertIdentity(scm: Scm): Identity {
        return suspendedTransaction {
            IdentityModel.upsertReturning(
                keys = arrayOf(
                    IdentityModel.id,
                ),
                onUpdateExclude = IdentityModel.columns - setOf(
                    IdentityModel.avatarUrl,
                    IdentityModel.displayName,
                    IdentityModel.externalId,
                    IdentityModel.externalTeamId,
                    IdentityModel.htmlUrl,
                    IdentityModel.isBot,
                    IdentityModel.provider,
                    IdentityModel.username,
                ),
            ) { stmt ->
                stmt[this.id] = lookupBotIdentityId(scm)
                stmt[this.avatarUrl] = urlBuilderProvider.staticAssets().withAsset(DEFAULT_AVATAR_ASSET).build().asString
                stmt[this.displayName] = DEFAULT_NAME
                stmt[this.externalId] = "000000"
                stmt[this.externalTeamId] = scm.uniqueSignature
                stmt[this.htmlUrl] = botWebUrl.asString
                stmt[this.isBot] = true
                stmt[this.provider] = scm.provider
                stmt[this.username] = DEFAULT_USERNAME
            }.let {
                it.first().toIdentity()
            }
        }
    }

    override suspend fun getMember(entity: ScmTeam): Member? {
        return suspendedTransaction {
            MemberModel
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.installation,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                ) {
                    ScmTeamModel.id eq entity.id
                }
                .selectAll()
                .where { (MemberModel.identity eq getIdentityId(entity)) }
                .limit(1)
                .map { row -> row.toMember() }
                .firstOrNull()
        }
    }

    override suspend fun upsertMember(entity: ScmTeam): Member {
        val scm = Scm.fromTeam(entity)
        val identityId = lookupBotIdentityId(scm)
        val setCurrentMember = true

        val identity = upsertIdentity(scm = scm)

        val orgMember = upsertOrgMember(orgId = entity.orgId, identityId = identity.id)

        return suspendedTransaction {
            MemberModel.upsertReturning(
                keys = arrayOf(
                    MemberModel.installation,
                    MemberModel.identity,
                ),
                onUpdateExclude = MemberModel.columns - setOf(
                    MemberModel.isCurrentMember,
                    MemberModel.isPrimaryMember,
                    MemberModel.ignoreThreads,
                    MemberModel.providerRole,
                ),
            ) { stmt ->
                stmt[this.installation] = entity.installationId
                stmt[this.identity] = identityId
                stmt[this.isCurrentMember] = setCurrentMember
                stmt[this.isPrimaryMember] = true
                stmt[this.providerRole] = ProviderRole.None
                stmt[this.orgMember] = orgMember.id
            }.let {
                it.first().toMember()
            }
        }
    }

    override suspend fun getMemberId(entityId: ScmTeamId): MemberId? {
        return suspendedTransaction {
            val scmTeam = ScmTeamDAO.findById(entityId)?.asDataModel() ?: return@suspendedTransaction null

            MemberModel
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.installation,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                ) {
                    ScmTeamModel.id eq scmTeam.id
                }
                .select(MemberModel.id)
                .where { (MemberModel.identity eq getIdentityId(scmTeam)) }
                .limit(1)
                .map { row -> row[MemberModel.id].value }
                .firstOrNull()
        }
    }

    private fun getIdentityId(scmTeam: ScmTeam): IdentityId {
        val scm = Scm.fromTeam(scmTeam)
        return lookupBotIdentityId(scm)
    }

    private val botWebUrl by lazy {
        urlBuilderProvider.dashboard().build()
    }

    companion object {
        private const val DEFAULT_NAME = "Unblocked"
        private const val DEFAULT_USERNAME = "Unblocked"
        private const val DEFAULT_AVATAR_ASSET = "bot-avatar.svg"
    }
}

internal class InstallationBotAccountManager(
    private val urlBuilderProvider: UrlBuilderProvider,
    private val unblockedInstallationMaintenance: UnblockedInstallationMaintenance = UnblockedInstallationMaintenance(),
    orgMemberMaintenance: OrgMemberMaintenance = OrgMemberMaintenance(),
) : BaseBotAccountManager<OrgId, OrgId>(orgMemberMaintenance = orgMemberMaintenance) {

    internal fun lookupBotIdentityId(item: Provider): IdentityId {
        return UUID.nameUUIDFromBytes("${item.uniqueSignature} Bot".toByteArray()).let(::IdentityId)
    }

    override suspend fun upsertIdentities() {
        upsertIdentity(provider = UnblockedInstallationMaintenance.UNBLOCKED_PROVIDER)
    }

    private suspend fun upsertInstallation(
        orgId: OrgId,
    ): Installation {
        return unblockedInstallationMaintenance.upsertInstallation(orgId = orgId)
    }

    private suspend fun upsertIdentity(provider: Provider): Identity {
        return suspendedTransaction {
            IdentityModel.upsertReturning(
                keys = arrayOf(
                    IdentityModel.id,
                ),
                onUpdateExclude = IdentityModel.columns - setOf(
                    IdentityModel.avatarUrl,
                    IdentityModel.displayName,
                    IdentityModel.externalId,
                    IdentityModel.externalTeamId,
                    IdentityModel.htmlUrl,
                    IdentityModel.isBot,
                    IdentityModel.provider,
                    IdentityModel.username,
                ),
            ) { stmt ->
                stmt[this.id] = lookupBotIdentityId(item = provider)
                stmt[this.avatarUrl] = urlBuilderProvider.staticAssets().withAsset(DEFAULT_AVATAR_ASSET).build().asString
                stmt[this.displayName] = DEFAULT_NAME
                stmt[this.externalId] = "000000"
                stmt[this.externalTeamId] = provider.uniqueSignature
                stmt[this.htmlUrl] = botWebUrl.asString
                stmt[this.isBot] = true
                stmt[this.provider] = provider
                stmt[this.username] = DEFAULT_USERNAME
            }.let {
                it.first().toIdentity()
            }
        }
    }

    override suspend fun getMember(entity: OrgId): Member? {
        val installation = upsertInstallation(orgId = entity)
        return suspendedTransaction {
            MemberModel
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                ) {
                    InstallationModel.id eq installation.id
                }
                .selectAll()
                .where { (MemberModel.identity eq lookupBotIdentityId(item = installation.provider)) }
                .limit(1)
                .map { row -> row.toMember() }
                .firstOrNull()
        }
    }

    override suspend fun upsertMember(entity: OrgId): Member {
        val identityId = lookupBotIdentityId(item = UnblockedInstallationMaintenance.UNBLOCKED_PROVIDER)
        val setCurrentMember = true

        val installation = upsertInstallation(orgId = entity)

        val identity = upsertIdentity(provider = installation.provider)

        val orgMember = upsertOrgMember(orgId = installation.orgId, identityId = identity.id)

        return suspendedTransaction {
            MemberModel.upsertReturning(
                keys = arrayOf(
                    MemberModel.installation,
                    MemberModel.identity,
                ),
                onUpdateExclude = MemberModel.columns - setOf(
                    MemberModel.isCurrentMember,
                    MemberModel.isPrimaryMember,
                    MemberModel.ignoreThreads,
                    MemberModel.providerRole,
                ),
            ) { stmt ->
                stmt[this.installation] = installation.id
                stmt[this.identity] = identityId
                stmt[this.isCurrentMember] = setCurrentMember
                stmt[this.isPrimaryMember] = true
                stmt[this.providerRole] = ProviderRole.None
                stmt[this.orgMember] = orgMember.id
            }.let {
                it.first().toMember()
            }
        }
    }

    override suspend fun getMemberId(entityId: OrgId): MemberId? {
        return suspendedTransaction {
            val installation = upsertInstallation(orgId = entityId)

            MemberModel
                .select(MemberModel.id)
                .whereAll(
                    MemberModel.identity eq lookupBotIdentityId(item = installation.provider),
                    MemberModel.installation eq installation.id,
                )
                .limit(1)
                .map { row -> row[MemberModel.id].value }
                .firstOrNull()
        }
    }

    private val botWebUrl by lazy {
        DashboardUrlBuilder().build()
    }

    companion object {
        private const val DEFAULT_NAME = "Unblocked"
        private const val DEFAULT_USERNAME = "Unblocked"
        private const val DEFAULT_AVATAR_ASSET = "bot-avatar.svg"
    }
}
