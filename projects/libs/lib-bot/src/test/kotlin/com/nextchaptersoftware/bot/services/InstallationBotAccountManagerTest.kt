package com.nextchaptersoftware.bot.services

import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.maintenance.OrgMemberMaintenance
import com.nextchaptersoftware.maintenance.UnblockedInstallationMaintenance
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class InstallationBotAccountManagerTest : DatabaseTestsBase() {

    private val orgMemberMaintenance = OrgMemberMaintenance()
    private val unblockedInstallationMaintenance = UnblockedInstallationMaintenance()

    private val installationBotAccountManager = InstallationBotAccountManager(
        unblockedInstallationMaintenance = unblockedInstallationMaintenance,
        orgMemberMaintenance = orgMemberMaintenance,
        urlBuilderProvider = StandardUrlBuilderProvider(),
    )

    private lateinit var org: OrgDAO

    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
    }

    @Test
    fun `test getOrCreateMember creates new member if none exists`() = suspendingDatabaseTest {
        setup()

        val member = installationBotAccountManager.getOrCreateMember(entity = org.idValue)
        val installation = suspendedTransaction {
            Stores.installationStore.findById(installationId = member.installationId)
        }

        assertThat(installation).isNotNull()
        assertThat(installation?.orgId).isEqualTo(org.idValue)
        assertThat(installation?.provider).isEqualTo(UnblockedInstallationMaintenance.UNBLOCKED_PROVIDER)
        assertThat(installation?.installationExternalId).isEqualTo(UnblockedInstallationMaintenance.UNBLOCKED_INSTALLATION_EXTERNAL_ID)

        val identity = suspendedTransaction {
            Stores.identityStore.findById(identityId = member.identityId)
        }

        assertThat(identity?.isBot).isTrue()
        assertThat(identity?.externalTeamId).isEqualTo(UnblockedInstallationMaintenance.UNBLOCKED_INSTALLATION_EXTERNAL_ID)

        val orgMember = suspendedTransaction {
            Stores.orgMemberStore.findById(id = member.orgMemberId)
        }

        assertThat(orgMember).isNotNull()
        assertThat(orgMember?.orgId).isEqualTo(org.idValue)
        assertThat(orgMember?.personId).isNull()
    }

    @Test
    fun `test getOrCreateMember returns existing member`() = suspendingDatabaseTest {
        setup()
        val member = installationBotAccountManager.getOrCreateMember(entity = org.idValue)
        val existingMember = installationBotAccountManager.getOrCreateMember(entity = org.idValue)

        assertThat(member.id).isEqualTo(existingMember.id)
        assertThat(member.installationId).isEqualTo(existingMember.installationId)
        assertThat(member.orgMemberId).isEqualTo(existingMember.orgMemberId)
        assertThat(member.identityId).isEqualTo(existingMember.identityId)
    }

    @Test
    fun `test upsertIdentities creates identities`() = suspendingDatabaseTest {
        IdentityDAO.findById(installationBotAccountManager.lookupBotIdentityId(item = UnblockedInstallationMaintenance.UNBLOCKED_PROVIDER)).also {
            assertThat(it).isNull()
        }

        installationBotAccountManager.upsertIdentities()

        IdentityDAO.findById(installationBotAccountManager.lookupBotIdentityId(item = UnblockedInstallationMaintenance.UNBLOCKED_PROVIDER)).also {
            assertThat(it).isNotNull
            assertThat(it?.displayName).isEqualTo("Unblocked")
            assertThat(it?.username).isEqualTo("Unblocked")
            assertThat(it?.avatarUrl).endsWith("/public/bot-avatar.svg")
            assertThat(it?.htmlUrl).isEqualTo("http://localhost:9000")
            assertThat(it?.isBot).isTrue
        }
    }

    @Test
    fun `test getMember returns correct member`() = suspendingDatabaseTest {
        setup()
        val otherOrg = ModelBuilders.makeOrg()

        installationBotAccountManager.getOrCreateMember(entity = org.idValue)
        installationBotAccountManager.getOrCreateMember(entity = otherOrg.idValue)

        val memberId = checkNotNull(installationBotAccountManager.getMemberId(entityId = org.idValue))
        val otherMemberId = checkNotNull(installationBotAccountManager.getMemberId(entityId = otherOrg.idValue))

        assertThat(memberId).isNotEqualTo(otherMemberId)

        suspendedTransaction {
            val member = checkNotNull(Stores.memberStore.findById(memberId = memberId))
            assertThat(member.orgMember.org.idValue).isEqualTo(org.idValue)
            assertThat(member.installation.org.idValue).isEqualTo(org.idValue)
        }

        suspendedTransaction {
            val member = checkNotNull(Stores.memberStore.findById(memberId = otherMemberId))
            assertThat(member.orgMember.org.idValue).isEqualTo(otherOrg.idValue)
            assertThat(member.installation.org.idValue).isEqualTo(otherOrg.idValue)
        }
    }
}
