package com.nextchaptersoftware.userengagement

import kotlinx.datetime.LocalDate

data class ActivationReport(
    val reportingDate: LocalDate,
    val signups: Int,
    val earlyActivations: Int,
    val activations: Int,
) {
    companion object {
        const val PERCENT = 100
    }

    val percentEarlyActivations
        get() = when {
            signups == 0 -> 0.0
            else -> earlyActivations.toDouble() / signups * PERCENT
        }

    val percentActivations
        get() = when {
            signups == 0 -> 0.0
            else -> activations.toDouble() / signups * PERCENT
        }
}
