package com.nextchaptersoftware.summarization.events.queue.payloads

import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.ThreadId
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class SummarizationEvent {
    @SerialName("GenerateThreadSummary")
    @Serializable
    data class GenerateThreadSummary(
        @Contextual
        val threadId: ThreadId,
    ) : SummarizationEvent()

    @SerialName("GenerateSlackThreadSummary")
    @Serializable
    data class GenerateSlackThreadSummary(
        val slackChannelId: SlackChannelId,
        val slackMessagesJson: String,
    ) : SummarizationEvent()
}
