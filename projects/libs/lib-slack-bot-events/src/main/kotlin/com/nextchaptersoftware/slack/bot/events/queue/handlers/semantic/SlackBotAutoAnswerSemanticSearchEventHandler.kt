package com.nextchaptersoftware.slack.bot.events.queue.handlers.semantic

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.bot.events.queue.handlers.exceptions.SlackBotSemanticSearchEventExceptionHandler
import com.nextchaptersoftware.slack.bot.events.queue.payloads.SlackBotSemanticSearchEvent
import com.nextchaptersoftware.slack.bot.models.semantic.SlackBotInputContext
import com.nextchaptersoftware.slack.bot.services.semantic.SlackBotAutoAnswerSemanticSearchService

class SlackBotAutoAnswerSemanticSearchEventHandler(
    private val slackBotAutoAnswerSemanticSearchService: SlackBotAutoAnswerSemanticSearchService,
    private val slackBotEventEnqueueService: SlackBotEventEnqueueService,
) : TypedEventHandler<SlackBotSemanticSearchEvent.SlackBotAutoAnswerSemanticSearchEvent> {
    override suspend fun handle(event: SlackBotSemanticSearchEvent.SlackBotAutoAnswerSemanticSearchEvent): Boolean {
        SlackBotSemanticSearchEventExceptionHandler.handleWithRetry(
            event = event,
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        ) {
            slackBotAutoAnswerSemanticSearchService.process(
                input = SlackBotInputContext(
                    slackUserId = event.slackUserId,
                    slackExternalTeamId = event.slackExternalTeamId,
                    slackExternalChannelId = event.slackExternalChannelId,
                    slackThreadTs = event.slackThreadTs,
                    slackTs = event.slackTs,
                    text = event.text,
                    pendingResponseTs = event.pendingResponseTs,
                    attemptCount = event.attemptCount,
                ),
            )
        }
        return true
    }
}
