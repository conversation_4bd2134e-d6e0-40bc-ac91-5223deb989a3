package com.nextchaptersoftware.slack.bot.events.queue.handlers.ephemeral

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.payloads.SlackBotClearEphemeralMessageEvent
import com.nextchaptersoftware.slack.services.SlackActionResponseService

class SlackBotClearEphemeralMessageEventHandler(
    private val slackActionResponseService: SlackActionResponseService,
) : TypedEventHandler<SlackBotClearEphemeralMessageEvent> {
    override suspend fun handle(event: SlackBotClearEphemeralMessageEvent): Boolean {
        slackActionResponseService.deleteEphemeral(responseUrl = event.responseUrl)
        return true
    }
}
