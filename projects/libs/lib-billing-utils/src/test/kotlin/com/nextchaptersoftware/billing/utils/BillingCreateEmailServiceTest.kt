package com.nextchaptersoftware.billing.utils

import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.ModelBuilders.makePlan
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgBillingEmailDAO
import com.nextchaptersoftware.db.models.OrgBillingEmailModel
import com.nextchaptersoftware.db.models.OrgBillingEmailType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BillingCreateEmailServiceTest : DatabaseTestsBase() {
    private val service = BillingCreateEmailService()

    @Test
    fun createEmails() = suspendingDatabaseTest {
        val now = Instant.nowWithMicrosecondPrecision()
        val fullTrial = now.plus(21.days)
        val partialTrial = now.plus(10.days)
        val trialPlan = makePlan(isTrialPlan = true)
        val notTrialPlan = makePlan()

        val orgA = makeOrg(createdAt = now)
        val orgB = makeOrg(createdAt = now.minus(11.days))
        val orgC = makeOrg(createdAt = now.minus(30.days))
        val orgBillingA = makeOrgBilling(org = orgA, plan = trialPlan, trialEnd = fullTrial).asDataModel()
        val orgBillingB = makeOrgBilling(org = orgB, plan = trialPlan, trialEnd = partialTrial).asDataModel()
        val orgBillingC = makeOrgBilling(org = orgC, plan = notTrialPlan, trialEnd = fullTrial).asDataModel()

        service.createEmails(orgBillingA.orgId)
        service.createEmails(orgBillingB.orgId)
        service.createEmails(orgBillingC.orgId)

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingA.id }.map { it.asDataModel() }
        }.also { results ->
            assertThat(results.size).isEqualTo(8)
            assertThat(results.map { it.type to it.send }).containsExactlyInAnyOrder(
                OrgBillingEmailType.TrialPitch to fullTrial.minus(16.days),
                OrgBillingEmailType.TrialTwoWeeksRemaining to fullTrial.minus(14.days),
                OrgBillingEmailType.TrialOneWeekRemaining to fullTrial.minus(7.days),
                OrgBillingEmailType.TrialThreeDaysRemaining to fullTrial.minus(3.days),
                OrgBillingEmailType.TrialOneDayRemaining to fullTrial.minus(1.days),
                OrgBillingEmailType.TrialSurvey to fullTrial.plus(3.days),
                OrgBillingEmailType.ConnectIntegrationsReminder to orgA.createdAt.plus(1.days),
                OrgBillingEmailType.InviteTeamReminder to orgA.createdAt.plus(5.days),
            )
        }

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingB.id }.map { it.asDataModel() }
        }.also { results ->
            assertThat(results.size).isEqualTo(4)
            assertThat(results.map { it.type to it.send }).containsExactlyInAnyOrder(
                OrgBillingEmailType.TrialOneWeekRemaining to partialTrial.minus(7.days),
                OrgBillingEmailType.TrialThreeDaysRemaining to partialTrial.minus(3.days),
                OrgBillingEmailType.TrialOneDayRemaining to partialTrial.minus(1.days),
                OrgBillingEmailType.TrialSurvey to partialTrial.plus(3.days),
            )
        }

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingC.id }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(0)
        }
    }
}
