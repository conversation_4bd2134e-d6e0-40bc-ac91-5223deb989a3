package com.nextchaptersoftware.billing.utils

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.ModelBuilders.makeOrgBillingEmail
import com.nextchaptersoftware.db.ModelBuilders.makePlan
import com.nextchaptersoftware.db.ModelBuilders.makeTeamInvitee
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgBillingEmailDAO
import com.nextchaptersoftware.db.models.OrgBillingEmailModel
import com.nextchaptersoftware.db.models.OrgBillingEmailState
import com.nextchaptersoftware.db.models.OrgBillingEmailType
import com.nextchaptersoftware.db.models.PlanTier
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class BillingSendEmailServiceTest : DatabaseTestsBase() {

    private val notificationEventEnqueueService = mock<NotificationEventEnqueueService>()

    private val orgBillingEmailSenderProvider by lazy {
        OrgBillingEmailSenderProvider(
            pitchEmailSender = PitchEmailSender(notificationEventEnqueueService),
            trialTwoWeeksSender = TrialTwoWeeksSender(notificationEventEnqueueService),
            trialOneWeekSender = TrialOneWeekSender(notificationEventEnqueueService),
            trialThreeDaysSender = TrialThreeDaysSender(notificationEventEnqueueService),
            trialOneDaySender = TrialOneDaySender(notificationEventEnqueueService),
            trialSurveySender = TrialSurveySender(notificationEventEnqueueService),
            connectIntegrationsReminderSender = ConnectIntegrationsReminderEmailSender(notificationEventEnqueueService),
            inviteTeamReminderEmailSender = InviteTeamReminderEmailSender(notificationEventEnqueueService),
        )
    }

    private val service = BillingSendEmailService(
        orgBillingEmailSenderProvider = orgBillingEmailSenderProvider,
    )

    @Test
    fun `sendEmails -- not sent`() = suspendingDatabaseTest {
        val orgBillingEmail = makeOrgBillingEmail(
            type = OrgBillingEmailType.TrialPitch,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.NotSent) // Org is not on trial
        }
    }

    @Test
    fun `sendEmails -- sent`() = suspendingDatabaseTest {
        val orgBilling = makeOrgBilling(
            plan = makePlan(isTrialPlan = true),
            trialEnd = Instant.nowWithMicrosecondPrecision().plus(1.days),
        )

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.TrialPitch,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.Sent) // Org is not on trial
        }
    }

    @Test
    fun `sendEmails -- TrialSurvey on business`() = suspendingDatabaseTest {
        val orgBilling = makeOrgBilling(
            plan = makePlan(tier = PlanTier.Business),
            trialEnd = Instant.nowWithMicrosecondPrecision().plus(1.days),
        )

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.TrialSurvey,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.NotSent) // Org is not free or trial
        }
    }

    @Test
    fun `sendEmails -- TrialSurvey on starter`() = suspendingDatabaseTest {
        val orgBilling = makeOrgBilling(
            plan = makePlan(tier = PlanTier.Starter),
            trialEnd = Instant.nowWithMicrosecondPrecision().plus(1.days),
        )

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.TrialSurvey,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.Sent) // Org is on starter
        }
    }

    @Test
    fun `sendEmails -- ConnectIntegrationsReminder`() = suspendingDatabaseTest {
        val orgBilling = makeOrgBilling()

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.ConnectIntegrationsReminder,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.Sent)
        }
    }

    @Test
    fun `sendEmails -- ConnectIntegrationsReminder where org has installations`() = suspendingDatabaseTest {
        val org = makeOrg()
        val orgBilling = makeOrgBilling(org = org)
        makeInstallation(org = org, provider = Provider.Slack)

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.ConnectIntegrationsReminder,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.NotSent)
        }
    }

    @Test
    fun `sendEmails -- InviteTeamReminder`() = suspendingDatabaseTest {
        val orgBilling = makeOrgBilling()

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.InviteTeamReminder,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.Sent)
        }
    }

    @Test
    fun `sendEmails -- InviteTeamReminder with existing invites`() = suspendingDatabaseTest {
        val org = makeOrg()
        val orgBilling = makeOrgBilling(org = org)
        makeTeamInvitee(org = org)

        val orgBillingEmail = makeOrgBillingEmail(
            orgBilling = orgBilling,
            type = OrgBillingEmailType.InviteTeamReminder,
            send = Instant.nowWithMicrosecondPrecision().minus(1.days),
            expire = Instant.nowWithMicrosecondPrecision().plus(1.days),
        ).asDataModel()

        service.sendEmails()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingEmail.orgBillingId }.map { it.asDataModel() }
        }.also {
            assertThat(it.size).isEqualTo(1)
            assertThat(it.first().state).isEqualTo(OrgBillingEmailState.NotSent)
        }
    }
}
