package com.nextchaptersoftware.billing.utils

import com.nextchaptersoftware.db.models.OrgBillingId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanTier
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.PlanStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant

class BillingPlanService(
    private val orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    private val planStore: PlanStore = Stores.planStore,
) {
    suspend fun isTrialExpired(orgId: OrgId): Boolean {
        return orgBillingStore.isTrialExpired(orgId = orgId)
    }

    suspend fun isOnTrial(orgBillingId: OrgBillingId): Boolean {
        val orgBilling = orgBillingStore.find(id = orgBillingId) ?: return false
        val plan = planStore.find(planId = orgBilling.planId)
        return plan.isTrialPlan && Instant.nowWithMicrosecondPrecision() < orgBilling.trialEnd
    }

    suspend fun isStarterOrTrialExpired(orgBillingId: OrgBillingId): Boolean {
        val orgBilling = orgBillingStore.find(id = orgBillingId) ?: error("OrgBilling not found")
        val plan = planStore.find(planId = orgBilling.planId)
        return plan.tier == PlanTier.Starter || orgBillingStore.isTrialExpired(orgId = orgBilling.orgId)
    }
}
