package com.nextchaptersoftware.slack.bot.services.notification

import com.nextchaptersoftware.bot.toolbox.IsSlackPrivateScopesDisabledFilter
import com.nextchaptersoftware.bot.toolbox.ShortCircuitBotContext
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.slack.bot.invites.SlackAccountInviteUrlBuilder
import com.nextchaptersoftware.slack.bot.models.notification.SlackBotMemberNotificationContext
import com.nextchaptersoftware.slack.bot.models.notification.SlackBotMemberNotificationInputContext
import com.nextchaptersoftware.slack.bot.models.payload.SlackBotMemberJoinedChannelPayload
import com.nextchaptersoftware.slack.bot.models.payload.SlackBotPayload
import com.nextchaptersoftware.slack.bot.models.payload.toAttachmentPayload
import com.nextchaptersoftware.slack.bot.models.payload.toBlockPayload
import com.nextchaptersoftware.slack.bot.services.settings.SlackBotDisclaimerSettingsService
import com.nextchaptersoftware.slack.services.SlackDeepLinkService
import com.nextchaptersoftware.slack.services.SlackTokenService
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackBotMemberJoinedPrivateScopesDisabledDisclaimerNotificationService(
    private val slackBotDisclaimerSettingsService: SlackBotDisclaimerSettingsService,
    private val slackTokenService: SlackTokenService,
    private val slackApiProvider: SlackApiProvider,
    private val isSlackPrivateScopesDisabledFilter: IsSlackPrivateScopesDisabledFilter,
    private val urlBuilderProvider: UrlBuilderProvider,
    identityStore: IdentityStore = Stores.identityStore,
    slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    slackAccountInviteUrlBuilder: SlackAccountInviteUrlBuilder,
    slackDeepLinkService: SlackDeepLinkService,
) : BaseSlackBotMemberNotificationService<SlackBotMemberNotificationInputContext.MemberJoinedDisclaimerInputContext>(
    slackBotMemberContextResolver = SlackBotMemberContextResolver(
        identityStore = identityStore,
        slackTeamStore = slackTeamStore,
        scmTeamStore = scmTeamStore,
        slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
        slackDeepLinkService = slackDeepLinkService,
    ),
) {
    override suspend fun notify(input: SlackBotMemberNotificationInputContext.MemberJoinedDisclaimerInputContext): Boolean = withLoggingContextAsync(
        "slackExternalTeamId" to input.slackExternalTeamId,
        "slackExternalChannelId" to input.slackExternalChannelId,
        "slackUserId" to input.slackUserId,
    ) {
        val slackBotMemberNotificationContext = getSlackBotMemberNotificationContext(input = input)
            ?: run {
                LOGGER.errorAsync { "Failed to get slack bot member notification context" }
                return@withLoggingContextAsync false
            }

        if (!slackBotDisclaimerSettingsService.shouldShowDisclaimer(slackExternalTeamId = input.slackExternalTeamId)) {
            LOGGER.debugAsync { "Skipping slack bot disclaimer due to slackBotDisclaimer being disabled" }
            return@withLoggingContextAsync false
        }

        // Filter checks slack channel information (if private or IM) as well
        if (!isSlackPrivateScopesDisabledFilter.filter(
                context = ShortCircuitBotContext.SlackShortCircuitBotContext(
                    orgId = slackBotMemberNotificationContext.slackChannelInfo.org.idValue,
                    humanQuery = "",
                    slackChannel = slackBotMemberNotificationContext.slackChannelInfo.slackChannel.asDataModel(),
                    slackTeam = slackBotMemberNotificationContext.slackChannelInfo.slackTeam.asDataModel(),
                ),
            )
        ) {
            LOGGER.debugAsync { "Skipping generate private scopes slack bot disclaimer due to private scopes being enabled" }
            return@withLoggingContextAsync false
        }

        val payload = createSlackBotPayload(
            slackBotMemberNotificationContext = slackBotMemberNotificationContext,
        )

        if (slackBotMemberNotificationContext.slackChannelInfo.slackTeam.botUserId == input.slackUserId) {
            executeSlackApiCall {
                slackApiProvider.slackChatApi.postChatMessage(
                    token = slackTokenService.getSlackToken(
                        slackTeamId = slackBotMemberNotificationContext.slackChannelInfo.slackTeam.idValue,
                        permissions = listOf(SlackPermission.CHAT_WRITE),
                    ).value,
                    channelId = input.slackExternalChannelId,
                    payload = payload.blocks.toBlockPayload(),
                )
            }
        } else {
            executeSlackApiCall {
                slackApiProvider.slackChatApi.postEphemeral(
                    token = slackTokenService.getSlackToken(
                        slackTeamId = slackBotMemberNotificationContext.slackChannelInfo.slackTeam.idValue,
                        permissions = listOf(SlackPermission.CHAT_WRITE),
                    ).value,
                    channelId = input.slackExternalChannelId,
                    userId = input.slackUserId,
                    payload = payload.attachments?.toAttachmentPayload() ?: payload.blocks.toBlockPayload(),
                )
            }
        }

        return@withLoggingContextAsync true
    }

    private fun createSlackBotPayload(
        slackBotMemberNotificationContext: SlackBotMemberNotificationContext,
    ): SlackBotPayload {
        val upgradeUrl = urlBuilderProvider
            .dashboard()
            .withOrg(orgId = slackBotMemberNotificationContext.slackChannelInfo.org.idValue.value)
            .withPlans()
            .build()

        val contactSupportUrl = urlBuilderProvider
            .dashboard()
            .withOrg(orgId = slackBotMemberNotificationContext.slackChannelInfo.org.idValue.value)
            .withIntercom()
            .build()

        return SlackBotMemberJoinedChannelPayload.SlackBotMemberJoinedChannelPrivateScopesDisabledDisclaimerBlock(
            upgradePlan = "Business Plan", // TODO DLam fix this
            upgradeUrl = upgradeUrl,
            contactSupportUrl = contactSupportUrl,
        )
    }
}
