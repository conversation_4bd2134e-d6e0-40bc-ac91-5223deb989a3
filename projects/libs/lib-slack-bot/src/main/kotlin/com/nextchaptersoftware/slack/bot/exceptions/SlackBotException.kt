package com.nextchaptersoftware.slack.bot.exceptions

sealed class SlackBotException(
    override val message: String?,
    override val cause: Throwable?,
    open val pendingResponseTs: String?,
) : Exception(message, cause) {
    class SlackBotResponseException(
        override val message: String?,
        override val cause: Throwable?,
        override val pendingResponseTs: String?,
    ) : SlackBotException(message = message, cause = cause, pendingResponseTs = pendingResponseTs)
}
