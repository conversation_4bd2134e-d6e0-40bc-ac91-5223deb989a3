package com.nextchaptersoftware.slack.bot.models.semantic

import com.nextchaptersoftware.db.models.FeedbackType

enum class SemanticFeedbackType(
    val text: String,
    val underlyingFeedbackType: FeedbackType,
) {
    Positive(text = "Great", underlyingFeedbackType = FeedbackType.Positive),
    Neutral(text = "Okay", underlyingFeedbackType = FeedbackType.Neutral),
    Negative(text = "Wrong", underlyingFeedbackType = FeedbackType.Negative),
    NotHelpful(text = "Not Helpful", underlyingFeedbackType = FeedbackType.Negative),
    Helpful(text = "Helpful", underlyingFeedbackType = FeedbackType.Positive),
}
