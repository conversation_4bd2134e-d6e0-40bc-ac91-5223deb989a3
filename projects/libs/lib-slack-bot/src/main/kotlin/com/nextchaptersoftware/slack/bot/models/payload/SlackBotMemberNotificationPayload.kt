package com.nextchaptersoftware.slack.bot.models.payload

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.slack.bot.models.SlackBotFeedbackSourceType
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotBlockAction
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotBlockActionValue
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotProductFeedbackButton
import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotSemanticSearchLinkButton
import com.nextchaptersoftware.slack.bot.models.color.SlackColor
import com.nextchaptersoftware.slack.notify.models.block.SlackBlockText
import com.nextchaptersoftware.slack.notify.models.block.asSlackBlockText
import com.nextchaptersoftware.slack.services.SlackUserConnectPromptCount
import com.nextchaptersoftware.utils.joinWithAnd
import com.slack.api.model.Attachment
import com.slack.api.model.block.LayoutBlock
import com.slack.api.model.kotlin_extension.block.withBlocks
import io.ktor.http.Url

sealed class SlackBotMemberNotificationPayload : SlackBotPayload() {

    companion object {
        const val SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID = "semantic_search_notification_buttons"
        const val AT_MENTION_CONNECTED_ACCOUNT_INGESTION_MESSAGE_BLOCK_ID = "at_mention_connected_account_ingestion_message"
        const val AT_MENTION_NOT_CONNECTED_ACCOUNT_INGESTION_MESSAGE_BLOCK_ID = "at_mention_not_connected_account_ingestion_message"
        const val AT_MENTION_TRIAL_EXPIRED_BLOCK_ID = "at_mention_trial_expired"
        const val AT_MENTION_QUERY_SIZE_OVER_LIMIT_BLOCK_ID = "at_mention_query_size_over_limit"
        const val AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID = "at_mention_connect_unblocked_account"
        const val AT_MENTION_PRODUCT_FEEDBACK_BLOCK_ID = "at_mention_product_feedback"
        const val AT_MENTION_PRODUCT_FEEDBACK_PUBLIC_BLOCK_ID = "at_mention_product_feedback_public"
        const val AT_MENTION_PRODUCT_FEEDBACK_BUTTONS_BLOCK_ID = "at_mention_product_feedback_buttons"
        const val AUTO_ANSWER_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID = "auto_answer_connect_unblocked_account"
        const val AUTO_ANSWER_FORCE_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID = "auto_answer_force_connect_unblocked_account"
        const val AUTO_ANSWER_INFORMATION_BLOCK_ID = "auto_answer_information"
        const val AUTO_ANSWER_TICKLE_RESPONSE_BLOCK_ID = "auto_answer_tickle_response_block_id"
    }

    @Suppress("MaxLineLength")
    data class SlackBotIngestionPendingConnectedAccountMessageBlock(
        val teamDisplayName: String,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                Unblocked can’t answer your question yet because it is still processing your team's repositories and data sources.

                *Note*: This can take a bit of time if you’re connecting large amounts of data, but you will receive an email when everything is complete.
            """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECTED_ACCOUNT_INGESTION_MESSAGE_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    data class SlackBotTickleResponseMessageBlock(
        val text: String = """
                Yes I'm here and actively listening. You can also mention @Unblocked in your message to get my attention.
            """.trimIndent(),
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            text,
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AUTO_ANSWER_TICKLE_RESPONSE_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotIngestionPendingNotConnectedAccountMessageBlock(
        val teamDisplayName: String,
        val connectAccountUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Unblocked can’t answer your question yet because it is still processing your team's repositories and data sources.

            Now is a good time to *<$connectAccountUrl|link your Slack account>* so Unblocked doesn’t miss any important information used to generate accurate responses for you.
             """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_NOT_CONNECTED_ACCOUNT_INGESTION_MESSAGE_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    data class SlackBotAtMentionTrialExpiredBlock(
        val upgradePlan: String,
        val upgradeUrl: Url,
        val contactSupportUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                Your team's free trial of Unblocked has expired. To keep using Unblocked in Slack, you need to <$upgradeUrl|upgrade to a $upgradePlan>.
            """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_TRIAL_EXPIRED_BLOCK_ID)
                    markdownText(message.text)
                }
                divider()
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchChoosePlanButton(
                            url = upgradeUrl,
                        ).let { choosePlanButton ->
                            button {
                                text(choosePlanButton.buttonText.text)
                                style(choosePlanButton.style)
                                actionId(choosePlanButton.actionId)
                                value(choosePlanButton.buttonValue.text)
                                url(choosePlanButton.url.asString)
                            }
                        }
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchContactSupportButton(
                            url = contactSupportUrl,
                        ).let { contactSupportButton ->
                            button {
                                text(contactSupportButton.buttonText.text)
                                actionId(contactSupportButton.actionId)
                                value(contactSupportButton.buttonValue.text)
                                url(contactSupportButton.url.asString)
                            }
                        }
                    }
                }
            }
        }
    }

    data class SlackBotAtMentionPrivateScopesDisabledBlock(
        val upgradePlan: String,
        val upgradeUrl: Url,
        val contactSupportUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                Your team's plan does not support private channels or DMs. To use these features, you need to <$upgradeUrl|upgrade to a $upgradePlan>.
            """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_TRIAL_EXPIRED_BLOCK_ID)
                    markdownText(message.text)
                }
                divider()
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchChoosePlanButton(
                            url = upgradeUrl,
                        ).let { choosePlanButton ->
                            button {
                                text(choosePlanButton.buttonText.text)
                                style(choosePlanButton.style)
                                actionId(choosePlanButton.actionId)
                                value(choosePlanButton.buttonValue.text)
                                url(choosePlanButton.url.asString)
                            }
                        }
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchContactSupportButton(
                            url = contactSupportUrl,
                        ).let { contactSupportButton ->
                            button {
                                text(contactSupportButton.buttonText.text)
                                actionId(contactSupportButton.actionId)
                                value(contactSupportButton.buttonValue.text)
                                url(contactSupportButton.url.asString)
                            }
                        }
                    }
                }
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotQuerySizeOverLimitMessageBlock(
        val contactSupportUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                The question you submitted was too long. Please try again with a shorter question.

                If this is unexpected, please <$contactSupportUrl|contact our team> - we're happy to help.
            """.trimIndent(),
        ),
        val attachmentColor: SlackColor = SlackColor.YELLOW,
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_QUERY_SIZE_OVER_LIMIT_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    data class SlackBotPromptAtMentionDsacLinkAccountsBlock(
        val linkAccountsUrl: Url,
        val unconnectedProviderNames: List<String>,
        val slackBotQuestionButtonValue: SlackBotBlockActionValue.SlackBotQuestionButtonValue,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            🚨 *Action Required:* Unblocked is missing important information from ${
                unconnectedProviderNames.joinWithAnd(
                    itemTransformer = { "*$it*" },
                )
            } used to generate accurate answers.

            <$linkAccountsUrl|Linking and confirming your accounts> only takes a few seconds and will improve the quality of the responses that Unblocked generates.
            """.trimIndent(),
        ),
        val attachmentColor: SlackColor = SlackColor.DANGER,
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchLinkAccountsButton(
                            url = linkAccountsUrl,
                            buttonValue = slackBotQuestionButtonValue.encode().asSlackBlockText(),
                        ).let { linksAccountsButton ->
                            button {
                                text(linksAccountsButton.buttonText.text)
                                actionId(linksAccountsButton.actionId)
                                value(linksAccountsButton.buttonValue.text)
                                style(linksAccountsButton.style)
                                url(linksAccountsButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionForceConnectWarningUnblockedAccountBlock(
        val slackUserId: String,
        val dashboardUrl: Url,
        val connectAccountUrl: Url,
        val slackUserConnectPromptCount: SlackUserConnectPromptCount,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
    ) : SlackBotMemberNotificationPayload() {
        val message: SlackBlockText by lazy {
            if (slackUserConnectPromptCount.hasHitPenultimateLimit) {
                SlackBlockText.TextBlockText(
                    """
                    Hi <@$slackUserId>. You’ll find the answer to your question above. Moving forward, you’ll need to <$connectAccountUrl|link your Slack account> before Unblocked can answer your questions.

                    Linking only takes a few seconds and makes sure Unblocked doesn’t miss any important information used to generate accurate responses for you.
                """.trimIndent(),
                )
            } else {
                val remainingQuestions = slackUserConnectPromptCount.inclusiveRemainingCount
                val questionText = if (remainingQuestions == 1) "question" else "questions"

                SlackBlockText.TextBlockText(
                    """
                    Hi <@$slackUserId>. You’ll find the answer to your question above. You can ask $remainingQuestions more $questionText before you'll need to <$connectAccountUrl|link your Slack account to Unblocked>.

                    Linking only takes a few seconds and makes sure Unblocked doesn’t miss any important information used to generate accurate responses for you.
                """.trimIndent(),
                )
            }
        }

        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionForceConnectDsacWarningUnblockedAccountBlock(
        val slackUserId: String,
        val dashboardUrl: Url,
        val connectAccountUrl: Url,
        val slackUserConnectPromptCount: SlackUserConnectPromptCount,
        val protectedDataSourceNames: List<String>,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
    ) : SlackBotMemberNotificationPayload() {
        val message: SlackBlockText by lazy {
            if (slackUserConnectPromptCount.hasHitPenultimateLimit) {
                SlackBlockText.TextBlockText(
                    """
                    Hi <@$slackUserId>. Unblocked has answered your question, but it’s missing important information from ${
                        protectedDataSourceNames.joinWithAnd(
                            itemTransformer = { "*$it*" },
                        )
                    }.

                    This is your last question before you'll need to <$connectAccountUrl|link your Slack account to Unblocked>.

                    Linking only takes a few seconds and improves Unblocked’s response quality.
                """.trimIndent(),
                )
            } else {
                val remainingQuestions = slackUserConnectPromptCount.inclusiveRemainingCount
                val questionText = if (remainingQuestions == 1) "question" else "questions"

                SlackBlockText.TextBlockText(
                    """
                    Hi <@$slackUserId>. Unblocked has answered your question, but it’s missing important information from ${
                        protectedDataSourceNames.joinWithAnd(
                            itemTransformer = { "*$it*" },
                        )
                    }.

                    You can ask $remainingQuestions more $questionText before you'll need to <$connectAccountUrl|link your Slack account to Unblocked>.

                    Linking only takes a few seconds and improves Unblocked’s response quality.
                """.trimIndent(),
                )
            }
        }

        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionForceConnectUnblockedAccountBlock(
        val slackUserId: String,
        val slackBotQuestionButtonValue: SlackBotBlockActionValue.SlackBotQuestionButtonValue,
        val connectAccountUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Hi <@$slackUserId>. To continue asking Unblocked questions in Slack, you’ll need to <$connectAccountUrl|link your Slack account to Unblocked>.

            Linking your Slack account makes sure Unblocked doesn’t miss any important information used to generate accurate responses for you.
        """.trimIndent(),
        ),
        val attachmentColor: SlackColor = SlackColor.DANGER,
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            buttonValue = slackBotQuestionButtonValue.encode().asSlackBlockText(),
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionPublicForceConnectUnblockedAccountBlock(
        val slackUserId: String,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Unblocked is waiting for <@$slackUserId> to take action before answering this question.
        """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptIMAtMentionPublicForceConnectUnblockedAccountBlock(
        val slackUserId: String,
        val dashboardUrl: Url,
        val connectAccountUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            You'll need you to <$connectAccountUrl|link your Slack account to Unblocked> before Unblocked can answer this question - this takes a few seconds and makes sure Unblocked doesn’t miss any important information used to generate accurate responses for you.
        """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionConnectUnblockedAccountBlock(
        val slackUserId: String,
        val connectingUserName: String,
        val slackChannelName: String,
        val slackAppHomeUrl: Url,
        val dashboardUrl: Url,
        val connectAccountUrl: Url,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Hello <@$slackUserId>! ${
                connectingUserName.split(" ").firstOrNull() ?: connectingUserName
            } added Unblocked to Slack to answer your team's questions. Simply at-mention *@Unblocked* in #$slackChannelName or *<$slackAppHomeUrl|DM the Unblocked bot>*.

            You will need to *<$connectAccountUrl|link your Slack account>* so Unblocked doesn’t miss any important information used to generate accurate responses for you.
        """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAutoAnswerConnectUnblockedAccountBlock(
        val slackUserId: String,
        val connectingUserName: String,
        val slackChannelName: String,
        val slackAppHomeUrl: Url,
        val dashboardUrl: Url,
        val connectAccountUrl: Url,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Hello <@$slackUserId>! ${
                connectingUserName.split(" ").firstOrNull() ?: connectingUserName
            } added <$dashboardUrl|Unblocked> to #$slackChannelName to answer your team's questions. Simply at-mention *@Unblocked* in #$slackChannelName or *<$slackAppHomeUrl|DM the Unblocked Bot>*.

            Unblocked can provide higher quality, tailored answers, based on your identity and accessible data sources when you *<$connectAccountUrl|link your Slack account to Unblocked>*.
        """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AUTO_ANSWER_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAutoAnswerInformationBlock(
        val clientDownloadUrl: Url,
        val answerUrl: String,
        val intercomUrl: String,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                Apologies for the interruption - we wanted to give you more context on why Unblocked just <$answerUrl|answered your question>.

                Unblocked’s goal is to provide helpful answers, including right here in Slack.

                Please let us know what you think about the answer above by choosing "Helpful" or "Not Helpful" in the response we provided.

                Feel free to <$intercomUrl|contact us> with any questions!
            """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AUTO_ANSWER_INFORMATION_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotAutoAnswerNeedsAccountBlock(
        val slackUserId: String,
        val slackBotQuestionButtonValue: SlackBotBlockActionValue.SlackBotQuestionButtonValue,
        val connectAccountUrl: Url,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
                Hi <@$slackUserId>, Unblocked has an answer to your question. Your team's security settings require that you <$connectAccountUrl|link your Slack account> to view it.
            """.trimIndent(),
        ),
        val attachmentColor: SlackColor = SlackColor.YELLOW,
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AUTO_ANSWER_FORCE_CONNECT_UNBLOCKED_ACCOUNT_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(SEMANTIC_SEARCH_NOTIFICATION_BUTTONS_BLOCK_ID)
                    elements {
                        SlackBotSemanticSearchLinkButton.SlackBotSemanticSearchCreateAccountButton(
                            buttonValue = slackBotQuestionButtonValue.encode().asSlackBlockText(),
                            url = connectAccountUrl,
                        ).let { connectAccountButton ->
                            button {
                                text(connectAccountButton.buttonText.text)
                                actionId(connectAccountButton.actionId)
                                value(connectAccountButton.buttonValue.text)
                                style(connectAccountButton.style)
                                url(connectAccountButton.url.asString)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionProductFeedbackPublicBlock(
        val slackUserId: String,
        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Unblocked is waiting for <@$slackUserId> to take action before answering this question.
        """.trimIndent(),
        ),
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(AT_MENTION_PRODUCT_FEEDBACK_PUBLIC_BLOCK_ID)
                    markdownText(message.text)
                }
            }
        }
    }

    @Suppress("MaxLineLength")
    data class SlackBotPromptAtMentionProductFeedbackBlock(
        val slackUserId: String,

        val message: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Hi <@$slackUserId>. Thank you for evaluating Unblocked.

            Before we answer your question, your team has requested this quick survey.

            Please let us know if you find Unblocked helpful!
        """.trimIndent(),
        ),
        val slackBotQuestionButtonValue: SlackBotBlockActionValue.SlackBotQuestionButtonValue,
        val feedbackSourceType: SlackBotFeedbackSourceType,
        val attachmentColor: SlackColor = SlackColor.YELLOW,
    ) : SlackBotMemberNotificationPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            val feedbackButtons: List<SlackBotBlockAction.SlackBotButton> = SlackBotProductFeedbackButton.defaultButtons(
                buttonValue = slackBotQuestionButtonValue.encode().asSlackBlockText(),
            )

            withBlocks {
                section {
                    blockId(AT_MENTION_PRODUCT_FEEDBACK_BLOCK_ID)
                    markdownText(message.text)
                }
                actions {
                    blockId(AT_MENTION_PRODUCT_FEEDBACK_BUTTONS_BLOCK_ID)
                    elements {
                        feedbackButtons.forEach { semanticSearchButton ->
                            button {
                                text(semanticSearchButton.buttonText.text)
                                style(semanticSearchButton.style)
                                actionId(semanticSearchButton.actionId)
                                value(semanticSearchButton.buttonValue.text)
                            }
                        }
                    }
                }
            }
        }

        override val attachments: List<Attachment> by lazy {
            buildList {
                add(Attachment.builder().color(attachmentColor.hexCode).blocks(blocks).build())
            }
        }
    }
}
