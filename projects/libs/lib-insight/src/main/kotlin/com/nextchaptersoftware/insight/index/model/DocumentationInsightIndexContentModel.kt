package com.nextchaptersoftware.insight.index.model

import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import java.util.UUID
import kotlinx.datetime.Instant

data class DocumentationInsightIndexContentModel(
    override val id: UUID,
    override val orgId: OrgId,
    override val installationId: InstallationId,
    override val groupId: UUID? = null,
    override val documentId: String,
    override val provider: Provider,
    override val insightType: InsightType = InsightType.Documentation,
    override val title: String,
    override val summary: String? = null,
    val createdAt: Instant?,
    val content: String,
    val supplementaryContent: String? = null,
    val slackChannelName: String? = null,
) : InsightIndexContentModel {

    override val contents by lazy {
        when (insightType) {
            InsightType.Slack -> {
                supplementaryContent?.let { listOf(it) } ?: listOf(content)
            }

            else -> {
                listOf(content)
            }
        }
    }

    override val contentsWithMetadata by lazy {
        listOf(
            """
            |Source: ${provider.displayName}
            |${slackChannelName?.let { "Slack channel: #$it" } ?: ""}
            |Title: $title
            |${createdAt?.let { "Created at: $it" } ?: ""}
            |Contents:
            |${contents.first()}
            """.trimMargin(),
        )
    }

    override val embeddableContents by lazy {
        contentsWithMetadata
    }
}
