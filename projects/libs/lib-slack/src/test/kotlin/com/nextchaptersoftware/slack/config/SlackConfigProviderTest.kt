package com.nextchaptersoftware.slack.config

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackConfigProviderTest : DatabaseTestsBase() {
    private val slackConfigProvider = SlackConfigProvider()
    private val store = Stores.slackSettingsStore

    private lateinit var org: OrgDAO

    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
    }

    @Test
    fun `test config retrieval`() = suspendingDatabaseTest {
        setup()
        slackConfigProvider.getSlackConfig(orgId = org.idValue)
            .let {
                assertThat(it).isEqualTo(GlobalConfig.INSTANCE.providers.slack)
            }

        store.upsert(
            orgId = org.idValue,
            slackUserAcceptanceTesting = true,
        )

        slackConfigProvider.getSlackConfig(orgId = org.idValue)
            .let {
                assertThat(it).isEqualTo(GlobalConfig.INSTANCE.providers.slackUAT)
            }
    }
}
