package com.nextchaptersoftware.slack.services

import com.nextchaptersoftware.auth.oauth.EncryptedOAuthTokens
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.user.secret.UserSecretServiceInterface
import com.sksamuel.hoplite.Secret
import kotlin.time.Duration.Companion.milliseconds
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.update
import org.junit.jupiter.api.Test

class SlackTokenServiceTest : DatabaseTestsBase() {
    private val userSecretService = object : UserSecretServiceInterface {
        override fun encrypt(secret: Secret): Ciphertext {
            return Ciphertext(secret.value.toByteArray())
        }

        override fun decrypt(ciphertext: Ciphertext): Secret {
            return Secret(String(ciphertext.value))
        }

        override suspend fun saveEncryptedIdentityTokens(trx: Transaction, identityId: IdentityId, newTokens: OAuthTokens) {
            TODO("Not yet implemented")
        }

        override suspend fun getDecryptedIdentityTokens(trx: Transaction?, identityId: IdentityId): OAuthTokens? {
            TODO("Not yet implemented")
        }

        override fun encryptTokens(tokens: OAuthTokens): EncryptedOAuthTokens {
            TODO("Not yet implemented")
        }

        override fun decryptTokens(tokens: EncryptedOAuthTokens): OAuthTokens? {
            TODO("Not yet implemented")
        }
    }

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var slackTeam: SlackTeamDAO
    private lateinit var installation: InstallationDAO
    private lateinit var identity1: IdentityDAO
    private lateinit var member1: MemberDAO
    private lateinit var identity2: IdentityDAO
    private lateinit var member2: MemberDAO

    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
        scmTeam = ModelBuilders.makeScmTeam(org = org)
        installation = ModelBuilders.makeInstallation(
            org = org,
            provider = Provider.Slack,
        )
        slackTeam = ModelBuilders.makeSlackTeam(
            org = org,
            installation = installation,
            slackExternalTeamId = installation.installationExternalId,
            botAccessToken = "bot".toByteArray(),
            botScope = "channels:history,channels:read",
            userAccessToken = "user".toByteArray(),
            userScope = "channels:read",
            botUserId = "BOT_USER_ID",
        )
        identity1 = ModelBuilders.makeIdentity(
            provider = Provider.Slack,
            externalTeamId = installation.installationExternalId,
            rawAccessToken = "identityUser1".toByteArray(),
            accessTokenScope = "groups:read",
            externalId = "IDENTITY_USER_1_ID",
        )
        member1 = ModelBuilders.makeMember(
            installation = installation,
            identity = identity1,
        )
        identity2 = ModelBuilders.makeIdentity(
            provider = Provider.Slack,
            externalTeamId = installation.installationExternalId,
            rawAccessToken = "identityUser2".toByteArray(),
            accessTokenScope = "groups:read",
            externalId = "IDENTITY_USER_2_ID",
        )
        member2 = ModelBuilders.makeMember(
            installation = installation,
            identity = identity2,
        )
    }

    @Test
    fun `test slack token retrieval`() = suspendingDatabaseTest {
        setup()

        val slackTokenService = SlackTokenService(
            userSecretService = userSecretService,
            cacheWriteTimeout = 5.milliseconds,
        )

        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = emptyList(),
            ),
        ).isNotNull
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.TEAM_READ),
            ),
        ).isNull()
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.CHANNELS_READ),
            ),
        ).isNotNull()
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.CHANNELS_HISTORY),
            ),
        ).isNotNull()
    }

    @Test
    fun `test slack token retrieval with deleted installation`() = suspendingDatabaseTest {
        setup()

        val slackTokenService = SlackTokenService(
            userSecretService = userSecretService,
            cacheWriteTimeout = 5.milliseconds,
        )

        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = emptyList(),
            ),
        ).isNotNull

        suspendedTransaction {
            InstallationModel.update({ InstallationModel.id eq installation.idValue }) {
                it[deletedInstallationExternalId] = installation.installationExternalId
            }
        }

        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = emptyList(),
            ),
        ).isNull()
    }

    @Test
    fun `test slack token prioritized retrieval`() = suspendingDatabaseTest {
        setup()

        val slackTokenService = SlackTokenService(
            userSecretService = userSecretService,
            cacheWriteTimeout = 5.milliseconds,
        )

        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.USER,
                permissions = listOf(SlackPermission.CHANNELS_READ),
            )?.value,
        ).isEqualTo(String(checkNotNull(slackTeam.userAccessToken)))
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.CHANNELS_HISTORY),
            )?.value,
        ).isEqualTo(String(checkNotNull(slackTeam.botAccessToken)))
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.USER,
                permissions = listOf(SlackPermission.CHANNELS_HISTORY),
            )?.value,
        ).isEqualTo(String(checkNotNull(slackTeam.botAccessToken)))
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.USER,
                permissions = listOf(SlackPermission.GROUPS_READ),
            )?.value,
        ).isEqualTo(String(checkNotNull(identity1.rawAccessToken)))
    }

    @Test
    fun `test slack token retrieval with slackUserIds prioritization`() = suspendingDatabaseTest {
        setup()

        val slackTokenService = SlackTokenService(
            userSecretService = userSecretService,
            cacheWriteTimeout = 5.milliseconds,
        )

        // Prioritize based on slackUserId matching the bot user ID
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.CHANNELS_READ),
                slackUserIds = listOf("BOT_USER_ID"),
            )?.value,
        ).isEqualTo(String(checkNotNull(slackTeam.botAccessToken)))

        // Prioritize based on slackUserId matching the identity user ID
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.USER,
                permissions = listOf(SlackPermission.GROUPS_READ),
                slackUserIds = listOf("IDENTITY_USER_1_ID"),
            )?.value,
        ).isEqualTo(String(checkNotNull(identity1.rawAccessToken)))

        // Prioritize based on slackUserId matching the identity user ID
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.USER,
                permissions = listOf(SlackPermission.GROUPS_READ),
                slackUserIds = listOf("IDENTITY_USER_2_ID"),
            )?.value,
        ).isEqualTo(String(checkNotNull(identity2.rawAccessToken)))

        // Prioritize the non-matching user ID; should fallback to prioritize parameter (BOT)
        assertThat(
            slackTokenService.getSlackTokenOrNull(
                slackTeamId = slackTeam.idValue,
                prioritize = SlackTokenType.BOT,
                permissions = listOf(SlackPermission.CHANNELS_HISTORY),
                slackUserIds = listOf("NON_EXISTENT_USER_ID"),
            )?.value,
        ).isEqualTo(String(checkNotNull(slackTeam.botAccessToken)))
    }
}
