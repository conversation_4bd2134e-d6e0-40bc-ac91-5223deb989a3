package com.nextchaptersoftware.slack.utils

import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.slack.utils.SlackTimestampUtils.fromTsWithMicroseconds
import io.ktor.http.URLBuilder
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.server.util.url

class SlackUrlBuilder(
    private val baseProtocol: URLProtocol = URLProtocol.HTTPS,
    private val baseHost: String,
    private val basePathSegments: List<String> = listOf("archives"),
) {
    @Suppress("SpreadOperator")
    fun build(channelId: String, threadTs: String?, ts: String?): Url {
        return url {
            protocol = baseProtocol
            host = baseHost
            pathSegments = listOfNotNull(
                *basePathSegments.toTypedArray(),
                channelId,
                ts?.let { "p${ts.fromTsWithMicroseconds()}" },
            )
            threadTs?.also {
                parameters["thread_ts"] = threadTs
                parameters["cid"] = channelId
            }
        }.asUrl
    }

    companion object {
        fun fromSlackUrl(slackUrl: Url): SlackUrlBuilder {
            return URLBuilder(slackUrl).let {
                SlackUrlBuilder(
                    baseProtocol = it.protocol,
                    baseHost = it.host,
                )
            }
        }
    }
}
