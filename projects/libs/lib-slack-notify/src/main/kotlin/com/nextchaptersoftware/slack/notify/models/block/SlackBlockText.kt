package com.nextchaptersoftware.slack.notify.models.block

import com.nextchaptersoftware.utils.truncateWithEllipsis
import java.util.UUID
import kotlinx.serialization.Serializable

@Serializable
sealed class SlackBlockText {
    abstract val maxWords: Int
    abstract val maxCharacters: Int
    abstract val minCharacters: Int
    protected abstract val rawText: String

    val text by lazy {
        rawText.truncateWithEllipsis(maxChars = maxCharacters, maxWords = maxWords).also {
            require(rawText.length >= minCharacters) {
                "Text must be at least $minCharacters characters long"
            }
        }
    }

    override fun toString(): String {
        return text
    }

    @Serializable
    data class RawSlackBlockText(
        override val rawText: String,
        override val maxWords: Int = Int.MAX_VALUE,
        override val maxCharacters: Int = Int.MAX_VALUE,
        override val minCharacters: Int = 0,
    ) : SlackBlockText() {
        override fun toString(): String {
            return rawText
        }
    }

    // Standard text block length maximum is 3000 characters and Slack will output an error
    // https://api.slack.com/reference/block-kit/blocks#section
    @Serializable
    data class TextBlockText(
        override val rawText: String,
        // Allow for a little buffer for markdown && text
        override val maxCharacters: Int = 2950,
        override val minCharacters: Int = 0,
        override val maxWords: Int = 5000,
    ) : SlackBlockText() {
        override fun toString(): String {
            return super.toString()
        }
    }

    // Standard text block length maximum is 76 characters and Slack will output an error
    // https://api.slack.com/reference/block-kit/blocks#section
    @Serializable
    data class ButtonBlockText(
        override val rawText: String,
        override val maxCharacters: Int = 70,
        override val minCharacters: Int = 0,
        override val maxWords: Int = 10,
    ) : SlackBlockText() {
        override fun toString(): String {
            return super.toString()
        }
    }

    // Standard view open title length maximum is 25 characters and Slack will output an error
    @Serializable
    data class ViewTitleText(
        override val rawText: String,
        override val maxCharacters: Int = 20,
        override val minCharacters: Int = 1,
        override val maxWords: Int = 3,
    ) : SlackBlockText() {
        override fun toString(): String {
            return super.toString()
        }
    }

    // Standard text block length maximum is 75 characters and Slack will output an error
    // https://api.slack.com/reference/block-kit/blocks#section
    @Serializable
    data class SelectOptionBlockText(
        override val rawText: String,
        override val maxCharacters: Int = 70,
        override val minCharacters: Int = 1,
        override val maxWords: Int = 30,
    ) : SlackBlockText() {
        override fun toString(): String {
            return super.toString()
        }
    }

    // Standard text block length maximum is 151 characters and Slack will output an error
    // https://api.slack.com/reference/block-kit/blocks#section
    @Serializable
    data class RadioOptionBlockText(
        override val rawText: String,
        override val maxCharacters: Int = 145,
        override val minCharacters: Int = 1,
        override val maxWords: Int = 30,
    ) : SlackBlockText() {
        override fun toString(): String {
            return super.toString()
        }
    }

    @Serializable
    data class ImageBlockText(
        val altText: String,
        val imageUrl: String,
        override val maxCharacters: Int = 2000,
        override val minCharacters: Int = 1,
        override val maxWords: Int = 30,
    ) : SlackBlockText() {
        override val rawText = altText

        override fun toString(): String = super.toString()
    }
}

object MentionRegexPatterns {
    val slackMentionRegex = """<@[WU][A-Z0-9]+>""".toRegex()
    val slackChannelMentionRegex = """<#C[A-Z0-9]+>""".toRegex()
    val groupMentionRegex = """<!subteam\^[A-Z0-9]+>""".toRegex()
    val slackLinkRegex = """<https?://[^>|]+(\|[^>]+)?>""".toRegex()
}

fun String.asRawSlackBlockText(): SlackBlockText.RawSlackBlockText {
    return SlackBlockText.RawSlackBlockText(
        rawText = this
            .convertMarkdownLinksToSlackLinks()
            .convertMarkdownBoldToSlackBold()
            .convertMarkdownHeadersToSlackHeaders(),
    )
}

fun String.asSlackBlockText(): SlackBlockText.TextBlockText {
    return SlackBlockText.TextBlockText(
        rawText = this
            .convertMarkdownLinksToSlackLinks()
            .convertMarkdownBoldToSlackBold()
            .convertMarkdownHeadersToSlackHeaders(),
    )
}

fun String.asSlackBlockButtonText(): SlackBlockText.ButtonBlockText {
    return SlackBlockText.ButtonBlockText(this)
}

fun String.asSlackBlockRadioOptionText(): SlackBlockText.RadioOptionBlockText {
    return SlackBlockText.RadioOptionBlockText(this)
}

fun String.asSlackBlockSelectOptionText(): SlackBlockText.SelectOptionBlockText {
    return SlackBlockText.SelectOptionBlockText(this)
}

fun UUID.asSlackBlockSelectOptionText(): SlackBlockText.SelectOptionBlockText {
    return SlackBlockText.SelectOptionBlockText(this.toString())
}

fun String.sanitizeMarkdownLinkText(): String {
    return this
        .replace(MentionRegexPatterns.slackMentionRegex, "")
        .replace(MentionRegexPatterns.slackChannelMentionRegex, "")
        .replace(MentionRegexPatterns.groupMentionRegex, "")
        .replace("<!channel>", "")
        .replace(MentionRegexPatterns.slackLinkRegex, "")
        .replace("  ", " ")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .trim()
}

private fun String.convertMarkdownLinksToSlackLinks(): String {
    return replace(Regex("""\[(.*?)]\((.*?)\)""")) { matchResult ->
        val linkText = matchResult.groupValues[1].sanitizeMarkdownLinkText()
        val url = matchResult.groupValues[2]
        "<$url|$linkText>"
    }
}

private fun String.convertMarkdownBoldToSlackBold(): String {
    return replace(
        Regex("\\*\\*(.*?)\\*\\*"),
        "*\$1*",
    )
}

private fun String.convertMarkdownHeadersToSlackHeaders(): String {
    return split('\n').joinToString("\n") {
        it.replace(
            Regex("""^#{1,3}\s+(\S.*?)\s*$"""), "*$1*",
        )
    }
}
