package com.nextchaptersoftware.ml.prompt.input

import com.nextchaptersoftware.ml.input.MlInput
import com.nextchaptersoftware.ml.input.token.TokenLedger

class PromptBuilder(
    private val prompt: MlInput = PromptInput(""),
    private val maxTokens: Int = 256,
    private var tokenLedger: TokenLedger = TokenLedger(maxTokens = maxTokens),
    private var sections: MutableList<PromptSection> = mutableListOf(),
) {
    init {
        tokenLedger.add(prompt)
    }

    fun section(name: String, order: Int, block: PromptSection.() -> Unit): PromptSection {
        val section = PromptSection(
            order = order,
            name = name,
            tokenLedger = tokenLedger,
        )
        section.block()
        sections.add(section)
        return section
    }

    fun build(): String {
        val sectionsString = sections.sortedBy { it.order }.joinToString(separator = "\n") { it.asInput() }
        return "${prompt.asInput()}\n\n$sectionsString\n"
    }
}

fun promptBuilder(
    prompt: MlInput,
    maxTokens: Int = 256,
    block: PromptBuilder.() -> Unit,
): PromptBuilder {
    val promptBuilder = PromptBuilder(prompt = prompt, maxTokens = maxTokens)
    promptBuilder.block()
    return promptBuilder
}
