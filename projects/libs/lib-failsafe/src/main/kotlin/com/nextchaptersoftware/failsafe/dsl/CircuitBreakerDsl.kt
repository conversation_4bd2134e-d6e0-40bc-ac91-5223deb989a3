package com.nextchaptersoftware.failsafe.dsl

import com.nextchaptersoftware.failsafe.event.CircuitBreakerStateChangedEventHandler
import com.nextchaptersoftware.failsafe.event.ExecutionCompletedEventHandler
import dev.failsafe.CircuitBreaker
import java.time.Duration

/**
 * Create [CircuitBreaker]
 *
 * @param R
 * @param setup
 * @return
 */
fun <R> circuitBreaker(setup: CircuitBreakerDsl<R>.() -> Unit): CircuitBreaker<R> =
    CircuitBreakerDsl<R>().also { setup(it) }.build()

@FailsafeBuilderDsl
class CircuitBreakerDsl<R> : FailsafePolicyDsl<R> {

    var delay: Duration? = null
    var failureThreshold: Int? = null
    var successThreshold: Int? = null

    private val onCloseHandlers = mutableListOf<CircuitBreakerStateChangedEventHandler>()
    private val onOpenHandlers = mutableListOf<CircuitBreakerStateChangedEventHandler>()
    private val onHalfOpenHandlers = mutableListOf<CircuitBreakerStateChangedEventHandler>()

    private val onSuccessHandlers = mutableListOf<ExecutionCompletedEventHandler<R>>()
    private val onFailureHandlers = mutableListOf<ExecutionCompletedEventHandler<R>>()

    fun onClose(handler: () -> Unit): CircuitBreakerDsl<R> =
        apply {
            onCloseHandlers.add(CircuitBreakerStateChangedEventHandler { handler.invoke() })
        }

    fun onOpen(handler: () -> Unit): CircuitBreakerDsl<R> =
        apply {
            onOpenHandlers.add(CircuitBreakerStateChangedEventHandler { handler.invoke() })
        }

    fun onHalfOpen(handler: () -> Unit): CircuitBreakerDsl<R> =
        apply {
            onHalfOpenHandlers.add(CircuitBreakerStateChangedEventHandler { handler.invoke() })
        }

    fun onSuccess(handler: ExecutionCompletedEventHandler<R>): CircuitBreakerDsl<R> =
        apply {
            onSuccessHandlers.add(handler)
        }

    fun onFailure(handler: ExecutionCompletedEventHandler<R>): CircuitBreakerDsl<R> =
        apply {
            onSuccessHandlers.add(handler)
        }

    internal fun build(): CircuitBreaker<R> {
        val builder = CircuitBreaker
            .builder<R>()
            .apply {
                delay?.let { withDelay(it) }
                failureThreshold?.let { withFailureThreshold(it) }
                successThreshold?.let { withSuccessThreshold(it) }
            }

        onCloseHandlers.forEach { builder.onClose(it) }
        onOpenHandlers.forEach { builder.onOpen(it) }
        onHalfOpenHandlers.forEach { builder.onHalfOpen(it) }

        onSuccessHandlers.forEach { builder.onSuccess(it) }
        onFailureHandlers.forEach { builder.onFailure(it) }

        return builder.build()
    }
}
