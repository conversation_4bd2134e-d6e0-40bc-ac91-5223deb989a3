package com.nextchaptersoftware.trace.jms

import com.nextchaptersoftware.trace.jms.attributes.CustomSemanticAttributes
import com.nextchaptersoftware.trace.jms.extractor.MessageHeader
import com.nextchaptersoftware.trace.jms.factory.JmsInstrumenterFactory
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.context.Context
import io.opentelemetry.instrumentation.api.instrumenter.Instrumenter
import io.opentelemetry.instrumentation.api.internal.InstrumenterUtil
import io.opentelemetry.instrumentation.api.internal.Timer
import io.opentelemetry.javaagent.bootstrap.CallDepth
import io.opentelemetry.javaagent.instrumentation.jms.MessageWithDestination
import io.opentelemetry.javaagent.instrumentation.jms.v3_0.JakartaDestinationAdapter
import io.opentelemetry.javaagent.instrumentation.jms.v3_0.JakartaMessageAdapter
import jakarta.jms.Destination
import jakarta.jms.Message
import jakarta.jms.MessageProducer

object JmsInstrumenter {
    private val PRODUCER_INSTRUMENTER: Instrumenter<MessageWithDestination, Void>
    private val CONSUMER_INSTRUMENTER: Instrumenter<MessageWithDestination, Void>

    init {
        val factory = JmsInstrumenterFactory(
            GlobalOpenTelemetry.get(),
            "io.opentelemetry.jms-1.1",
        ).setCapturedHeaders(
            listOf(
                MessageHeader(attributeKey = CustomSemanticAttributes.MESSAGING_CALL_STACK),
            ),
        )

        PRODUCER_INSTRUMENTER = factory.createProducerInstrumenter()
        CONSUMER_INSTRUMENTER = factory.createConsumerReceiveInstrumenter()
    }

    @Suppress("TooGenericExceptionCaught")
    fun <T : Message> instrumentProducer(
        message: T,
        destination: Destination,
        callback: () -> Unit,
    ) {
        var context: Context? = null
        var error: Throwable? = null
        val callDepth = CallDepth.forClass(MessageProducer::class.java)
        try {
            context = PRODUCER_INSTRUMENTER.startProducer(
                message = message,
                destination = destination,
                callDepth = callDepth,
            )
            callback()
        } catch (t: Throwable) {
            error = t
            throw t
        } finally {
            PRODUCER_INSTRUMENTER.endProducer(
                context = context,
                message = message,
                destination = destination,
                callDepth = callDepth,
                error = error,
            )
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun <R : Message?> instrumentConsumer(
        callback: () -> R?,
    ): R? {
        var message: Message? = null
        var error: Throwable? = null
        lateinit var timer: Timer

        try {
            timer = CONSUMER_INSTRUMENTER.startConsumer()
            message = callback()
            return message
        } catch (t: Throwable) {
            error = t
            throw t
        } finally {
            CONSUMER_INSTRUMENTER.endConsumer(
                timer = timer,
                message = message,
                error = error,
            )
        }
    }
}

private fun Message.asMessageDestination(destination: Destination?): MessageWithDestination {
    val messageAdapter = JakartaMessageAdapter.create(this)
    val destinationAdapter = destination?.let { JakartaDestinationAdapter.create(destination) }
    return MessageWithDestination.create(
        messageAdapter,
        destinationAdapter,
    )
}

private fun <T : Message> Instrumenter<MessageWithDestination, Void>.startProducer(
    message: T,
    destination: Destination,
    callDepth: CallDepth,
): Context? {
    if (callDepth.getAndIncrement() > 0) {
        return null
    }
    val context = Context.current()
    return this.start(context, message.asMessageDestination(destination = destination))
}

private fun <T : Message> Instrumenter<MessageWithDestination, Void>.endProducer(
    context: Context?,
    message: T,
    destination: Destination,
    callDepth: CallDepth,
    error: Throwable?,
) {
    if (context == null) {
        return
    }
    if (callDepth.decrementAndGet() > 0) {
        return
    }
    this.end(context, message.asMessageDestination(destination), null, error)
}

private fun <R> Instrumenter<MessageWithDestination, R>.startConsumer(): Timer {
    return Timer.start()
}

private fun <T : Message?, R> Instrumenter<MessageWithDestination, R>.endConsumer(
    timer: Timer,
    message: T,
    error: Throwable?,
) {
    if (message == null) {
        return
    }

    val parentContext = Context.current()
    val request = message.asMessageDestination(destination = null)

    InstrumenterUtil.startAndEnd(
        this,
        parentContext,
        request,
        null,
        error,
        timer.startTime(),
        timer.now(),
    )
}
