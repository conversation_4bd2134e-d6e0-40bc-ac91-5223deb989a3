package com.nextchaptersoftware.search.semantic.services

import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.log.sensitive.withSensitiveLoggingContextAsync
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.ml.doc.rerank.services.DocumentRerankService
import com.nextchaptersoftware.ml.embedding.services.IEmbeddingService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.ml.query.context.DocumentQueryContext
import com.nextchaptersoftware.ml.query.context.QueryContext
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.chain.ChainExecutionContext
import com.nextchaptersoftware.search.semantic.services.chain.ChainStep
import com.nextchaptersoftware.search.semantic.services.chain.and
import com.nextchaptersoftware.search.semantic.services.chain.execute
import com.nextchaptersoftware.search.semantic.services.chain.map
import com.nextchaptersoftware.search.semantic.services.chain.steps.CompilePromptStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.CompletionStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.ConnectedRepoStateStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.DocumentListMergeStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.DocumentRerankStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.DocumentRetrievalStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.DocumentSortStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.DynamicLoadingMessageStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.ExtractLocalDocumentsStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.FlowCompletionStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.FollowupSuggestionsEnqueueStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.InitRepoContextualizationStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.InlineReferencesResolverStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.MaliciousQueryDetectionStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.QueryCompressionStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.QueryEmbeddingStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.ReferencesResolverStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.RepoExtractionStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.ResponseSanitizationStep
import com.nextchaptersoftware.search.semantic.services.chain.steps.ResultGenerationStep
import com.nextchaptersoftware.search.semantic.services.chain.then
import com.nextchaptersoftware.search.semantic.services.chain.thenAsync
import com.nextchaptersoftware.search.semantic.services.defence.MaliciousQueryDetectionService
import com.nextchaptersoftware.search.semantic.services.defence.MaliciousQueryException
import com.nextchaptersoftware.search.semantic.services.presets.MessageDataSourcePresetService
import com.nextchaptersoftware.search.semantic.services.references.DemoReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InPromptReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesCleanupService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesResolverService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesToMarkdownLinksService
import com.nextchaptersoftware.search.semantic.services.references.MessageReferencesService
import com.nextchaptersoftware.search.semantic.services.references.ReferencesExpansionService
import com.nextchaptersoftware.search.semantic.services.references.ResponseReferenceResolver
import com.nextchaptersoftware.search.semantic.services.retrieval.ChatCompressionService
import com.nextchaptersoftware.search.semantic.services.retrieval.RepoExtractorService
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.PostRetrievalFilter
import com.nextchaptersoftware.search.semantic.services.sanitizer.ResponseSanitizer
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.datetime.Instant

class SemanticSearchQueryService(
    private val semanticDocumentRetriever: SemanticDocumentRetriever,
    private val documentEvaluationRetriever: DocumentEvaluationRetriever,
    private val completionService: CompletionService,
    private val promptCompilerService: PromptCompilerService,
    private val documentQueryMaxTotalTokens: Int? = null,
    private val inPromptReferenceResolver: InPromptReferenceResolver,
    private val messageReferencesService: MessageReferencesService,
    private val messageDataSourcePresetService: MessageDataSourcePresetService,
    private val responseReferenceResolver: ResponseReferenceResolver,
    private val demoReferenceResolver: DemoReferenceResolver,
    private val responseSanitizer: ResponseSanitizer,
    private val documentRerankService: DocumentRerankService,
    private val chatCompressionService: ChatCompressionService,
    private val inlineReferencesResolverService: InlineReferencesResolverService,
    private val inlineReferencesToMarkdownLinksService: InlineReferencesToMarkdownLinksService,
    private val inlineReferencesCleanupService: InlineReferencesCleanupService,
    private val referencesExpansionService: ReferencesExpansionService,
    private val priorityEventEnqueueService: SearchPriorityEventEnqueueService,
    private val repoExtractorService: RepoExtractorService,
    private val maliciousQueryDetectionService: MaliciousQueryDetectionService,
    private val maliciousQueryDetectionTimeout: Duration,
    private val embedder: IEmbeddingService,
    private val documentRetrievalTimeout: Duration,
    private val repoExtractionTimeout: Duration,
    private val queryCompressionTimeout: Duration,
    private val postRetrievalFilters: List<PostRetrievalFilter>,
) {

    private fun documentStepChain(
        useAgenticRetrievalOverride: Boolean?,
        loadingStateUpdater: suspend (String) -> Unit,
    ): ChainStep<QueryContext, DocumentQueryContext, DocumentContext> {
        return QueryEmbeddingStep(
            embedder = embedder,
        ).thenAsync { _, _ ->
            listOf(
                ExtractLocalDocumentsStep().then(
                    DocumentRerankStep(
                        documentRerankService = documentRerankService,
                    ),
                ),
                DocumentRetrievalStep(
                    semanticDocumentRetriever = semanticDocumentRetriever,
                    documentEvaluationRetriever = documentEvaluationRetriever,
                    documentRetrievalTimeout = documentRetrievalTimeout,
                    useAgenticRetrievalOverride = useAgenticRetrievalOverride,
                    loadingStateUpdater = loadingStateUpdater,
                    postRetrievalFilters = postRetrievalFilters,
                ),
            )
        }.then(
            DocumentListMergeStep(),
        ).then(
            DocumentSortStep(),
        )
    }

    @Suppress("LongMethod")
    suspend fun query(
        queryContext: QueryContext,
        compressQuery: Boolean,
        completionFlow: MutableSharedFlow<String>? = null,
        useAgenticRetrievalOverride: Boolean? = null,
        loadingStateUpdater: suspend (String) -> Unit = { },
    ): MLInference = withSensitiveLoggingContextAsync(
        "orgId" to queryContext.org.id,
        "botMessageId" to queryContext.messageId,
        sensitiveFields = mapOf(
            "query" to queryContext.query,
        ),
    ) {
        when (compressQuery) {
            true -> {
                DynamicLoadingMessageStep<QueryContext, Unit>(loadingStateUpdater, "Analyzing background context").then(
                    QueryCompressionStep(
                        chatCompressionService = chatCompressionService,
                        documentQueryMaxTotalTokens = documentQueryMaxTotalTokens,
                        queryCompressionTimeout = queryCompressionTimeout,
                    ),
                )
            }

            false -> {
                object : ChainStep<QueryContext, Unit, String> {
                    override suspend fun execute(context: ChainExecutionContext<QueryContext>, input: Unit): String {
                        return context.inputContext.query ?: ""
                    }
                }
            }
        }.then(
            InitRepoContextualizationStep(),
        ).thenAsync(
            MaliciousQueryDetectionStep(
                maliciousQueryDetectionService = maliciousQueryDetectionService,
                maliciousQueryDetectionTimeout = maliciousQueryDetectionTimeout,
            ),
        ).and(
            DynamicLoadingMessageStep<QueryContext, DocumentQueryContext>(loadingStateUpdater, "Analyzing your question").then(
                RepoExtractionStep(
                    repoExtractorService = repoExtractorService,
                    repoExtractionTimeout = repoExtractionTimeout,
                ),
            ),
        ).map { result ->
            val isMalicious = result.first
            val documentContext = result.second

            if (isMalicious) {
                throw MaliciousQueryException()
            }
            documentContext
        }.then(
            DynamicLoadingMessageStep<QueryContext, DocumentQueryContext>(loadingStateUpdater, "Searching team documents")
                .then(
                    documentStepChain(useAgenticRetrievalOverride, loadingStateUpdater),
                )
                .then(
                    DynamicLoadingMessageStep(loadingStateUpdater, "Preparing answer"),
                ),
        ).then(
            ConnectedRepoStateStep(),
        )
            .then(
                CompilePromptStep(
                    promptCompilerService = promptCompilerService,
                ),
            ).then(
                completionFlow?.let {
                    FlowCompletionStep(
                        completionService = completionService,
                        responseSanitizer = responseSanitizer,
                        markdownLinkResolver = inlineReferencesToMarkdownLinksService,
                        referenceCleanupService = inlineReferencesCleanupService,
                        inPromptReferenceResolver = inPromptReferenceResolver,
                        completionFlow = it,
                    )
                } ?: CompletionStep(
                    completionService = completionService,
                ),
            ).then(
                ReferencesResolverStep(
                    inPromptReferenceResolver = inPromptReferenceResolver,
                    responseReferenceResolver = responseReferenceResolver,
                    referencesExpansionService = referencesExpansionService,
                    demoReferenceResolver = demoReferenceResolver,
                ),
            ).then(
                ResponseSanitizationStep(
                    responseSanitizer = responseSanitizer,
                ),
            ).then(
                InlineReferencesResolverStep(
                    inlineReferencesResolverService = inlineReferencesResolverService,
                    inlineReferencesToMarkdownLinksService = inlineReferencesToMarkdownLinksService,
                    inlineReferencesCleanupService = inlineReferencesCleanupService,
                ),
            ).then(
                ResultGenerationStep(
                    messageReferencesService = messageReferencesService,
                    messageDataSourcePresetService = messageDataSourcePresetService,
                ),
            ).then(
                FollowupSuggestionsEnqueueStep(
                    priorityEventEnqueueService = priorityEventEnqueueService,
                ),
            ).execute(
                queryContext.copy(
                    executionStart = Instant.nowWithMicrosecondPrecision(),
                ),
            )
    }
}
