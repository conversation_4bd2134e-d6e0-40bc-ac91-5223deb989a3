package com.nextchaptersoftware.search.semantic.services.suggestions

import com.nextchaptersoftware.api.serialization.SerializationExtensions.lenientDecode
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberBundle
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.threadId
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.MessageSuggestionStore
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadStore
import com.nextchaptersoftware.db.stores.UnknownMessageException
import com.nextchaptersoftware.db.stores.UnknownThreadException
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.log.sensitive.warnSensitiveSync
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.search.semantic.services.chain.steps.SuggestionsData
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class MessageSuggestionsService(
    private val completionService: CompletionService,
    private val inferenceService: MLInferenceService,
    private val messageStore: MessageStore = Stores.messageStore,
    private val messageSuggestionStore: MessageSuggestionStore = Stores.messageSuggestionStore,
    private val orgMemberDecorator: OrgMemberDecorator = Stores.orgMemberDecorator,
    private val orgStore: OrgStore = Stores.orgStore,
    private val promptCompilerService: PromptCompilerService,
    private val templateService: MLInferenceTemplateService,
    private val threadStore: ThreadStore = Stores.threadStore,
) {
    /**
     * Generate a list of suggestions from an inference results
     */
    suspend fun generateSuggestions(
        orgId: OrgId,
        inferenceId: MLInferenceId,
    ): List<String> {
        val org = requireNotNull(orgStore.findById(orgId = orgId)) { "Org was null" }
        val inference = inferenceService.get(inferenceId = inferenceId)
        val template = templateService.orgTemplate(orgId, MLInferenceTemplateKind.FollowupSuggestions)
        val questioner = requireNotNull(
            inference.questionerOrgMemberId?.let { orgMemberDecorator.decorateOrgMember(orgMemberId = it) },
        ) { "Questioner was null" }

        if (template.promptTemplate.isBlank()) {
            LOGGER.debugAsync { "message suggestions invoked but template was blank" }
            return emptyList()
        }

        return nextGenGenerateSuggestions(
            org = org,
            questioner = questioner,
            template = template,
            inference = inference,
        )
    }

    private suspend fun nextGenGenerateSuggestions(
        org: Org,
        questioner: OrgMemberBundle,
        template: MLInferenceTemplate,
        inference: MLInference,
    ): List<String> {
        val prompt = promptCompilerService.compilePrompt(
            template = template,
            questioner = questioner,
            documents = inference.documents,
            org = org,
            query = inference.query,
            answer = inference.response,
        ).prompt

        return runSuspendCatching {
            val rawResponse = completionService.query(
                prompt = prompt,
                template = template,
            )

            LOGGER.debugSensitiveAsync(
                sensitiveFields = mapOf(
                    "prompt" to prompt,
                    "response" to rawResponse,
                ),
            ) {
                "next gen followup questions generated"
            }

            rawResponse.lenientDecode<SuggestionsData>().followupQuestions
        }.onFailure {
            LOGGER.warnSensitiveSync(
                t = it,
                sensitiveFields = mapOf(
                    "prompt" to prompt,
                ),
            ) {
                "Error generating next gen followup suggestions"
            }
        }.getOrElse {
            emptyList()
        }
    }

    /**
     * Generate a list of suggestions from an inference results and persist it to a corresponding thread associated with an inference result.
     */
    suspend fun persistSuggestions(
        orgId: OrgId,
        inferenceId: MLInferenceId,
    ) {
        val inference = inferenceService.get(inferenceId = inferenceId)
        val messageId = requireNotNull(inference.botMessageId) { "Bot message ID was null" }
        val threadId = messageStore.find(id = messageId).threadId

        return runSuspendCatching {
            val suggestions = generateSuggestions(orgId = orgId, inferenceId = inferenceId)
            if (suggestions.isNotEmpty()) {
                val suggestionsStr = "[${suggestions.joinToString(separator = ", ")}]"
                LOGGER.debugSensitiveAsync(
                    sensitiveFields = mapOf(
                        "query" to inference.query,
                        "answer" to inference.response,
                        "suggestions" to suggestionsStr,
                    ),
                ) { "message suggestions generated" }
                persistSuggestions(
                    messageId = messageId,
                    threadId = threadId,
                    suggestions = suggestions,
                )
            } else {
                LOGGER.debugSensitiveAsync(
                    sensitiveFields = mapOf(
                        "query" to inference.query,
                        "answer" to inference.response,
                    ),
                ) { "no suggestions generated" }
            }
        }.getOrElse {
            // User deleted the message or thread while processing, ignore.
            if (it is UnknownMessageException || it is UnknownThreadException) {
                return
            }

            LOGGER.errorSensitiveAsync(
                t = it,
                sensitiveFields = mapOf(
                    "query" to inference.query,
                    "answer" to inference.response,
                ),
            ) { "Error generating followup suggestions" }
            throw it
        }
    }

    suspend fun persistSuggestions(
        messageId: MessageId,
        threadId: ThreadId,
        suggestions: List<String>,
        updateModifiedAt: Boolean = true,
    ) = suspendedTransaction {
        messageSuggestionStore.createSuggestionsForMessage(
            trx = this,
            messageId = messageId,
            threadId = threadId,
            suggestions = suggestions,
        )
        if (updateModifiedAt) {
            threadStore.updateModifiedAt(
                trx = this,
                threadIds = listOf(threadId),
            )
        }
    }
}
