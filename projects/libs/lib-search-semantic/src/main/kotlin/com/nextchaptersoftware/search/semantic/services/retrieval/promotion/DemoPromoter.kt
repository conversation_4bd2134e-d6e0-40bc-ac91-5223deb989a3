package com.nextchaptersoftware.search.semantic.services.retrieval.promotion

import com.nextchaptersoftware.db.models.MLTypedDocument

class DemoPromoter : DocumentPromotionCondition {
    companion object {
        private val DEMO_DOCS = listOf(
            "SourceMarkEngine.ts",
            "SourceMarkScheduler.ts",
            "Source mark snippet file content hash problem",
            "do you know how the sourcemark engine works?",
            "SourceMark Engine Must Handle Merge Commits",
            "ScimService.kt",
            "SamlAuthProviderService.kt",
            "SAMLAuthApiDelegateImpl.kt",
            "ConfigureSamlDialog.tsx.",
            "AuthApiDelegateImpl.kt",
        )
    }

    override fun shouldPromote(document: MLTypedDocument): Boolean {
        // Check if the document is a demo
        return DEMO_DOCS.any { demoTitle ->
            document.title?.contains(demoTitle, ignoreCase = true) ?: false
        }
    }
}
