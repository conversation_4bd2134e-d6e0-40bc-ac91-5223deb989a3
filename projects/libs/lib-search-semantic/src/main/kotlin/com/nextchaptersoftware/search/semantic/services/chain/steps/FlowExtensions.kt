package com.nextchaptersoftware.search.semantic.services.chain.steps

import kotlin.time.Duration
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach

object FlowExtensions {

    /**
     * A version of `Flow.sample()` that samples the flow at a regular interval and keeps the last value.
     */
    fun <T> Flow<T>.sampleAndKeepLast(duration: Duration): Flow<T> {
        require(duration.isPositive()) { "Sample period should be positive" }
        return channelFlow {
            var isDone = false
            var lastValue: T? = null
            onEach { lastValue = it }
                .onCompletion { isDone = true }
                .launchIn(this)
            while (!isDone) {
                delay(duration)
                lastValue?.let { value ->
                    send(value)
                    lastValue = null
                }
            }
            lastValue?.let { value ->
                send(value)
            }
        }
    }
}
