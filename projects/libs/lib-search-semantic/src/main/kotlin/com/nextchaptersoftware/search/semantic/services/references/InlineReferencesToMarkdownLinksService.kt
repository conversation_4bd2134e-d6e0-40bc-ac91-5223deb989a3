package com.nextchaptersoftware.search.semantic.services.references

import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.scm.ScmFileUrlProvider
import com.nextchaptersoftware.search.semantic.services.references.MarkdownUrlTransformer.transformMarkdownUrls
import io.ktor.http.Url

class InlineReferencesToMarkdownLinksService(
    private val urlBuilderProvider: Url<PERSON>uilderProvider,
) {

    companion object {
        private const val REF_PREFIX = "REF-"
        private val REF_PATTERN = Regex("""(REF-\d+)""")
        private val SOURCE_TITLE_PATTERN = Regex("""\[[Ss]ource]""")

        fun transformInlineReferences(
            input: String,
            transformation: (matched: String, text: String, link: String) -> String,
        ): String = transformMarkdownUrls(input) { match, text, link ->
            if (!text.contains(REF_PREFIX) && REF_PATTERN.matches(link)) {
                transformation(match, text, link)
            } else {
                match
            }
        }

        fun cleanDisplayedTitle(title: String, ref: MLTypedDocument): String {
            return if (ref.title != null && SOURCE_TITLE_PATTERN.matches(title)) {
                """[${ref.title}]"""
            } else {
                title
            }
        }
    }

    fun transformInlineReferencesToMarkdownLinks(
        orgId: OrgId,
        repos: List<Repo>,
        references: List<MLTypedDocument>,
        response: String,
        scmFileUrlProvider: ScmFileUrlProvider,
    ): InlineReferencesResult {
        val reposById = repos.associateBy { it.id }
        val capturedRefs: MutableList<MLTypedDocument> = mutableListOf()

        val replacementResponse = transformInlineReferences(
            input = response,
        ) { match, title, ref ->
            val document = references.find { it.reference?.asString == ref }
            val url = document?.inlineReferenceLink(
                orgId = orgId,
                repo = reposById[document.groupId?.let(::RepoId)],
                urlBuilderProvider = urlBuilderProvider,
                scmFileUrlProvider = scmFileUrlProvider,
            )

            if (document != null) {
                capturedRefs.add(document)
                val displayedTitle = cleanDisplayedTitle(title, document)
                "[$displayedTitle]($url)"
            } else {
                match // If the REF value doesn't match any document, return the original value
            }
        }

        return InlineReferencesResult(
            response = replacementResponse,
            references = capturedRefs,
        )
    }
}

data class InlineReferencesResult(
    val response: String,
    val references: List<MLTypedDocument>,
)

fun MLTypedDocument.inlineReferenceLink(
    orgId: OrgId,
    repo: Repo?,
    urlBuilderProvider: UrlBuilderProvider,
    scmFileUrlProvider: ScmFileUrlProvider,
): Url? {
    val teamPath = urlBuilderProvider.dashboard().withOrg(orgId.value)

    return when (sourceType) {
        InsightType.Answer,
        -> {
            teamPath.withThread(sourceId).build()
        }

        InsightType.PullRequest -> {
            externalUrl ?: teamPath.withPullRequest(sourceId).build()
        }

        InsightType.Slack,
        InsightType.Documentation,
        InsightType.Discussion,
        -> {
            externalUrl
        }

        InsightType.Issue,
        InsightType.PullRequestComment,
        -> {
            externalUrl ?: teamPath.withThread(sourceId).build()
        }

        InsightType.SourceCode -> {
            repo?.let { scmFileUrlProvider.get(repo = it, filePath = source) }
        }
    }
}
