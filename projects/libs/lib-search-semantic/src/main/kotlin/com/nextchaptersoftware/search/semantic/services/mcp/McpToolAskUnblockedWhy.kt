package com.nextchaptersoftware.search.semantic.services.mcp

import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationService
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.McpSearchMode
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.stores.MLInferenceStore
import com.nextchaptersoftware.db.stores.McpToolStore
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DataSourceAccessControlManager
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.warnSensitiveAsync
import com.nextchaptersoftware.mcp.McpToolDefinition
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.ml.query.context.MLQuery
import com.nextchaptersoftware.ml.query.context.QueryContext
import com.nextchaptersoftware.search.semantic.services.SemanticSearchQueryService
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.WideSlackEmbeddingsRetrievalStyle
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlin.time.Duration.Companion.seconds
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class McpToolAskUnblockedWhy(
    private val dataSourceAccessControlManager: DataSourceAccessControlManager = DataSourceAccessControlManager(),
    private val dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
    private val orgMemberDecorator: OrgMemberDecorator = Stores.orgMemberDecorator,
    private val orgStore: OrgStore = Stores.orgStore,
    private val templateService: MLInferenceTemplateService,
    private val semanticSearchQueryService: SemanticSearchQueryService,
    private val mcpToolStore: McpToolStore = Stores.mcpToolStore,
    private val documentEvaluationRetriever: DocumentEvaluationRetriever,
    private val promptCompilerService: PromptCompilerService,
    private val inferenceStore: MLInferenceStore = Stores.mlInferenceStore,
) {
    companion object {
        val RAG_TIMEOUT = 10.seconds
    }

    suspend fun run(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        parameters: McpToolDefinition.AskUnblockedWhy,
        productAgent: ProductAgentType,
    ): MLInference {
        val searchMode = mcpToolStore.find(McpToolDefinition.AskUnblockedWhy.NAME)?.searchMode ?: McpSearchMode.DEFAULT

        val query = parameters.query
        val repoId = parameters.repoId

        val dsacContext = dataSourceAccessControlManager.getDsacContext(orgId, orgMemberId)
        val dataSourcePresetConfiguration = dataSourcePresetConfigurationService.configurationForOrgMember(orgId = orgId, orgMemberId = orgMemberId)

        return runSuspendCatching {
            when (searchMode) {
                McpSearchMode.FullSearch -> search(
                    orgId = orgId,
                    orgMemberId = orgMemberId,
                    dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                    dsacContext = dsacContext,
                    query = query,
                    repoId = repoId?.let(::RepoId),
                    productAgent = productAgent,
                )

                McpSearchMode.RagOnly -> rag(
                    orgId = orgId,
                    orgMemberId = orgMemberId,
                    dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                    dsacContext = dsacContext,
                    query = query,
                    repoId = repoId?.let(::RepoId),
                    productAgent = productAgent,
                )
            }
        }.onFailure {
            LOGGER.warnAsync(it) { "Failed to run MCP search" }
        }.getOrThrow()
    }

    private suspend fun rag(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetConfiguration: DataSourcePresetConfiguration,
        dsacContext: DsacContext,
        query: String,
        repoId: RepoId?,
        productAgent: ProductAgentType,
    ): MLInference = runSuspendCatching {
        LOGGER.debugSensitiveAsync(
            "orgId" to orgId,
            "orgMemberId" to orgMemberId,
            "repoId" to repoId,
            sensitiveFields = mapOf(
                "query" to query,
            ),
        ) {
            "Running MCP RAG"
        }

        val org = requireNotNull(orgStore.findById(orgId = orgId))
        val questioner = orgMemberDecorator.decorateOrgMember(orgMemberId = orgMemberId).required()

        val template = templateService.orgTemplate(orgId, MLInferenceTemplateKind.McpRag)

        val results = documentEvaluationRetriever.retrieve(
            org = org,
            questioner = questioner,
            searchTemplate = template,
            userQuery = query,
            documentQuery = MLQuery.MLTextQuery(query),
            maxDepthOverride = 0,
            nonExclusiveRepoSet = repoId?.let { setOf(it) } ?: emptySet(),
            wideSlackEmbeddingsRetrievalStyle = WideSlackEmbeddingsRetrievalStyle.Always,
            documentTimeout = RAG_TIMEOUT,
            dataSourcePresetConfiguration = dataSourcePresetConfiguration,
            dsacContext = dsacContext,
        )

        val (topDocs, leftovers) = results.first.partition { it.score == 1.0f }

        val docs = topDocs.nullIfEmpty() ?: leftovers

        val summaries = results.second

        if (docs.isEmpty() && summaries.isEmpty()) {
            LOGGER.warnSensitiveAsync(
                "orgId" to orgId,
                "orgMemberId" to orgMemberId,
                "repoId" to repoId,
                sensitiveFields = mapOf(
                    "query" to query,
                ),
            ) {
                "No documents found for query"
            }
            throw NotFoundException("No documents found for query")
        }

        val compiled = promptCompilerService.compilePrompt(
            template = template,
            query = query,
            documents = docs,
            miscellaneousInfo = summaries.toList(),
        )

        val response = compiled.prompt

        inferenceStore.createExample(
            orgId = orgId,
            botMessageId = null,
            questionerOrgMemberId = orgMemberId,
            runtimeConfigurationId = null,
            runtimeConfigType = null,
            category = MLInferenceCategory.Mcp,
            templateId = template.id,
            query = query,
            documentQuery = query,
            response = response,
            rawResponse = response,
            isSuggestion = false,
            runDuration = null,
            executionTrace = null,
            validationInferenceId = null,
            validationDistance = null,
            prompt = "Not applicable - see response",
            documents = compiled.includedDocuments,
            references = null,
            dataSourcePreset = null,
            productAgent = productAgent,
        ).asDataModel()
    }.onFailure {
        LOGGER.warnAsync(it) { "Failed to run MCP RAG" }
    }.getOrThrow()

    private suspend fun search(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetConfiguration: DataSourcePresetConfiguration,
        dsacContext: DsacContext,
        query: String,
        repoId: RepoId?,
        productAgent: ProductAgentType,
    ): MLInference {
        LOGGER.debugSensitiveAsync(
            "orgId" to orgId,
            "orgMemberId" to orgMemberId,
            "repoId" to repoId,
            sensitiveFields = mapOf(
                "query" to query,
            ),
        ) {
            "Running MCP search"
        }

        val org = requireNotNull(orgStore.findById(orgId = orgId))
        val questioner = orgMemberDecorator.decorateOrgMember(orgMemberId = orgMemberId).required()

        val template = templateService.orgTemplate(orgId, MLInferenceTemplateKind.Search)

        val queryContext = QueryContext(
            org = org,
            template = template,
            questioner = questioner,
            contextRepos = repoId?.let { listOf(it) },
            query = query,
            dsacContext = dsacContext,
            dataSourcePresetConfiguration = dataSourcePresetConfiguration,
            productAgent = productAgent,
        )

        return semanticSearchQueryService.query(
            queryContext = queryContext,
            compressQuery = true,
        )
    }
}
