package com.nextchaptersoftware.search.semantic.services.retrieval

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceType
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.MessageWithAuthor
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.markdown.MessageBodyExtensions.asMessageBody
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.security.Hashing.asSha1Hash
import kotlin.time.Duration.Companion.minutes
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any

class ChatCompressionServiceTest {

    private fun makeMessage(
        text: String,
        authorId: MemberId = MemberId.random(),
        authorOrgMemberId: OrgMemberId = OrgMemberId.random(),
    ): MessageWithAuthor {
        return MockDataClasses.messageWithAuthor(
            authorId = authorId,
            authorOrgMemberId = authorOrgMemberId,
            content = text.asMessageBody().toByteArray(),
            isLoading = false,
        )
    }

    @Test
    fun `test message reducer`() = runTest {
        val botMessageId = MessageId.random()
        val questionerId = OrgMemberId.random()
        val inferenceService: MLInferenceService = mock()
        `when`(inferenceService.findByMessageId(any())).thenReturn(
            MLInference(
                id = MLInferenceId.random(),
                orgId = OrgId.random(),
                botMessageId = botMessageId,
                questionerOrgMemberId = questionerId,
                query = "hello",
                queryHash = "hello".asSha1Hash(),
                response = "world",
                documentQuery = "hello world",
                rawResponse = "world",
                humanFeedback = null,
                sentiment = 1,
                type = MLInferenceType.Unlabeled,
                isSuggestion = false,
                category = MLInferenceCategory.Search,
                isGlobalRegressionInference = false,
                runDuration = 5.minutes,
                executionTrace = null,
                // transitional result fields
                templateId = null,
                configurationId = null,
                runtimeConfigType = null,
                goldenRecordInferences = null,
                validationInferenceId = null,
                validationDistance = null,
                compressedPrompt = null,
                compressedDocuments = null,
                serializedReferences = null,
                serializedDataSourcePreset = null,
                productAgent = null,
            ),
        )

        val service = ChatCompressionService(
            reducerCompletionService = mock(),
            promptCompilerService = mock(),
            templateService = mock(),
            inferenceService = inferenceService,
        )

        val messages = listOf(
            makeMessage("once upon a time"),
            makeMessage("there was a dog"),
            makeMessage("who liked to bark"),
            makeMessage("the world likes dogs", authorOrgMemberId = questionerId),
            makeMessage("tell me more"),
        )

        val reducedMessages = service.reduceMessages(messages, questionerId)

        assertThat(reducedMessages).hasSize(3)
        assertThat(reducedMessages[0].message.content.asMessageBody()?.asMarkdown()).isEqualTo("hello world")
    }
}
