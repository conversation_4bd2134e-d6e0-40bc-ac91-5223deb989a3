package com.nextchaptersoftware.coda.ingestion.queue.handlers

import com.nextchaptersoftware.coda.events.queue.payloads.CodaEvent
import com.nextchaptersoftware.coda.ingestion.services.CodaPermissionsService
import com.nextchaptersoftware.db.stores.CodaFolderStore
import com.nextchaptersoftware.db.stores.CodaOrganizationStore
import com.nextchaptersoftware.db.stores.CodaWorkspaceStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SyncFolderPermissionsEventHandler(
    private val codaPermissionsService: CodaPermissionsService,
    private val codaOrganizationStore: CodaOrganizationStore = Stores.codaOrganizationStore,
    private val codaWorkspaceStore: CodaWorkspaceStore = Stores.codaWorkspaceStore,
    private val codaFolderStore: CodaFolderStore = Stores.codaFolderStore,
) : TypedEventHandler<CodaEvent.SyncFolderPermissionsEvent> {
    override suspend fun handle(event: CodaEvent.SyncFolderPermissionsEvent): Boolean {
        val codaOrganization = codaOrganizationStore.findById(id = event.codaOrganizationId) ?: run {
            LOGGER.debugAsync("codaOrganizationId" to event.codaOrganizationId) { "No Coda organization found" }
            return true
        }

        codaWorkspaceStore.list(codaOrganizationId = codaOrganization.id).mapNotNull {
            codaFolderStore.findByWorkspaceAndFolderId(
                codaOrganizationId = it.codaOrganizationId,
                workspaceId = it.workspaceId,
                folderId = event.folderId,
            )
        }.forEach {
            codaPermissionsService.syncFolderPermissions(
                folderId = it.id,
            )
        }

        return true
    }
}
