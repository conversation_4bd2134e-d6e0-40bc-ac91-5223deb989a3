package com.nextchaptersoftware.coda.ingestion.services

import com.nextchaptersoftware.coda.api.CodaApiProvider
import com.nextchaptersoftware.coda.api.models.Permission
import com.nextchaptersoftware.coda.services.CodaTokenProvider
import com.nextchaptersoftware.db.models.Access
import com.nextchaptersoftware.db.models.CodaDocId
import com.nextchaptersoftware.db.models.CodaFolderId
import com.nextchaptersoftware.db.models.CodaInstallation
import com.nextchaptersoftware.db.models.CodaOrganizationId
import com.nextchaptersoftware.db.models.CodaResourceType
import com.nextchaptersoftware.db.models.PrincipalType
import com.nextchaptersoftware.db.stores.CodaDocStore
import com.nextchaptersoftware.db.stores.CodaFolderStore
import com.nextchaptersoftware.db.stores.CodaOrganizationStore
import com.nextchaptersoftware.db.stores.CodaPermissionStore
import com.nextchaptersoftware.db.stores.CodaWorkspaceStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.log.kotlin.debugAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class CodaPermissionsService(
    private val codaApiProvider: CodaApiProvider,
    private val codaTokenProvider: CodaTokenProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val codaOrganizationStore: CodaOrganizationStore = Stores.codaOrganizationStore,
    private val codaWorkspaceStore: CodaWorkspaceStore = Stores.codaWorkspaceStore,
    private val codaFolderStore: CodaFolderStore = Stores.codaFolderStore,
    private val codaDocStore: CodaDocStore = Stores.codaDocStore,
    private val codaPermissionStore: CodaPermissionStore = Stores.codaPermissionStore,
) {
    suspend fun syncFolderPermissions(
        folderId: CodaFolderId,
    ) {
        LOGGER.debugAsync("folderId" to folderId) { "Syncing folder permissions" }

        val folder = codaFolderStore.findById(id = folderId) ?: error("Folder not found")
        val workspace = codaWorkspaceStore.findById(id = folder.codaWorkspaceId) ?: error("Workspace not found")
        val organization = codaOrganizationStore.findById(id = workspace.codaOrganizationId) ?: error("Organization not found")
        val installation = installationStore.findById(installationId = organization.installationId) ?: error("Installation not found")

        val accessToken = codaTokenProvider.getAccessToken(installation as CodaInstallation)

        val permissions = mutableSetOf<Permission>()
        var nextPageToken: String? = null

        do {
            nextPageToken = codaApiProvider.foldersApi.listFolderPermissions(
                organizationId = organization.organizationId,
                workspaceId = workspace.workspaceId,
                folderId = folder.folderId,
                accessToken = accessToken,
                nextPageToken = nextPageToken,
            ).let {
                permissions.addAll(it.items)
                it.nextPageToken
            }
        } while (nextPageToken != null)

        upsertPermissions(
            codaOrganizationId = organization.id,
            permissions = permissions,
            resourceType = CodaResourceType.Folder,
            resourceId = folder.folderId,
        )
    }

    suspend fun syncDocPermissions(
        docId: CodaDocId,
    ) {
        val doc = codaDocStore.findById(id = docId) ?: error("Doc not found")
        val organization = codaOrganizationStore.findById(id = doc.codaOrganizationId) ?: error("Organization not found")
        val installation = installationStore.findById(installationId = organization.installationId) ?: error("Installation not found")

        val accessToken = codaTokenProvider.getAccessToken(installation as CodaInstallation)

        val permissions = mutableSetOf<Permission>()
        var nextPageToken: String? = null

        do {
            nextPageToken = codaApiProvider.docsApi.listDocPermissions(
                organizationId = organization.organizationId,
                docId = doc.docId,
                accessToken = accessToken,
                nextPageToken = nextPageToken,
            ).let {
                permissions.addAll(it.items)
                it.nextPageToken
            }
        } while (nextPageToken != null)

        upsertPermissions(
            codaOrganizationId = organization.id,
            permissions = permissions,
            resourceType = CodaResourceType.Doc,
            resourceId = doc.docId,
        )
    }

    internal suspend fun upsertPermissions(
        codaOrganizationId: CodaOrganizationId,
        permissions: Set<Permission>,
        resourceType: CodaResourceType,
        resourceId: String,
    ) {
        LOGGER.debugAsync(
            "codaOrganizationId" to codaOrganizationId,
            "resourceId" to resourceId,
            "resourceType" to resourceType,
            "permissions" to permissions,
        ) {
            "Upserting folder permissions"
        }

        val existingPermission = codaPermissionStore.listForResourceTypeAndId(
            codaOrganizationId = codaOrganizationId,
            resourceType = resourceType,
            resourceId = resourceId,
        )

        val upsertedPermissions = permissions.map {
            codaPermissionStore.upsert(
                codaOrganizationId = codaOrganizationId,
                codaId = it.id,
                principalType = it.principal.type.asPrincipalType,
                workspaceId = it.principal.workspaceId,
                domain = it.principal.domain,
                groupId = it.principal.groupId,
                email = it.principal.email,
                access = it.access.asAccess,
                resourceType = resourceType,
                resourceId = resourceId,
            )
        }

        val permissionsToDelete = existingPermission.filterNot { permission ->
            upsertedPermissions.any { it.id == permission.id }
        }

        codaPermissionStore.delete(
            codaOrganizationId = codaOrganizationId,
            codaPermissionIds = permissionsToDelete.map { it.id }.toSet(),
        )
    }
}

private val Permission.Principal.PrincipalType.asPrincipalType: PrincipalType
    get() = when (this) {
        Permission.Principal.PrincipalType.Email -> PrincipalType.Email
        Permission.Principal.PrincipalType.Domain -> PrincipalType.Domain
        Permission.Principal.PrincipalType.Anyone -> PrincipalType.Anyone
        Permission.Principal.PrincipalType.Workspace -> PrincipalType.Workspace
        Permission.Principal.PrincipalType.Group -> PrincipalType.Group
    }

private val Permission.Access.asAccess: Access
    get() = when (this) {
        Permission.Access.ReadOnly -> Access.ReadOnly
        Permission.Access.Write -> Access.Write
        Permission.Access.Comment -> Access.Comment
        Permission.Access.None -> Access.None
    }
