package com.nextchaptersoftware.semantic.bot.services

import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.models.ThreadParticipant

data class MessageAndRelatedModels(
    val org: Org,
    val thread: Thread,
    val message: Message,
    val newMentionedThreadParticipants: List<ThreadParticipant>,
)
