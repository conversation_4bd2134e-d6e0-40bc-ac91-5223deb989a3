package com.nextchaptersoftware.config

import com.nextchaptersoftware.network.utils.NetworkAddressUtils
import java.nio.file.Path
import java.nio.file.Paths
import java.util.Properties
import kotlin.io.path.bufferedReader
import kotlin.io.path.exists

object ServiceInitializer {
    val HOSTNAME by lazy {
        NetworkAddressUtils().safelyGetLocalHostName()
    }

    val NAME = System.getenv()["SERVICE_NAME"] ?: "service"
    val ENVIRONMENT = System.getenv()["SERVICE_ENV"] ?: "local"
    val REGION = System.getenv()["SERVICE_REGION"] ?: "us-west-2"
    val PLATFORM_VERSION = System.getenv()["PLATFORM_VERSION"] ?: "development"
    val PLATFORM_BUILD_NUMBER = System.getenv()["PLATFORM_BUILD_NUMBER"] ?: "1"
    val LOCAL_USER: String? = if (ENVIRONMENT == "local") System.getenv()["USER"] else null
    private val USER_HOME: String = System.getProperty("user.home") ?: "/Users/<USER>"
    private val SECRETS_DIR: Path = Paths.get(USER_HOME, ".secrets", "unblocked", "local")
    val SECRETS_CONFIG_FILE: Path = Paths.get(SECRETS_DIR.toString(), "secrets.conf")
    private val SECRETS_PROPERTIES_FILE: Path = Paths.get(SECRETS_DIR.toString(), "secrets.properties")
    val EXTERNAL_CONFIG_FILE: Path? = System.getenv()["EXTERNAL_CONFIG_FILE"]?.let { Paths.get(it) }

    init {
        if (SECRETS_PROPERTIES_FILE.exists()) {
            SECRETS_PROPERTIES_FILE.bufferedReader().use { reader ->
                val properties = Properties()
                properties.load(reader)
                properties.forEach { property ->
                    System.setProperty(property.key.toString(), property.value.toString())
                }
            }
        }
    }
}
