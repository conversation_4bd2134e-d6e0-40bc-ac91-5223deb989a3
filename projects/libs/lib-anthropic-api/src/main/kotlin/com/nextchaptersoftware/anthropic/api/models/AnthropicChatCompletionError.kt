package com.nextchaptersoftware.anthropic.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

sealed class AnthropicChatCompletionError(message: String) : Exception(message) {
    class InvalidRequestError(message: String) : AnthropicChatCompletionError(message)
}

@Serializable
data class AnthropicErrorContainer(
    val error: AnthropicResponseError,
)

@Serializable
data class AnthropicResponseError(
    val type: AnthropicErrorType,
    val message: String,
)

@Serializable
enum class AnthropicErrorType {
    @SerialName("invalid_request_error")
    InvalidRequestError,
}

fun AnthropicResponseError.toException(): AnthropicChatCompletionError {
    return when (type) {
        AnthropicErrorType.InvalidRequestError -> AnthropicChatCompletionError.InvalidRequestError(message)
    }
}
