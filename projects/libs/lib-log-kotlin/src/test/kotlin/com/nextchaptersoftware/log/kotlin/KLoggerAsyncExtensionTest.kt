package com.nextchaptersoftware.log.kotlin

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.LoggerContext
import ch.qos.logback.classic.encoder.PatternLayoutEncoder
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.Appender
import ch.qos.logback.core.OutputStreamAppender
import java.io.ByteArrayOutputStream
import kotlinx.coroutines.test.runTest
import mu.KLogging
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode.SAME_THREAD
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC

class ClassWithLogging {
    companion object : KLogging()

    fun test() {
        logger.info { "test ClassWithLogging" }
    }

    fun testFields(vararg fields: Pair<String, String?>) {
        logger.infoSync(*fields) { "test ClassWithLogging" }
    }

    suspend fun testFieldsAsync(vararg fields: Pair<String, String?>) {
        logger.infoAsync(*fields) { "test ClassWithLogging" }
    }

    fun testThrowable() {
        val ex = Throwable()
        logger.traceSync(ex) { "test ClassWithLogging" }
    }

    fun testThrowableFields(vararg fields: Pair<String, String?>) {
        val ex = Throwable()
        logger.traceSync(ex, *fields) { "test ClassWithLogging" }
    }

    suspend fun testThrowableFieldsAsync(vararg fields: Pair<String, String?>) {
        val ex = Throwable()
        logger.traceAsync(ex, *fields) { "test ClassWithLogging" }
    }
}

fun addAppender(appender: Appender<ILoggingEvent>) {
    val root = LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME) as ch.qos.logback.classic.Logger
    root.addAppender(appender)
    root.level = Level.TRACE
    appender.start()
}

fun removeAppender(appender: Appender<ILoggingEvent>) {
    val root = LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME) as ch.qos.logback.classic.Logger
    root.detachAppender(appender)
    root.level = Level.TRACE
    appender.stop()
}

@Disabled("Flaky test")
@Execution(SAME_THREAD)
@TestMethodOrder(MethodOrderer.MethodName::class)
class LoggingTest {
    private val appenderWithWriter: AppenderWithWriter = AppenderWithWriter()

    @BeforeEach
    fun setupAppender() {
        MDC.clear()
        addAppender(appenderWithWriter.appender)
    }

    @AfterEach
    fun removeAppender() {
        removeAppender(appenderWithWriter.appender)
    }

    @Test
    fun testBasicFunctionality() {
        ClassWithLogging().apply {
            test()
            testThrowable()
        }
        val lines = appenderWithWriter.toLines()
        assertAll(
            { assertThat(lines).contains("[{}]INFO  ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
            { assertThat(lines).contains("[{}]TRACE ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
        )
    }

    @Test
    fun testBasicFunctionalityWithFields() {
        ClassWithLogging().apply {
            testFields("a" to "b")
            testThrowableFields("a" to "b")
        }
        val lines = appenderWithWriter.toLines()
        assertAll(
            { assertThat(lines).contains("[{a=b}]INFO  ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
            { assertThat(lines).contains("[{a=b}]TRACE ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
        )
    }

    @Test
    fun testBasicFunctionalityWithFieldsAsync() = runTest {
        ClassWithLogging().apply {
            testFieldsAsync("a" to "b")
            testThrowableFieldsAsync("a" to "b")
        }
        val lines = appenderWithWriter.toLines()
        assertAll(
            { assertThat(lines).contains("[{a=b}]INFO  ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
            { assertThat(lines).contains("[{a=b}]TRACE ${ClassWithLogging::class.java.name}  - test ClassWithLogging") },
        )
    }
}

data class AppenderWithWriter(
    val pattern: String = "[{%mdc}]%-5p %c %marker - %m%n",
    val printStream: ByteArrayOutputStream = ByteArrayOutputStream(),
    val appender: OutputStreamAppender<ILoggingEvent> = OutputStreamAppender<ILoggingEvent>()
        .apply {
            name = "writer"
            val encoder = PatternLayoutEncoder()
            (LoggerFactory.getILoggerFactory() as? LoggerContext)?.let {
                encoder.context = it
            }
            encoder.pattern = pattern
            encoder.start()

            this.encoder = encoder
            this.outputStream = printStream
        },
) {
    fun toLines(): List<String> {
        return printStream.toString().trim()
            .replace("\r", "\n")
            .replace("\r\n", "\n")
            .split("\n")
            .filter { it.isNotEmpty() }
            .filter { it.contains("java.lang.Throwable: null") || it.contains(ClassWithLogging::class.java.name) }
    }
}
