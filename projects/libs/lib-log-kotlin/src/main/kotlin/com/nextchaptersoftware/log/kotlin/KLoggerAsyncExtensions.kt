@file:Suppress("TooManyFunctions")

package com.nextchaptersoftware.log.kotlin

import mu.KLogger

inline fun KLogger.invokeMessage(
    msg: () -> Any?,
    body: KLogger.(msg: Any?) -> Unit,
) {
    val newMsg = msg()
    this.body(newMsg)
}

suspend inline fun KLogger.traceAsync(
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.trace { it }
        }
    }
}

suspend inline fun KLogger.traceAsync(
    t: Throwable,
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.trace(t) { it }
        }
    }
}

suspend inline fun KLogger.debugAsync(
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.debug { it }
        }
    }
}

suspend inline fun KLogger.debugAsync(
    t: Throwable,
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.debug(t) { it }
        }
    }
}

suspend inline fun KLogger.infoAsync(
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.info { it }
        }
    }
}

suspend inline fun KLogger.infoAsync(
    t: Throwable,
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.info(t) { it }
        }
    }
}

suspend inline fun KLogger.warnAsync(
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.warn { it }
        }
    }
}

suspend inline fun KLogger.warnAsync(
    t: Throwable,
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.warn(t) { it }
        }
    }
}

suspend inline fun KLogger.errorAsync(
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.error { it }
        }
    }
}

suspend inline fun KLogger.errorAsync(
    t: Throwable,
    vararg fields: Pair<String, Any?>,
    crossinline msg: () -> Any?,
) {
    withLoggingContextAsync(*fields) {
        invokeMessage(msg) {
            this.error(t) { it }
        }
    }
}
