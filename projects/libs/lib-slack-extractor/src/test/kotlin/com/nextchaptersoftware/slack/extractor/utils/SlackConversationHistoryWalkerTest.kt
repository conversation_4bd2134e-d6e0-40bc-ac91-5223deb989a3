package com.nextchaptersoftware.slack.extractor.utils

import com.nextchaptersoftware.config.GlobalConfig
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackConversationHistoryWalkerTest {
    private val slackConversationHistoryWalker = SlackConversationHistoryWalker(
        token = GlobalConfig.INSTANCE.providers.slack.botAuthToken.value,
    )

    @Test
    fun `test history walking`() = runTest {
        val slackChannelExternalId = "C07ME5MSY7M"
        val messages = slackConversationHistoryWalker.walk(
            channelId = slackChannelExternalId,
            includeReplies = true,
            limit = 100,
        ).toList()

        assertThat(messages).isNotEmpty
    }
}
