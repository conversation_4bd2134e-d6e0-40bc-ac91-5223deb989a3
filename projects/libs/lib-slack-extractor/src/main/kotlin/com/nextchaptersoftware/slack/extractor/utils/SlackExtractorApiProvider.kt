package com.nextchaptersoftware.slack.extractor.utils

import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.SlackInstance

object SlackExtractorApiProvider {
    private const val DEFAULT_THREAD_POOL_SIZE = 30

    val INSTANCE by lazy {
        SlackApiProvider(
            client = SlackInstance.createSlackInstance {
                methodsConfig.defaultThreadPoolSize = DEFAULT_THREAD_POOL_SIZE
                methodsConfig.customRateLimitResolver = SlackExtractorRateLimiter()
            }.methodsAsync(),
        )
    }
}
