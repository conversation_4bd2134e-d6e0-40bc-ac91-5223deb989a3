package com.nextchaptersoftware.billing.services.downgrade

import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.maintenance.installation.StandardUninstallService

class MultiScmCapabilityDowngradeHandler(
    private val uninstallService: StandardUninstallService,
    private val installationStore: InstallationStore = Stores.installationStore,
) : CapabilityDowngradeHandler {

    override suspend fun handleCapabilityDowngrade(orgId: OrgId, capability: PlanCapabilityType) {
        check(capability == PlanCapabilityType.MultiSCM) { "Unexpected capability: $capability" }

        val scmInstallations = installationStore.findByProvider(orgId = orgId, providers = Provider.scmProviders)
        if (scmInstallations.size <= 1) {
            return
        }

        val bestInstallation = selectBestInstallation(scmInstallations)

        scmInstallations.minus(bestInstallation).forEach { installation ->
            uninstallService.uninstall(
                orgId = orgId,
                personId = PersonId.random(), // dont care
                installationId = installation.id,
                notify = false,
            )
        }
    }

    /**
     * We pick the installation with the oldest creation date as the best installation.
     */
    private fun selectBestInstallation(installations: Set<Installation>): Installation {
        return installations.minBy { it.createdAt }
    }
}
