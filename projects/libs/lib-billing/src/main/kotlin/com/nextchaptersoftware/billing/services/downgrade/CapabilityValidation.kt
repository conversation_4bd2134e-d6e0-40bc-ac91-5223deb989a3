package com.nextchaptersoftware.billing.services.downgrade

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.ktor.ForbiddenException
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService

class CapabilityValidation(
    private val planCapabilitiesService: PlanCapabilitiesService,
) {
    suspend fun requiresCapability(
        orgId: OrgId,
        capability: PlanCapabilityType,
    ) {
        if (!allowsCapability(orgId = orgId, capability = capability)) {
            throw ForbiddenException("Expected capability $capability")
        }
    }

    suspend fun allowsCapability(
        orgId: OrgId,
        capability: PlanCapabilityType,
    ): <PERSON><PERSON><PERSON> {
        return planCapabilitiesService.hasCapability(orgId = orgId, planCapabilityType = capability)
    }
}
