plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-stripe", "default"))
    implementation(project(":projects:libs:lib-billing-events", "default"))
    implementation(project(":projects:libs:lib-maintenance-events", "default"))
    implementation(project(":projects:libs:lib-maintenance-installation", "default"))
    implementation(project(":projects:libs:lib-notification-events", "default"))
    implementation(project(":projects:libs:lib-plan-capabilities", "default"))
    implementation(project(":projects:libs:lib-slack-notify", "default"))
    implementation(project(":projects:libs:lib-stripe", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:models", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)
    testImplementation(testLibs.bundles.test.postgresql)
}
