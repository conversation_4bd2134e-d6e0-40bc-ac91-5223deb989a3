package com.nextchaptersoftware.pr.summary.ingestion.pipeline

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.PullRequestIngestionModel
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.UserEngagementModel
import com.nextchaptersoftware.db.models.toRepo
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.RepoStore.Companion.ACTIVE_REPO_CLAUSE
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.UserEngagementStore
import com.nextchaptersoftware.scm.ingestion.pipeline.RepoIngestionService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.exists
import org.jetbrains.exposed.sql.longLiteral
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.update

class RepoPullRequestSummaryService(
    private val ingestionInterval: Duration,
    private val lastActiveAtThreshold: Duration,
    private val maxReposPerTeam: Int,
) : RepoIngestionService<Int>() {

    override suspend fun updateLatestContinuation(repoId: RepoId, continuation: Int?) {
        Database.suspendedTransaction {
            RepoModel.update({ RepoModel.id eq repoId }) {
                it[pullRequestIngestLastPrNumber] = continuation
            }
        }
    }

    override suspend fun getLatestContinuation(repoId: RepoId): Int? {
        return Database.suspendedTransaction {
            RepoModel
                .select(RepoModel.id, RepoModel.pullRequestIngestLastPrNumber)
                .where { RepoModel.id eq repoId }
                .firstOrNull()
                ?.let { it[RepoModel.pullRequestIngestLastPrNumber] }
        }
    }

    override suspend fun selectRepoCandidatesToIngest(): List<Repo> {
        val now = Instant.nowWithMicrosecondPrecision()
        val since = now - ingestionInterval
        val lastActiveAt = now - lastActiveAtThreshold

        val prExistsSubQuery = exists(
            PullRequestModel.select(PullRequestModel.id).where(PullRequestModel.repo.eq(RepoModel.id)).limit(1),
        )

        // https://stackoverflow.com/questions/64163627/handling-subquery-in-a-kotlin-exposed-framework
        val subQuery = RepoModel
            .select(RepoModel.id, RepoModel.lastActiveRank)
            .withDistinct()
            .whereAll(
                prExistsSubQuery,
                ACTIVE_REPO_CLAUSE,
                RepoModel.lastActiveAt greater lastActiveAt,
            )
            .alias("subQuery")

        return Database.suspendedTransaction {
            RepoModel
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.id,
                    onColumn = RepoModel.scmTeam,
                    joinType = JoinType.INNER,
                ) {
                    AllOp(
                        ScmTeamStore.SCM_TEAM_CONNECTED_CLAUSE,
                        ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE,
                        ScmTeamStore.NOT_PERSONAL_TEAM_CLAUSE,
                    )
                }
                .join(
                    otherTable = UserEngagementModel,
                    otherColumn = UserEngagementModel.orgId,
                    onColumn = ScmTeamModel.org,
                    joinType = JoinType.INNER,
                ) {
                    AllOp(
                        UserEngagementModel.createdAt greaterEq now.minus(ScmTeamStore.DEFAULT_ACTIVE_PERIOD),
                        UserEngagementStore.ENGAGED_ACTIVITY_CLAUSE,
                    )
                }
                .join(subQuery, JoinType.INNER, subQuery[RepoModel.id], RepoModel.id)
                .join(
                    otherTable = PullRequestIngestionModel,
                    joinType = JoinType.INNER,
                    onColumn = RepoModel.id,
                    otherColumn = PullRequestIngestionModel.repo,
                ) {
                    PullRequestIngestionModel.ingestionComplete eq true
                }
                .select(RepoModel.columns + subQuery[RepoModel.lastActiveRank])
                .withDistinct()
                .whereAll(
                    (RepoModel.ingestionBackoffUntil.isNull()) or (RepoModel.ingestionBackoffUntil less now),
                    (RepoModel.pullRequestIngestLastStartAt.isNull()) or (RepoModel.pullRequestIngestLastStartAt less since),
                    subQuery[RepoModel.lastActiveRank] lessEq longLiteral(maxReposPerTeam.toLong()),
                )
                .orderBy(
                    RepoModel.pullRequestIngestLastStartAt to SortOrder.ASC_NULLS_FIRST,
                    RepoModel.createdAt to SortOrder.ASC,
                )
                .limit(<EMAIL>)
                .map { it.toRepo() }
        }
    }

    override suspend fun updateIngestLastStartAt(repoId: RepoId) {
        Database.suspendedTransaction {
            RepoModel.update({ RepoModel.id eq repoId }) {
                it[pullRequestIngestLastStartAt] = Instant.nowWithMicrosecondPrecision()
            }
        }
    }

    override suspend fun reingestRepoNonIncrementally(repoId: RepoId) {
        Database.suspendedTransaction {
            RepoModel.update({ RepoModel.id eq repoId }) {
                it[pullRequestIngestLastStartAt] = null
            }
        }
    }

    override suspend fun reingestTeamNonIncrementally(teamId: ScmTeamId) {
        Database.suspendedTransaction {
            RepoModel.update({ RepoModel.scmTeam eq teamId }) {
                it[pullRequestIngestLastStartAt] = null
            }
        }
    }
}
