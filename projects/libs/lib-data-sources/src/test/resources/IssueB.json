{"expand": "operations,versionedRepresentations,editmeta,changelog,customfield_10010.requestTypePractice,renderedFields", "id": "10055", "self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/issue/10055", "key": "UN-39", "fields": {"statuscategorychangedate": "2024-07-25T21:28:27.554-0700", "issuetype": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/issuetype/10001", "id": "10001", "description": "Tasks track small, distinct pieces of work.", "iconUrl": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/universal_avatar/view/type/issuetype/avatar/10318?size=medium", "name": "Task", "subtask": false, "avatarId": 10318, "entityId": "ad96f681-bebf-4036-a3b4-1f8198eafe37", "hierarchyLevel": 0}, "timespent": null, "customfield_10030": null, "project": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/project/10000", "id": "10000", "key": "UN", "name": "Unblocked", "projectTypeKey": "software", "simplified": true, "avatarUrls": {"48x48": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/universal_avatar/view/type/project/avatar/10406", "24x24": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/universal_avatar/view/type/project/avatar/10406?size=small", "16x16": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/universal_avatar/view/type/project/avatar/10406?size=xsmall", "32x32": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/universal_avatar/view/type/project/avatar/10406?size=medium"}}, "fixVersions": [], "customfield_10034": null, "aggregatetimespent": null, "resolution": null, "customfield_10035": null, "customfield_10036": null, "customfield_10037": null, "customfield_10027": null, "customfield_10028": null, "customfield_10029": null, "resolutiondate": null, "workratio": -1, "lastViewed": "2025-01-31T16:27:58.727-0800", "issuerestriction": {"issuerestrictions": {}, "shouldDisplay": true}, "watches": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/issue/UN-39/watchers", "watchCount": 1, "isWatching": true}, "created": "2024-07-25T21:28:27.212-0700", "customfield_10020": null, "customfield_10021": null, "customfield_10022": null, "priority": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/priority/3", "iconUrl": "https://getunblocked.atlassian.net/images/icons/priorities/medium.svg", "name": "Medium", "id": "3"}, "customfield_10023": null, "customfield_10024": null, "customfield_10025": null, "labels": [], "customfield_10016": 1.0, "customfield_10017": null, "customfield_10018": {"hasEpicLinkFieldDependency": false, "showField": false, "nonEditableReason": {"reason": "PLUGIN_LICENSE_ERROR", "message": "The Parent Link is only available to Jira Premium users."}}, "customfield_10019": "0|i0003j:", "timeestimate": null, "aggregatetimeoriginalestimate": null, "versions": [], "issuelinks": [], "assignee": null, "updated": "2025-01-31T16:28:05.826-0800", "status": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/status/10000", "description": "", "iconUrl": "https://api.atlassian.com/ex/jira/************************99370e885441/", "name": "To Do", "id": "10000", "statusCategory": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/statuscategory/2", "id": 2, "key": "new", "colorName": "blue-gray", "name": "To Do"}}, "components": [], "timeoriginalestimate": null, "description": "The properties `autoSelectNewSourceRepos`, `enablePullRequests`, and `enableScmIssues` in the `ScmConfiguration` interface are optional because they are not always required to be set explicitly. This design allows for flexibility when creating or updating SCM configurations, as not all properties need to be provided every time.\n\n### Default Values\n\nThe default values for these properties are managed in the backend, specifically in the implementation of the SCM configuration API. Here are the default values based on the backend logic:\n\n1) **`autoSelectNewSourceRepos`**: Defaults to `true` if not explicitly set. This is managed in the [TeamSettingsStore](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/models/src/main/kotlin/com/nextchaptersoftware/db/stores/TeamSettingsStore.kt) and [SCMConfigurationsApiDelegateImpl](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/services/apiservice/src/main/kotlin/com/nextchaptersoftware/apiservice/api/SCMConfigurationsApiDelegateImpl.kt).\n2) **`enablePullRequests`**: Defaults to `true` if not explicitly set. This is also managed in the [TeamSettingsStore](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/models/src/main/kotlin/com/nextchaptersoftware/db/stores/TeamSettingsStore.kt) and [SCMConfigurationsApiDelegateImpl](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/services/apiservice/src/main/kotlin/com/nextchaptersoftware/apiservice/api/SCMConfigurationsApiDelegateImpl.kt).\n3) **`enableScmIssues`**: Defaults to `true` if not explicitly set. This is managed in the [GitHubIssuesSettingsStore](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/services/apiservice/src/main/kotlin/com/nextchaptersoftware/apiservice/api/SCMConfigurationsApiDelegateImpl.kt) and [SCMConfigurationsApiDelegateImpl](https://github.com/NextChapterSoftware/unblocked/blob/HEAD/projects/services/apiservice/src/main/kotlin/com/nextchaptersoftware/apiservice/api/SCMConfigurationsApiDelegateImpl.kt).\n\n### Example Code\n\nHere is an example of how these defaults are applied in the backend:\n\n```\noverride suspend fun getScmConfiguration(\n context: PipelineContext<Unit, ApplicationCall>,\n input: Resources.getScmConfiguration,\n): ScmConfiguration {\n val autoSelectNewSourceRepos = teamSettingsStore.getAutoSelectNewSourceRepos(input.teamId)\n val enablePullRequests = teamSettingsStore.getEnablePullRequests(input.teamId)\n val enableScmIssues = gitHubIssuesSettingsStore.find(input.teamId)?.enable ?: true\n\n return ScmConfiguration(\n autoSelectNewSourceRepos = autoSelectNewSourceRepos,\n enablePullRequests = enablePullRequests,\n enableScmIssues = enableScmIssues,\n )\n}\n\n```\n\nThis ensures that if any of these properties are not provided, they will default to `true`.", "customfield_10010": null, "customfield_10014": null, "timetracking": {}, "customfield_10015": null, "customfield_10005": null, "customfield_10006": null, "security": null, "customfield_10007": null, "customfield_10008": null, "customfield_10009": null, "aggregatetimeestimate": null, "attachment": [], "summary": "This is a task summary", "creator": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/user?accountId=6418dc3c5534b0bf74414b54", "accountId": "6418dc3c5534b0bf74414b54", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "24x24": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "16x16": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "32x32": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png"}, "displayName": "<PERSON>", "active": true, "timeZone": "America/Los_Angeles", "accountType": "atlassian"}, "subtasks": [], "customfield_10041": null, "reporter": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/user?accountId=6418dc3c5534b0bf74414b54", "accountId": "6418dc3c5534b0bf74414b54", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "24x24": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "16x16": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png", "32x32": "https://secure.gravatar.com/avatar/a9539254a0f303fcf5521b22265a7900?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FDL-2.png"}, "displayName": "<PERSON>", "active": true, "timeZone": "America/Los_Angeles", "accountType": "atlassian"}, "aggregateprogress": {"progress": 0, "total": 0}, "customfield_10001": null, "customfield_10002": [], "customfield_10003": null, "customfield_10004": null, "customfield_10039": null, "environment": null, "duedate": null, "progress": {"progress": 0, "total": 0}, "comment": {"comments": [], "self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/issue/10055/comment", "maxResults": 0, "total": 0, "startAt": 0}, "votes": {"self": "https://api.atlassian.com/ex/jira/************************99370e885441/rest/api/2/issue/UN-39/votes", "votes": 0, "hasVoted": false}, "worklog": {"startAt": 0, "maxResults": 20, "total": 0, "worklogs": []}}}