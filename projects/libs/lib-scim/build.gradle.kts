plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(libs.bundles.atlassian.jwt)

    implementation(project(":projects:models", "default"))
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-maintenance", "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)
}
