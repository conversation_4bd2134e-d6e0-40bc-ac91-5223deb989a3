plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-plan-capabilities", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
    testImplementation(project(":projects:models", "test"))

    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.core)
}
