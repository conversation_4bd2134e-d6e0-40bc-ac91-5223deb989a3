plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-anthropic", "default"))
    implementation(project(":projects:clients:client-cohere", "default"))
    implementation(project(":projects:clients:client-ml", "default"))
    implementation(project(":projects:clients:client-openai", "default"))
    implementation(project(":projects:clients:client-google-ai", "default"))
    implementation(project(":projects:libs:lib-aws-bedrock-anthropic", "default"))
    implementation(project(":projects:libs:lib-failsafe", "default"))
    implementation(project(":projects:libs:lib-log-sensitive", "default"))

    testImplementation(testLibs.bundles.test.core)

    testImplementation(project(":projects:libs:lib-common", "test"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
