package com.nextchaptersoftware.ml.doc.rerank.utils

import com.nextchaptersoftware.ml.doc.rerank.models.RerankDocument
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.types.MLReference

object RerankDocumentExtensions {
    fun Map<String, RerankDocument>.asDocumentContext(
        documentContext: DocumentContext,
    ): DocumentContext {
        val reRankedLocalDocs = documentContext.documentPartitions.activeLocalDocuments.mapIndexed { index, doc ->
            doc.copy(
                score = 1.0f,
                reference = MLReference(index),
            )
        }

        val startingDocIndex = reRankedLocalDocs.size

        val reRankedDocs = documentContext.documentPartitions.otherDocuments.map { doc ->
            val id = doc.searchDocumentIdCompat
            val rerankDoc = this[id]
            val rank = rerankDoc?.rank ?: 0
            doc.copy(
                score = rerankDoc?.score?.toFloat() ?: 0.0f,
                cerrRank = rank,
                reference = MLReference(rank + startingDocIndex),
            )
        }.sortedByDescending {
            it.score
        }

        return documentContext.copy(
            documents = reRankedLocalDocs + reRankedDocs,
        )
    }
}
