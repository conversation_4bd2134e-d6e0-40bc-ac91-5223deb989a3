package com.nextchaptersoftware.linear.events.queue.handlers

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.linear.events.queue.payloads.LinearEvent
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import org.jetbrains.exposed.sql.and

class LinearReembedEventHandler(
    private val indexingAndEmbeddingService: IndexingAndEmbeddingService,
) : TypedEventHandler<LinearEvent.LinearReembedEvent> {
    override suspend fun handle(event: LinearEvent.LinearReembedEvent): Boolean {
        val orgId = Stores.scmTeamStore.getOrgId(event.teamId)

        suspendedTransaction {
            ThreadModel
                .select(ThreadModel.id)
                .where { (ThreadModel.org eq orgId) and (ThreadModel.linearTeam eq event.linearTeamId) }
                .map { it[ThreadModel.id].value }
        }.forEach {
            indexingAndEmbeddingService.indexThread(
                threadId = it,
                priority = MessagePriority.LOW,
            )
        }

        return true
    }
}
