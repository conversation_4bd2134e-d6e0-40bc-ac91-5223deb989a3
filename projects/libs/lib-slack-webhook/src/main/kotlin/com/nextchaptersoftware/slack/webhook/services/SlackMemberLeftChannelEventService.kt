package com.nextchaptersoftware.slack.webhook.services

import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackChannelMemberEventDeletionService
import com.nextchaptersoftware.slack.webhook.services.models.SlackWebhookEventContext
import com.slack.api.app_backend.events.payload.MemberLeftChannelPayload
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackMemberLeftChannelEventService(
    private val slackChannelMemberEventDeletionService: SlackChannelMemberEventDeletionService,
) {
    suspend fun process(payload: MemberLeftChannelPayload) = withLoggingContextAsync(
        "slackTeamId" to payload.teamId,
        "slackChannelId" to payload.event?.channel,
        "user" to payload.event?.user,
        "type" to payload.event?.type,
        "subType" to payload.event?.subtype,
    ) {
        LOGGER.infoAsync { "Received member left channel event" }

        val slackExternalTeamId = payload.teamId
        val slackExternalChannelId = payload.event?.channel ?: error("slackChannelId is missing in payload")
        val slackUserId = payload.event?.user ?: error("Failed to find slackUserId for member departure")
        val isBotTokenEvent = payload.authorizations?.any { it.isBot } ?: false

        slackChannelMemberEventDeletionService.deleteMember(
            context = SlackWebhookEventContext.SlackMemberEventContext(
                slackExternalTeamId = slackExternalTeamId,
                slackUserId = slackUserId,
                slackExternalChannelId = slackExternalChannelId,
                isBotTokenEvent = isBotTokenEvent,
            ),
        )

        LOGGER.infoAsync { "Finished member left channel event" }
    }
}
