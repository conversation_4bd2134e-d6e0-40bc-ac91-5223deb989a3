package com.nextchaptersoftware.slack.webhook.services

import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.webhook.services.bot.SlackBotAtMentionWebhookEventProcessor
import com.nextchaptersoftware.slack.webhook.services.bot.SlackBotAutoAnswerWebhookEventProcessor
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackMessageEventIngestionService
import com.nextchaptersoftware.slack.webhook.services.models.SlackWebhookEventContext
import com.slack.api.app_backend.events.payload.MessageFileSharePayload
import com.slack.api.model.event.MessageFileShareEvent

class SlackMessageFileShareEventService(
    private val slackMessageEventIngestionService: SlackMessageEventIngestionService,
    private val slackBotAutoAnswerWebhookEventProcessor: SlackBotAutoAnswerWebhookEventProcessor,
    private val slackBotAtMentionWebhookEventProcessor: SlackBotAtMentionWebhookEventProcessor,
) {
    suspend fun process(payload: MessageFileSharePayload) = withLoggingContextAsync(
        "slackTeamId" to payload.teamId,
        "slackChannelId" to payload.event.channel,
        "messageTs" to payload.event.ts,
        "type" to payload.event.type,
        "subType" to payload.event.subtype,
    ) {
        slackBotAtMentionWebhookEventProcessor.processAtMention(
            payload = payload,
        )
        slackMessageEventIngestionService.ingest(
            slackExternalChannelId = payload.event.channel,
            slackExternalTeamId = payload.teamId,
            slackMessage = payload.event.toSlackMessageEventContext(),
            slackPreviousMessage = null,
        )
        // Ensure Slack message is ingested into the database before being processed by AutoAnswer services
        slackBotAutoAnswerWebhookEventProcessor.processAutoAnswer(
            payload = payload,
        )
    }
}

private fun MessageFileShareEvent.toSlackMessageEventContext(): SlackWebhookEventContext.SlackMessageEventContext {
    return SlackWebhookEventContext.SlackMessageEventContext(
        // displayAsBot can be NULL
        isBot = this.displayAsBot == true,
        slackTs = this.ts,
        slackThreadTs = this.threadTs,
        text = this.text,
        blocks = this.blocks,
    )
}
