package com.nextchaptersoftware.slack.webhook.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.event.SlackEventType
import com.nextchaptersoftware.slack.event.SlackPayloadExtractor
import com.nextchaptersoftware.slack.event.SlackPayloadParser
import com.nextchaptersoftware.slack.webhook.queue.payloads.SlackWebhookEvent
import com.nextchaptersoftware.slack.webhook.services.SlackMessageEventService
import com.slack.api.app_backend.events.payload.MessageBotPayload
import com.slack.api.model.event.MessageBotEvent

class MessageBotEventHandler(
    private val slackMessageEventService: SlackMessageEventService,
) : TypedEventHandler<SlackWebhookEvent.SlackWebhookApiEvent> {
    override suspend fun handle(event: SlackWebhookEvent.SlackWebhookApiEvent): Boolean {
        val webhookEventPayload = SlackPayloadExtractor.extract(event.body) ?: return false

        if (SlackEventType.isEventType(
                webhookEventPayload,
                MessageBotEvent.TYPE_NAME,
                MessageBotEvent.SUBTYPE_NAME,
            )
        ) {
            val messagePayload = SlackPayloadParser.parsePayload<MessageBotPayload>(webhookEventPayload) ?: return false
            slackMessageEventService.process(payload = messagePayload)
            return true
        }

        return false
    }
}
