package com.nextchaptersoftware.slack.webhook.services

import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackFileEventIngestionService
import com.nextchaptersoftware.slack.webhook.services.models.SlackWebhookEventContext
import com.slack.api.app_backend.events.payload.FileCreatedPayload

class SlackFileCreatedEventService(
    private val slackFileEventIngestionService: SlackFileEventIngestionService,
) {
    suspend fun process(payload: FileCreatedPayload) = withLoggingContextAsync(
        "slackTeamId" to payload.teamId,
        "fileId" to payload.event?.fileId,
        "type" to payload.event?.type,
        "subType" to payload.event?.subtype,
    ) {
        slackFileEventIngestionService.process(
            context = SlackWebhookEventContext.SlackFileEventContext.SlackFileIngestEventContext(
                slackExternalTeamId = payload.teamId,
                slackFileId = payload.event.fileId,
            ),
        )
    }
}
