package com.nextchaptersoftware.partitioning

import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class NoopContentPartitioningTest {

    private val partitioner = NoopContentPartitioning()

    @Test
    fun `partitions anything`() {
        val input = Random.nextBytes(100).toString()
        assertThat(partitioner.partition(input)).isEqualTo(listOf(input))
    }

    @Test
    fun `partitions empty`() {
        val input = ""
        assertThat(partitioner.partition(input)).isEqualTo(listOf(input))
    }
}
