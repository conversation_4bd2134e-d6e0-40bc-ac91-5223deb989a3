package com.nextchaptersoftware.partitioning

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * A partitioning algorithm that greedily adds items to the current partition until it is full.
 */
class GreedyListPartitioning(
    override val maxChunkSize: Int,
) : ListPartitioning {

    override fun partition(
        items: List<String>,
        separator: String,
    ): Flow<String> {
        val remaining = items.toMutableList()

        return flow {
            var currentPartition = StringBuilder()
            while (remaining.isNotEmpty()) {
                // If the current partition is full, emit it and start a new one.
                if (currentPartition.isNotEmpty()) {
                    if (currentPartition.length + separator.length + remaining.first().length > maxChunkSize) {
                        emit(currentPartition.toString())
                        currentPartition = StringBuilder()
                    }
                }

                // Add the next item to the current partition.
                if (currentPartition.isNotEmpty()) {
                    currentPartition.append(separator)
                }
                currentPartition.append(remaining.removeFirst())
            }

            // Emit the last partition.
            if (currentPartition.isNotEmpty()) {
                emit(currentPartition.toString())
            }
        }
    }
}
