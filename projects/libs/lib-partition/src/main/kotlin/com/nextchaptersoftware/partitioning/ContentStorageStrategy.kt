package com.nextchaptersoftware.partitioning

import com.nextchaptersoftware.db.models.DocumentType

class ContentStorageStrategy {
    fun shouldStoreContentAsMetadata(
        documentType: DocumentType,
    ): Boolean {
        return when (documentType) {
            DocumentType.Documentation, DocumentType.Code -> true
            DocumentType.PullRequest, DocumentType.Thread -> false
        }
    }
}
