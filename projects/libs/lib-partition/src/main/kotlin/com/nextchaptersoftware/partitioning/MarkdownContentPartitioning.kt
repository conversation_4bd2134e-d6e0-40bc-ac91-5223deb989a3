package com.nextchaptersoftware.partitioning

import com.nextchaptersoftware.config.GlobalConfig

/**
 * Limitations
 *  - doesn't handle tables
 *  - doesn't handle code blocks
 *  - doesn't handle lists
 *  - doesn't handle links
 *  - doesn't handle blockquotes
 */
class MarkdownContentPartitioning(
    private val maxChunkSize: Int = GlobalConfig.INSTANCE.embedding.maxPartitionSizeInBytes,
) : ContentPartitioning {

    companion object {
        private val HEADING_LEVELS = 1..6
    }

    override fun partition(content: String): List<String> {
        return merge(split(content))
    }

    /**
     * Split the content into chunks until the chunks are small enough.
     */
    private fun split(content: String): List<String> {
        return splitByHeading(content).flatMap { partition ->
            splitByPatterns(
                partition,
                listOf(
                    "\n\n\n",
                    "\n\n",
                    "\n",
                    """\.  """,
                    """\. """,
                    "; ",
                    ", ",
                    " ",
                ),
            )
        }
            .filter { it.isNotEmpty() }
            .filter { it.length <= maxChunkSize }
    }

    /**
     * Merge adjacent chunks that are too small.
     */
    private fun merge(chunks: List<String>): List<String> {
        val merged = mutableListOf<String>()
        var currentGroup = mutableListOf<String>()
        var currentGroupSize = 0

        fun flush() {
            if (currentGroup.isNotEmpty()) {
                merged.add(currentGroup.joinToString(""))
            }
            currentGroup = mutableListOf()
            currentGroupSize = 0
        }

        chunks.forEach { chunk ->
            if (currentGroupSize + chunk.length > maxChunkSize) {
                flush()
            }
            currentGroup += chunk
            currentGroupSize += chunk.length
        }
        flush()

        return merged
    }

    private fun splitByHeading(data: String, level: Int = 1): List<String> {
        if (level !in HEADING_LEVELS) {
            return listOf(data)
        }

        /*
         * No need to split if the data is already small enough.
         */
        if (data.length <= maxChunkSize) {
            return listOf(data)
        }

        /*
         * If no headings of the current level are found, try the next level.
         */
        val lines = data.split("\n")
        val heading = "#".repeat(level) + " "
        if (lines.none { it.startsWith(heading) }) {
            return splitByHeading(data, level + 1)
        }

        /*
         * Split on headings of the current level.
         */
        val partitions = mutableListOf<String>()
        var currentPartition = mutableListOf<String>()

        fun flush() {
            if (currentPartition.isNotEmpty()) {
                partitions.add(currentPartition.joinToString(separator = "\n", postfix = "\n"))
            }
            currentPartition = mutableListOf()
        }

        lines.forEach { line ->
            if (line.startsWith(heading)) {
                flush()
            }
            currentPartition += line
        }
        flush()

        /*
         * Recursively partition, if necessary.
         */
        return merge(partitions.flatMap { splitByHeading(it, level + 1) })
    }

    private fun splitByPatterns(data: String, patterns: List<String>): List<String> {
        if (patterns.isEmpty()) {
            return listOf(data)
        }

        /*
         * No need to split if the data is already small enough.
         */
        if (data.length <= maxChunkSize) {
            return listOf(data)
        }

        /*
         * Split on the first pattern.
         */
        val currentPattern = patterns.first()
        val partitions = data
            .split(currentPattern)
            .let { splits ->
                splits.dropLast(1).map { "$it$currentPattern" } + splits.last()
            }

        /*
         * Recursively partition, if necessary.
         */
        return merge(partitions.flatMap { splitByPatterns(it, patterns.drop(1)) })
    }
}
