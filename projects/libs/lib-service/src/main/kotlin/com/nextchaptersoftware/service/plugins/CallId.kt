package com.nextchaptersoftware.service.plugins

import io.ktor.http.HttpHeaders
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.callid.CallId
import io.ktor.server.plugins.callid.generate

fun Application.configureCallId() {
    install(CallId) {
        retrieveFromHeader(HttpHeaders.XRequestId)
        retrieveFromHeader("X-B3-TraceId")

        // The X-B3-TraceId header is encoded as 32 or 16 lower-hex characters.
        generate(length = 16, dictionary = "abcdef0123456789")

        replyToHeader(HttpHeaders.XRequestId)
    }
}
