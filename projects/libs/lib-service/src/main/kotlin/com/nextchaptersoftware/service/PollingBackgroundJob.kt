package com.nextchaptersoftware.service

import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.trace.coroutine.withSpan
import com.nextchaptersoftware.trace.service.attributes.ServiceAttributes
import io.opentelemetry.api.common.Attributes
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class PollingBackgroundJob(
    val interval: Duration = 1.seconds,
    val disabled: suspend () -> <PERSON><PERSON>an = { false },
    private val job: BackgroundJob,
) : Background<PERSON><PERSON> {
    override val name: String
        get() = "Polling on interval: $interval for: ${job.name}"

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var runningJob: Job? = null

    private val mutex = Mutex()

    @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
    override suspend fun run() {
        runningJob = scope.launch {
            while (isActive) {
                if (mutex.isLocked) {
                    LOGGER.debugAsync { "Mutex is already locked" }
                }
                mutex.withLock {
                    runCatching {
                        withSpan(
                            spanName = "PollingBackgroundJob",
                            attributes = Attributes.of(ServiceAttributes.SERVICE_POLLING_BACKGROUND_JOB_NAME, job.name),
                        ) {
                            when (disabled()) {
                                true -> LOGGER.debugAsync { "PollingBackgroundJob is disabled" }
                                else -> job.run()
                            }
                        }
                    }.onFailure {
                        LOGGER.errorAsync(
                            it,
                            "job" to this::class.java.simpleName,
                        ) { "PollingBackgroundJob failed due to exception for job" }
                    }
                }
                delay(interval)
            }
            LOGGER.debugAsync { "PollingBackgroundJob died" }
        }
    }

    override suspend fun shutdownGracefully() {
        mutex.withLock { // Force waits on job completion
            job.shutdownGracefully()
            runningJob?.cancelAndJoin()
            scope.cancel()
        }
    }

    override suspend fun terminate() {
        job.terminate()
        scope.cancel()
    }
}

/**
 * @see PollingBackgroundJob
 */
fun BackgroundJob.polling(
    interval: Duration,
) = PollingBackgroundJob(
    job = this,
    interval = interval,
)
