package com.nextchaptersoftware.service

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.lifecycle.ShutdownHook

interface BackgroundJob : ShutdownHook {

    val name: String
        get() = javaClass.simpleName

    suspend fun run()

    override suspend fun shutdownGracefully() {}

    suspend fun terminate() {}
}

fun createBackgroundJob(
    name: String,
    eventDequeueService: EventDequeueService,
): BackgroundJob {
    return object : BackgroundJob {
        override val name: String = name

        override suspend fun run() = eventDequeueService.process()
    }
}
