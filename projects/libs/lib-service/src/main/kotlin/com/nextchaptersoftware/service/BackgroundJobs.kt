package com.nextchaptersoftware.service

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import io.ktor.server.application.Application
import io.ktor.server.application.BaseApplicationPlugin
import io.ktor.util.AttributeKey
import java.io.Closeable
import kotlin.concurrent.thread
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class BackgroundJobs(configuration: Configuration) : Closeable {
    private val jobs = configuration.jobs

    data class Configuration(
        var jobs: List<BackgroundJob>? = null,
    )

    companion object Plugin : BaseApplicationPlugin<Application, Configuration, BackgroundJobs> {
        override val key: AttributeKey<BackgroundJobs> = AttributeKey("BackgroundJobs")
        private val SUPERVISOR = SupervisorJob()

        override fun install(pipeline: Application, configure: Configuration.() -> Unit): BackgroundJobs {
            val configuration = Configuration().apply(configure)
            val backgroundJobs = BackgroundJobs(configuration)
            thread(name = "BackgroundJobExecutor", start = true) {
                runBlocking(SUPERVISOR) {
                    configuration.jobs?.forEach { job ->
                        launch(Dispatchers.IO + CoroutineName(job.name)) {
                            runSuspendCatching {
                                job.run()
                            }.onFailure {
                                LOGGER.errorAsync(
                                    it,
                                    "job" to this::class.java.simpleName,
                                ) { "BackgroundJob failed due to exception for job" }
                            }.getOrThrow()
                        }
                    }
                }
            }
            return backgroundJobs
        }
    }

    override fun close() {
        runBlocking {
            jobs?.let { jobs ->
                jobs.forEach { it.shutdownGracefully() }
            }
        }
    }
}
