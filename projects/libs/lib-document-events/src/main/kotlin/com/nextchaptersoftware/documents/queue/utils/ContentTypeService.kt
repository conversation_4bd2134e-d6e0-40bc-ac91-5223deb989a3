package com.nextchaptersoftware.documents.queue.utils

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import io.ktor.http.ContentType
import org.apache.tika.Tika

class ContentTypeService {
    private val tika = Tika()

    fun contentTypeFrom(body: String): ContentType {
        val content = runSuspendCatching {
            body.base64DecodeAsByteArray()
        }.getOrElse {
            body.toByteArray()
        }

        val mimeType = tika.detect(content)

        return ContentType.parse(mimeType)
    }
}
