package com.nextchaptersoftware.linear.ingestion.services

import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.api.threads.services.ThreadUnreadService
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService

class LinearThreadFinalizationService(
    private val threadService: ThreadService,
    private val threadUnreadService: ThreadUnreadService,
    private val indexingAndEmbeddingService: IndexingAndEmbeddingService,
) {
    suspend fun finalizeThreadCreation(threadId: ThreadId) {
        threadService.updateLastMessageCreatedAt(threadId = threadId)
        threadUnreadService.setLatestMessageAllThreadUnreads(threadId = threadId)
        indexingAndEmbeddingService.indexThread(threadId = threadId)
    }
}
