package com.nextchaptersoftware.maintenance.installation

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeLinearOrganization
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationModel
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamModel
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class InstallationCleanupTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var installation: InstallationDAO

    private suspend fun setup() {
        org = makeOrg()
        installation = makeInstallation(org = org)

        makeLinearOrganization(installation = installation)
        makeSlackTeam(installation = installation)

        repeat(INITIAL_SIZE) {
            makeThread(installation = installation)
        }
    }

    @Test
    fun `LinearInstallationCleanup should delete in batches`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            assertThat(ThreadDAO.find { ThreadModel.installation eq installation.id.value }.count()).isEqualTo(INITIAL_SIZE.toLong())
            assertThat(LinearOrganizationDAO.find { LinearOrganizationModel.installation eq installation.id.value }.count()).isOne()
        }

        val cleanup = LinearInstallationCleanup(batchSize = BATCH_SIZE)

        cleanup.cleanup(installation.asDataModel())

        suspendedTransaction {
            assertThat(ThreadDAO.find { ThreadModel.installation eq installation.id.value }.count()).isZero()
            assertThat(LinearOrganizationDAO.find { LinearOrganizationModel.installation eq installation.id.value }.count()).isZero()
        }
    }

    @Test
    fun `SlackInstallationCleanUp should delete in batches`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            assertThat(ThreadDAO.find { ThreadModel.installation eq installation.id.value }.count()).isEqualTo(INITIAL_SIZE.toLong())
            assertThat(SlackTeamDAO.find { SlackTeamModel.installation eq installation.id.value }.count()).isOne()
        }

        val cleanup = SlackInstallationCleanUp(batchSize = BATCH_SIZE)
        cleanup.cleanup(installation.asDataModel())

        suspendedTransaction {
            assertThat(ThreadDAO.find { ThreadModel.installation eq installation.id.value }.count()).isZero()
            assertThat(SlackTeamDAO.find { SlackTeamModel.installation eq installation.id.value }.count()).isZero()
        }
    }

    companion object {
        private const val BATCH_SIZE = 10
        private const val INITIAL_SIZE = 500
    }
}
