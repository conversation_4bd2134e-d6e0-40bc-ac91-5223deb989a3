package com.nextchaptersoftware.confluence

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.confluence.AtlassianDocumentFormatConverter.asMarkdown
import com.nextchaptersoftware.confluence.models.Page
import com.nextchaptersoftware.confluence.models.Results
import com.nextchaptersoftware.test.utils.TestUtils
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

@Suppress("MaxLineLength")
class AtlassianDocumentFormatConverterTest {
    @Test
    fun asMarkdown() {
        val raw = TestUtils.getResource(this, "/pages.json")
        val pages = raw.decode<Results<Page>>()
        val page = pages.results.first { it.id == "5865473" }
        val result = page.body.atlasDocFormat?.value?.asMarkdown()
        assertThat(result).isEqualTo(
            "> This is a block quote\n" +
                "> across multiple lines\n" +
                "\n" +
                "- This is a \n" +
                "- bullet list\n" +
                "\n" +
                "```\n" +
                "This is a \n" +
                "code block\n" +
                "```\n" +
                "\n" +
                "# This is a header\n" +
                "\n" +
                "1. this is an\n" +
                "2. ordered list\n" +
                "\n" +
                "This is an info panel\n" +
                "\n" +
                "This is _just_ a **paragraph** with words and `code`, <sup>superscript</sup>, <sub>subscript</sub>, and ~strikethroughs~.\n" +
                "\n" +
                "This is a [link](http://getunblocked.com/).\n" +
                "\n" +
                "**This** **Is** **A**\n" +
                "table with rows\n" +
                "columns and cells",
        )
    }
}
