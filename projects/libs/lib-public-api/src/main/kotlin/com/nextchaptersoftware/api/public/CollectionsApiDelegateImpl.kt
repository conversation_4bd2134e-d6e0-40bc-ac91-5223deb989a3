package com.nextchaptersoftware.api.public

import com.nextchaptersoftware.api.integration.extension.services.CollectionService
import com.nextchaptersoftware.api.public.RoutingContextExtensions.orgId
import com.nextchaptersoftware.api.public.models.Collection as ApiCollection
import com.nextchaptersoftware.api.public.models.CollectionRequest
import com.nextchaptersoftware.api.utils.CallExtensions.addResponseHeader
import com.nextchaptersoftware.db.cursors.CollectionCursor
import com.nextchaptersoftware.db.cursors.OpaqueCursor
import com.nextchaptersoftware.db.models.Collection
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.environment.PublicApiVersion
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.http.LinkHeader
import io.ktor.http.append
import io.ktor.server.routing.RoutingContext
import kotlin.reflect.full.memberProperties
import org.openapitools.server.Resources

class CollectionsApiDelegateImpl(
    private val collectionService: CollectionService,
    private val urlBuilderProvider: UrlBuilderProvider,
) : CollectionsApiDelegateInterface {

    override suspend fun listCollections(
        context: RoutingContext,
        input: Resources.listCollections,
    ): List<ApiCollection> {
        val orgId = context.orgId

        val after = input.after
            ?.let<String, CollectionCursor>(OpaqueCursor::decode)
            ?.let(OpaqueCursor::toCursor)

        val before = input.before
            ?.let<String, CollectionCursor>(OpaqueCursor::decode)
            ?.let(OpaqueCursor::toCursor)

        val limit = Limiter.applyTo(input.limit)

        val collections = collectionService.list(
            orgId = orgId,
            limit = limit + 1, // fetch one more than the limit to determine if there is a next page
            after = after,
            before = before,
        )

        val page = Paginator.toPageWithMaybeNext(items = collections, limit = limit)

        context.call.addResponseHeader(
            Headers.build {
                page.onNext { next ->
                    append(
                        HttpHeaders.Link,
                        LinkHeader(
                            linkListCollections(
                                limit = input.limit,
                                after = next,
                            ),
                            "next",
                        ),
                    )
                }
            },
        )

        return page.items.map {
            it.asCollection
        }
    }

    private fun linkListCollections(
        limit: Int?,
        before: Collection? = null,
        after: Collection? = null,
    ): String {
        return urlBuilderProvider
            .publicApi(version = PublicApiVersion.V1)
            .withListCollections(
                limit = limit,
                before = before?.createdAt?.let(::CollectionCursor)?.let(OpaqueCursor::toCursor)?.value,
                after = after?.createdAt?.let(::CollectionCursor)?.let(OpaqueCursor::toCursor)?.value,
            )
            .build()
            .toString()
    }

    override suspend fun createCollection(
        context: RoutingContext,
        input: Resources.createCollection,
        body: CollectionRequest,
    ): ApiCollection {
        val name = body.name ?: throw BadRequestException("name is required")
        val description = body.description ?: throw BadRequestException("description is required")
        val iconUrl = body.iconUrl ?: throw BadRequestException("iconUrl is required")

        return runSuspendCatching {
            collectionService.create(
                orgId = context.orgId,
                installationId = InstallationId.random(),
                name = name,
                description = description,
                iconUrl = iconUrl,
            ).asCollection
        }.getOrElse {
            when (it is UserVisibleException) {
                true -> throw BadRequestException(message = it.title)
                else -> throw BadRequestException()
            }
        }
    }

    override suspend fun saveCollection(
        context: RoutingContext,
        input: Resources.saveCollection,
        body: CollectionRequest,
    ) {
        if (!hasNonNullProperty(body)) {
            throw BadRequestException("At least one property is required")
        }

        collectionService.update(
            orgId = context.orgId,
            installationId = input.collectionId.let(::InstallationId), // input.collectionId == InstallationId of the collection's installation
            name = body.name,
            description = body.description,
            iconUrl = body.iconUrl,
        )
    }

    override suspend fun getCollection(
        context: RoutingContext,
        input: Resources.getCollection,
    ): ApiCollection {
        return collectionService.get(orgId = context.orgId, installationId = input.collectionId.let(::InstallationId))?.asCollection
            ?: throw NotFoundException()
    }

    override suspend fun deleteCollection(
        context: RoutingContext,
        input: Resources.deleteCollection,
    ) {
        collectionService.delete(orgId = context.orgId, installationId = input.collectionId.let(::InstallationId))
    }
}

val Collection.asCollection: ApiCollection
    get() = ApiCollection(
        id = installationId.value, // ApiCollection.id == InstallationId of the collection's installation
        name = name,
        description = description,
        iconUrl = iconUrl.asString,
    )

inline fun <reified T : Any> hasNonNullProperty(instance: T): Boolean {
    return T::class.memberProperties.any { property ->
        when (val value = property.get(instance)) {
            is String -> value.isNotBlank()
            else -> value != null
        }
    }
}
