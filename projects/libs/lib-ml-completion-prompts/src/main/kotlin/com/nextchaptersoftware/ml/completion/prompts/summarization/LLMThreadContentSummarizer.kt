package com.nextchaptersoftware.ml.completion.prompts.summarization

import com.nextchaptersoftware.insight.index.model.ThreadInsightIndexContentModel

class LLMThreadContentSummarizer(
    val contentSummarizer: LLMContentSummarizer,
) {
    suspend fun summarize(context: ThreadInsightIndexContentModel): String {
        val threadContents = buildList {
            add(context.title)
            addAll(context.contents)
        }
        val content = threadContents.joinToString("\n")
        return contentSummarizer.summarize(content)
    }
}
