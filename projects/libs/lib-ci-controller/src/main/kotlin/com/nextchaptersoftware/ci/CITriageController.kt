package com.nextchaptersoftware.ci

import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.ci.services.CIRepoControlService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType

class CITriageController(
    private val capabilityValidation: CapabilityValidation,
    private val ciRepoControlService: CIRepoControlService = CIRepoControlService(),
    private val clientConfigService: ClientConfigService,
    private val orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val orgStore: OrgStore = Stores.orgStore,
) : CITriageControllerInterface {

    override suspend fun ciEnabled(
        orgId: OrgId,
        repo: Repo,
        orgMemberId: OrgMemberId,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
    ): Boolean {
        if (repo.isPublic == true) {
            return false
        }

        // Feature flag check
        if (!clientConfigService.computeMergedCapabilityForType(personId = null, orgId = orgId, type = ClientCapabilityType.FeatureConfigureCI)) {
            return false
        }

        // Plan check: Trial expired
        if (orgBillingStore.isTrialExpired(orgId = orgId)) {
            return false
        }

        // Plan check: CI enabled
        if (!capabilityValidation.allowsCapability(orgId, PlanCapabilityType.CI)) {
            return false
        }

        // Org exists
        val org = orgStore.findById(orgId = orgId)
            ?: return false

        // Org enabled
        if (org.isPending) {
            return false
        }

        // Repo enabled
        if (!ciRepoControlService.isRepoEnabled(repo = repo, ciInstallationId = ciInstallationId, scmInstallationId = scmInstallationId)) {
            return false
        }

        // Org member exists
        val orgMember = orgMemberStore.findById(id = orgMemberId)
            ?: return false

        // Org member enabled
        if (!orgMember.enableCiTriage) {
            return false
        }

        return true
    }

    override suspend fun isTriageEnabled(
        orgId: OrgId,
    ): Boolean {
        return orgSettingsStore.getEnableTriagePipeline(orgId = orgId)
    }
}
