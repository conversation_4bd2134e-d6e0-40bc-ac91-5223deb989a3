package com.nextchaptersoftware.trace.coroutines

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test

class CoroutineExtensionsTest {

    @Test
    fun metrics() = runTest {
        val metrics = coroutineDispatcherMetrics()
        assertNotNull(metrics.poolSize.core)
        assertNotNull(metrics.poolSize.max)
        assertNotNull(metrics.workerStates.cpu)
        assertNotNull(metrics.workerStates.blocking)
        assertNotNull(metrics.workerStates.parked)
        assertNotNull(metrics.workerStates.dormant)
        assertNotNull(metrics.workerStates.terminated)
        assertNotNull(metrics.queues.globalCpu)
        assertNotNull(metrics.queues.globalBlocking)
        assertNotNull(metrics.controlState.createdWorkers)
        assertNotNull(metrics.controlState.blockingTasks)
        assertNotNull(metrics.controlState.cpusAcquired)
    }
}
