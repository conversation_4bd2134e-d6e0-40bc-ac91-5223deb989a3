@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.slack.ingestion.utils.prioritizer

import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.SlackTeamIngestionDAO
import com.nextchaptersoftware.db.stores.SlackTeamIngestionStore
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import kotlinx.datetime.Instant

class SlackTeamIngestionPrioritizer(
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val slackTeamIngestionStore: SlackTeamIngestionStore = Stores.slackTeamIngestionStore,
) : BaseSlackTeamPrioritizer<SlackTeamIngestionDAO>(
    slackTeamStore = slackTeamStore,
) {
    override suspend fun getIngestionBySlackTeamId(slackTeamId: SlackTeamId): SlackTeamIngestionDAO? {
        return slackTeamIngestionStore.getBySlackTeamId(
            slackTeamId = slackTeamId,
        )
    }

    override suspend fun sort(
        slackTeamsWithIngestion: List<SlackTeamWithIngestion<SlackTeamIngestionDAO>>,
    ): List<SlackTeamWithIngestion<SlackTeamIngestionDAO>> {
        return slackTeamsWithIngestion.sortedBy {
            it.slackIngestion?.lastFinishedAt ?: Instant.DISTANT_PAST
        }
    }
}
