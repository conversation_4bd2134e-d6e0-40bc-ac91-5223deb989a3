package com.nextchaptersoftware.slack.ingestion.services

import com.nextchaptersoftware.api.threads.services.ThreadUnreadServiceImpl
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ingestion.services.EmojiMarkdownPreProcessorService
import com.nextchaptersoftware.ingestion.services.MarkdownToMessageBodyService
import com.nextchaptersoftware.ingestion.services.StandardMarkdownMentionResolutionService
import com.nextchaptersoftware.ingestion.services.ThreadParticipantService
import com.nextchaptersoftware.semantic.bot.services.MessageMentionService
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackMessageRepliesVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackRequiredPullRequestsRelevancyVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackThreadSizeVerifier
import com.nextchaptersoftware.slack.services.SlackMemberResolutionService
import com.slack.api.model.Message
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class SlackChannelMessageAnalysisServiceTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO
    private lateinit var slackTeam: SlackTeamDAO

    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
        scmTeam = ModelBuilders.makeScmTeam(
            org = org,
            provider = Provider.GitHub,
            providerExternalId = Random.nextInt(100000, 200000).toString(),
        )
        repo = ModelBuilders.makeRepo(scmTeam = scmTeam, httpUrl = "https://example.com/org/repo")
        slackTeam = ModelBuilders.makeSlackTeam(org = org)
    }

    private val slackMessageMarkdownService = CompositeSlackMessageMarkdownService(
        slackMessageMarkdownServices = listOf(
            StandardSlackMessageMarkdownService(),
        ),
    )

    private val slackMessageBodyService = SlackMessageBodyService(
        markdownToMessageBodyService = MarkdownToMessageBodyService(
            markdownPreProcessorService = EmojiMarkdownPreProcessorService(),
            markdownMentionResolutionService = StandardMarkdownMentionResolutionService(
                memberStore = Stores.memberStore,
            ),
        ),
        slackMessageMarkdownService = StandardSlackMessageMarkdownService(),
    )
    private val slackPullRequestResolutionService = SlackPullRequestResolutionService(
        pullRequestStore = Stores.pullRequestStore,
        repoStore = Stores.repoStore,
    )
    private var slackMemberResolutionService: SlackMemberResolutionService = mock()
    private var slackMessageUrlService: SlackMessageUrlService = mock()

    private val slackMessageModelService = SlackMessageModelService(
        memberStore = Stores.memberStore,
        threadParticipantService = ThreadParticipantService(
            threadUnreadService = ThreadUnreadServiceImpl(threadUnreadStore = Stores.threadUnreadStore),
        ),
        slackMemberResolutionService = slackMemberResolutionService,
        slackMessageBodyService = slackMessageBodyService,
        messageStore = Stores.messageStore,
        messageMentionService = MessageMentionService(
            messageMentionStore = Stores.messageMentionStore,
        ),
        slackMessageUrlService = slackMessageUrlService,
    )

    private val slackChannelMessageAnalyzer = SlackChannelMessageAnalysisService(
        slackThreadModelService = SlackThreadModelService(
            threadStore = Stores.threadStore,
            threadPullRequestStore = Stores.threadPullRequestStore,
            memberStore = Stores.memberStore,
            threadParticipantService = ThreadParticipantService(
                threadUnreadService = mock(),
            ),
            slackMessageModelService = slackMessageModelService,
            slackMemberResolutionService = slackMemberResolutionService,
            slackMessageMarkdownService = slackMessageMarkdownService,
        ),
        slackPullRequestResolutionService = slackPullRequestResolutionService,
        slackPullRequestRelevancyVerifier = SlackRequiredPullRequestsRelevancyVerifier(),
        slackMessageRelevancyVerifier = SlackMessageRepliesVerifier(),
        slackThreadRelevancyVerifier = SlackThreadSizeVerifier(),
    )

    @Test
    fun `test slack message analyzer success`() = suspendingDatabaseTest {
        setup()
        val pullRequest = ModelBuilders.makePullRequest(repo = repo)
        val slackMessage1 = Message().apply {
            text = "hello man! https://example.com/org/repo/pull/${pullRequest.number}|PR"
            user = "richie"
            ts = "1675121645.486049"
            threadTs = "1675121645.486049"
            replyCount = 1
        }
        val slackMessage2 = Message().apply {
            text = "hello man! hello man! https://example.com/org/repo1/pull/${pullRequest.number}|PR"
            user = "richie"
            ts = "1675121645.486050"
            threadTs = "1675121645.486049"
        }

        val result = slackChannelMessageAnalyzer.analyze(
            org = org.asDataModel(),
            slackTeam = slackTeam.asDataModel(),
            slackMessage = slackMessage1,
            slackMessages = listOf(slackMessage1, slackMessage2),
        )

        assertThat(result).isExactlyInstanceOf(SlackChannelMessageAnalysisDecision.SlackChannelMessageWithPRsApproved::class.java)
    }

    @Test
    fun `test slack message analyzer success without approved prs - no pr link`() = suspendingDatabaseTest {
        setup()
        val slackMessage1 = Message().apply {
            text = "hello man!"
            user = "richie"
            ts = "1675121645.486049"
            threadTs = "1675121645.486049"
            replyCount = 1
        }

        val slackMessage2 = Message().apply {
            text = "hello man!"
            user = "richie"
            ts = "1675121645.486050"
            threadTs = "1675121645.486049"
        }

        val result = slackChannelMessageAnalyzer.analyze(
            org = org.asDataModel(),
            slackTeam = slackTeam.asDataModel(),
            slackMessage = slackMessage1,
            slackMessages = listOf(slackMessage1, slackMessage2),
        )

        assertThat(result).isExactlyInstanceOf(SlackChannelMessageAnalysisDecision.SlackChannelMessagesWithoutPRsApproved::class.java)
    }

    @Test
    fun `test slack message analyzer failure - not enough messages`() = suspendingDatabaseTest {
        setup()
        val slackMessage1 = Message().apply {
            text = "hello man!"
            user = "richie"
            ts = "1675121645.486049"
            threadTs = "1675121645.486049"
        }

        val result = slackChannelMessageAnalyzer.analyze(
            org = org.asDataModel(),
            slackTeam = slackTeam.asDataModel(),
            slackMessage = slackMessage1,
            slackMessages = listOf(slackMessage1),
        )

        assertThat(result).isExactlyInstanceOf(SlackChannelMessageAnalysisDecision.SlackMessageRelevancyFailed::class.java)
    }

    @Test
    fun `test slack message analyzer failure - not enough thread messages`() = suspendingDatabaseTest {
        setup()
        val slackMessage1 = Message().apply {
            text = "hello man!"
            user = "richie"
            ts = "1675121645.486049"
            threadTs = "1675121645.486049"
            replyCount = 2
        }

        val result = slackChannelMessageAnalyzer.analyze(
            org = org.asDataModel(),
            slackTeam = slackTeam.asDataModel(),
            slackMessage = slackMessage1,
            slackMessages = listOf(slackMessage1),
        )

        assertThat(result).isExactlyInstanceOf(SlackChannelMessageAnalysisDecision.SlackThreadRelevancyFailed::class.java)
    }
}
