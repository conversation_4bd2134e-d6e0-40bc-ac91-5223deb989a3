package com.nextchaptersoftware.utils

import java.io.InputStreamReader
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement

object ResourceLoader {
    fun loadResourceAsString(resourcePath: String): String {
        val resourceStream = this::class.java.getResourceAsStream(resourcePath)
            ?: throw IllegalArgumentException("Resource not found: $resourcePath")
        return InputStreamReader(resourceStream).use { it.readText() }
    }

    fun loadResourceAsJsonElement(resourcePath: String): JsonElement {
        val jsonString = loadResourceAsString(resourcePath)
        return Json.parseToJsonElement(jsonString)
    }
}
