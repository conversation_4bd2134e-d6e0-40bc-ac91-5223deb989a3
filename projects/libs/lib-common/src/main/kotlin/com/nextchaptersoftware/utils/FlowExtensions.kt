package com.nextchaptersoftware.utils

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.take

object FlowExtensions {

    fun <T> suspendedFlow(block: suspend () -> Flow<T>?): Flow<T> = flow {
        block()?.also { emitAll(it) }
    }

    /**
     * When provided, returns a flow that contains the first [count] elements. Otherwise, returns the original flow.
     *
     * @see Flow.take
     */
    fun <T> Flow<T>.takeOnNotNull(count: Int?): Flow<T> = let { flow ->
        when (count) {
            null -> flow
            else -> flow.take(count)
        }
    }
}
