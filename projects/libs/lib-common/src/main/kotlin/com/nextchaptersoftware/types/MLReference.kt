package com.nextchaptersoftware.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@JvmInline
@Serializable(with = MLReferenceSerializer::class)
value class MLReference(val value: Int) {
    val asString: String
        get() = """REF-$value"""

    companion object {
        fun from(text: String): Set<MLReference> {
            val regex = Regex("""\bREF-(\d{1,4})\b""")
            val matchResults = regex.findAll(text)

            return matchResults.map {
                MLReference(it.groupValues[1].toInt())
            }.toSet()
        }
    }
}

object MLReferenceSerializer : KSerializer<MLReference> {
    override val descriptor: SerialDescriptor
        get() = PrimitiveSerialDescriptor("MLReference", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: MLReference) {
        encoder.encodeString(value.asString)
    }

    override fun deserialize(decoder: Decoder): MLReference {
        val value = decoder.decodeString().removePrefix("REF-").toInt()
        return MLReference(value)
    }
}
