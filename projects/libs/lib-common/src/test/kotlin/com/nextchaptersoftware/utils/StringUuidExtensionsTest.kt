package com.nextchaptersoftware.utils

import com.nextchaptersoftware.utils.StringUuidExtensions.isHex
import java.util.UUID
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

internal class StringUuidExtensionsTest {
    private val validUuid = UUID.randomUUID().toString()

    @Test
    fun testAsUUID() {
        assertThrows<Exception> { "".asUUID() }
        assertThrows<Exception> { "foo".asUUID() }
        assertThrows<Exception> { " $validUuid ".asUUID() }

        assertThat(validUuid.asUUID().toString()).isEqualTo(validUuid)
        assertThat(validUuid.uppercase().asUUID().toString()).isEqualTo(validUuid)
    }

    @Test
    fun testIsUUID() {
        assertThat("".isUUID()).isFalse
        assertThat("foo".isUUID()).isFalse
        assertThat(" $validUuid ".isUUID()).isFalse

        assertThat(validUuid.isUUID()).isTrue
        assertThat(validUuid.uppercase().isUUID()).isTrue
    }

    @Test
    fun testAsUUIDOrNull() {
        assertThat("".asUUIDOrNull()).isNull()
        assertThat("foo".asUUIDOrNull()).isNull()
        assertThat(" $validUuid ".asUUIDOrNull()).isNull()

        assertThat(validUuid.asUUIDOrNull()).isNotNull
        assertThat(validUuid.uppercase().asUUIDOrNull()).isNotNull
    }

    @Test
    fun testAsUUIDs() {
        assertThrows<Exception> { listOf("foo").asUUIDs() }

        listOf(validUuid, validUuid.uppercase()).asUUIDs().also { ids ->
            assertThat(ids).hasSize(2)
            assertThat(ids.map { it.toString() }).containsExactlyInAnyOrder(validUuid, validUuid)
        }
    }

    @Test
    fun testUuidFromEmptyString() {
        val expectedUuid = "d41d8cd9-8f00-3204-a980-0998ecf8427e".asUUID()
        val resultUuid = UUID.nameUUIDFromBytes("".toByteArray())
        assertThat(resultUuid).isEqualTo(expectedUuid)
    }

    @Test
    fun testUuidFromValidString() {
        val expectedUuid = "acbd18db-4cc2-385c-adef-654fccc4a4d8".asUUID()
        val resultUuid = UUID.nameUUIDFromBytes("foo".toByteArray())
        assertThat(resultUuid).isEqualTo(expectedUuid)
    }

    @Test
    fun testUuidFromFile() {
        val expectedUuid = "d28d78b5-5ca2-351c-a518-ee8b163ad5df".asUUID()
        val repoId = "4b49f903-4bb6-4695-9fb0-b016f7d8e2ab"
        val filePath = "projects/libs/File.kt"
        val resultUuid = UUID.nameUUIDFromBytes("$repoId/$filePath".toByteArray())
        assertThat(resultUuid).isEqualTo(expectedUuid)
        assertThat(resultUuid).isEqualTo(StringUuidExtensions.uuidFromFile(repoId.asUUID(), filePath))
    }

    @Test
    fun testUuidFromString() {
        val expectedUuid = "5d41402a-bc4b-3a76-b971-9d911017c592".asUUID()
        val resultUuid = UUID.nameUUIDFromBytes("hello".toByteArray())
        assertThat(resultUuid).isEqualTo(expectedUuid)
        assertThat(resultUuid).isEqualTo(StringUuidExtensions.uuidFromString("hello"))
    }

    @Test
    fun testUuidToBytes() {
        val originalUuid = "d28d78b5-5ca2-351c-a518-ee8b163ad5df".asUUID()

        val bytes = originalUuid.asBytes.also { bytes ->
            assertThat(bytes).hasSize(16)
        }

        assertThat(bytes.asUUID).isEqualTo(originalUuid)
    }

    @Test
    fun isHex() {
        listOf(
            "",
            "0",
            "f",
            "0123456789ABCDEF",
            "0123456789abcdef",
        ).forEach {
            assertThat(it.isHex).isTrue()
        }

        listOf(
            " ",
            "g",
            " 0123456789abcdef",
        ).forEach {
            assertThat(it.isHex).isFalse()
        }
    }

    @Test
    fun asInstant() {
        val testUUID = "b54adc00-67f9-11d9-9669-0800200c9a66".asUUID()
        val date = testUUID.asInstant
        val result = Instant.parse("2005-01-16T20:03:37.024000Z")
        assertThat(date).isEqualTo(result)

        val nonDateUUID = "e149dae8-f04b-432b-b544-465a082be508".asUUID()
        val nonDate = nonDateUUID.asInstant
        assertThat(nonDate).isNull()
    }

    @Test
    fun asInstantMatchClient() {
        val jsUUID = "710b8000-041c-11e1-9234-0123456789ab".asUUID()
        val date = jsUUID.asInstant
        val result = Instant.parse("2011-11-01T00:00:00.0Z")
        assertThat(date).isEqualTo(result)
    }
}
