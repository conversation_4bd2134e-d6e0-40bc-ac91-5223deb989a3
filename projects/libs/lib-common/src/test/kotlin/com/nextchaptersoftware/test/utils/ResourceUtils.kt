package com.nextchaptersoftware.test.utils

import java.io.File
import java.io.InputStream
import java.net.URL
import java.util.zip.GZIPInputStream

/**
 * @see Class.getResource
 */
fun Class<*>.getResourceOrThrow(resourceName: String): URL {
    return getResource(resourceName)
        ?: throw IllegalArgumentException("Resource not found: '$resourceName'")
}

/**
 * @see Class.getResourceAsStream
 */
fun Class<*>.getResourceAsStreamOrThrow(resourceName: String): InputStream {
    return getResourceAsStream(resourceName)
        ?: throw IllegalArgumentException("Resource not found: '$resourceName'")
}

private fun Any.javaClass(): Class<*> {
    return when (this) {
        is Class<*> -> this
        else -> javaClass
    }
}

fun Any.getResourceAsFile(resourceName: String): File {
    val resource = javaClass().getResourceOrThrow(resourceName)
    return File(resource.toURI())
}

fun Any.getResourceAsText(resourceName: String): String {
    val stream = javaClass().getResourceAsStreamOrThrow(resourceName)
    val resource = when {
        resourceName.endsWith(".gz") -> stream.let(::GZIPInputStream)
        else -> stream
    }
    return resource.use { it.bufferedReader().readText() }
}
