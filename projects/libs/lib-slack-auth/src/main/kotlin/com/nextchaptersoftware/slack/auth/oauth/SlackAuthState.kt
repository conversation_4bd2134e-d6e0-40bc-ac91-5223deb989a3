package com.nextchaptersoftware.slack.auth.oauth

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.oauth.OAuthStateProvider
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.SlackUserConnectOrigin
import com.nextchaptersoftware.db.stores.Stores.sessionEventStore
import com.nextchaptersoftware.slack.session.SlackUserConnectSessionActionIdentifier
import com.nextchaptersoftware.utils.Base64.base64Decode
import com.nextchaptersoftware.utils.Base64.base64Encode
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import kotlin.time.Duration.Companion.minutes
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

suspend fun SlackUserConnectOrigin.Companion.fromPersonSession(
    personId: PersonId,
): SlackUserConnectOrigin {
    val startInstant = Instant.nowWithMicrosecondPrecision().minus(5.minutes)
    sessionEventStore.findPersonSessionActions(
        actionIds = listOf(
            SlackUserConnectSessionActionIdentifier.CONNECT_SLACK_ACCOUNT_LOGIN.actionId,
            SlackUserConnectSessionActionIdentifier.CHECK_CONNECT_INTEGRATION_SLACK.actionId,
        ),
        personId = personId,
        start = startInstant,
        end = null,
    ).let {
        if (it.isEmpty()) {
            return SlackUserConnectOrigin.DASHBOARD
        } else {
            return SlackUserConnectOrigin.SLACK
        }
    }
}

@Serializable
data class SlackAuthState(
    val orgId: OrgId,
    val identityId: IdentityId,
    val personId: PersonId,
    val installationId: InstallationId?,
    @Contextual val redirectUrl: Url?,
    @Contextual val origin: SlackUserConnectOrigin,
) : OAuthStateProvider {
    val isUserAuth get() = installationId != null

    override fun encodeAuthState(): String {
        return encodeAuthState(this)
    }

    companion object {
        fun encodeAuthState(slackAuthState: SlackAuthState): String {
            return slackAuthState.encode().base64Encode()
        }

        fun decodeAuthState(rawSlackAuthState: String): SlackAuthState {
            return rawSlackAuthState.base64Decode().decode()
        }
    }
}
