package com.nextchaptersoftware.auth.saml.config

import com.nextchaptersoftware.environment.BaseUrlBuilder
import io.ktor.http.Url

class SamlUrlBuilder(
    samlBaseUrl: Url,
    basePathSegments: List<String> = emptyList(),
) : BaseUrlBuilder(
    hostName = samlBaseUrl.host,
    port = samlBaseUrl.port,
    enableTls = true,
    basePathSegments = samlBaseUrl.rawSegments + basePathSegments,
) {
    fun withEndpoint(samlEndpoint: String) = apply {
        addPathSegments(samlEndpoint)
    }
}
