package com.nextchaptersoftware.regression.testing.queue.enqueue

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.activemq.models.messageProperties
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.regression.testing.queue.payloads.RegressionEvent
import kotlin.time.Duration

class RegressionEventEnqueueService(
    private val eventEnqueueService: EventEnqueueService,
) {
    fun enqueueEvent(
        event: RegressionEvent,
        priority: MessagePriority = MessagePriority.DEFAULT,
        withDelay: Duration? = null,
    ) {
        eventEnqueueService.enqueueEvent(
            body = event.encode(),
            priority = priority,
            properties = withDelay?.let {
                messageProperties { withScheduledDelay(it) }
            },
        )
    }
}
