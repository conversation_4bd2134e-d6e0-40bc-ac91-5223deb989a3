package com.nextchaptersoftware.jira.ingestion.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.jira.queue.payloads.JiraEvent
import com.nextchaptersoftware.log.kotlin.debugAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class JiraForgeEventHandler : TypedEventHandler<JiraEvent.JiraForgeEvent> {
    override suspend fun handle(event: JiraEvent.JiraForgeEvent): Boolean {
        LOGGER.debugAsync("siteId" to event.siteId) {
            "Handling Jira Forge event"
        }

        return true
    }
}
