package com.nextchaptersoftware.topic.ingestion.services.internal

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.ingestion.pipeline.store.keys.IngestionPipelineKeyProvider

class TopicIngestionPipelineKeyProvider : IngestionPipelineKeyProvider.TopicIngestionPipelineKeyProvider() {
    fun provide(
        orgId: OrgId,
        repoId: RepoId,
        vararg segments: String,
    ): List<String> {
        val pathSegments = listOf(orgId.toString(), repoId.toString(), *segments)
        return provide(*pathSegments.toTypedArray())
    }
}
