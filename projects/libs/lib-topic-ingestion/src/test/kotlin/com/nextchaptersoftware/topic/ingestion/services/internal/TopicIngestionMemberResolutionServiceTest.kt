package com.nextchaptersoftware.topic.ingestion.services.internal

import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.types.EmailAddress
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TopicIngestionMemberResolutionServiceTest : DatabaseTestsBase() {
    private val teamMemberStore = Stores.memberStore
    private val topicIngestionMemberResolutionService = TopicIngestionMemberResolutionService(
        memberStore = teamMemberStore,
    )
    private lateinit var scmTeam: ScmTeamDAO

    private lateinit var person1: PersonDAO
    private lateinit var identity1: IdentityDAO
    private lateinit var member1: MemberDAO
    private lateinit var associatedMember1: MemberDAO

    private lateinit var person2: PersonDAO
    private lateinit var identity2: IdentityDAO
    private lateinit var member2: MemberDAO
    private lateinit var associatedMember2: MemberDAO

    private lateinit var person3: PersonDAO
    private lateinit var identity3: IdentityDAO
    private lateinit var member3: MemberDAO
    private lateinit var associatedMember3: MemberDAO

    private lateinit var botIdentity: IdentityDAO
    private lateinit var botTeamMember: MemberDAO

    private suspend fun setup() {
        scmTeam = ModelBuilders.makeScmTeam()
        person1 = ModelBuilders.makePerson()
        identity1 = ModelBuilders.makeIdentity(displayName = "Richard Bresnan")
        member1 = ModelBuilders.makeMember(scmTeam = scmTeam, identity = identity1)
        associatedMember1 =
            ModelBuilders.makeMember(
                scmTeam = scmTeam,
                identity = ModelBuilders.makeIdentity(displayName = "Richie Bresnan", primaryEmail = EmailAddress.of("<EMAIL>")),
                association = member1.idValue,
            )

        person2 = ModelBuilders.makePerson()
        identity2 = ModelBuilders.makeIdentity(displayName = "Rashin Arab")
        member2 = ModelBuilders.makeMember(scmTeam = scmTeam, identity = identity2)
        associatedMember2 =
            ModelBuilders.makeMember(
                scmTeam = scmTeam,
                identity = ModelBuilders.makeIdentity(displayName = "Rashin Arab"),
                association = member2.idValue,
            )

        person3 = ModelBuilders.makePerson()
        identity3 = ModelBuilders.makeIdentity(displayName = "Ben Ng")
        member3 = ModelBuilders.makeMember(scmTeam = scmTeam, identity = identity3)
        associatedMember3 =
            ModelBuilders.makeMember(
                scmTeam = scmTeam,
                identity = ModelBuilders.makeIdentity(displayName = "Benjamin Ng"),
                association = member2.idValue,
            )

        botIdentity = ModelBuilders.makeIdentity(displayName = "Renovate", isBot = true)
        botTeamMember = ModelBuilders.makeMember(scmTeam = scmTeam, identity = botIdentity)
    }

    @Test
    fun `test team member resolution using primary association`() = suspendingDatabaseTest {
        setup()

        val foundTeamMember = topicIngestionMemberResolutionService.resolveMember(
            teamId = scmTeam.idValue,
            name = "Richie Bresnan",
            email = EmailAddress.random(),
        )

        assertThat(foundTeamMember).isNotNull
        assertThat(foundTeamMember?.isCurrentMember).isTrue()
    }

    @Test
    fun `test team member resolution using name similarity matching`() = suspendingDatabaseTest {
        setup()

        val foundTeamMember = topicIngestionMemberResolutionService.resolveMember(
            teamId = scmTeam.idValue,
            name = "Richard Bresnn",
            email = EmailAddress.random(),
        )

        assertThat(foundTeamMember).isNotNull
        assertThat(foundTeamMember?.isCurrentMember).isTrue()
    }

    @Test
    fun `test team member resolution using email matching`() = suspendingDatabaseTest {
        setup()

        val foundTeamMember = topicIngestionMemberResolutionService.resolveMember(
            teamId = scmTeam.idValue,
            name = "Richie Rich",
            email = EmailAddress.of("<EMAIL>"),
        )

        assertThat(foundTeamMember).isNotNull
        assertThat(foundTeamMember?.isCurrentMember).isTrue()
    }

    @Test
    fun `test team member bot is excluded`() = suspendingDatabaseTest {
        setup()

        val foundTeamMember = topicIngestionMemberResolutionService.resolveMember(
            teamId = scmTeam.idValue,
            name = "Renovate",
            email = EmailAddress.random(),
        )

        assertThat(foundTeamMember).isNull()
    }
}
