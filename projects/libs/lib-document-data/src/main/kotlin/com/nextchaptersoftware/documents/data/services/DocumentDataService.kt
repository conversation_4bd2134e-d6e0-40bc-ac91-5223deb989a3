package com.nextchaptersoftware.documents.data.services

import com.nextchaptersoftware.compress.Base64Compressed
import com.nextchaptersoftware.compress.CompressionBase64
import com.nextchaptersoftware.db.models.CollectionId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.documents.data.store.DocumentDataStore
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.utils.isGzipCompressedAndBase64Encoded
import com.sksamuel.hoplite.Secret
import java.util.UUID
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class DocumentDataService(
    private val dataStore: DocumentDataStore,
) {
    companion object {
        const val METADATA_TITLE = "title"
        const val METADATA_URL = "url"

        fun String.compressAndBase64Encode(): Base64Compressed {
            return when (this.isGzipCompressedAndBase64Encoded()) {
                true -> Base64Compressed(this)
                false -> CompressionBase64.compress(this)
            }
        }
    }

    suspend fun persist(
        orgId: OrgId,
        installationId: InstallationId,
        collectionId: CollectionId,
        documentId: UUID,
        body: Secret,
        metadata: Map<String, Secret>,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "collectionId" to collectionId,
        "documentId" to documentId,
    ) {
        runSuspendCatching {
            dataStore.put(
                orgId = orgId,
                installationId = installationId,
                collectionId = collectionId,
                documentId = documentId,
                body = Secret(body.value.compressAndBase64Encode().value),
                metadata = metadata,
            )
        }.onFailure {
            LOGGER.errorAsync(it) { "Failed to persist document data" }
            throw it
        }.onSuccess {
            LOGGER.traceAsync { "Finished document data persistence" }
        }
    }

    suspend fun delete(
        orgId: OrgId,
        installationId: InstallationId,
        collectionId: CollectionId,
        documentId: UUID,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
        "collectionId" to collectionId,
        "documentId" to documentId,
    ) {
        dataStore.delete(
            orgId = orgId,
            installationId = installationId,
            collectionId = collectionId,
            documentId = documentId,
        )
    }

    suspend fun get(
        orgId: OrgId,
        installationId: InstallationId,
        collectionId: CollectionId,
        documentId: UUID,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "installationId" to installationId,
        "collectionId" to collectionId,
        "documentId" to documentId,
    ) {
        dataStore.getWithMetadata(
            orgId = orgId,
            installationId = installationId,
            collectionId = collectionId,
            documentId = documentId,
        ).let { (body, metadata) ->
            val bodyDecompressed = Secret(CompressionBase64.decompress(Base64Compressed(body.value)))
            bodyDecompressed to metadata
        }
    }
}
