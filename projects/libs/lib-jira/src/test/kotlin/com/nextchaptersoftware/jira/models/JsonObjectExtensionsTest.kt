package com.nextchaptersoftware.jira.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.TestUtils
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JsonObjectExtensionsTest {

    @Test
    fun withJiraIssueFieldsPruned() {
        val raw = TestUtils.getResource(this, "/ResolvedIssue.json").decode<JsonObject>()
        assertThat(raw.containsKey("expand")).isTrue
        assertThat(raw.containsKey("self")).isTrue

        val rawFields = raw["fields"] as JsonObject
        assertThat(rawFields.containsKey("summary")).isTrue
        assertThat(rawFields["summary"].toString()).isEqualTo("\"Add a Jira sync job\"")
        assertThat(rawFields.containsKey("customfield_10034")).isTrue
        assertThat(rawFields["customfield_10034"]).isExactlyInstanceOf(JsonNull::class.java)
        assertThat(rawFields.containsKey("subtasks")).isTrue
        assertThat(rawFields["subtasks"] as JsonArray).isEmpty()
        assertThat(rawFields.containsKey("timetracking")).isTrue
        assertThat(rawFields["timetracking"] as JsonObject).isEmpty()

        val rawProject = rawFields["project"] as JsonObject
        assertThat(rawProject.containsKey("self")).isTrue
        assertThat(rawProject.containsKey("avatarUrls")).isTrue

        val rawIssueType = rawFields["issuetype"] as JsonObject
        assertThat(rawIssueType.containsKey("self")).isTrue
        assertThat(rawIssueType.containsKey("iconUrl")).isTrue

        val stripped = raw.withJiraIssueFieldsPruned()
        assertThat(stripped.containsKey("expand")).isFalse
        assertThat(stripped.containsKey("self")).isFalse

        val strippedFields = stripped["fields"] as JsonObject
        assertThat(strippedFields.containsKey("summary")).isTrue
        assertThat(strippedFields["summary"].toString()).isEqualTo("\"Add a Jira sync job\"")
        assertThat(strippedFields.containsKey("customfield_10034")).isFalse
        assertThat(strippedFields.containsKey("subtasks")).isFalse
        assertThat(strippedFields.containsKey("timetracking")).isFalse

        val strippedProject = strippedFields["project"] as JsonObject
        assertThat(strippedProject.containsKey("self")).isFalse
        assertThat(strippedProject.containsKey("avatarUrls")).isFalse

        val strippedIssueType = strippedFields["issuetype"] as JsonObject
        assertThat(strippedIssueType.containsKey("self")).isFalse
        assertThat(strippedIssueType.containsKey("iconUrl")).isFalse
    }
}
