package com.nextchaptersoftware.experts

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.repo.RepoAccessResult

interface ExpertService {
    suspend fun expertsForTopic(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dsacContext: DsacContext,
        topic: String,
    ): String?

    suspend fun expertsForFile(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dsacContext: DsacContext,
        file: String,
    ): String?

    suspend fun expertsForNamedFileEntity(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dsacContext: DsacContext,
        entity: String,
    ): String?

    suspend fun expertsForRepo(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        fuzzyRepoName: String,
        overrideRepoAccess: RepoAccessResult?,
    ): String?

    suspend fun expertsForTeam(orgId: OrgId): String?
}
