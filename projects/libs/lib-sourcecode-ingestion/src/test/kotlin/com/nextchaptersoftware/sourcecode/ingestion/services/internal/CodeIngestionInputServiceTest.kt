package com.nextchaptersoftware.sourcecode.ingestion.services.internal

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encodePretty
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.EmbeddingPlatform
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.ingestion.pipeline.config.IngestionPipelineConfig
import com.nextchaptersoftware.ingestion.pipeline.store.keys.IngestionPipelineType
import com.nextchaptersoftware.sourcecode.config.SourceCodeConfig
import com.nextchaptersoftware.sourcecode.ingestion.models.CodeIngestionInputPayloadConfig
import com.nextchaptersoftware.sourcecode.ingestion.models.EmbeddingPlatformConfig
import com.nextchaptersoftware.sourcecode.ingestion.pipeline.RepoCodeIngestionCompletion
import com.nextchaptersoftware.types.Hash
import com.sksamuel.hoplite.Secret
import io.ktor.http.Url
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CodeIngestionInputServiceTest {
    private val gConfig = GlobalConfig.INSTANCE
    private val ingestionPipelineConfig = IngestionPipelineConfig.INSTANCE
    private val sourceCodeConfig = SourceCodeConfig.INSTANCE
    private val service = CodeIngestionInputService()

    private val orgId = OrgId.fromString("*************-2222-2222-************")
    private val installationId = InstallationId.fromString("*************-4444-4444-************")
    private val repoId = RepoId.fromString("11111111-1111-1111-1111-111111111111")
    private val ingestionId = UUID.fromString("*************-3333-3333-************")

    private val config = CodeIngestionInputPayloadConfig.fromConfigs(
        config = gConfig,
        ingestionPipelineConfig = ingestionPipelineConfig,
        sourceCodeConfig = sourceCodeConfig,
        orgId = orgId,
        codeIngestionId = ingestionId,
        installationId = installationId,
        repoId = repoId,
        repoRevision = 0,
        repoFullName = "org/repo",
        repoHttpCloneUrl = Url("https://github.com/org/repo.git"),
        repoCloneAuth = Secret("Bearer secret"),
        provider = Provider.GitHub,
        embeddingPlatformConfigs = listOf(
            EmbeddingPlatformConfig(
                platform = EmbeddingPlatform.Pinecone,
                model = EmbeddingModel.E5Mistral,
                indexName = "source-code-hybrid",
            ),
        ),
    )

    @Test
    fun `test source code input payload encoding`() {
        val inputPayload = service.generateCodeIngestionInputPayload(
            config = config,
            continuation = RepoCodeIngestionCompletion(
                completedCommitSha = Hash.parse("cc94312f7b362c698d91e02f5c3d7ea6939692db"),
                completedCommitTimestamp = **********,
            ),
        )
        inputPayload.encodePretty().also { json ->
            assertThat(json).isEqualTo(
                """
                {
                    "TeamId": "*************-2222-2222-************",
                    "RepoId": "11111111-1111-1111-1111-111111111111",
                    "CodeIngestion": {
                        "UseLargeRunner": true,
                        "ProcessingJobName": "CodeIngestion-*************-3333-3333-************",
                        "ProcessOutput": "s3://${ingestionPipelineConfig.ingestionPipeline.s3.bucket}/${IngestionPipelineType.CODE_INGESTION.ingestionName}/*************-2222-2222-************/code-ingestion/output/*************-3333-3333-************",
                        "ProcessEnvironment": {
                            "PROCESS_ORG_ID": "*************-2222-2222-************",
                            "PROCESS_INSTALLATION_ID": "*************-4444-4444-************",
                            "PROCESS_REPO_ID": "11111111-1111-1111-1111-111111111111",
                            "PROCESS_REPO_FULL_NAME": "org/repo",
                            "PROCESS_REPO_HTTP_CLONE_URL": "https://github.com/org/repo.git",
                            "PROCESS_REPO_CLONE_AUTH": "Bearer secret",
                            "PROCESS_DOCUMENT_SOURCE": "1",
                            "PROCESS_DOCUMENT_TYPE": "1",
                            "PROCESS_INSIGHT_TYPE": "8",
                            "PROCESS_INCREMENTAL_MODE": "Incremental",
                            "PROCESS_INCREMENTAL_SINCE_TIMESTAMP": "**********",
                            "PROCESS_INCREMENTAL_SINCE_COMMIT_SHA": "cc94312f7b362c698d91e02f5c3d7ea6939692db",
                            "PROCESS_EMBEDDING_NAMESPACE": "*************-2222-2222-************",
                            "PROCESS_EMBEDDING_PLATFORM_CONFIGS": "H4sIAAAAAAAA/4uuVirISSxJyy/KVbJSCsjMS03Oz0tV0lHKzEtJrfBLzE0FChfnlxYlp+om56ek6mZUJhVlpgAV5AJ5OUBJV1PfzOKSosQcpdpYAJVxg51OAAAA",
                            "PROCESS_DOCUMENT_KEY_PREFIX": "c9f0f895-fb98-3b91-99f5-1fd0297e236d|11111111-1111-1111-1111-111111111111|0|",
                            "PROCESS_S3_OUTPUT": "s3://${ingestionPipelineConfig.ingestionPipeline.s3.bucket}/${IngestionPipelineType.CODE_INGESTION.ingestionName}/*************-2222-2222-************/code-ingestion/output/*************-3333-3333-************",
                            "PROCESS_LLM_ENDPOINT_URL": "https://ml.alb.prod.gcp.getunblocked.com/api/ml/transformers/llama-31-8B",
                            "PROCESS_E5_MISTRAL_ENDPOINT_URL": "https://ml.alb.prod.gcp.getunblocked.com/api/ml/embeddings/e5-mistral",
                            "PROCESS_OPENSEARCH_ENDPOINT_URL": "http://localhost:9200/",
                            "PROCESS_ACTIVEMQ_HOSTS": "localhost",
                            "PROCESS_ACTIVEMQ_QUEUE": "source_code_events",
                            "PROCESS_ACTIVEMQ_EVENT_TYPE": "CodeIngestionCompletionEvent"
                        }
                    }
                }
                """.trimIndent(),
            )
        }
    }

    @Test
    fun `runs incrementally when previously run`() {
        val inputPayload = service.generateCodeIngestionInputPayload(
            config = config,
            continuation = RepoCodeIngestionCompletion(
                completedCommitSha = Hash.parse("cc94312f7b362c698d91e02f5c3d7ea6939692db"),
                completedCommitTimestamp = **********,
            ),
        )
        assertThat(inputPayload.codeIngestion.processEnvironment.incrementalMode).isEqualTo("Incremental")
    }

    @Test
    fun `runs non-incrementally when not previously run`() {
        val inputPayload = service.generateCodeIngestionInputPayload(
            config = config,
            continuation = null,
        )
        assertThat(inputPayload.codeIngestion.processEnvironment.incrementalMode).isEqualTo("Full")
    }
}
