package com.nextchaptersoftware.ml.functions

import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.ml.functions.core.MLFunction
import com.nextchaptersoftware.ml.functions.core.MLFunctionDescription
import com.nextchaptersoftware.ml.functions.core.MLFunctionExecutionContext
import com.nextchaptersoftware.scm.services.CommitService
import kotlin.time.Duration
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CIMLFunctions(
    private val commitService: CommitService,
    private val ciFunctionTimeout: Duration,
) : MLFunction {

    override fun isIntegrationEnabled(enabledProviders: Set<Provider>): Boolean {
        return enabledProviders.any {
            it in setOf(
                Provider.GitHub,
            )
        }
    }

    override val timeoutOverride: Duration
        get() = ciFunctionTimeout

    @MLFunctionDescription(
        description = """
            Call this function to analyze PR's build and checks. This function is useful for debugging build failures.
            If the user passes a link to a GitHub action run, call getActionRunBuildDetails instead.
            Only call this function if the user is asking about CI or build failures.

            # Arguments to Pass:
            - pullRequestNumber (string): Extracted from the user's input.
            - suggestedRepoName (optional string): If the user specifies a repository name, include it. Omit this parameter if not provided.

            # Special Handling for URLs:
            - If the user provides a GitHub, Bitbucket, or GitLab URL:
            - Extract the repository name and assign it to suggestedRepoName.
            - Extract the pull request number from the URL.

            # Example User Queries:
            - "Why is PR 123 failing?"
                - pullRequestNumber: 123
                - suggestedRepoName: null
            - "Explain why my build is failing: https://github.com/owner/repo/pull/123"
                - pullRequestNumber: 123
                - suggestedRepoName: "owner/repo"
        """,
    )
    suspend fun getPullRequestBuildDetails(
        context: MLFunctionExecutionContext,
        suggestedRepoName: String?,
        pullRequestNumber: String,
    ): String? {
        val orgId = context.orgId

        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to (context.orgMemberId),
            "suggestedRepoName" to (suggestedRepoName ?: "null"),
            "pullRequestNumber" to pullRequestNumber,
        ) {
            "PRSummaryMLFunctions::getPullRequestBuildDetails"
        }

        return pullRequestNumber.toIntOrNull()?.let {
            commitService.getPullRequestBuildDetails(
                orgId = orgId,
                currentOrgMemberId = context.orgMemberId,
                suggestedRepoName = suggestedRepoName,
                pullRequestNumber = it,
                overrideRepoAccess = context.overrideRepoAccess,
            )
        }
    }

    @MLFunctionDescription(
        description = """
            Call this function to understand a build status and result. This function is useful for debugging build failures.
            If the user passes a link to a GitHub action run, call this function.
            If the user passes a pull request number or url, use getPullRequestBuildDetails instead.

            # Arguments to Pass:
                - suggestedRepoName (optional string): If the user specifies a repository name, include it. Omit this parameter if not provided.
                - suggestedActionPath (string): If the user specifies a action run, include it. Omit this parameter if not provided.

            # Special Handling for URLs:
                - If the user provides a GitHub, Bitbucket, or GitLab URL:
                - Extract the repository name and assign it to suggestedRepoName.
                - Extract the action path and assign it to suggestedActionPath.

            # Example User Queries:
                - "Explain why my build is failing: https://github.com/owner/repo/actions/runs/12719240575/job/35459155077?pr=17765"
                    - suggestedRepoName: "owner/repo"
                    - suggestedActionPath: "/actions/runs/12719240575/job/35459155077?pr=17765
        """,
    )
    suspend fun getActionRunBuildDetails(
        context: MLFunctionExecutionContext,
        suggestedRepoName: String?,
        suggestedActionPath: String?,
    ): String? {
        val orgId = context.orgId

        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to (context.orgMemberId),
            "suggestedRepoName" to (suggestedRepoName ?: "null"),
            "suggestedActionPath" to suggestedActionPath,
        ) {
            "PRSummaryMLFunctions::getActionRunBuildDetails"
        }

        return commitService.getActionBuildDetails(
            orgId = orgId,
            currentOrgMemberId = context.orgMemberId,
            suggestedRepoName = suggestedRepoName,
            suggestedActionPath = suggestedActionPath,
            overrideRepoAccess = context.overrideRepoAccess,
        )
    }
}
