package com.nextchaptersoftware.ingestion.utils

import com.nextchaptersoftware.ktor.utils.RepoUrl

@ConsistentCopyVisibility
data class PullRequestUrl private constructor(
    val repoUrl: RepoUrl,
    val prNumber: Int,
) {
    companion object {
        private val SCHEMES = """\b(http|https)\b""".toRegex()
        private val HOST = """[a-zA-Z\d.-]+""".toRegex()
        private val PORT = """(:(?<port>\b\d+\b)?)?""".toRegex()

        /**
         * Matches:
         * - GitHub         /pull/123
         * - Bitbucket      /pull-requests/123
         * - GitLab         /-/merge_requests/123
         */
        private val PR_NUMBER = """/(pull|pull-requests|-/merge_requests)/(?<prNumber>\d+\b)""".toRegex()

        private val URL_REGEX = """
        ($SCHEMES://(?<host>$HOST)$PORT/)(?<org>[/~\w-_.]+)/(?<repo>[\w-_.]+)$PR_NUMBER
        """.trimIndent().toRegex()

        /**
         * Parse a structured [PullRequestUrl] or throw when invalid.
         */
        fun parseOrThrow(input: String): PullRequestUrl {
            return parseOrNull(input)
                ?: throw IllegalArgumentException("Not a valid pull request url '$input'.")
        }

        /**
         * Parse a structured [PullRequestUrl] or null when invalid.
         */
        fun parseOrNull(input: String): PullRequestUrl? {
            return URL_REGEX.find(input)
                ?.let { match -> resultAsPrUrl(match) }
        }

        fun parseAll(input: String): List<PullRequestUrl> {
            return URL_REGEX.findAll(input)
                .mapNotNull { match -> resultAsPrUrl(match) }
                .distinct()
                .toList()
        }

        private fun resultAsPrUrl(match: MatchResult): PullRequestUrl? {
            val matchedInput = match.groups[0]?.value ?: return null
            val prNumber = match.groups["prNumber"]?.value?.toInt() ?: return null
            val repoPart = matchedInput.replaceFirst(PR_NUMBER, "")
            val repoUrl = RepoUrl.parseOrNull(repoPart) ?: return null
            return PullRequestUrl(repoUrl, prNumber)
        }
    }
}
