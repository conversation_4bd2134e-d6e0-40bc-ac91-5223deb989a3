plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-activemq", "default"))
    implementation(project(":projects:libs:lib-api-threads", "default"))
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-emoji", "default"))
    implementation(project(":projects:libs:lib-environment", "default"))
    implementation(project(":projects:libs:lib-log-kotlin", "default"))
    implementation(project(":projects:libs:lib-prefect", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
    compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
}
