package com.nextchaptersoftware.aws.bedrock.anthropic.api

import com.nextchaptersoftware.anthropic.api.models.AnthropicCompletionsModel
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeAsyncProvider
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeProvider
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProvider
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProvider
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.test.utils.SkipInCI
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.scan
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider
import software.amazon.awssdk.regions.Region

@SkipInCI
class BedrockAnthropicCompletionsApiTest {

    private lateinit var bedrockRuntimeProvider: BedrockRuntimeProvider
    private lateinit var bedrockRuntimeAsyncProvider: BedrockRuntimeAsyncProvider
    private lateinit var bedrockAnthropicCompletionsApi: BedrockAnthropicCompletionsApi

    @BeforeEach
    fun setup() {
        val awsClientProvider = AWSClientProvider(
            region = Region.US_WEST_2,
            credentialsProvider = ProfileCredentialsProvider.builder()
                .profileName("dev")
                .build(),
        )

        bedrockRuntimeProvider = StandardBedrockRuntimeProvider(awsClientProvider)
        bedrockRuntimeAsyncProvider = StandardBedrockRuntimeAsyncProvider(awsClientProvider)
        bedrockAnthropicCompletionsApi = BedrockAnthropicCompletionsApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    @Nested
    inner class ChatCompletionTests {
        private suspend fun assertChatCompletionWithModel(model: AnthropicCompletionsModel) {
            val chatCompletion = bedrockAnthropicCompletionsApi.chatCompletion(
                model = model,
                prompt = """
                    [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi.
                    [[USER]] What is your name?
                """.trimIndent(),
                maxTokens = 128,
                respondWithJson = false,
            )
            assertThat(chatCompletion.content).isNotEmpty()
        }

        @Test
        fun testChatCompletionClaude37Sonnet() = runTest {
            assertChatCompletionWithModel(AnthropicCompletionsModel.Claude37Sonnet)
        }

        @Test
        fun testChatCompletionClaude4Sonnet() = runTest {
            assertChatCompletionWithModel(AnthropicCompletionsModel.Claude4Sonnet)
        }

        @Test
        fun testChatCompletionClaude35Sonnet() = runTest {
            assertChatCompletionWithModel(AnthropicCompletionsModel.Claude35Sonnet)
        }
    }

    @Nested
    inner class JsonCoercionTests {
        private suspend fun assertJsonCoercionWithModel(model: AnthropicCompletionsModel) {
            val chatCompletion = bedrockAnthropicCompletionsApi.chatCompletion(
                model = model,
                prompt = """
                    [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi. Reply in JSON format.
                    [[USER]] What is your name?
                """.trimIndent(),
                temperature = 0.0f,
                maxTokens = 256,
                respondWithJson = true,
            )
            assertThat(chatCompletion.content).isNotEmpty()
            assertThat(chatCompletion.content.joinToString("") { it.text }).contains("Unblocked")
        }

        @Test
        fun testJsonCoercionClaude37Sonnet() = runTest {
            assertJsonCoercionWithModel(AnthropicCompletionsModel.Claude37Sonnet)
        }

        @Test
        fun testJsonCoercionClaude4Sonnet() = runTest {
            assertJsonCoercionWithModel(AnthropicCompletionsModel.Claude4Sonnet)
        }

        @Test
        fun testJsonCoercionClaude35Sonnet() = runTest {
            assertJsonCoercionWithModel(AnthropicCompletionsModel.Claude35Sonnet)
        }
    }

    @Nested
    inner class ChatFlowTests {
        private suspend fun assertChatFlowWithModel(model: AnthropicCompletionsModel) {
            val chatCompletionString = bedrockAnthropicCompletionsApi.chatCompletions(
                model = model,
                prompt = """
                    [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi.
                    [[USER]] Write a long poem
                """.trimIndent(),
                temperature = 0.0f,
                maxTokens = 4096,
                respondWithJson = false,
            ).scan("") { acc, chunk ->
                val next = acc + chunk.content.joinToString { it.text }
                println(next)
                next
            }.last()

            assertThat(chatCompletionString).isNotNull()
            assertThat(chatCompletionString).isNotEmpty()
        }

        @Test
        fun testChatFlowClaude37Sonnet() = runTest {
            assertChatFlowWithModel(AnthropicCompletionsModel.Claude37Sonnet)
        }

        @Test
        fun testChatFlowClaude4Sonnet() = runTest {
            assertChatFlowWithModel(AnthropicCompletionsModel.Claude4Sonnet)
        }

        @Test
        fun testChatFlowClaude35Sonnet() = runTest {
            assertChatFlowWithModel(AnthropicCompletionsModel.Claude35Sonnet)
        }
    }
}
