package com.nextchaptersoftware.version

import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.s3.S3ProviderFactory
import com.nextchaptersoftware.aws.s3.StandardS3ProviderFactory
import com.nextchaptersoftware.aws.test.utils.MockAWSClientProvider
import com.nextchaptersoftware.aws.test.utils.S3TestUtils.createBucket
import com.nextchaptersoftware.aws.test.utils.S3TestUtils.createRandomObject
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode.SAME_THREAD
import software.amazon.awssdk.services.s3.model.NoSuchKeyException

@Suppress("MaxLineLength")
@Execution(SAME_THREAD)
class S3OperationsTests {
    private val s3ProviderFactory: S3ProviderFactory = StandardS3ProviderFactory()
    private val awsClientProvider: AWSClientProvider = MockAWSClientProvider.from()
    private val bucket: String = "download-assets.local.getunblocked.com"

    private fun setup() {
        createBucket(awsClientProvider.s3Client, bucket)
    }

    @Test
    fun `s3 list objects`() = runTest {
        setup()
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)

        // No prefix
        createRandomObject(awsClientProvider.s3Client, bucket, "")
        assertThat(s3Provider.listObjects().contents().count()).isGreaterThan(0)

        // With prefix
        val prefix = java.util.UUID.randomUUID().toString()
        createRandomObject(awsClientProvider.s3Client, bucket, "/$prefix")
        assertThat(s3Provider.listObjects().contents().count()).isGreaterThan(0)
        assertThat(s3Provider.listObjects("/$prefix").contents().count()).isEqualTo(1)
    }

    @Test
    fun `s3 objects exists`() = runTest {
        setup()

        // Negative test
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)
        assertThat(s3Provider.doesObjectExist("ThisObjectShouldNotExist")).isFalse()

        // Object with no prefix
        val objectWithNoPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "")
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isTrue()

        // Object with prefix
        val objectWithPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "/customPrefix")
        assertThat(s3Provider.doesObjectExist("/customPrefix/$objectWithPrefix")).isTrue()
    }

    @Test
    fun `s3 get object as bytes`() = runTest {
        setup()
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)
        val objectWithNoPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "")

        // Test objects contain a string of the object key
        assertThat(
            s3Provider.getObjectAsBytes(objectWithNoPrefix).asString(charset("UTF-8"))
                .equals(objectWithNoPrefix),
        ).isTrue()

        // Missing object test
        assertThrows<NoSuchKeyException> {
            s3Provider.getObjectAsBytes("non-existent-object").asString(charset("UTF-8"))
        }
    }

    @Test
    fun `s3 delete object`() = runTest {
        setup()
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)

        // Non-existent object
        assertThat(s3Provider.deleteObject("NonExistingObjectToDelete")).isNotNull

        // Object with no prefix
        val objectWithNoPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "")
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isTrue()
        s3Provider.deleteObject(objectWithNoPrefix)
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isFalse()

        // Object with prefix
        val prefix = "/customDeletePrefix"
        val objectWithPrefix = createRandomObject(awsClientProvider.s3Client, bucket, prefix)
        assertThat(s3Provider.doesObjectExist("$prefix/$objectWithPrefix")).isTrue()
        s3Provider.deleteObject("$prefix/$objectWithPrefix")
        assertThat(s3Provider.listObjects("$prefix/$objectWithPrefix").contents().count()).isEqualTo(0)
        assertThat(s3Provider.doesObjectExist("$prefix/$objectWithPrefix")).isFalse()
    }

    @Test
    fun `s3 copy object`() = runTest {
        setup()
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)

        // Object with no prefix
        val objectWithNoPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "")
        s3Provider.copyObject(
            objectWithNoPrefix,
            bucket,
            "$objectWithNoPrefix-after-copy",
        )
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isTrue()
        assertThat(s3Provider.doesObjectExist("$objectWithNoPrefix-after-copy")).isTrue()

        // Copy and overwrite object from last step
        s3Provider.copyObject(
            objectWithNoPrefix,
            bucket,
            "$objectWithNoPrefix-after-copy",
        )
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isTrue()
        assertThat(s3Provider.doesObjectExist("$objectWithNoPrefix-after-copy")).isTrue()

        // Object with prefix
        val objectWithPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "/copytest")
        s3Provider.copyObject(
            "/copytest/$objectWithPrefix",
            bucket,
            "/copytest/$objectWithPrefix-after-copy",
        )
        assertThat(s3Provider.doesObjectExist("/copytest/$objectWithPrefix")).isTrue()
        assertThat(s3Provider.doesObjectExist("/copytest/$objectWithPrefix-after-copy")).isTrue()
    }

    @Test
    fun `s3 move object`() = runTest {
        setup()
        val s3Provider = s3ProviderFactory.generate(awsClientProvider, bucket)

        // Object with no prefix
        val objectWithNoPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "")
        s3Provider.moveObject(
            objectWithNoPrefix,
            bucket,
            "$objectWithNoPrefix-after-move",
        )
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefix)).isFalse()
        assertThat(s3Provider.doesObjectExist("$objectWithNoPrefix-after-move")).isTrue()

        // Object with prefix
        val objectWithPrefix = createRandomObject(awsClientProvider.s3Client, bucket, "/movetest")
        s3Provider.moveObject(
            "/movetest/$objectWithPrefix",
            bucket,
            "/movetest/$objectWithPrefix-after-move",
        )
        assertThat(s3Provider.doesObjectExist("/movetest/$objectWithPrefix")).isFalse()
        assertThat(s3Provider.doesObjectExist("/movetest/$objectWithPrefix-after-move")).isTrue()

        // Simulate move retry after a failed previous attempt
        val objectWithNoPrefixFailedMove = createRandomObject(awsClientProvider.s3Client, bucket, "")
        s3Provider.copyObject(
            objectWithNoPrefixFailedMove,
            bucket,
            "$objectWithNoPrefixFailedMove-after-move",
        )
        s3Provider.moveObject(
            objectWithNoPrefixFailedMove,
            bucket,
            "$objectWithNoPrefixFailedMove-after-move",
        )
        assertThat(s3Provider.doesObjectExist(objectWithNoPrefixFailedMove)).isFalse()
        assertThat(s3Provider.doesObjectExist("$objectWithNoPrefixFailedMove-after-move")).isTrue()
    }
}
