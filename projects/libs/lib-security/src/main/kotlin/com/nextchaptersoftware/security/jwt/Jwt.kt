package com.nextchaptersoftware.security.jwt

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.JWTVerifier
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.warnSync
import com.nextchaptersoftware.security.RSAKeyLoader
import com.nextchaptersoftware.security.ScopedResource
import com.nextchaptersoftware.security.jwt.JwtUtils.getListClaim
import com.nextchaptersoftware.security.jwt.JwtUtils.getListClaimOrNull
import com.nextchaptersoftware.security.store.TokenChainRedisStore
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.asUUIDOrNull
import com.nextchaptersoftware.utils.asUUIDs
import com.nextchaptersoftware.utils.date
import io.ktor.http.HttpMethod
import io.ktor.server.auth.jwt.JWTPayloadHolder
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.util.decodeBase64String
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds
import kotlinx.datetime.Clock.System.now
import kotlinx.datetime.Instant
import kotlinx.datetime.toKotlinInstant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class Jwt(
    private val authenticationConfig: AuthenticationConfig,
) {

    enum class Audience(val value: String) {
        Authentication("com.unblocked.token"),
        CIContext("com.unblocked.token.ci"),
        Exchange("com.unblocked.token.exchange"),
        ReadOnly("com.unblocked.token.readonly"),
        Refresh("com.unblocked.token.refresh"),
    }

    enum class Claim(val value: String) {
        Orgs("orgs"),
        Scopes("scopes"),
        ClientSecret("clientSecret"),
        AdminPermissions("adminPermissions"),
        AdminIdentityId("adminIdentityId"),
        TokenChainId("tokenChainId"),
        PersonId("psn"),

        /**
         * This claim contains a list of orgId indices that the service must validate repo access control for.
         * If the claim is not present, the service should not validate repo access control.
         * If the claim is present, the service should only validate repo access control for the orgs specified.
         */
        MustValidateRepoAccess("mvra"),

        /**
         * This claim contains a list of orgIds that the user needs to re-authenticate with in order to have resource access to.
         * All resource requests to this orgId will be rejected with a 403 until the user re-authenticates.
         */
        OrgsAuthRequired("orgsAuthReq"),

        ScmAvatarUrl("scmAvatar"),
        ScmRepoFullName("scmRepo"),
        ScmProviderDisplayName("scmProvider"),
        ScmUsername("scmUsername"),
    }

    companion object {
        val LEEWAY = 10.seconds

        fun isReadOnlyAudience(audience: List<String>?): Boolean {
            return audience?.contains(Audience.ReadOnly.value) ?: false
        }
    }

    internal val issuer = authenticationConfig.tokenIssuer

    val unblockedAuthTokenSigningAlgorithm by lazy {
        runSuspendCatching {
            Algorithm.RSA256(
                null,
                RSAKeyLoader.createPrivateKey(authenticationConfig.tokenPrivateKey.value.decodeBase64String()),
            )
        }.onFailure {
            LOGGER.errorSync(it) { "Failed to create RSA256 algorithm" }
        }.getOrNull()
    }

    internal val unblockedAuthTokenVerificationAlgorithm by lazy {
        Algorithm.RSA256(
            RSAKeyLoader.createPublicKey(authenticationConfig.tokenPublicKey.value.decodeBase64String()),
            null,
        ).required()
    }

    val authTokenVerifier: JWTVerifier by lazy {
        JWT.require(unblockedAuthTokenVerificationAlgorithm)
            .withIssuer(issuer)
            .withAudience(Audience.Authentication.value)
            .acceptLeeway(LEEWAY.inWholeSeconds)
            .build()
    }

    internal val readOnlyTokenVerifier: JWTVerifier by lazy {
        JWT.require(unblockedAuthTokenVerificationAlgorithm)
            .withIssuer(issuer)
            .withAudience(Audience.ReadOnly.value)
            .acceptLeeway(LEEWAY.inWholeSeconds)
            .build()
    }

    val exchangeTokenVerifier: JWTVerifier by lazy {
        JWT.require(unblockedAuthTokenVerificationAlgorithm)
            .withIssuer(issuer)
            .withAudience(Audience.Exchange.value)
            .acceptLeeway(LEEWAY.inWholeSeconds)
            .build()
    }

    val refreshTokenVerifier: JWTVerifier by lazy {
        JWT.require(unblockedAuthTokenVerificationAlgorithm)
            .withIssuer(issuer)
            .withAudience(Audience.Refresh.value)
            .acceptLeeway(LEEWAY.inWholeSeconds)
            .build()
    }

    suspend fun authTokenValidator(
        orgId: UUID?,
        credential: JWTPayloadHolder,
        method: HttpMethod,
        path: String,
        forbidden: (suspend () -> Unit)? = null,
        forbiddenAuthRequired: (suspend () -> Unit)? = null,
    ): JWTPayloadHolder? {
        // Subject is required
        credential.subject?.asUUIDOrNull() ?: run {
            LOGGER.warnSync { "subject is missing or malformed" }
            return null
        }

        orgId?.toString()?.also { orgIdString ->
            credential.getListClaim<String>(name = Claim.Orgs.value).also { authorizedOrgs ->
                // This means that the requested orgId is not in the list of authorized orgs.
                // An inadvertent or intentional attack, so we log this as a warning.
                if (!authorizedOrgs.contains(orgIdString)) {
                    LOGGER.warnSync(
                        "orgId" to orgIdString,
                        "authorizedOrgs" to authorizedOrgs.joinToString(),
                    ) {
                        "'orgs' claim does not contains requested 'orgId'"
                    }
                    forbidden?.invoke()
                    return null
                }

                credential.getListClaimOrNull<String>(Claim.OrgsAuthRequired.value)?.asUUIDs()?.nullIfEmpty()?.also { orgsAuthReq ->
                    if (orgsAuthReq.contains(orgId)) {
                        LOGGER.warnSync(
                            "orgId" to orgId,
                            "orgsAuthReq" to orgsAuthReq.joinToString(),
                        ) {
                            "'orgsAuthReq' claim contains requested 'orgId'"
                        }
                        forbiddenAuthRequired?.invoke()
                        return null
                    }
                }
            }
        }

        runSuspendCatching {
            credential.getListClaimOrNull<String>(Claim.Scopes.value)
        }.getOrNull()?.also { scopeList ->
            if (scopeList.asSequence().map(ScopedResource.Companion::fromString)
                    .none { it.method == method && it.path == path }
            ) {
                return null
            }
        }

        return JWTPrincipal(credential.payload)
    }

    fun exchangeTokenValidator(credential: JWTPayloadHolder): JWTPayloadHolder? {
        if (!credential[Claim.ClientSecret.value].isNullOrEmpty()) {
            return JWTPrincipal(credential.payload)
        }
        return null
    }

    suspend fun refreshTokenValidator(credential: JWTPayloadHolder, tokenChainRedisStore: TokenChainRedisStore): JWTPayloadHolder? {
        if (!credential.subject.isNullOrEmpty()) {
            val tokenChainId = credential[Claim.TokenChainId.value]?.asUUIDOrNull()
            val identityId = credential.subject?.asUUIDOrNull()
            val issuedAt = credential.issuedAt?.toInstant()?.toKotlinInstant()

            // Validation check is intentionally eagerly permissive.
            // In any cases where token information is missing or Redis fails we let them in.
            // TODO enforce token info (iat and tokenChainId)
            // TODO https://linear.app/unblocked/issue/UNB-974/enforce-refresh-token-claims-during-refresh-token-validation
            suspend fun isValid(): Boolean {
                if (tokenChainId != null && identityId != null && issuedAt != null) {
                    return runSuspendCatching {
                        tokenChainRedisStore.getTokenChainMinIAT(
                            identityId = identityId,
                            tokenChainId = tokenChainId,
                        )?.let {
                            it < issuedAt
                        } ?: true
                    }.getOrDefault(true)
                } else {
                    if (identityId == null) {
                        LOGGER.warnAsync { "refreshTokenValidator: identityId is null" }
                    }
                    if (tokenChainId == null) {
                        LOGGER.warnAsync { "refreshTokenValidator: tokenChainId is null" }
                    }
                    if (issuedAt == null) {
                        LOGGER.warnAsync { "refreshTokenValidator: issueAt is null" }
                    }
                }
                return true
            }

            if (isValid()) {
                return JWTPrincipal(credential.payload)
            } else {
                LOGGER.errorAsync(
                    "issuedAt" to issuedAt,
                    "identityId" to identityId,
                    "tokenChainId" to tokenChainId,
                ) {
                    "Jwt::refreshTokenValidator failed to validate token"
                }
            }
        }
        return null
    }

    fun generateAuthToken(
        identityId: UUID,
        personId: UUID,
        orgIds: Set<UUID>?,
        tokenChainId: UUID,
        orgsMvra: Set<UUID>,
        orgsAuthReq: Set<UUID>,
        readOnly: Boolean,
        scopes: List<ScopedResource>? = null,
    ): String {
        val now = now()
        val builder = JWT.create()
            .withAudience(
                when (readOnly) {
                    true -> Audience.ReadOnly.value
                    false -> Audience.Authentication.value
                },
            )
            .withIssuer(issuer)
            .withSubject(identityId.toString())
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt((now + authenticationConfig.authTokenExpiry).date)

        orgIds?.also {
            builder.withArrayClaim(
                Claim.Orgs.value,
                orgIds.map { it.toString() }.toTypedArray(),
            )
            MustValidateRepoAccessClaim.toClaim(orgIds, orgsMvra)?.also {
                builder.withArrayClaim(Claim.MustValidateRepoAccess.value, it)
            }
            orgsAuthReq.also {
                builder.withArrayClaim(
                    Claim.OrgsAuthRequired.value,
                    orgsAuthReq.map { it.toString() }.toTypedArray(),
                )
            }
        }

        scopes?.also {
            builder.withArrayClaim(Claim.Scopes.value, scopes.map { it.asString }.toTypedArray())
            builder.withExpiresAt((now + authenticationConfig.refreshTokenExpiry).date)
        }

        builder.withClaim(Claim.TokenChainId.value, tokenChainId.toString())

        builder.withClaim(Claim.PersonId.value, personId.toString())

        return builder.sign(unblockedAuthTokenSigningAlgorithm)
    }

    fun generateExchangeToken(secret: UUID? = UUID.randomUUID()): String {
        val now = now()
        val expiry = now + authenticationConfig.exchangeTokenExpiry
        return JWT.create()
            .withAudience(Audience.Exchange.value)
            .withIssuer(issuer)
            .withClaim(Claim.ClientSecret.value, secret.toString())
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt(expiry.date)
            .sign(unblockedAuthTokenSigningAlgorithm)
    }

    fun generateRefreshToken(
        identityId: UUID,
        tokenChainId: UUID,
        maxExpiresAt: Instant?,
    ): String {
        val now = now()
        val expiry = now
            .plus(authenticationConfig.refreshTokenExpiry)
            .coerceAtMost(maxExpiresAt ?: Instant.DISTANT_FUTURE)
        val builder = JWT.create()
            .withAudience(Audience.Refresh.value)
            .withIssuer(issuer)
            .withSubject(identityId.toString())
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt(expiry.date)

        builder.withClaim(Claim.TokenChainId.value, tokenChainId.toString())

        return builder.sign(unblockedAuthTokenSigningAlgorithm)
    }

    fun testGenerateTokenWithEmptySubject(): String {
        val now = now()
        val expiry = now + 1.days
        return JWT.create()
            .withAudience(Audience.Authentication.value)
            .withIssuer(issuer)
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt(expiry.date)
            .sign(unblockedAuthTokenSigningAlgorithm)
    }

    fun testGenerateNBFDriftedRefreshToken(uuid: UUID): String {
        val now = now()
        val expiry = now + authenticationConfig.refreshTokenExpiry
        return JWT.create()
            .withAudience(Audience.Refresh.value)
            .withIssuer(issuer)
            .withSubject(uuid.toString())
            .withIssuedAt((now + 20.seconds).date)
            .withNotBefore((now + 20.seconds).date)
            .withExpiresAt(expiry.date)
            .sign(unblockedAuthTokenSigningAlgorithm)
    }
}
