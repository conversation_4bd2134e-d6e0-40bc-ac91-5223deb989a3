package com.nextchaptersoftware.security

import com.sksamuel.hoplite.Secret
import java.security.MessageDigest
import org.bouncycastle.crypto.digests.SHA1Digest
import org.bouncycastle.crypto.digests.SHA256Digest
import org.bouncycastle.crypto.macs.HMac
import org.bouncycastle.crypto.params.KeyParameter
import org.bouncycastle.util.encoders.Hex

class HMACAuthenticationException : Exception("HMAC Authentication Error")

class HMACAuthenticator(
    private val authenticationSecret: Secret,
    private val signaturePrefix: String? = null,
) {
    enum class Algorithm {
        SHA1,
        SHA256,
    }

    fun validate(
        signature: String,
        payload: String,
        algorithm: Algorithm = Algorithm.SHA256,
    ) {
        val reconstructed = "${signaturePrefix ?: ""}${digest(payload = payload, algorithm = algorithm)}"
        val reconstructedBytes = reconstructed.toByteArray()
        val signatureBytes = signature.toByteArray()
        val timingAttackProtectedComparison = MessageDigest.isEqual(reconstructedBytes, signatureBytes)
        if (!timingAttackProtectedComparison) {
            throw HMACAuthenticationException()
        }
    }

    fun digest(
        payload: String,
        algorithm: Algorithm = Algorithm.SHA256,
    ): String {
        val hmac = when (algorithm) {
            Algorithm.SHA1 -> HMac(SHA1Digest())
            Algorithm.SHA256 -> HMac(SHA256Digest())
        }
        hmac.init(KeyParameter(authenticationSecret.value.toByteArray()))
        val resultBytes = ByteArray(size = hmac.macSize)
        val payloadBytes = payload.toByteArray()
        hmac.update(payloadBytes, 0, payloadBytes.size)
        hmac.doFinal(resultBytes, 0)
        return Hex.toHexString(resultBytes)
    }
}
