package com.nextchaptersoftware.security.store

import io.lettuce.core.SetArgs
import io.lettuce.core.cluster.api.coroutines.RedisClusterCoroutinesCommands
import java.util.UUID
import kotlin.time.Duration
import kotlinx.datetime.Instant

class TokenChainRedisStore(
    redisApi: RedisClusterCoroutinesCommands<String, String>,
) {
    private val tokenChainKey = "auth:tokenchain:iat"
    private val commands = redisApi

    private fun getTokenChainKey(identityId: UUID, tokenChainId: UUID): String {
        return "$tokenChainKey:$identityId:$tokenChainId"
    }

    suspend fun getTokenChainMinIAT(identityId: UUID, tokenChainId: UUID) =
        commands.get(getTokenChainKey(identityId = identityId, tokenChainId = tokenChainId))?.let { Instant.parse(it) }

    suspend fun setTokenChainMinIAT(identityId: UUID, tokenChainId: UUID, iat: Instant, expiry: Duration) {
        commands.set(
            key = getTokenChainKey(
                identityId = identityId,
                tokenChainId = tokenChainId,
            ),
            value = iat.toString(),
            setArgs = SetArgs().nx().ex(expiry.inWholeSeconds),
        )
    }
}
