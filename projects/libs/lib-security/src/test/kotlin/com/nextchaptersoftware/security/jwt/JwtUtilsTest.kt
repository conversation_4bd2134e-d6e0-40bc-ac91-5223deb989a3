package com.nextchaptersoftware.security.jwt

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JwtUtilsTest {

    private val jwtString = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW5" +
            "0LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MTIyNzU1MzAsIm5iZiI6MTcxMjI3NTIzMCwicGF0aCI6Ii8xMzQzMTM3Mi8yNzAwNjIwMTItMmQzMTY" +
            "xMGItOTYxZi00MmU4LTkxYWMtNThhNGI3NzgxYTQ2LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUF" +
            "LSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA0MDUlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwNDA1VDAwMDA" +
            "zMFomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTVmZTM5MTNhZDM4MGFjYTJiM2ZkZmU5ZDU3NDA2ODg2MDlkMDk0NzgzMzVkNzMzN2F" +
            "jYjYwNzhkNWQwODU5ZjgmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.KQo2RqsgINeGWuTjCdU" +
            "ixQy4iHuJ330_cJ_HxEDR5Fs"

    @Test
    fun `parse well formed jwt`() {
        assertThat(JwtUtils.parseJwt(jwtString)).isNotNull
    }

    @Test
    fun `null for badly formed jwt`() {
        assertThat(JwtUtils.parseJwt("")).isNull()
        assertThat(JwtUtils.parseJwt("foo bar")).isNull()
        assertThat(JwtUtils.parseJwt(jwtString.drop(1))).isNull()
        assertThat(JwtUtils.parseJwt(jwtString.drop(1) + jwtString.last().uppercase())).isNull()
    }

    @Test
    fun getExpiryTime() {
        assertThat(
            checkNotNull(
                JwtUtils.getExpiryTime(jwtString),
            ).epochSeconds.toInt(),
        ).isEqualTo(1712275530)
    }
}
