package com.nextchaptersoftware.ci.payloads.params

import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.utils.KotlinUtils.required

interface ParamPullRequestId {

    val pullRequestId: PullRequestId

    suspend fun pullRequest(): PullRequest {
        return pullRequestStore.findById(prId = pullRequestId).required { "Missing Pull Request" }
    }
}
