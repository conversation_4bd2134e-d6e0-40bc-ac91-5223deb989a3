package com.nextchaptersoftware.topic.insight.services.map

import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.SearchInsightStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadPrivacyContext
import com.nextchaptersoftware.db.stores.ThreadStore
import java.util.UUID

enum class TopicMappingRuleDecision {
    InvalidInsightType,
    PullRequestDoesNotExist,
    PullRequestClosed,
    ThreadDoesNotExist,
    IsPendingQAThread,
    IsArchived,
    IsApproved,
}

class TopicMappingRuleService(
    private val searchInsightStore: SearchInsightStore = Stores.searchInsightStore,
    private val threadStore: ThreadStore = Stores.threadStore,
    private val pullRequestStore: PullRequestStore = Stores.pullRequestStore,
    private val questionAnswerThreadService: QuestionAnswerThreadService,
) {
    suspend fun shouldMap(orgId: OrgId, insightId: UUID): TopicMappingRuleDecision {
        return when (searchInsightStore.insightType(orgId = orgId, insightId = insightId)) {
            InsightType.PullRequest,
            -> shouldMapPullRequestToTopic(orgId = orgId, pullRequestId = insightId.let(::PullRequestId))
                ?: TopicMappingRuleDecision.PullRequestDoesNotExist

            InsightType.Issue,
            InsightType.Slack,
            InsightType.PullRequestComment,
            InsightType.Answer,
            -> shouldMapThreadsToTopics(orgId = orgId, threadIds = listOf(insightId.let(::ThreadId)))[insightId.let(::ThreadId)]
                ?: TopicMappingRuleDecision.ThreadDoesNotExist

            InsightType.SourceCode,
            InsightType.Documentation,
            InsightType.Discussion,
            -> TopicMappingRuleDecision.InvalidInsightType

            null -> shouldMapInsightToTopics(orgId = orgId, insightId = insightId)
        }
    }

    internal suspend fun shouldMapInsightToTopics(
        orgId: OrgId,
        insightId: UUID,
    ): TopicMappingRuleDecision {
        val threadRuleDecision =
            shouldMapThreadsToTopics(orgId = orgId, threadIds = listOf(insightId.let(::ThreadId)))[insightId.let(::ThreadId)]

        return when (threadRuleDecision) {
            TopicMappingRuleDecision.InvalidInsightType,
            TopicMappingRuleDecision.PullRequestDoesNotExist,
            TopicMappingRuleDecision.PullRequestClosed,
            TopicMappingRuleDecision.IsPendingQAThread,
            TopicMappingRuleDecision.IsArchived,
            TopicMappingRuleDecision.IsApproved,
            -> threadRuleDecision

            TopicMappingRuleDecision.ThreadDoesNotExist, null -> {
                val pullRequestId = insightId.let(::PullRequestId)
                shouldMapPullRequestToTopic(orgId = orgId, pullRequestId = pullRequestId)
                    ?: TopicMappingRuleDecision.InvalidInsightType
            }
        }
    }

    internal suspend fun shouldMapPullRequestToTopic(
        orgId: OrgId,
        pullRequestId: PullRequestId,
    ) = shouldMapPullRequestsToTopics(
        orgId = orgId,
        pullRequestIds = listOf(pullRequestId),
    )
        .let { map -> map[pullRequestId] }

    internal suspend fun shouldMapPullRequestsToTopics(
        orgId: OrgId,
        pullRequestIds: List<PullRequestId>,
    ): Map<PullRequestId, TopicMappingRuleDecision> {
        val pullRequests = pullRequestStore.find(
            trx = null,
            orgId = orgId,
            ids = pullRequestIds,
            limit = null,
        )

        return pullRequestIds.associateWith { id ->
            val pullRequest = pullRequests.find { it.id == id }
            when {
                pullRequest == null -> TopicMappingRuleDecision.PullRequestDoesNotExist
                pullRequest.state == PullRequestState.Closed -> TopicMappingRuleDecision.PullRequestClosed
                pullRequest.isArchived -> TopicMappingRuleDecision.IsArchived
                else -> TopicMappingRuleDecision.IsApproved
            }
        }
    }

    internal suspend fun shouldMapThreadsToTopics(
        orgId: OrgId,
        threadIds: List<ThreadId>,
    ): Map<ThreadId, TopicMappingRuleDecision> {
        val threads = threadStore.findIn(
            trx = null,
            orgId = orgId,
            ids = threadIds,
            privacyContext = ThreadPrivacyContext.IgnorePrivacy,
        )

        return threadIds.associateWith { id ->
            val thread = threads.find { it.id == id }
            when {
                thread == null -> TopicMappingRuleDecision.ThreadDoesNotExist

                thread.isArchived -> TopicMappingRuleDecision.IsArchived

                questionAnswerThreadService.isPendingQAThread(
                    orgId = orgId,
                    threadId = id,
                ) -> TopicMappingRuleDecision.IsPendingQAThread

                else -> TopicMappingRuleDecision.IsApproved
            }
        }
    }
}
