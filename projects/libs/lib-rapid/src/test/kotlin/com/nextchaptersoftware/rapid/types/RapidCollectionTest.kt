package com.nextchaptersoftware.rapid.types

import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RapidCollectionTest {

    @Test
    fun `test attribute conversion`() {
        val uuid = UUID.randomUUID()
        val collection = RapidCollection(uuid)
        assertThat(collection.value).isEqualTo(uuid)

        val attribute = collection.toAttribute()
        assertThat(attribute.name).isEqualTo("collection")
        assertThat(RapidCollection.fromAttribute(attribute)).isEqualTo(collection)
    }
}
