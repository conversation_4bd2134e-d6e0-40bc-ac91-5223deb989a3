package com.nextchaptersoftware.rapid.types

import com.nextchaptersoftware.aws.dynamo.DynamoAttribute
import com.nextchaptersoftware.aws.dynamo.DynamoValueType
import com.nextchaptersoftware.compress.Compression
import com.nextchaptersoftware.rapid.models.RapidKeys

@JvmInline
value class RapidContent(
    val value: String,
) {

    fun toAttribute() = DynamoAttribute(
        name = RapidKeys.CONTENT_KEY,
        value = DynamoValueType.BinaryType(Compression.compress(value)).asAttributeValue,
    )

    companion object {
        fun fromAttribute(attribute: DynamoAttribute): RapidContent {
            return RapidContent(
                Compression.decompress(attribute.value.b().asByteArray()),
            )
        }
    }
}
