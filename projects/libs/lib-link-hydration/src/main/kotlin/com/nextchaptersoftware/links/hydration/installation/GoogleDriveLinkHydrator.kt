package com.nextchaptersoftware.links.hydration.installation

import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.links.models.LinkedDocument
import com.nextchaptersoftware.types.Hostname
import io.ktor.http.Url

internal class GoogleDriveLinkHydrator(
    override val hostname: Hostname,
    override val installation: Installation,
) : InstallationLinkHydrator {

    override suspend fun hydrateLink(
        orgMemberId: OrgMemberId,
        dsacContext: DsacContext,
        url: Url,
        verbose: Boolean,
    ): LinkedDocument? {
        return null
    }
}
