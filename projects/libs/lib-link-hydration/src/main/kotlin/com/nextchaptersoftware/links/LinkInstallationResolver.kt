package com.nextchaptersoftware.links

import com.nextchaptersoftware.links.hydration.LinkHydrator
import com.nextchaptersoftware.types.Hostname
import io.ktor.http.Url

class LinkInstallationResolver(
    private val linkHydrators: Set<LinkHydrator>,
) {
    fun matchLinkHydrators(url: Url): List<LinkHydrator> {
        val hostname = Hostname.parseOrNull(url.host)
            ?: return emptyList()

        return linkHydrators.filter { it.hostname == hostname }
    }
}
