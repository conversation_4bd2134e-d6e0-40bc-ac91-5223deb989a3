package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.LoginProvider
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.SignInSection

@Suppress("CyclomaticComplexMethod")
fun Provider.asApiModel(): ApiProvider = when (this) {
    Provider.Asana -> ApiProvider.asana
    Provider.Aws -> ApiProvider.aws
    Provider.AwsIdentityCenter -> ApiProvider.awsIdentityCenter
    Provider.AzureDevOps -> ApiProvider.azureDevOps
    Provider.Bitbucket -> ApiProvider.bitbucket
    Provider.BitbucketDataCenter -> ApiProvider.bitbucketDataCenter
    Provider.BitbucketPipelines -> ApiProvider.bitbucketPipelines
    Provider.Buildkite -> ApiProvider.buildKite
    Provider.CircleCI -> ApiProvider.circleci
    Provider.Coda -> ApiProvider.coda
    Provider.Confluence -> ApiProvider.confluence
    Provider.ConfluenceDataCenter -> ApiProvider.confluenceDataCenter
    Provider.CustomIntegration -> ApiProvider.customIntegration
    Provider.GenericSaml -> ApiProvider.saml
    Provider.GitHub -> ApiProvider.github
    Provider.GitHubActions -> ApiProvider.githubActions
    Provider.GitHubEnterprise -> ApiProvider.githubEnterprise
    Provider.GitLab -> ApiProvider.gitlab
    Provider.GitLabSelfHosted -> ApiProvider.gitlabSelfHosted
    Provider.GoogleDrive -> ApiProvider.google
    Provider.GoogleDriveWorkspace -> ApiProvider.googleDriveWorkspace
    Provider.GoogleWorkspace -> ApiProvider.googleWorkspace
    Provider.Jira -> ApiProvider.jira
    Provider.JiraDataCenter -> ApiProvider.jiraDataCenter
    Provider.Linear -> ApiProvider.linear
    Provider.MicrosoftEntra -> ApiProvider.microsoftEntra
    Provider.Notion -> ApiProvider.notion
    Provider.Okta -> ApiProvider.okta
    Provider.PingOne -> ApiProvider.pingOne
    Provider.Slack -> ApiProvider.slack
    Provider.StackOverflowTeams -> ApiProvider.stackOverflowTeams
    Provider.Unblocked -> ApiProvider.unblocked
    Provider.Web -> ApiProvider.web
}

fun SignInSection.asApiModel(): LoginProvider.Section = when (this) {
    SignInSection.BOTTOM -> LoginProvider.Section.bottom
    SignInSection.TOP -> LoginProvider.Section.top
}

@Suppress("CyclomaticComplexMethod")
fun ApiProvider.asProvider(): Provider = when (this) {
    ApiProvider.asana -> Provider.Asana
    ApiProvider.aws -> Provider.Aws
    ApiProvider.awsIdentityCenter -> Provider.AwsIdentityCenter
    ApiProvider.azureDevOps -> Provider.AzureDevOps
    ApiProvider.bitbucket -> Provider.Bitbucket
    ApiProvider.bitbucketDataCenter -> Provider.BitbucketDataCenter
    ApiProvider.bitbucketPipelines -> Provider.BitbucketPipelines
    ApiProvider.buildKite -> Provider.Buildkite
    ApiProvider.circleci -> Provider.CircleCI
    ApiProvider.coda -> Provider.Coda
    ApiProvider.confluence -> Provider.Confluence
    ApiProvider.confluenceDataCenter -> Provider.ConfluenceDataCenter
    ApiProvider.customIntegration -> Provider.CustomIntegration
    ApiProvider.github -> Provider.GitHub
    ApiProvider.githubActions -> Provider.GitHubActions
    ApiProvider.githubEnterprise -> Provider.GitHubEnterprise
    ApiProvider.gitlab -> Provider.GitLab
    ApiProvider.gitlabSelfHosted -> Provider.GitLabSelfHosted
    ApiProvider.google -> Provider.GoogleDrive
    ApiProvider.googleDriveWorkspace -> Provider.GoogleDriveWorkspace
    ApiProvider.googleWorkspace -> Provider.GoogleWorkspace
    ApiProvider.jira -> Provider.Jira
    ApiProvider.jiraDataCenter -> Provider.JiraDataCenter
    ApiProvider.linear -> Provider.Linear
    ApiProvider.microsoftEntra -> Provider.MicrosoftEntra
    ApiProvider.notion -> Provider.Notion
    ApiProvider.okta -> Provider.Okta
    ApiProvider.pingOne -> Provider.PingOne
    ApiProvider.saml -> Provider.GenericSaml
    ApiProvider.slack -> Provider.Slack
    ApiProvider.stackOverflowTeams -> Provider.StackOverflowTeams
    ApiProvider.unblocked -> Provider.Unblocked
    ApiProvider.web -> Provider.Web
}
