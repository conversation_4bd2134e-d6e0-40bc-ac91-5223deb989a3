package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.VersionInfo as ApiVersionInfo
import com.nextchaptersoftware.db.models.VersionInfo
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString

fun VersionInfo.asApiModel(): ApiVersionInfo {
    return com.nextchaptersoftware.api.models.VersionInfo(
        productAgent = AgentType.hub, // this is hard-coded as some older clients assume this matters
        productNumber = productNumber.toString(),
        productVersion = productVersion,
        productSha = productSha.asString(),
        downloadUrl = downloadUrl?.asString,
        downloadChecksum = downloadChecksum,
        markdownDescription = markdownDescription,
        jetbrainsDownloadUrl = jetbrainsDownloadUrl?.asString,
        jetbrainsDownloadChecksum = jetbrainsDownloadChecksum,
        vscodeDownloadUrl = vscodeDownloadUrl?.asString,
        vscodeDownloadChecksum = vscodeDownloadChecksum,
        desktopMacARMDownloadUrl = desktopMacARMDownloadUrl?.asString,
        desktopMacARMDownloadChecksum = desktopMacARMDownloadChecksum,
        desktopMacIntelDownloadUrl = desktopMacIntelDownloadUrl?.asString,
        desktopMacIntelDownloadChecksum = desktopMacIntelDownloadChecksum,
        desktopMacInstallerDownloadUrl = desktopMacInstallerDownloadUrl?.asString,
        desktopMacInstallerDownloadChecksum = desktopMacInstallerDownloadChecksum,
        nodeVersion = nodeVersion,
        nodeMacARMDownloadUrl = nodeMacARMDownloadUrl?.asString,
        nodeMacIntelDownloadUrl = nodeMacIntelDownloadUrl?.asString,
        nodeLinuxIntelDownloadUrl = nodeLinuxIntelDownloadUrl?.asString,
        nodeLinuxARM64DownloadUrl = nodeLinuxARM64DownloadUrl?.asString,
        nodeLinuxARMv7lDownloadUrl = nodeLinuxARMv7lDownloadUrl?.asString,
        nodeWinX64DownloadUrl = nodeWinX64DownloadUrl?.asString,
        nodeWinX86DownloadUrl = nodeWinX86DownloadUrl?.asString,
        nodeWinARM64DownloadUrl = nodeWinARM64DownloadUrl?.asString,
    )
}
