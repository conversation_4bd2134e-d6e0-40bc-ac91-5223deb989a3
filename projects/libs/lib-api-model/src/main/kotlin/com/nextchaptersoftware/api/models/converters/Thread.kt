package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.api.models.Thread as ApiThread
import com.nextchaptersoftware.api.models.ThreadLinks
import com.nextchaptersoftware.api.models.ThreadType
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.models.ThreadSource
import com.nextchaptersoftware.db.models.dashboardUrl
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.utils.asApiDateTime

@Suppress("CyclomaticComplexMethod")
fun Thread.asApiModel(
    orgId: OrgId,
    repo: Repo?,
    urlBuilderProvider: UrlBuilderProvider,
): ApiThread {
    val thread = ApiThread(
        id = id.value,
        teamId = orgId.value, // this assignment of org to team is correct
        title = title,
        lastMessageCreatedAt = lastMessageCreatedAt.asApiDateTime(),
        provider = when (source) {
            // Temporary hack for now, translate source to provider
            ThreadSource.Slack -> Provider.slack

            ThreadSource.Linear -> Provider.linear

            ThreadSource.GitHubIssue -> Provider.github

            ThreadSource.Answer -> Provider.unblocked

            // PR comments: use repo provider
            ThreadSource.PrComment -> repo?.provider?.asApiModel() ?: error("PR Comment is missing Repo")
        },
        archivedAt = archivedAt?.asApiDateTime(),
        archivedBy = archivedBy?.value,
        isDeleted = isDeleted,
        isPrivate = isPrivate,
        threadType = ThreadType.discussion, // TODO to be removed from API
        links = ThreadLinks(
            dashboardUrl = dashboardUrl(urlBuilderProvider).asString,
            webExtensionUrl = "", // TODO to be removed from API
            externalUrl = externalUrl(repo)?.asString,
        ),
    )

    return when (isDeleted) {
        true -> thread.copy(title = "")
        else -> thread
    }
}
