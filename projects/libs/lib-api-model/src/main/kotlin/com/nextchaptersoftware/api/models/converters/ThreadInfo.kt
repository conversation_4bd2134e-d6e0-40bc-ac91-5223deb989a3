package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.ThreadCapabilities
import com.nextchaptersoftware.api.models.ThreadInfo as ApiThreadInfo
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ReferenceBundle
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.models.ThreadSource
import com.nextchaptersoftware.db.stores.PaginatedThreadInfo
import com.nextchaptersoftware.db.stores.SourceMarkBundle
import com.nextchaptersoftware.db.stores.ThreadInfo
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.scm.ScmFileUrlProvider
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.StandardScmFileUrlProvider
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.toEpochMicroseconds

suspend fun List<ThreadInfo>.scmFileUrlProvider(scmWebFactory: ScmWebFactory): ScmFileUrlProvider {
    val teams = mapNotNull { it.referenceBundle }.flatMap { it.teams() }.distinct()
    return StandardScmFileUrlProvider.create(scmWebFactory = scmWebFactory, scmTeams = teams)
}

suspend fun PaginatedThreadInfo.asApiModel(
    orgId: OrgId,
    authorizedMemberId: OrgMemberId,
    urlBuilderProvider: UrlBuilderProvider,
    scmFileUrlProvider: ScmFileUrlProvider,
    botAccountService: BotAccountService,
): ApiThreadInfo {
    return info.asApiModel(
        orgId = orgId,
        authorizedMemberId = authorizedMemberId,
        urlBuilderProvider = urlBuilderProvider,
        scmFileUrlProvider = scmFileUrlProvider,
        botAccountService = botAccountService,
    ).copy(cursor = cursor.value)
}

suspend fun ThreadInfo.asApiModel(
    orgId: OrgId,
    authorizedMemberId: OrgMemberId,
    urlBuilderProvider: UrlBuilderProvider,
    botAccountService: BotAccountService,
    scmFileUrlProvider: ScmFileUrlProvider,
): ApiThreadInfo {
    return ApiThreadInfo(
        thread = thread.asApiModel(
            orgId = orgId,
            repo = repo,
            urlBuilderProvider = urlBuilderProvider,
        ),
        messages = messages.map { message: Message ->
            message.asApiModel(
                orgId = orgId,
                repo = repo,
                thread = thread,
                urlBuilderProvider = urlBuilderProvider,
                mentions = mentions.filter { it.messageId == message.id }.mapNotNull { it.memberId?.value },
                feedback = feedback?.filter { it.messageId == message.id }.orEmpty(),
                followupSuggestions = suggestions?.filter { it.messageId == message.id },
                referenceBundle = referenceBundle ?: ReferenceBundle.empty(),
                botAccountService = botAccountService,
                scmFileUrlProvider = scmFileUrlProvider,
                presetsById = presetsById,
            )
        },
        repoId = (sourcemark?.repoId ?: repo?.id)?.value,
        participants = participantOrgMemberIds.map { it.value },
        modifiedAt = lastModified.toEpochMicroseconds(),
        mark = sourcemark?.let { SourceMarkBundle(sourceMark = it, sourcePoints = originalSourcePoints).asApiModel() },
        pullRequest = pullRequest?.let {
            it.asApiModel(
                orgId = orgId,
                provider = checkNotNull(reposById[it.repoId]).provider,
                urlBuilderProvider = urlBuilderProvider,
            )
        },
        pullRequests = pullRequests?.map {
            it.asApiModel(
                orgId = orgId,
                provider = checkNotNull(reposById[it.repoId]).provider,
                urlBuilderProvider = urlBuilderProvider,
            )
        },
        unread = unread?.asApiModel(),
        mentions = mentions.map { it.asApiModel() },
        slack = slackChannel?.asApiSlackThreadInfo(),
        topics = topicIds?.map { it.value },
        experts = expertOrgMemberIds?.map { it.value },
        capabilities = ThreadCapabilities(
            canReply = thread.canReply,
            canUpdatePrivacy = thread.authorOrgMemberId == authorizedMemberId,
        ),
        sensitiveDataSources = messages.flatMap { it.sensitiveDataSources }.toSet().map { it.asApiModel() }.nullIfEmpty(),
    )
}

private val Thread.canReply: Boolean
    get() = when (this.source) {
        ThreadSource.Answer,
            -> true

        ThreadSource.Slack,
        ThreadSource.Linear,
        ThreadSource.GitHubIssue,
            -> false

        ThreadSource.PrComment -> listOf(Provider.GitHub, Provider.GitHubEnterprise).contains(this.provider)
    }
