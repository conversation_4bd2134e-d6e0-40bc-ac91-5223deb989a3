package com.nextchaptersoftware.api.integration.extension.services.slack

import com.nextchaptersoftware.api.models.SlackConfigurationV3
import com.nextchaptersoftware.api.models.SlackConfigurationV4
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.models.converters.asApiV4Model
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.SlackChannelPatternPreferencesStore
import com.nextchaptersoftware.db.stores.SlackChannelPreferencesStore
import com.nextchaptersoftware.db.stores.SlackDefaultChannelPreferencesStore
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.UnknownSlackTeamException
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.bot.events.queue.payloads.SlackBotChannelEvent
import com.nextchaptersoftware.slack.events.queue.enqueue.SlackEventEnqueueService
import com.nextchaptersoftware.slack.events.queue.payloads.SlackTeamEvent
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackChannelPatternPreferenceEntry
import com.nextchaptersoftware.slack.services.SlackChannelPatternPreferencesService
import com.nextchaptersoftware.slack.services.SlackChannelPreferenceEntry
import com.nextchaptersoftware.slack.services.SlackChannelPreferencesService
import com.nextchaptersoftware.slack.services.SlackPreferences
import org.jetbrains.exposed.sql.Transaction

class SlackConfigurationService(
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    private val slackChannelPatternPreferencesStore: SlackChannelPatternPreferencesStore = Stores.slackChannelPatternPreferencesStore,
    private val slackChannelPreferencesStore: SlackChannelPreferencesStore = Stores.slackChannelPreferencesStore,
    private val slackDefaultChannelPreferencesStore: SlackDefaultChannelPreferencesStore = Stores.slackDefaultChannelPreferencesStore,
    private val slackChannelFilterService: SlackChannelFilterService,
    private val slackChannelPatternPreferencesService: SlackChannelPatternPreferencesService,
    private val slackEventEnqueueService: SlackEventEnqueueService,
    private val slackBotEventEnqueueService: SlackBotEventEnqueueService,
    private val slackChannelPreferencesService: SlackChannelPreferencesService,
) {
    private suspend fun checkSlackTeamExists(
        trx: Transaction? = null,
        orgId: OrgId,
        slackTeamId: SlackTeamId,
    ) = suspendedTransaction(trx = trx) {
        val slackTeamExists = slackTeamStore.findByOrgs(trx = this, orgIds = listOf(orgId)).any { it.idValue == slackTeamId }

        if (!slackTeamExists) {
            throw UnknownSlackTeamException(slackTeamId = slackTeamId)
        }
    }

    suspend fun getSlackConfigurationV3(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
    ): SlackConfigurationV3 = suspendedTransaction {
        checkSlackTeamExists(trx = this, orgId = orgId, slackTeamId = slackTeamId)

        val slackChannelPatternPreferences = slackChannelPatternPreferencesStore.findBySlackTeamId(
            trx = this,
            slackTeamId = slackTeamId,
        )

        val slackChannels = slackChannelFilterService.getValidChannels(
            trx = this,
            slackTeamId = slackTeamId,
            orgId = orgId,
            orgMemberId = orgMemberId,
        )

        val slackChannelPreferences = slackChannelPreferencesStore.findBySlackTeamId(
            trx = this,
            slackTeamId = slackTeamId,
            slackChannelIds = slackChannels.map { it.id },
        ).associateBy {
            it.slackChannelId
        }

        SlackConfigurationV3(
            channelConfigurations = slackChannels.sortedBy { it.name }.mapNotNull { slackChannel ->
                slackChannelPreferences[slackChannel.id]?.asApiModel(slackChannel = slackChannel.asApiModel(orgId = orgId))
            },
            channelPatternConfigurations = slackChannelPatternPreferences.map {
                it.asApiModel()
            },
        )
    }

    suspend fun getSlackConfigurationV4(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
    ): SlackConfigurationV4 = suspendedTransaction {
        checkSlackTeamExists(trx = this, orgId = orgId, slackTeamId = slackTeamId)

        val slackChannelPatternPreferences = slackChannelPatternPreferencesStore.findBySlackTeamId(
            trx = this,
            slackTeamId = slackTeamId,
        )

        val slackChannels = slackChannelFilterService.getValidChannels(
            trx = this,
            slackTeamId = slackTeamId,
            orgId = orgId,
            orgMemberId = orgMemberId,
        )

        val slackChannelPreferences = slackChannelPreferencesStore.findBySlackTeamId(
            trx = this,
            slackTeamId = slackTeamId,
            slackChannelIds = slackChannels.map { it.id },
        ).associateBy {
            it.slackChannelId
        }

        val defaultChannelPreferences = slackDefaultChannelPreferencesStore.getDefault(
            trx = this,
            slackTeamId = slackTeamId,
        )

        SlackConfigurationV4(
            channelConfigurations = slackChannels.sortedBy { it.name }.mapNotNull { slackChannel ->
                slackChannelPreferences[slackChannel.id]?.asApiV4Model(
                    slackChannel = slackChannel.asApiModel(orgId = orgId),
                )
            },
            channelPatternConfigurations = slackChannelPatternPreferences.map {
                it.asApiV4Model()
            },
            defaultChannelSettings = defaultChannelPreferences.asApiModel(),
        )
    }

    suspend fun updateSlackConfigurationV3(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
        slackChannelPatternPreferenceEntries: List<SlackChannelPatternPreferenceEntry>,
        slackChannelPreferenceEntries: List<SlackChannelPreferenceEntry>,
    ) = suspendedTransaction {
        checkSlackTeamExists(trx = this, orgId = orgId, slackTeamId = slackTeamId)

        val previouslyValidSlackChannels = slackChannelFilterService.getValidChannels(
            trx = this,
            orgId = orgId,
            slackTeamId = slackTeamId,
            orgMemberId = null,
        )

        slackChannelPatternPreferencesService.upsert(
            trx = this,
            slackTeamId = slackTeamId,
            slackChannelPatternPreferenceEntries = slackChannelPatternPreferenceEntries,
        )

        slackChannelPreferencesService.upsert(
            trx = this,
            orgId = orgId,
            orgMemberId = orgMemberId,
            slackTeamId = slackTeamId,
            slackChannelPreferenceEntries = slackChannelPreferenceEntries,
            defaultChannelPreferences = null,
        )

        previouslyValidSlackChannels
    }.also { previouslyValidSlackChannels ->
        slackEventEnqueueService.enqueueEvent(
            event = SlackTeamEvent.SlackIngestionEvent(
                orgId = orgId,
                slackTeamId = slackTeamId,
            ),
        )

        slackBotEventEnqueueService.enqueueEvent(
            SlackBotChannelEvent.SlackBotChannelsJoinEvent(
                slackTeamId = slackTeamId,
                slackChannelIds = slackChannelPreferenceEntries.map { it.slackChannelId },
            ),
        )

        slackBotEventEnqueueService.enqueueEvent(
            SlackBotChannelEvent.SlackBotChannelsLeaveEvent(
                slackTeamId = slackTeamId,
                slackChannelIds = previouslyValidSlackChannels.map { it.id },
            ),
        )

        deleteEmbeddingsIfNoLongerValid(
            orgId = orgId,
            slackTeamId = slackTeamId,
            slackChannels = previouslyValidSlackChannels,
        )
    }

    suspend fun updateSlackConfigurationV4(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
        slackChannelPatternPreferenceEntries: List<SlackChannelPatternPreferenceEntry>,
        slackChannelPreferenceEntries: List<SlackChannelPreferenceEntry>,
        defaultChannelPreferences: SlackPreferences?,
    ) = suspendedTransaction {
        checkSlackTeamExists(trx = this, orgId = orgId, slackTeamId = slackTeamId)

        val previouslyValidSlackChannels = slackChannelFilterService.getValidChannels(
            trx = this,
            orgId = orgId,
            slackTeamId = slackTeamId,
            orgMemberId = null,
        )

        slackChannelPatternPreferencesService.upsert(
            trx = this,
            slackTeamId = slackTeamId,
            slackChannelPatternPreferenceEntries = slackChannelPatternPreferenceEntries,
        )

        slackChannelPreferencesService.upsert(
            trx = this,
            orgId = orgId,
            orgMemberId = orgMemberId,
            slackTeamId = slackTeamId,
            slackChannelPreferenceEntries = slackChannelPreferenceEntries,
            defaultChannelPreferences = defaultChannelPreferences,
        )

        previouslyValidSlackChannels
    }.also { previouslyValidSlackChannels ->
        slackEventEnqueueService.enqueueEvent(
            event = SlackTeamEvent.SlackIngestionEvent(
                orgId = orgId,
                slackTeamId = slackTeamId,
            ),
        )

        slackBotEventEnqueueService.enqueueEvent(
            SlackBotChannelEvent.SlackBotChannelsJoinEvent(
                slackTeamId = slackTeamId,
                slackChannelIds = slackChannelPreferenceEntries.map { it.slackChannelId },
            ),
        )

        slackBotEventEnqueueService.enqueueEvent(
            SlackBotChannelEvent.SlackBotChannelsLeaveEvent(
                slackTeamId = slackTeamId,
                slackChannelIds = previouslyValidSlackChannels.map { it.id },
            ),
        )

        deleteEmbeddingsIfNoLongerValid(
            orgId = orgId,
            slackTeamId = slackTeamId,
            slackChannels = previouslyValidSlackChannels,
        )
    }

    private suspend fun deleteEmbeddingsIfNoLongerValid(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        slackChannels: List<SlackChannel>,
    ) {
        if (slackChannels.isEmpty()) {
            return
        }

        checkSlackTeamExists(orgId = orgId, slackTeamId = slackTeamId)

        val channelsToDelete = slackChannelFilterService.getValidity(
            trx = null,
            orgId = orgId,
            slackTeamId = slackTeamId,
            slackChannels = slackChannels,
            orgMemberId = null,
        ).filter { (_, valid) -> !valid }.keys

        if (channelsToDelete.isEmpty()) {
            return
        }

        installationStore.findBySlackTeam(orgId = orgId, slackTeamId = slackTeamId)?.let { installation ->
            channelsToDelete.forEach {
                embeddingDeleteStore.markGroupForDeletion(
                    namespaceId = orgId,
                    installationId = installation.id,
                    groupId = it.value,
                )
            }
        }
    }
}
