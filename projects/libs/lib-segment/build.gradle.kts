plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-segment", configuration = "default"))
    implementation(project(":projects:libs:lib-log-kotlin", configuration = "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)
    testImplementation(testLibs.bundles.test.postgresql)
}
