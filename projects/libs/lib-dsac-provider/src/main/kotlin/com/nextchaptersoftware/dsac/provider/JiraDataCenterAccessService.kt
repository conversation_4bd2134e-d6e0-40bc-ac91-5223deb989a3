package com.nextchaptersoftware.dsac.provider

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decompressAndDeserialize
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.JiraGroup
import com.nextchaptersoftware.db.models.JiraPermissionScheme
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.stores.JiraGroupStore
import com.nextchaptersoftware.db.stores.JiraPermissionSchemeStore
import com.nextchaptersoftware.db.stores.JiraProjectRoleStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.jira.models.DataCenterPermissionScheme
import com.nextchaptersoftware.jira.models.DataCenterRole
import kotlin.collections.map

class JiraDataCenterAccessService(
    private val jiraPermissionSchemeStore: JiraPermissionSchemeStore = Stores.jiraPermissionSchemeStore,
    private val jiraGroupStore: JiraGroupStore = Stores.jiraGroupStore,
    private val jiraProjectRoleStore: JiraProjectRoleStore = Stores.jiraProjectRoleStore,
) {
    suspend fun allowedProjects(
        installationId: InstallationId,
        identities: List<Identity>,
        projectIds: List<JiraProjectId>,
    ): Set<JiraProjectId> {
        return identities.flatMap { identity ->
            val groups = jiraGroupStore.listGroupsForUser(
                installationId = installationId,
                userId = identity.externalId,
            )

            val permissionSchemes = jiraPermissionSchemeStore.getPermissionSchemes(
                installationId = installationId,
                jiraProjectIds = projectIds.toSet(),
            )

            allowed(
                identity = identity,
                groups = groups,
                permissionSchemes = permissionSchemes,
            )
        }.toSet()
    }

    internal suspend fun allowed(
        identity: Identity,
        groups: List<JiraGroup>,
        permissionSchemes: Map<JiraProjectId, JiraPermissionScheme>,
    ): List<JiraProjectId> {
        return permissionSchemes.filter { (jiraProjectId, jiraPermissionScheme) ->
            val scheme = jiraPermissionScheme.permissionScheme.decompressAndDeserialize<DataCenterPermissionScheme>()

            if (scheme.hasPublicBrowsePermission || scheme.hasAnyLoggedInUserBrowsePermission) {
                return@filter true
            }

            // Only fetch the project's roles if its scheme grants browse permissions for to a project role
            val roles = when (scheme.hasProjectRoleBrowsePermission) {
                true -> jiraProjectRoleStore.list(jiraProjectId = jiraProjectId).map { it.role.decompressAndDeserialize<DataCenterRole>() }
                else -> emptyList()
            }

            scheme.grantedBrowsePermissions(identity = identity, groups = groups, roles = roles).isNotEmpty()
        }.map {
            it.key
        }
    }
}

/**
 * Gets the permissions from the permission scheme that grants the identity view access to the project
 * */
internal fun DataCenterPermissionScheme.grantedBrowsePermissions(
    identity: Identity,
    groups: List<JiraGroup>,
    roles: List<DataCenterRole>,
): List<DataCenterPermissionScheme.Permission> = browsePermissions.filter { permission ->
    when (permission.holder.type) {
        "applicationRole",
            -> groups.any { it.applicationRoles.contains(permission.holder.parameter) } || permission.holder.parameter == null

        "user",
            -> identity.externalId == permission.holder.parameter

        "group",
            -> groups.any { it.groupId == permission.holder.parameter }

        "projectRole",
            -> roles.firstOrNull { it.id.toString() == permission.holder.parameter }?.actors.orEmpty().any { actor ->
            when (actor.type) {
                "atlassian-user-role-actor" -> identity.username == actor.name
                "atlassian-group-role-actor" -> groups.any { it.groupId == actor.name }
                else -> false
            }
        }

        "anyone",
            -> true

        // TODO implement
        "assignee",
        "reporter",
        "projectLead",
        "groupCustomField",
            -> false

        else
            -> {
            false
        }
    }
}
