package com.nextchaptersoftware.dsac.filter

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.dsac.provider.NotionDocumentAccessProvider

class NotionDsacFilter(
    override val dsacContext: DsacContext,
    override val installationId: InstallationId,
    private val notionDocumentAccessProvider: NotionDocumentAccessProvider,
) : InstallationDsacFilter() {

    override suspend fun filterInstallationDocuments(
        orgId: OrgId,
        installationId: InstallationId,
        documents: List<MLTypedDocument>,
    ): List<MLTypedDocument> {
        return notionDocumentAccessProvider.filterDocuments(
            installationId = installationId,
            dsacContext = dsacContext,
            documents = documents,
        )
    }
}
