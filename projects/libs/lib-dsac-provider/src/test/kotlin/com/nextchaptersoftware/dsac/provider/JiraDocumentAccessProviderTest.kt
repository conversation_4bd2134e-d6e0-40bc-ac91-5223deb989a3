package com.nextchaptersoftware.dsac.provider

import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeJiraProject
import com.nextchaptersoftware.db.ModelBuilders.makeJiraSite
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.IssueStatus
import com.nextchaptersoftware.db.models.JiraIssue
import com.nextchaptersoftware.db.models.JiraProjectDAO
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.JiraIssueStore
import com.nextchaptersoftware.db.stores.MemberIdentityPair
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.api.JiraIssuesApi
import com.nextchaptersoftware.jira.models.IssueResults
import com.nextchaptersoftware.jira.models.IssueWithoutFields
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.http.Url
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq

class JiraDocumentAccessProviderTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var orgMember: OrgMemberDAO
    private lateinit var installation: InstallationDAO
    private lateinit var jiraSite: JiraSiteDAO
    private lateinit var jiraProject: JiraProjectDAO
    private lateinit var person: PersonDAO
    private lateinit var identity: IdentityDAO

    private lateinit var jiraIssueA: JiraIssue
    private lateinit var jiraIssueB: JiraIssue

    private val atlassianTokenProvider: AtlassianTokenProvider = mock()
    private val apiProvider: JiraApiProvider = mock()
    private val issuesApi: JiraIssuesApi = mock()
    private val documentAccessProvider = JiraDocumentAccessProvider(atlassianTokenProvider, apiProvider)
    private val dsacContext = mock<DsacContext>()

    private val mockInstallationOAuthTokens = OAuthTokens(accessToken = Secret("installation"))
    private val mockIdentityOAuthTokens = OAuthTokens(accessToken = Secret("identity"))

    private suspend fun setup() {
        org = makeOrg()
        orgMember = makeOrgMember(org = org)
        installation = makeInstallation(org = org, provider = Provider.Jira)
        jiraSite = makeJiraSite(installation = installation)
        jiraProject = makeJiraProject(site = jiraSite)
        person = makePerson()
        identity = makeIdentity(person = person, provider = Provider.Jira, rawAccessToken = "token".toByteArray())

        `when`(dsacContext.isDsacEnforced(installation.idValue)).thenReturn(true)
        `when`(dsacContext.getMemberIdentityPair(installation.idValue)).thenReturn(
            MemberIdentityPair(
                member = makeMember(identity = identity).asDataModel(),
                identity = identity.asDataModel(),
            ),
        )

        `when`(
            atlassianTokenProvider.getOAuthTokens(
                identityId = identity.idValue,
                provider = Provider.Jira,
            ),
        ).thenReturn(mockIdentityOAuthTokens)

        `when`(
            atlassianTokenProvider.getOAuthTokens(
                installationId = eq(installation.idValue),
                withScope = anyOrNull(),
            ),
        ).thenReturn(mockInstallationOAuthTokens)

        jiraIssueA = Stores.jiraIssueStore.upsert(
            id = JiraIssueStore.generateJiraIssueId(
                installationId = installation.idValue,
                projectId = jiraProject.projectId,
                issueId = "10000",
            ),
            installationId = installation.idValue,
            project = jiraProject.asDataModel(),
            issueId = "10000",
            issueKey = "KEY-10000",
            issueStatus = IssueStatus.Completed,
            reporter = orgMember.idValue,
            assignee = null,
            createdAt = Instant.nowWithMicrosecondPrecision(),
            updatedAt = Instant.nowWithMicrosecondPrecision(),
            resolvedAt = null,
            isRestricted = false,
            needsExtraction = false,
            needsEmbedding = false,
        )

        jiraIssueB = Stores.jiraIssueStore.upsert(
            id = JiraIssueStore.generateJiraIssueId(
                installationId = installation.idValue,
                projectId = jiraProject.projectId,
                issueId = "10001",
            ),
            installationId = installation.idValue,
            project = jiraProject.asDataModel(),
            issueId = "10001",
            issueKey = "KEY-10001",
            issueStatus = IssueStatus.Completed,
            reporter = orgMember.idValue,
            assignee = null,
            createdAt = Instant.nowWithMicrosecondPrecision(),
            updatedAt = Instant.nowWithMicrosecondPrecision(),
            resolvedAt = null,
            isRestricted = false,
            needsExtraction = false,
            needsEmbedding = false,
        )

        `when`(apiProvider.jiraIssuesApi).thenReturn(issuesApi)
        `when`(issuesApi.getIssuesWithoutFields(url = any(), tokens = any())).thenReturn(
            IssueResults(
                issues = listOf(IssueWithoutFields(id = "10001", key = "KEY-1", self = Url("https://example.com"))),
                startAt = 0,
                maxResults = 0,
                total = 0,
            ),
        )
    }

    @Test
    fun filterDocuments() = suspendingDatabaseTest {
        setup()

        val documentA = MockDataClasses.typedDocument(sourceId = jiraIssueA.id.value, provider = Provider.Jira)
        val documentB = MockDataClasses.typedDocument(sourceId = jiraIssueB.id.value, provider = Provider.Jira)

        val result = documentAccessProvider.filterDocuments(
            installationId = installation.idValue,
            dsacContext = dsacContext,
            documents = listOf(documentA, documentB),
        )

        assertThat(result).containsExactly(documentB)
    }
}
