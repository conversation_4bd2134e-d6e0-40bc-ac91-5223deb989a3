package com.nextchaptersoftware.plan.capabilities

import com.nextchaptersoftware.config.BillingConfig
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanCapability
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.PlanId
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.PlanCapabilityStore
import com.nextchaptersoftware.db.stores.PlanStore
import com.nextchaptersoftware.db.stores.Stores
import java.util.Stack

interface PlanCapabilitiesService {
    suspend fun resolveCapabilities(planId: PlanId, capabilities: Set<PlanCapabilityType>? = null): Map<PlanCapabilityType, Boolean>

    suspend fun getAllowedCapabilities(orgId: OrgId): Set<PlanCapabilityType>?

    suspend fun hasCapability(orgId: OrgId, planCapabilityType: PlanCapabilityType): Boolean
}

class PlanCapabilitiesServiceImpl(
    private val orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    private val planCapabilityStore: PlanCapabilityStore = Stores.planCapabilityStore,
    private val planStore: PlanStore = Stores.planStore,
) : PlanCapabilitiesService {
    override suspend fun resolveCapabilities(
        planId: PlanId,
        capabilities: Set<PlanCapabilityType>?, // null means all capabilities
    ): Map<PlanCapabilityType, Boolean> {
        val stack = Stack<List<PlanCapability>>()

        val visited = mutableListOf<PlanId>()
        var nextPlanId: PlanId? = planId

        while (nextPlanId != null && !visited.contains(nextPlanId)) {
            val plan = planStore.find(nextPlanId).also { visited.add(it.id) }
            stack.add(planCapabilityStore.find(plan.id, capabilities))
            nextPlanId = plan.basePlanId
        }

        val capabilitiesByType = mutableMapOf<PlanCapabilityType, Boolean>()

        while (stack.isNotEmpty()) {
            stack.pop().forEach { capabilitiesByType[it.capability] = it.enabled }
        }

        return capabilitiesByType
    }

    override suspend fun getAllowedCapabilities(orgId: OrgId): Set<PlanCapabilityType>? {
        // Not safe to proceed if the org has no billing model
        val orgBilling = orgBillingStore.findByOrg(orgId)
            ?: return null

        return resolveCapabilities(orgBilling.planId)
            .filter { (_, enabled) -> enabled }
            .keys
    }

    override suspend fun hasCapability(orgId: OrgId, planCapabilityType: PlanCapabilityType): Boolean {
        // Not safe to proceed if the org has no billing model
        val orgBilling = orgBillingStore.findByOrg(orgId)
            ?: return false

        return resolveCapabilities(orgBilling.planId, setOf(planCapabilityType))
            .filter { (_, enabled) -> enabled }
            .keys
            .contains(planCapabilityType)
    }
}

class NoopPlanCapabilitiesService : PlanCapabilitiesService {
    override suspend fun resolveCapabilities(planId: PlanId, capabilities: Set<PlanCapabilityType>?): Map<PlanCapabilityType, Boolean> {
        return PlanCapabilityType.entries.associateWith { true }
    }

    override suspend fun getAllowedCapabilities(orgId: OrgId): Set<PlanCapabilityType>? {
        return PlanCapabilityType.entries.toSet()
    }

    override suspend fun hasCapability(orgId: OrgId, planCapabilityType: PlanCapabilityType): Boolean {
        return true
    }
}

class PlanCapabilitiesServiceProvider(val config: BillingConfig) {
    fun get(): PlanCapabilitiesService = when (config.enabled) {
        true -> PlanCapabilitiesServiceImpl()
        false -> NoopPlanCapabilitiesService()
    }
}
