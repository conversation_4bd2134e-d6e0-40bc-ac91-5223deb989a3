package com.nextchaptersoftware.ci.triage

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BuildTriageServiceTest {

    @Nested
    inner class MatchFileByPath {

        @Test
        fun `matchFileByPath -- empty PR files`() {
            assertThat(
                matchFileByPath(
                    files = emptySet(),
                    path = "foo.txt",
                ),
            ).isNull()
        }

        @Test
        fun `matchFileByPath -- single file`() {
            val files = setOf(
                "path/to/real/file/script.sh",
            )
            assertThat(
                matchFileByPath(
                    files = files,
                    path = "foo.txt",
                ),
            ).isNull()

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "file/script.sh",
                ),
            ).isEqualTo(
                "path/to/real/file/script.sh",
            )

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "wrong/path/to/file/script.sh",
                ),
            ).isEqualTo(
                "path/to/real/file/script.sh",
            )
        }

        @Test
        fun `matchFileByPath -- conflicts`() {
            val files = setOf(
                "path/one/to/script.sh",
                "path/two/to/script.sh",
            )
            assertThat(
                matchFileByPath(
                    files = files,
                    path = "foo.txt",
                ),
            ).isNull()

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "script.sh",
                ),
            ).isNull()

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "wrong/path/to/file/script.sh",
                ),
            ).isNull()

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "one/to/script.sh",
                ),
            ).isEqualTo(
                "path/one/to/script.sh",
            )

            assertThat(
                matchFileByPath(
                    files = files,
                    path = "wrong/path/two/to/script.sh",
                ),
            ).isEqualTo(
                "path/two/to/script.sh",
            )
        }
    }
}
