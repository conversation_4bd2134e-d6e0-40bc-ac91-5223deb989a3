package com.nextchaptersoftware.ci.triage

import com.nextchaptersoftware.ci.triage.diff.ChunkDiffResult
import com.nextchaptersoftware.ci.triage.diff.DiffChunk
import com.nextchaptersoftware.ci.triage.diff.DiffParser
import com.nextchaptersoftware.db.MockDataClasses.build
import com.nextchaptersoftware.db.MockDataClasses.buildJob
import com.nextchaptersoftware.db.MockDataClasses.org
import com.nextchaptersoftware.db.MockDataClasses.pullRequest
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.ml.prompt.services.CompiledPromptResult
import com.nextchaptersoftware.test.utils.TestArguments
import kotlin.collections.joinToString
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ArgumentsSource
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.reset
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

private const val RETRACTED_MESSAGE = "<-- HUNK PARTIALLY RETRACTED AS IRRELEVANT -->"
private const val MIN_DIFF_SIZE_FOR_COMPRESSION = 30_000
private const val MAX_HUNK_SIZE = 3_000

class BuildTriageDiffCompressionServiceTest {
    private val org = org()
    private val pullRequest = pullRequest()
    private val build = build()
    private val job = buildJob()
    private val defaultDiffChunk = DiffChunk(
        id = "D-0",
        content = "Diff chunk content",
        fileMetadata = "File metadata",
        isFirst = true,
        isLast = true,
    )
    private val jobLogSummary = "Build log summary"
    private val queryExecutor: BuildTriageQueryExecutor = mock()
    private val diffParser: DiffParser = mock()
    private val longDiff = "a".repeat(MIN_DIFF_SIZE_FOR_COMPRESSION + 1)

    private val service = BuildTriageDiffCompressionService(
        queryExecutor = queryExecutor,
        diffParser = diffParser,
    )

    @BeforeEach
    fun setUp() = runTest {
        reset(queryExecutor)
    }

    private suspend fun compressSuccess(longDiff: String): String {
        val actual = service.compressDiffIfNecessary(
            org = org,
            pullRequest = pullRequest,
            build = build,
            job = job,
            jobLogSummary = jobLogSummary,
            rawDiff = longDiff,
        )
        return actual.unwrapOrElseStop {
            fail("Diff should be compressed with no issues. ${it.stage}")
        }
            .contents
    }

    @Test
    fun `it should not compress diff if it's too short`() = runTest {
        // Given a short diff
        val smallDiff = "a".repeat(MIN_DIFF_SIZE_FOR_COMPRESSION - 1)
        // When
        val actual = compressSuccess(smallDiff)
        // Then
        assertThat(actual).isEqualTo(smallDiff)
        verify(queryExecutor, never()).executeWithRetry(
            templateKind = any(),
            miscellaneousInfo = any(),
            org = any(),
            inferenceType = any(),
            responseKlass = eq(SelectedDiffChunks::class),
            questioner = anyOrNull(),
            documents = anyOrNull(),
            answer = anyOrNull(),
        )
        verify(diffParser, never()).chunkDiff(any())
    }

    @Test
    fun `it should compress diff if it's too long`() = runTest {
        // Given a long diff
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = listOf(defaultDiffChunk),
                annotatedDiff = "Annotated Diff",
            ),
        )
        setupQueryExecutorToReturn(SelectedDiffChunks(chunks = listOf(defaultDiffChunk.id).joinToString(",")))
        // When
        val actual = compressSuccess(longDiff)
        // Then
        val expectedDiff = """
            |${defaultDiffChunk.fileMetadata}
            |${defaultDiffChunk.content}
        """.trimMargin()
        assertThat(actual).describedAs("Diff should be compressed").isNotEqualTo(longDiff)
        assertThat(actual).describedAs("Diff should contain only selected chunk").isEqualTo(expectedDiff)
        verify(queryExecutor)
            .executeWithRetry(
                templateKind = eq(MLInferenceTemplateKind.CITriagePrDiffCompression),
                miscellaneousInfo = any(),
                org = eq(org),
                inferenceType = eq("Compress PR Diff"),
                responseKlass = eq(SelectedDiffChunks::class),
                questioner = eq(null),
                documents = eq(null),
                answer = eq(null),
            )
    }

    @Test
    fun `it should add file headers for separate hanks`() = runTest {
        // Given a long diff
        // And model selects first parts of each hunk
        val diffChunk1 = defaultDiffChunk.copy(id = "D-0.0", content = "diff 1")
        val diffChunk2 = defaultDiffChunk.copy(id = "D-1.0", content = "diff 2")
        val selectedDiffChunks = SelectedDiffChunks(chunks = listOf(diffChunk1.id, diffChunk2.id).joinToString(","))
        setupQueryExecutorToReturn(selectedDiffChunks)
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = listOf(diffChunk1, diffChunk2),
                annotatedDiff = "Annotated Diff",
            ),
        )
        // When
        val actual = compressSuccess(longDiff)
        // Then
        val expectedDiff = """
            |${diffChunk1.fileMetadata}
            |${diffChunk1.content}
            |${diffChunk2.fileMetadata}
            |${diffChunk2.content}
        """.trimMargin()
        assertThat(actual).isEqualTo(expectedDiff)
    }

    @Test
    fun `it should keep file metadata if selected hank part is in the middle`() = runTest {
        // Given two chunks in the middle of the diff
        val diffChunk1 = defaultDiffChunk.copy(id = "D-0.1", content = "diff 1", isFirst = false, isLast = false)
        val diffChunk2 = defaultDiffChunk.copy(id = "D-1.1", content = "diff 2", isFirst = false, isLast = false)
        val selectedDiffChunks = SelectedDiffChunks(chunks = listOf(diffChunk1.id, diffChunk2.id).joinToString(","))
        setupQueryExecutorToReturn(selectedDiffChunks)
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = listOf(diffChunk1, diffChunk2),
                annotatedDiff = "Annotated Diff",
            ),
        )
        // When
        val expectedDiff = """
            |${diffChunk1.fileMetadata}
            |$RETRACTED_MESSAGE
            |${diffChunk1.content}
            |$RETRACTED_MESSAGE
            |${diffChunk2.fileMetadata}
            |$RETRACTED_MESSAGE
            |${diffChunk2.content}
            |$RETRACTED_MESSAGE
        """.trimMargin()

        val actual = compressSuccess(longDiff)
        // Then
        assertThat(actual).isEqualTo(expectedDiff)
    }

    @Test
    fun `it should join hunks if middle part is not selected`() = runTest {
        // Given the first and last chunk in a file
        val diffChunk1 = defaultDiffChunk.copy(id = "D-0.0", content = "diff 1", isFirst = true, isLast = false)
        val diffChunk2 = defaultDiffChunk.copy(id = "D-0.2", content = "diff 2", isFirst = false, isLast = true)
        val selectedDiffChunks = SelectedDiffChunks(chunks = listOf(diffChunk1.id, diffChunk2.id).joinToString(","))
        setupQueryExecutorToReturn(selectedDiffChunks)
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = listOf(diffChunk1, diffChunk2),
                annotatedDiff = "Annotated Diff",
            ),
        )
        // When
        val expectedDiff = """
            |${diffChunk1.fileMetadata}
            |${diffChunk1.content}
            |$RETRACTED_MESSAGE
            |${diffChunk2.content}
        """.trimMargin()

        val actual = compressSuccess(longDiff)
        // Then
        assertThat(actual).isEqualTo(expectedDiff)
    }

    private suspend fun setupQueryExecutorToReturn(selectedDiffChunks: SelectedDiffChunks) {
        whenever(
            queryExecutor.executeWithRetry(
                templateKind = any(),
                miscellaneousInfo = any(),
                org = any(),
                inferenceType = any(),
                responseKlass = eq(SelectedDiffChunks::class),
                questioner = anyOrNull(),
                documents = anyOrNull(),
                answer = anyOrNull(),
            ),
        ).thenReturn(selectedDiffChunks.toQueryResult())
    }

    class InvalidChunkIdProvider : TestArguments<Arguments>(
        Arguments.of(listOf("")),
        Arguments.of(listOf("D-200.0")),
    )

    @ParameterizedTest
    @ArgumentsSource(InvalidChunkIdProvider::class)
    fun `it should return default message if no diff chunks are selected`(invalidIds: List<String>) = runTest {
        // Given
        setupQueryExecutorToReturn(SelectedDiffChunks(chunks = invalidIds.joinToString(",")))
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = listOf(defaultDiffChunk),
                annotatedDiff = "Annotated Diff",
            ),
        )
        // When
        val actual = compressSuccess(longDiff)

        // Then
        assertThat(actual).isEqualTo("<NO DIFF SELECTED>")
    }

    @Test
    fun `it should reject invalid diff`() = runTest {
        whenever(diffParser.chunkDiff(any())).thenReturn(null)

        val actual = service.compressDiffIfNecessary(
            org = org,
            pullRequest = pullRequest,
            build = build,
            job = job,
            jobLogSummary = jobLogSummary,
            rawDiff = longDiff,
        )
        assertThat(actual).isInstanceOf(TriagePipelineAnswer.Stop::class.java)
        val stop = actual as TriagePipelineAnswer.Stop
        assertThat(stop.stage).describedAs("Should reject invalid diff").isEqualTo(BuildTriageFilterStage.TriageInvalidDiff)
    }

    @Test
    fun `it should respect chunk ids order and do not exceed max output size if not all selected chunks fit`() = runTest {
        // Given a long diff with hunks double the max size
        val numberOfChunks = MIN_DIFF_SIZE_FOR_COMPRESSION * 2 / MAX_HUNK_SIZE
        val chunks = (0 until numberOfChunks).map {
            DiffChunk(
                id = "D-$it",
                fileMetadata = "file-$it",
                content = (it % 10).toString().repeat(MAX_HUNK_SIZE),
                isFirst = true,
                isLast = true,
            )
        }
        // And model selects all chunks in reverse priority order
        val selectedIds = chunks.map { it.id }.reversed()
        setupQueryExecutorToReturn(SelectedDiffChunks(chunks = selectedIds.joinToString(",")))
        whenever(diffParser.chunkDiff(any())).thenReturn(
            ChunkDiffResult(
                chunks = chunks,
                annotatedDiff = "Annotated Diff",
            ),
        )

        // When
        val actual = compressSuccess(longDiff)

        // Then
        // Instead of trying to predict exactly which chunks will be included,
        // verify that the output follows the expected pattern and constraints:
        // 1. It should contain some of the chunks in the correct order
        // 2. It should not exceed the max size

        // Verify the output doesn't exceed the max size
        assertThat(actual.length).isLessThanOrEqualTo(MIN_DIFF_SIZE_FOR_COMPRESSION)

        // Verify the chunks are in the correct order (reversed)
        val reversedChunks = chunks.reversed()
        var lastFoundIndex = -1

        for (i in reversedChunks.indices) {
            val chunk = reversedChunks[i]
            if (actual.contains(chunk.content)) {
                // If we found this chunk, make sure it's after any previously found chunks
                assertThat(i).isGreaterThan(lastFoundIndex)
                lastFoundIndex = i
            }
        }

        // Verify at least one chunk is included
        assertThat(lastFoundIndex).isGreaterThanOrEqualTo(0)
    }
}

private fun SelectedDiffChunks.toQueryResult(): Result<QueryResult<SelectedDiffChunks>> {
    return Result.success(QueryResult(this, mock<CompiledPromptResult>()))
}
