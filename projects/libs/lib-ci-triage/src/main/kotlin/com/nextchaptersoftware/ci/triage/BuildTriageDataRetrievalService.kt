package com.nextchaptersoftware.ci.triage

import arrow.fx.coroutines.parMap
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encodePretty
import com.nextchaptersoftware.ci.triage.observability.Recordable
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberBundle
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.stores.MLSettingsStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.infoSensitiveAsync
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.markdown.MessageBodyExtensions.asMessageBody
import com.nextchaptersoftware.ml.embedding.query.services.fusion.RankFusionQuery
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionService
import com.nextchaptersoftware.ml.embedding.services.IEmbeddingService
import com.nextchaptersoftware.ml.functions.core.MLFunctionExecutionCIContext
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.query.context.MLQuery
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.WideSlackEmbeddingsRetrievalStyle
import com.nextchaptersoftware.trace.coroutine.withSpan
import kotlinx.serialization.Serializable
import mu.KotlinLogging

@Serializable
data class BuildTriageQuery(
    val prioritizedSearches: List<PrioritizedSearch>,
) : Recordable {
    override fun record(): Map<String, Any?> = mapOf("prioritizedSearchesSize" to prioritizedSearches.size)
}

@Serializable
data class PrioritizedSearch(val query: String, val directReferences: List<String>? = null)

data class DocumentRetrievalResult(
    val query: String,
    val documents: List<MLTypedDocument>,
    val mlFunctionsOutput: String,
)

private typealias RetrievalResult = Pair<List<MLTypedDocument>, Set<String>>

private val LOGGER = KotlinLogging.logger { }
private const val PARALLEL_SEARCHES = 3

class BuildTriageDataRetrievalService(
    private val inferenceTemplateService: MLInferenceTemplateService,
    private val queryExecutor: BuildTriageQueryExecutor,
    private val documentEvaluationRetriever: DocumentEvaluationRetriever,
    private val embedder: IEmbeddingService,
    private val reciprocalRankFusionService: ReciprocalRankFusionService = ReciprocalRankFusionService(),
    private val mlSettingsStore: MLSettingsStore = Stores.mlSettingsStore,
) {
    private val searchTriageConfig = GlobalConfig.INSTANCE.searchTriage

    suspend fun doRetrieval(
        jobLogSummary: String,
        questioner: OrgMemberBundle,
        org: Org,
        pullRequest: PullRequest,
        build: Build,
        pullRequestDiff: String,
        ciContext: MLFunctionExecutionCIContext,
        dsacContext: DsacContext,
    ): TriagePipelineAnswer<DocumentRetrievalResult> = withSpan(
        spanName = "CITriage:MainDocumentRetrievalPipeline",
        attributes = mapOf(
            "pullRequestDiffSize" to pullRequestDiff.length,
            "jobLogSummarySize" to jobLogSummary.length,
        ),
    ) {
        val miscellaneousInfo = BuildTriagePrompt.compileMiscellaneousInfo(
            pullRequest = pullRequest,
            pullRequestDiff = pullRequestDiff,
            build = build,
            jobLogSummary = jobLogSummary,
        )
        val searches = createQuery(
            org = org,
            questioner = questioner,
            miscellaneousInfo = miscellaneousInfo,
        ).unwrapOrElseStop {
            return@withSpan it.propagate()
        }
        LOGGER.debugSensitiveAsync(
            "searchesSize" to searches.size,
            sensitiveFields = mapOf("searches" to searches),
        ) { "Running document retrieval" }
        val docsAndFunctionAnswers = withSpan(spanName = "CITriage:DocumentRetrieval", attributes = mapOf("searchesSize" to searches.size)) {
            searches.parMap(concurrency = PARALLEL_SEARCHES) { prioritizedSearch ->
                runRetrieval(
                    prioritizedSearch = prioritizedSearch,
                    pullRequest = pullRequest,
                    questioner = questioner,
                    org = org,
                    ciContext = ciContext,
                    dsacContext = dsacContext,
                    miscellaneousInfo = miscellaneousInfo,
                )
            }
        }

        val docsAndFunctions = docsAndFunctionAnswers.mapNotNull {
            when (it) {
                is TriagePipelineAnswer.ContinueWith -> it.answer
                is TriagePipelineAnswer.Stop -> null
            }
        }

        if (docsAndFunctions.isEmpty()) {
            LOGGER.errorAsync { "Failed to retrieve documents, stopping pipeline" }
            return@withSpan TriagePipelineAnswer.stop(BuildTriageFilterStage.TriageFailedRetrieveDocuments)
        }

        return@withSpan DocumentRetrievalResult(
            searches.encodePretty(),
            mergeDocuments(docsAndFunctions),
            mergeMlDocuments(docsAndFunctions),
        ).continueWith()
    }

    private suspend fun runRetrieval(
        prioritizedSearch: PrioritizedSearch,
        pullRequest: PullRequest,
        questioner: OrgMemberBundle,
        org: Org,
        ciContext: MLFunctionExecutionCIContext,
        dsacContext: DsacContext,
        miscellaneousInfo: List<String>,
    ): TriagePipelineAnswer<RetrievalResult> {
        val linksBlock = constructLinkResolverBlock(prioritizedSearch, pullRequest)
        return retrieve(
            query = prioritizedSearch.query,
            questioner = questioner,
            org = org,
            ciContext = ciContext,
            dsacContext = dsacContext,
            linksBlock = linksBlock,
            miscellaneousInfo = miscellaneousInfo,
        ).unwrapOrElseStop {
            return it.propagate()
        }.also { (docs, mlFunctionOutput) ->
            LOGGER.debugSensitiveAsync(
                "orgMemberId" to questioner.orgMemberId,
                "docs" to docs.map { "${it.sourceType.displayName}: ${it.score}" },
                "mlFunctionResultsSize" to mlFunctionOutput.size,
                sensitiveFields = mapOf(
                    "mlFunctionOutput" to mlFunctionOutput.joinToString("\n\n"),
                    "query" to prioritizedSearch.query,
                    "directReferences" to prioritizedSearch.directReferences,
                    "docs" to docs.joinToString("\n\n") {
                        """
                        |Title: ${it.title}
                        |Source type: ${it.sourceType.displayName}
                        |Document type: ${it.documentType}
                        |Score: ${it.score}
                        |Content length: ${it.content.length}
""".trimMargin()
                    },
                ),
            ) {
                "Got docs and ML functions"
            }
        }.continueWith()
    }

    private fun mergeMlDocuments(docsAndFunctions: List<Pair<List<MLTypedDocument>, Set<String>>>): String = docsAndFunctions
        .flatMap { it.second }
        .map { it.trim() }
        .distinct()
        .joinToString("\n\n")

    private fun mergeDocuments(docsAndFunctions: List<Pair<List<MLTypedDocument>, Set<String>>>): List<MLTypedDocument> {
        val docFusionQueries = docsAndFunctions.map { RankFusionQuery(it.first) }
        return reciprocalRankFusionService.fuse(docFusionQueries)
    }

    private fun constructLinkResolverBlock(search: PrioritizedSearch, pullRequest: PullRequest): String {
        val uniqueDirectReferences = search.directReferences
            .orEmpty()
            .map { it.trim() }
            .toSet()
            .joinToString("\n")
        return pullRequest.description?.asMessageBody()?.asMarkdown()?.let {
            "${it}\n\n$uniqueDirectReferences"
        } ?: uniqueDirectReferences
    }

    private suspend fun createQuery(
        org: Org,
        questioner: OrgMemberBundle,
        miscellaneousInfo: List<String>,
    ): TriagePipelineAnswer<List<PrioritizedSearch>> = withSpan(spanName = "CITriage:SearchQueryGeneration") {
        return@withSpan queryExecutor.executeWithRetry<BuildTriageQuery>(
            templateKind = MLInferenceTemplateKind.CITriageQuery,
            miscellaneousInfo = miscellaneousInfo,
            org = org,
            questioner = questioner,
            inferenceType = "Generate Search Query",
        ).map { (result, _) ->
            result.prioritizedSearches.continueWith()
        }.getOrElse {
            return@withSpan TriagePipelineAnswer.stop(BuildTriageFilterStage.TriageFailedRetrieveDocuments)
        }
    }

    private suspend fun retrieve(
        query: String,
        questioner: OrgMemberBundle,
        org: Org,
        ciContext: MLFunctionExecutionCIContext,
        dsacContext: DsacContext,
        linksBlock: String,
        miscellaneousInfo: List<String>,
    ): TriagePipelineAnswer<Pair<List<MLTypedDocument>, Set<String>>> = withSpan("CITriage:SemanticDocumentRetrieval") {
        withLoggingContextAsync(
            "orgId" to org.id,
            "orgMemberId" to questioner.orgMemberId,
        ) {
            LOGGER.infoSensitiveAsync(
                sensitiveFields = mapOf(
                    "linksBlock" to linksBlock,
                    "query" to query,
                ),
            ) { "Retrieving documents" }
            val template = inferenceTemplateService.orgTemplate(org.id, MLInferenceTemplateKind.CITriageResponse)
            val mlVectorQuery = createVectorQuery(query, org.id)

            runSuspendCatching {
                documentEvaluationRetriever.retrieve(
                    org = org,
                    questioner = questioner,
                    userQuery = query,
                    documentQuery = mlVectorQuery,
                    searchTemplate = template,
                    maxDocuments = template.maxDocuments,
                    priorMessages = null,
                    highlightedCode = null,
                    visibleCode = null,
                    exclusiveRepoSet = emptySet(),
                    nonExclusiveRepoSet = emptySet(),
                    documentTimeout = searchTriageConfig.documentRetrievalTimeout,
                    wideSlackEmbeddingsRetrievalStyle = WideSlackEmbeddingsRetrievalStyle.Always,
                    ciContext = ciContext,
                    linksText = linksBlock,
                    evaluationTemplateKind = MLInferenceTemplateKind.CITriageDocumentEvaluator,
                    evaluationMiscellaneousInfo = miscellaneousInfo,
                    dsacContext = dsacContext,
                    dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
                ).also {
                    LOGGER.infoAsync(
                        "numberOfDocuments" to it.first.size,
                    ) { "Expended document retrieval finished" }
                }.continueWith()
            }.getOrElse {
                LOGGER.errorAsync(it) { "Error retrieving expanding document retrieval" }
                TriagePipelineAnswer.stop(BuildTriageFilterStage.TriageFailedRetrieveDocuments)
            }
        }
    }

    private suspend fun createVectorQuery(query: String, orgId: OrgId): MLQuery.MLVectorQuery {
        val embeddingModel = mlSettingsStore.getReadEmbeddingModel(orgId = orgId)
        val vectorPair = embedder.getQueryEmbedding(query, embeddingModel)
        return MLQuery.MLVectorQuery(query, vectorPair)
    }
}
