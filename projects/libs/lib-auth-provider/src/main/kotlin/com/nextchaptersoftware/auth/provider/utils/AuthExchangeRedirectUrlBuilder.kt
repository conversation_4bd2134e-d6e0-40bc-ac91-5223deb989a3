package com.nextchaptersoftware.auth.provider.utils

import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.http.Url

class AuthExchangeRedirectUrlBuilder(redirectUrl: Url) {
    private val urlBuilder: URLBuilder = URLBuilder(redirectUrl)

    fun withErrorCode(code: HttpStatusCode) = apply {
        urlBuilder.parameters.append("errorCode", code.toString())
    }

    fun withInstallationId(installationId: InstallationId) = apply {
        urlBuilder.parameters.append("installationId", installationId.toString())
    }

    fun withProvider(provider: Provider) = apply {
        urlBuilder.parameters.append("provider", provider.asApiModel().enumValue)
    }

    fun build(): Url {
        return urlBuilder.build()
    }
}

fun authExchangeRedirectUrl(redirectUrl: Url, block: AuthExchangeRedirectUrlBuilder.() -> Unit): Url {
    val builder = AuthExchangeRedirectUrlBuilder(redirectUrl = redirectUrl)
    builder.block()
    return builder.build()
}
