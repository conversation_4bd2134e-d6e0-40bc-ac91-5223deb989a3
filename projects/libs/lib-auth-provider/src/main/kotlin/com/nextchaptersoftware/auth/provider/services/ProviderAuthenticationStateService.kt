package com.nextchaptersoftware.auth.provider.services

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderAuthenticationState
import com.nextchaptersoftware.db.models.ProviderAuthenticationStateId
import com.nextchaptersoftware.db.stores.ProviderAuthenticationStateStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlin.time.Duration
import kotlinx.datetime.Instant

class ProviderAuthenticationStateService(
    private val providerAuthenticationStateStore: ProviderAuthenticationStateStore = Stores.providerAuthenticationStateStore,
    private val jwt: Jwt,
    private val expiry: Duration,
) {
    private fun getSignature(state: String): ByteArray {
        return jwt.unblockedAuthTokenSigningAlgorithm?.sign(state.toByteArray())
            ?: state.toByteArray()
    }

    suspend fun setState(
        provider: Provider,
        orgId: OrgId,
        identityId: IdentityId,
        nonce: UUID,
        state: String,
    ): ProviderAuthenticationState {
        val signature = getSignature(state = state)

        providerAuthenticationStateStore.evict(
            orgId = orgId,
            identityId = identityId,
            provider = provider,
            olderThan = Instant.nowWithMicrosecondPrecision().minus(expiry),
        )

        return providerAuthenticationStateStore.upsert(
            orgId = orgId,
            identityId = identityId,
            provider = provider,
            nonce = nonce,
            signature = signature,
        )
    }

    suspend fun getState(
        nonce: UUID,
        state: String,
    ): ProviderAuthenticationState? {
        val signature = getSignature(state = state)

        return providerAuthenticationStateStore.getByNonceAndSignature(
            nonce = nonce,
            signature = signature,
        )
    }

    suspend fun deleteState(
        providerAuthenticationStateId: ProviderAuthenticationStateId,
    ) {
        providerAuthenticationStateStore.deleteById(
            providerAuthenticationStateId = providerAuthenticationStateId,
        )
    }
}
