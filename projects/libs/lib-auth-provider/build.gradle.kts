plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(libs.bundles.ktor.client)
    implementation(libs.exposed.kotlin.datetime)

    implementation(project(":projects:libs:lib-api-model", "default"))
    implementation(project(":projects:libs:lib-auth", "default"))
    implementation(project(":projects:libs:lib-security", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:models", "test"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
