package com.nextchaptersoftware.notification.events.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationPersonEvent

class InactivePersonIntercomHandler : TypedEventHandler<NotificationPersonEvent.InactivePersonEvent> {
    override suspend fun handle(event: NotificationPersonEvent.InactivePersonEvent): Boolean {
        // TODO look up person's information, contact via intercom
        return true
    }
}
