@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.notification.events.queue.handlers

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.EmailEventType
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.legacyMemberForIdentityWithOrgId
import com.nextchaptersoftware.db.stores.EmailEventStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.ForbiddenException
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEventHandler
import com.nextchaptersoftware.types.EmailAddress

class ProcessingCompleteEmailHandler(
    private val sender: EmailAddress,
    private val senderName: String,
    private val sendGridTemplateEventHandler: SendGridTemplateEventHandler,
    private val urlBuilderProvider: UrlBuilderProvider,
    private val emailEventStore: EmailEventStore = Stores.emailEventStore,
) : TypedEventHandler<NotificationOrgEvent.ProcessingCompleteEmailEvent> {
    data class ProcessingCompleteEmailObjects(
        val primaryEmail: EmailAddress,
        val teamName: String,
        val person: PersonDAO,
        val memberId: MemberId,
    )

    override suspend fun handle(event: NotificationOrgEvent.ProcessingCompleteEmailEvent): Boolean {
        val org = orgStore.findById(orgId = event.orgId)
            ?: throw ForbiddenException("No org found")

        val processingCompleteEmailObjects = Database.suspendedTransaction {
            val teamMember = MemberDAO.legacyMemberForIdentityWithOrgId(
                trx = this,
                identityId = event.senderIdentityId,
                orgId = org.id,
            ) ?: throw ForbiddenException("No active team member found")

            val primaryEmail = teamMember.identity.asDataModel().primaryEmail
                ?: throw ForbiddenException("No active primary email found")

            val person = teamMember.identity.person ?: throw ForbiddenException("No personId found")

            ProcessingCompleteEmailObjects(
                primaryEmail = primaryEmail,
                teamName = org.displayName,
                person = person,
                memberId = teamMember.idValue,
            )
        }

        val hasSentEmail = emailEventStore.hasExistingEvents(
            personId = processingCompleteEmailObjects.person.idValue,
            orgId = event.orgId,
            emailEventTypes = listOf(EmailEventType.ProcessingComplete),
        )

        if (hasSentEmail) {
            return true
        }

        val emailEventId = emailEventStore.createEvent(
            personId = processingCompleteEmailObjects.person.idValue,
            orgId = event.orgId,
            memberId = processingCompleteEmailObjects.memberId,
            emailAddress = processingCompleteEmailObjects.primaryEmail,
            emailEventType = EmailEventType.ProcessingComplete,
        )

        runSuspendCatching {
            sendGridTemplateEventHandler.handle(
                SendGridTemplateEvent.ProcessingComplete(
                    teamName = processingCompleteEmailObjects.teamName,
                    openUnblockedUrl = urlBuilderProvider.dashboard().withProcessingComplete(
                        orgId = org.id.value,
                        continueOnboarding = true,
                    ).build().toString(),
                    fromEmailAddress = sender,
                    fromName = senderName,
                    toEmailAddress = processingCompleteEmailObjects.primaryEmail,
                    subject = "Processing for \"${processingCompleteEmailObjects.teamName}\" is Complete!",
                ),
            )
        }.onFailure {
            emailEventStore.removeEvent(id = emailEventId)
            throw it
        }

        return true
    }
}
