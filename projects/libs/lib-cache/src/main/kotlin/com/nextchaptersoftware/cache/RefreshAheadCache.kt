package com.nextchaptersoftware.cache

import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.Duration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

object CacheBackgroundTasks {
    val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
}

/**
 * # Refresh-Ahead Cache
 * A refresh-ahead cache computes an item **before** it expires from the cache,
 * unlike a read-through cache which computes the item just-in-time when it expires.
 * By refreshing ahead of the expiry, this approach further minimizes the computational cost for the client accessing the cache.
 *
 * ## Refresh Ahead Behavior
 * When an item is already in the cache, the client experiences zero compute latency, just like a read-through cache.
 * If the item is close to expiry, the cache will compute the item in the background to reduce the likelihood of a future cache miss.
 *
 * ## Just-in-Time Behavior
 * A cache client can operate in two modes: blocking and non-blocking.
 * Since the cache expiry of the refresh-ahead cache is typically long,
 * a just-in-time (JIT) event usually only happens the first time the entry is encountered or after a long period of inactivity.
 *
 * - BLOCKING MODE:
 *   When an item is not in the cache, the client will block in the foreground until the item is computed and inserted into the cache.
 *   This is essentially the same behavior of a read-through cache.
 *
 * - NON-BLOCKING MODE:
 *   When an item is not in the cache, the client will receive an exception.
 *   The item will then be computed and inserted into the cache asynchronously.
 *   In non-blocking mode, it is up to the client to decide how to handle the exception, whether by ignoring it or managing it in another way.
 */
abstract class RefreshAheadCache<K, V>(
    /** Entries will be evicted after this time */
    private val expireAfter: Duration,

    /** Entries will be refreshed on access after this time */
    private val refreshAfter: Duration,
) {
    init {
        require(expireAfter > refreshAfter) {
            "expireAfter must be greater than refreshAfter"
        }
    }

    suspend fun invalidate(key: K) {
        delete(key)
    }

    /**
     * Get the value for the key, or compute it if it is not present in the cache.
     * This is the primary method for clients to interact with the cache.
     *
     * @param key The key to look up in the cache
     * @param forceCompute If true, the value will be unconditionally recomputed even if it is already in the cache
     */
    suspend fun getOrCompute(
        key: K,
        forceCompute: Boolean = false,
        compute: suspend (K) -> V,
    ): V {
        val value = get(key)

        // If the value is not present in the cache, compute it in the foreground.
        // The client will experience the latency of the cache miss.
        if (value == null) {
            LOGGER.debugAsync("key" to key) { "computeBlocking" }
            return computeBlocking(key, compute)
        }

        // If the value is present in the cache, then return it, but also compute it in the background if it is time to refresh.
        if (forceCompute || shouldCompute(key)) {
            LOGGER.debugAsync("key" to key) { "computeNonBlocking" }
            computeNonBlocking(key, compute)
        }

        return value
    }

    /**
     * Get the value for the key, or fail with [CacheEntryNotPresent] if the value is not present in the cache.
     * This is useful for clients that cannot tolerate the latency of a cache miss.
     */
    suspend fun getOrFail(key: K, compute: suspend (K) -> V): V {
        val value = get(key)

        // If the value is not present in the cache, compute it in the background.
        // The client will receive an exception.
        if (value == null) {
            computeNonBlocking(key, compute)
            throw CacheEntryNotPresent()
        }

        // If the value is present in the cache, then return it, but also compute it in the background if it is time to refresh.
        if (shouldCompute(key)) {
            computeNonBlocking(key, compute)
        }

        return value
    }

    /** Track running computations to avoid duplicates */
    private val runningComputations = ConcurrentHashMap<K, Job>()

    @Suppress("TooGenericExceptionCaught", "SwallowedException")
    private fun computeNonBlocking(key: K, compute: suspend (K) -> V) {
        // Skip if already computing this key
        if (runningComputations.containsKey(key)) return

        val job = CacheBackgroundTasks.scope.launch {
            try {
                LOGGER.debugAsync("key" to key) { "computeNonBlocking start" }
                val value = compute(key)
                put(key, value)
                LOGGER.debugAsync("key" to key) { "computeNonBlocking put" }
            } catch (e: CancellationException) {
                LOGGER.warnAsync("key" to key) { "computeNonBlocking cancelled" }
            } catch (e: Exception) {
                LOGGER.errorAsync(e, "key" to key) { "computeNonBlocking failed" }
            } finally {
                runningComputations.remove(key)
            }
        }

        runningComputations[key] = job
    }

    private suspend fun computeBlocking(key: K, compute: suspend (K) -> V): V {
        LOGGER.debugAsync("key" to key) { "computeBlocking start" }
        return compute(key).also { value ->
            put(key, value)
            LOGGER.debugAsync("key" to key) { "computeBlocking put" }
        }
    }

    private suspend fun shouldCompute(key: K): Boolean {
        val expiry = getRemainingExpiry(key)
            ?: return true

        return expiry < (expireAfter - refreshAfter)
    }

    protected abstract suspend fun put(key: K, value: V)

    protected abstract suspend fun get(key: K): V?

    protected abstract suspend fun delete(key: K)

    protected abstract suspend fun getRemainingExpiry(key: K): Duration?
}
