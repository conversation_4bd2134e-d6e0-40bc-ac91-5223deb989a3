package com.nextchaptersoftware.cache

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class LocalWriteThroughCacheTest {

    @Test
    fun `updates the store when not already cached`() {
        val cache = LocalWriteThroughCache<String, String>()
        val store = mock<Store<String, String>>()
        `when`(store.store(eq("key"), eq("value"))).thenReturn("value")

        runBlocking {
            repeat(10) {
                cache.set("key", "value", store::store)
            }
            verify(store, times(1)).store(eq("key"), eq("value"))
            verify(store, times(1)).store(any(), any())
        }
    }

    @Test
    fun `updates the store when value changes`() {
        val cache = LocalWriteThroughCache<String, String>()
        val store = mock<Store<String, String>>()
        `when`(store.store(eq("key"), eq("value"))).thenReturn("value")
        `when`(store.store(eq("key"), eq("newValue"))).thenReturn("newValue")

        runBlocking {
            cache.set("key", "value", store::store)
            repeat(10) {
                cache.set("key", "newValue", store::store)
            }

            verify(store, times(1)).store(eq("key"), eq("value"))
            verify(store, times(1)).store(eq("key"), eq("newValue"))
            verify(store, times(2)).store(any(), any())
        }
    }
}
