package com.nextchaptersoftware.maintenance.migration.services.orgmember

import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class OrgMemberReaperServiceTest : DatabaseTestsBase() {
    private val service = OrgMemberReaperService(orgMemberDeletionService = mock())

    @Test
    fun getNextNotReferencedByAMemberModel() = suspendingDatabaseTest {
        val org = makeOrg()

        makeMember(orgMember = makeOrgMember(org = org))
        assertThat(service.getNextNotReferencedByAMemberModel()).isNull()

        val orgMemberA = makeOrgMember(org = org)
        assertThat(service.getNextNotReferencedByAMemberModel()).isEqualTo(orgMemberA.idValue)

        val orgMemberB = makeOrgMember(org = org)
        assertThat(service.getNextNotReferencedByAMemberModel()).isEqualTo(orgMemberA.idValue)

        val memberA = makeMember(orgMember = orgMemberA)
        assertThat(service.getNextNotReferencedByAMemberModel()).isEqualTo(orgMemberB.idValue)

        makeMember(orgMember = orgMemberB)
        assertThat(service.getNextNotReferencedByAMemberModel()).isNull()

        suspendedTransaction { memberA.orgMember = makeOrgMember(trx = this, org = org) }
        assertThat(service.getNextNotReferencedByAMemberModel()).isEqualTo(orgMemberA.idValue)
    }
}
