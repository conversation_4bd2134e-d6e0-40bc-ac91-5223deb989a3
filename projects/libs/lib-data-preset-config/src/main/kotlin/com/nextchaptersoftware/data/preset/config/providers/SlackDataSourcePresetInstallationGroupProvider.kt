package com.nextchaptersoftware.data.preset.config.providers

import com.nextchaptersoftware.api.models.DataSourcePresetInstallationGroup
import com.nextchaptersoftware.api.models.SlackConfigurationV3
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores

class SlackDataSourcePresetInstallationGroupProvider(
    private val slackConfigurationProvider: suspend (OrgId, OrgMemberId, SlackTeamId) -> SlackConfigurationV3,
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
) {
    suspend fun get(
        installation: Installation,
        orgMemberId: OrgMemberId,
    ): List<DataSourcePresetInstallationGroup> {
        val slackTeam = slackTeamStore.findByInstallation(installationId = installation.id)
            ?: return emptyList()

        return slackConfigurationProvider(installation.orgId, orgMemberId, slackTeam.id).channelConfigurations.map {
            DataSourcePresetInstallationGroup(
                id = it.channel.id,
                displayName = it.channel.name,
                iconType = when (it.channel.isPrivate) {
                    true -> DataSourcePresetInstallationGroup.IconType.privateChannel
                    else -> DataSourcePresetInstallationGroup.IconType.channel
                },
            )
        }
    }
}
