package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.rpc.params.ParamIdentityId
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmUserApiHandler
 */
interface ScmUserCalls {

    suspend fun scmUserGetScmInstallationAccount(
        params: ScmUserGetScmInstallationAccountParams,
    ): ScmInstallationAccount?

    @Serializable
    data class ScmUserGetScmInstallationAccountParams(
        override val identityId: IdentityId,
        val externalInstallationId: String,
    ) : ParamIdentityId
}
