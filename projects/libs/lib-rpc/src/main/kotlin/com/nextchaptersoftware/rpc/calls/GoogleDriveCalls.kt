package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.api.models.GoogleDriveConfiguration
import com.nextchaptersoftware.api.models.GoogleDriveWorkspace
import com.nextchaptersoftware.api.models.SearchGoogleDriveFilesResponse
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.GoogleDriveHandler
 */
interface GoogleDriveCalls {

    suspend fun googleDriveSearchFiles(
        params: GoogleDriveSearchFilesParams,
    ): SearchGoogleDriveFilesResponse

    @Serializable
    data class GoogleDriveSearchFilesParams(
        val orgMemberId: OrgMemberId,
        val installationId: InstallationId,
        val orgId: OrgId,
        val query: String,
    )

    suspend fun googleDriveGetConfiguration(
        params: GoogleDriveGetConfigurationParams,
    ): GoogleDriveConfiguration

    @Serializable
    data class GoogleDriveGetConfigurationParams(
        val orgId: OrgId,
        val installationId: InstallationId,
        val orgMemberId: OrgMemberId,
    )

    suspend fun googleDriveSaveConfiguration(
        params: GoogleDriveSaveConfigurationParams,
    )

    @Serializable
    data class GoogleDriveSaveConfigurationParams(
        val orgId: OrgId,
        val installationId: InstallationId,
        val orgMemberId: OrgMemberId,
        val configuration: GoogleDriveConfiguration,
    )

    suspend fun googleDriveUpsertGoogleWorkspaceDrive(
        params: GoogleDriveUpsertGoogleWorkspaceDriveParams,
    ): GoogleDriveWorkspace

    @Serializable
    data class GoogleDriveUpsertGoogleWorkspaceDriveParams(
        val orgId: OrgId,
        val installationId: InstallationId,
        val serviceAccountKey: String,
        val adminEmail: String,
    )
}
