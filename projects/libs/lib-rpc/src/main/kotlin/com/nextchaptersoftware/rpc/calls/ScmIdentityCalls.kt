package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmIdentityHandler
 */
interface ScmIdentityCalls {

    suspend fun scmIdentityExchangeAuthCodeForIdentity(
        params: ScmIdentityExchangeAuthCodeForIdentityParams,
    ): ScmExchangeAuthCodeForIdentityResult

    @Serializable
    data class ScmIdentityExchangeAuthCodeForIdentityParams(
        val code: String,
        val state: String?,
        val signedInPersonId: PersonId?,
        val oAuthApiType: OAuthApiTypeSerializable,
        @Contextual
        val overrideOAuthRedirectUrl: Url?,
    )

    @Serializable
    data class ScmExchangeAuthCodeForIdentityResult(
        val identityId: IdentityId,
    ) {
        suspend fun asIdentity(): Identity = suspendedTransaction {
            IdentityDAO
                .findById(id = identityId)
                .let(::checkNotNull)
                .asDataModel()
        }
    }
}

@Serializable
sealed class OAuthApiTypeSerializable {

    @Serializable
    data class ProviderData(
        val dbOrdinal: Int,
    ) : OAuthApiTypeSerializable()

    @Serializable
    data class ScmData(
        val providerDbOrdinal: Int,
        val providerEnterpriseId: EnterpriseAppConfigId?,
    ) : OAuthApiTypeSerializable()

    fun unwrap(): OAuthApiType {
        return when (this) {
            is ProviderData -> Provider.fromDbOrdinal(dbOrdinal)

            is ScmData -> {
                val provider = Provider.fromDbOrdinal(providerDbOrdinal)
                Scm.fromProvider(provider, providerEnterpriseId)
            }
        }
    }
}

fun OAuthApiType.wrapSerializable(): OAuthApiTypeSerializable = when (this) {
    is Provider -> OAuthApiTypeSerializable.ProviderData(
        dbOrdinal = dbOrdinal,
    )

    is Scm -> OAuthApiTypeSerializable.ScmData(
        providerDbOrdinal = provider.dbOrdinal,
        providerEnterpriseId = providerEnterpriseId,
    )

    else -> error("Missing asOAuthApiTypeData for ${this::class.simpleName}")
}
