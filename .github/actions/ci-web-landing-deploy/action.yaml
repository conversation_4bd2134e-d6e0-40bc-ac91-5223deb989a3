name: Deploy Landing Page
description: Deploys landing page to a given environment
inputs:
  environment:
    description: "The environment the service is being deployed to"
    required: true
  aws-region:
    description: "AWS region to deploy changes"
    required: true
    default: "us-west-2"
  role-arn:
    description: "IAM role to use for deployment"
    required: true
  distribution-bucket:
    description: "The full uri of the target s3 bucket for deployment"
    required: true
  distribution-id:
    description: "ID of target CloudFront distribution"
    required: true
  output-artifact-name:
    description: "Name of build artifact to deploy"
    required: true
  build-artifact-dir:
    description: "Path to which the artifact should be downloaded and expanded"
    required: true
  aws-access-key-id:
    description: "The AWS access key"
    required: true
  aws-secret-access-key:
    description: "The AWS secret access key"
    required: true
  gh-personal-access-token:
    description: "The Github personal access token"
    required: true
runs:
  using: "composite"
  steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        submodules: "recursive"
        token: ${{ inputs.gh-personal-access-token }}

    - name: Download Build Artifacts
      uses: actions/download-artifact@v4
      with:
        name: ${{ inputs.output-artifact-name}}
        path: ${{ inputs.build-artifact-dir}}

    - name: Configure S3 AWS credentials
      id: aws-s3-creds
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}
        role-to-assume: ${{ inputs.role-arn }}
        role-skip-session-tagging: true
        role-duration-seconds: 1200

    - name: list files in dist
      shell: bash
      working-directory: ./web-landing
      run: pwd

    - name: Copy landing page assets
      shell: bash
      working-directory: ./web-landing
      run: |
        mkdir -p ./dist/email-assets/ ./dist/error-assets/ ./dist/public/ ./dist/diagram-assets/
        cp -R ../shared/clientAssets/email/* ./dist/email-assets
        cp -R ../shared/clientAssets/diagramAssets/* ./dist/diagram-assets/
        cp ../projects/libs/lib-asset/src/main/resources/error-images/* ./dist/error-assets/
        cp -R ../shared/clientAssets/public/* ./dist/public/
        cp ../projects/libs/lib-asset/src/main/resources/public/* ./dist/public/

    - name: Create robots.txt file
      if: ${{ contains(inputs.environment, 'dev') }}
      shell: bash
      working-directory: ./web-landing
      run: |
        echo 'User-agent: *' > ./dist/robots.txt
        echo 'Disallow: /' >> ./dist/robots.txt

    - name: Deploy web app build to ${{ inputs.environment }} S3 bucket for landing page
      shell: bash
      working-directory: ./web-landing
      run: aws s3 sync ./dist ${{ inputs.distribution-bucket }}/ --delete --exclude "download-assets/*"

    - name: Invalidate ${{ inputs.environment }} for landing page Cloudfront Caches
      shell: bash
      working-directory: ./web-landing
      run: aws cloudfront create-invalidation --distribution-id ${{ inputs.distribution-id }} --paths "/*"
