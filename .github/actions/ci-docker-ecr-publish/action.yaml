name: Build docker image and publish to ECR
description: Builds a docker image and publishes the image to ECR
inputs:
  ecr-repo-name:
    description: "The name of ecr repo which holds images for this service"
    required: true
  image-dir:
    description: "The image directory"
    required: true
  aws-access-key-id:
    description: "The AWS access key"
    required: true
  aws-secret-access-key:
    description: "The AWS secret access key"
    required: true
  aws-region:
    description: "The AWS region"
    required: true
  image-dockerfile:
    description: "The Dockerfile to use for building the image"
    default: "Dockerfile"

outputs:
  image-tag:
    description: "The docker tag for current image"
    value: ${{ steps.publish-image.outputs.image-tag }}

runs:
  using: "composite"

  steps:
    - name: Configure ECR AWS credentials
      id: aws-ecr-creds
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        aws-region: ${{ inputs.aws-region }}
        role-to-assume: arn:aws:iam::877923746456:role/EcrDeployerRole
        role-skip-session-tagging: true
        role-duration-seconds: 1200

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push image to Amazon ECR
      id: publish-image
      shell: bash
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.run_attempt }}-${{ github.sha }}
      working-directory: ${{ inputs.image-dir }}
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS.
        docker build -f ${{ inputs.image-dockerfile }} -t $ECR_REGISTRY/${{ inputs.ecr-repo-name }}:$IMAGE_TAG .
        docker push $ECR_REGISTRY/${{ inputs.ecr-repo-name }}:$IMAGE_TAG
        echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
