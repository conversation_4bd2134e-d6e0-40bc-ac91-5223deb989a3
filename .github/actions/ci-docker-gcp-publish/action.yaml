name: Build docker image and publish to GCP
description: Builds a docker image and publishes the image to GCP
inputs:
  image-name:
    description: "The image name"
    required: true
  image-repo:
    description: "The docker repo full identifier"
    required: true
  gcp-project:
    description: "The path to docker image build context"
    required: true
  gcp-region:
    description: "The target gcp region"
    required: true
  gcp-auth-json:
    description: "The GCP service account auth json"
    required: true

runs:
  using: "composite"

  steps:
    - name: Setup docker auth
      shell: bash
      run: |-
        gcloud info
        gcloud config set project ${{ inputs.gcp-project }} --quiet
        gcloud auth configure-docker ${{ inputs.gcp-region }}-docker.pkg.dev --quiet
        docker tag ${{ inputs.image-name }} ${{ inputs.gcp-region }}-docker.pkg.dev/${{ inputs.gcp-project }}/${{ inputs.image-repo }}/${{ inputs.image-name }}
        docker push ${{ inputs.gcp-region }}-docker.pkg.dev/${{ inputs.gcp-project }}/${{ inputs.image-repo }}/${{ inputs.image-name }}
