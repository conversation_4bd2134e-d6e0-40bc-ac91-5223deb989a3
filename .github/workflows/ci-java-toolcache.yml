# https://docs.github.com/en/enterprise-server@3.3/admin/github-actions/managing-access-to-actions-from-githubcom/setting-up-the-tool-cache-on-self-hosted-runners-without-internet-access
# This workflow is designed to generate a java tool cache artifact that we can install on self hosted runners.
# Instructions:
# 1. Download latest java tool cache artifact from: https://github.com/NextChapterSoftware/unblocked/actions/workflows/ci-java-toolcache.yml
# 2. Unzip tool cache artifact on 'GithubActionImageBuilder' instance in secops environment.
# 3. Untar the resulting tar into '/actions-runner/_work/_tool'
# 4. Ensure permissions are correct for untarred directories: 'chmod -R a+rw /actions-runner'
# 5. Generate new AMI from updated 'GithubActionImageBuilder' instance.
# 6. Update the ec2 ami id for 'start-ec2-runner' step in 'ci-services.yml' workflow.
name: JavaToolCache

on:
  schedule: # Run once a month
    - cron: "0 0 1 * *"

jobs:
  upload_tool_cache:
    runs-on: ubuntu-22.04
    steps:
      - name: Clear any existing tool cache
        run: |
          mv "${{ runner.tool_cache }}" "${{ runner.tool_cache }}.old"
          mkdir -p "${{ runner.tool_cache }}"
      - name: Set up JDK
        uses: actions/setup-java@v4.7.1
        with:
          java-version: ${{ vars.DOCKER_JAVA_VERSION }}
          distribution: ${{ vars.DOCKER_JAVA_DISTRO }}
        timeout-minutes: 10
      - name: Archive tool cache
        run: |
          cd "${{ runner.tool_cache }}"
          tar -czf tool_cache.tar.gz *
      - name: Upload tool cache artifact
        uses: actions/upload-artifact@v4
        with:
          path: ${{runner.tool_cache}}/tool_cache.tar.gz
          retention-days: 5
