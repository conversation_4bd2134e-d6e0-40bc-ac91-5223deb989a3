name: Add PRs and issues to Octokit org project

on:
  issues:
    types: [reopened, opened]
  pull_request_target:
    types: [reopened, opened]

jobs:
  add-to-project:
    name: Add issue to project
    runs-on: ubuntu-latest
    continue-on-error: true
    steps:
      - uses: actions/add-to-project@v1.0.2
        with:
          project-url: https://github.com/orgs/octokit/projects/10
          github-token: ${{ secrets.OCTOKITBOT_PROJECT_ACTION_TOKEN }}
          labeled: "Status: Stale"
          label-operator: NOT
