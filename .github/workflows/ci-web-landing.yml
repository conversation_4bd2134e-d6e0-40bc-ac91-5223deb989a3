name: Web Landing Page

on:
  push:
    branches:
      - main
      - new_landing_page_ci
    paths:
      - '.github/actions/ci-web-landing-deploy/**'
      - '.github/workflows/**'
      - '.gitmodules'
      - 'api/**'
      - 'build.gradle.kts'
      - 'common/**'
      - 'gradle.*'
      - 'gradle/**'
      - 'openapi/**'
      - 'package*.json'
      - 'settings.gradle.kts'
      - 'shared/**'
      - 'sharedConfigs/**'
      - 'web-landing/**'
      - 'web/**'
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  AWS_REGION: us-west-2
  OUTPUT_ARTIFACT_NAME: built-artifacts-${{ github.run_number }}
  # upload-artifact does not use working-directory!
  BUILD_ARTIFACTS_DIR: ./web-landing/dist
  # hack for https://github.com/actions/cache/issues/810#issuecomment-1222550359
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
  GH_CACHE_BUCKET: unblocked-gh-actions-s3-cache-sec-ops-us-west-2

jobs:
  check-for-web-changes:
    timeout-minutes: 10
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: "recursive"
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Paths Changes Filter
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - '.github/actions/ci-web-landing-deploy/**'
              - '.github/workflows/**'
              - '.gitmodules'
              - 'api/**'
              - 'build.gradle.kts'
              - 'common/**'
              - 'gradle.*'
              - 'gradle/**'
              - 'openapi/**'
              - 'package*.json'
              - 'settings.gradle.kts'
              - 'shared/**'
              - 'sharedConfigs/**'
              - 'web-landing/**'
              - 'web/**'

  start-runner:
    name: Start self-hosted EC2 runner
    needs:
      - check-for-web-changes
    if: ${{ needs.check-for-web-changes.outputs.files == 'true' }}
    runs-on: ubuntu-latest
    permissions:
      actions: write
    steps:
      - name: Start EC2 runner
        id: start-ec2-runner
        uses: NextChapterSoftware/ec2-action-builder@v1.10
        with:
          aws_access_key_id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws_region: "us-west-2"
          aws_iam_role_arn: "arn:aws:iam::877923746456:role/Ec2DeploybotRole"
          github_token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          github_action_runner_version: ${{ vars.EC2_BUILDER_GH_ACTION_RUNNER_VERSION }}
          github_action_runner_extra_cli_args: "--disableupdate"
          github_api_retry_delay: 15
          ec2_instance_type: c5.2xlarge
          ec2_ami_id: ${{ vars.EC2_BUILDER_AMI_ID }}
          ec2_subnet_id: subnet-0ef97dd0c62b7874e
          ec2_security_group_id: sg-07436087a768ad7c3
          ec2_instance_ttl: 40
          ec2_spot_instance_strategy: ${{ vars.EC2_BUILDER_INSTANCE_STRATEGY }}
          ec2_instance_tags: >
            [
              {"Key": "Owner", "Value": "deploybot"},
              {"Key": "DrataExclude", "Value": "Ephemeral Build Machine"}
            ]

  build:
    timeout-minutes: 45
    needs:
      - check-for-web-changes
      - start-runner
    if: ${{ needs.check-for-web-changes.outputs.files == 'true' }}
    name: web-landing-build
    runs-on: ${{ github.run_id }}
    defaults:
      run:
        working-directory: ./web-landing
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: "recursive"
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Configure S3 AWS credentials
        id: aws-s3-creds
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: "arn:aws:iam::877923746456:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache"
          role-skip-session-tagging: true
          role-duration-seconds: 1200

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: ""

      - name: Set up JDK
        uses: actions/setup-java@v4.7.1
        with:
          java-version: ${{ vars.DOCKER_JAVA_VERSION }}
          distribution: ${{ vars.DOCKER_JAVA_DISTRO }}
        timeout-minutes: 10

      - name: Install Protoc
        uses: arduino/setup-protoc@v1.3.0
        with:
          version: "3.x"
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        run: cd .. && npm ci
        env:
          FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.FONTAWESOME_NPM_AUTH_TOKEN }}

      - name: Set Gradle Cache Key
        env:
          cache-key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        shell: bash
        run: |
          echo "GRADLE_CACHE_KEY=$(echo ${{ env.cache-key }})" >> $GITHUB_ENV

      - name: Restore Gradle Cache
        uses: invitetest1/action-s3-cache@v1.0.4
        timeout-minutes: 4
        continue-on-error: true
        with:
          action: get
          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ env.AWS_SESSION_TOKEN }}
          aws-region: ${{ env.AWS_REGION }}
          bucket: ${{ env.GH_CACHE_BUCKET }}
          key: ${{ env.GRADLE_CACHE_KEY }}
          default-key: ${{ runner.os }}-gradle

      - name: Run build
        run: npm run build:prod --product_number=${{ github.run_number }} --product_version=0.0.${{ github.run_number }}
        timeout-minutes: 15

      - name: Run tests
        continue-on-error: true
        run: npm run test

      #- name: Create Sentry release
      #  if: github.ref == 'refs/heads/main'
      #  uses: getsentry/action-release@v1
      #  env:
      #    SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      #    SENTRY_ORG: "nextchaptersoftware"
      #    SENTRY_PROJECT: "unblocked-web"
      #  with:
      #    environment: prod
      #    sourcemaps: ${{ env.BUILD_ARTIFACTS_DIR }}
      #    ignore_missing: true
      #    ignore_empty: true

      - name: Upload Build Artifacts
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.OUTPUT_ARTIFACT_NAME }}
          path: |
            ${{ env.BUILD_ARTIFACTS_DIR }}
            !**/*.js.map

  deploy-dev:
    if: ${{ (github.ref == 'refs/heads/main') }}
    timeout-minutes: 15
    needs: build
    environment: "development-landing"
    name: deploy-landing-page-dev
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web-landing
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy landing page - dev
        uses: ./.github/actions/ci-web-landing-deploy
        with:
          environment: "dev"
          role-arn: "arn:aws:iam::129540529571:role/S3StaticSiteDeployerRole-landing-page"
          distribution-bucket: s3://landing-page.dev.getunblocked.com
          distribution-id: E1U394B5JV5R7Z
          output-artifact-name: ${{ env.OUTPUT_ARTIFACT_NAME }}
          build-artifact-dir: ${{ env.BUILD_ARTIFACTS_DIR }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key:  ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          gh-personal-access-token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

  deploy-prod:
    if: ${{ (github.ref == 'refs/heads/main') && contains(vars.SUSPEND_PROD_LANDING_DEPLOYMENTS, 'false') }}
    timeout-minutes: 15
    needs: build
    environment: "production-landing"
    name: deploy-landing-page-prod
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web-landing
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy landing page - prod
        uses: ./.github/actions/ci-web-landing-deploy
        with:
          environment: "prod"
          role-arn: "arn:aws:iam::029574882031:role/S3StaticSiteDeployerRole-landing-page"
          distribution-bucket: s3://landing-page.prod.getunblocked.com
          distribution-id: E1XCNPRVPG9C73
          output-artifact-name: ${{ env.OUTPUT_ARTIFACT_NAME }}
          build-artifact-dir: ${{ env.BUILD_ARTIFACTS_DIR }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key:  ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          gh-personal-access-token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
