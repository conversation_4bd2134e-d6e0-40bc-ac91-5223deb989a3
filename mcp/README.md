# MCP Server

An MCP server for Unblocked.

## Setup

1. Install dependencies. In `/path/to/unblocked/mcp` run:

```bash
npm install
```

2. (Optional) Install MCP server globally into cursor. Copy this into `~/.cursor/.mcp.json`:

NB: You don't have to do this if you're only using the unblocked folder in Cursor, only if you want to use the MCP server
in other projects.

```json
{
    "mcpServers": {
        "unblocked": {
            "command": "node",
            "args": ["--inspect", "/path/to/unblocked/mcp/dist/mcp/server.js"]
        }
    }
}
```

3. Build

This will build the MCP tool and make it available in the dist folder. This will also rebuild
whenever changes occur.

```bash
npm run start
```

4. Make sure your Mac app is running

5. Check it's running in Cursor

    - Restart Cursor
    - In Cursor -> Settings -> Cursor Settings -> MC<PERSON>, make sure the Unblocked tool is present and enabled. You should see a green dot.

6. Ask a question
    - Ask a question, or run an agent command that requires context. Cursor should prompt to ensure
      it's OK to run the unblocked tool.

## What is this doing?

The MCP server is launched by Cursor immediately on start, and sits in the background until Cursor
decides to run one of the tools. While in the background, the server will attempt to get a login
token from the Mac app, and refresh it to ensure it can talk to Unblocked.

When the tool is invoked, we do the following:

-   Try to determine the teamId and repoId relevant to the question/project/file
-   Create a new thread with the provided query and teamId/repoId context
-   Wait until the thread is complete
-   Return the thread response content to Cursor

The MCP server largely behaves like other clients. It should honor your selected environment and
other settings.

## Debugging

Debug logs are available in `/tmp/unblocked-mcp.log

You can use Chrome to attach to the node instance that Cursor launches, to debug using the Chrome
developer tools. (Instructions coming)
