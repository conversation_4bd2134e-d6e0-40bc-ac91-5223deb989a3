import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { CreateTeamStore } from '@shared/stores/CreateTeamStore';
import { useStream } from '@shared/stores/DataCacheStream';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { ConnectedAccountSection } from '@web/components/ConnectedAccountSection/ConnectedAccountSection';
import { useOnboardingNavigatorOutletContext } from '@web/onboardingV5/OnboardingNavigatorOutletContext';

import { OnboardingProviderIcon } from '../OnboardingProviderIcon/OnboardingProviderIcon';
import { CreateTeamStep, OnboardingCreateSteps } from '../OnboardingSteps';
import { AzureDevOpsCreateTeam } from './AzureDevOps/AzureDevOpsCreateTeam';
import { CreateInstallUrlTeam } from './CreateInstallUrlTeam';
import { CreateStandardTeam } from './CreateStandardTeam';

import './CreateTeam.scss';

type CreateTeamState =
    | { $case: 'standard' }
    | { $case: 'installUrlBased'; installUrl: string }
    | { $case: 'azureDevOps' };

export const CreateTeam = () => {
    const store = useMemo(() => new CreateTeamStore(), []);
    const { setCurrentStep, setSteps } = useOnboardingNavigatorOutletContext();
    const [hasRefreshed, setHasRefreshed] = useState(false);
    useEffect(() => {
        setSteps(OnboardingCreateSteps);
        setCurrentStep(OnboardingCreateSteps.indexOf(CreateTeamStep));
    });
    const state = useStream(() => store.stream, [store], { $case: 'loading' });
    const triggerRefresh = useCallback(() => {
        setHasRefreshed(true);
        store.refresh();
    }, [store]);

    if (state.$case === 'loading') {
        return <Loading centerAlign />;
    }
    const provider = state.provider;
    const installationsToConnect = state.installations.filter((installation) => !installation.isInstalled);

    let currentState: CreateTeamState;
    if (provider === 'azureDevOps' && installationsToConnect.length === 0) {
        currentState = {
            $case: 'azureDevOps',
        };
    } else if (state.installUrl) {
        currentState = {
            $case: 'installUrlBased',
            installUrl: state.installUrl,
        };
    } else {
        currentState = {
            $case: 'standard',
        };
    }

    const showIcon = installationsToConnect.length === 0 && currentState.$case !== 'standard';
    const className = classNames({
        create_team: true,
        'create_team--with_icon': showIcon,
    });

    return (
        <div className={className}>
            {showIcon && <OnboardingProviderIcon provider={state.provider} />}
            <h1>
                {currentState.$case === 'azureDevOps' ? (
                    <>Connect your Organization</>
                ) : (
                    <>
                        Connect your <span className="title-highlighted">source code</span>
                    </>
                )}
            </h1>
            <div className="create_team__content">
                {currentState.$case === 'standard' && <CreateStandardTeam {...state} />}
                {currentState.$case === 'installUrlBased' && (
                    <CreateInstallUrlTeam {...state} installUrl={currentState.installUrl} />
                )}
                {currentState.$case === 'azureDevOps' && (
                    <AzureDevOpsCreateTeam refresh={() => triggerRefresh()} hasAttemptedRefresh={hasRefreshed} />
                )}
                <ConnectedAccountSection provider={state.provider} />
            </div>
        </div>
    );
};
