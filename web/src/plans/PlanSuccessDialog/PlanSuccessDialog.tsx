import { useMemo } from 'react';

import { Team } from '@shared/api/models';

import { SuccessDialog } from '@shared/webComponents/Modal/IconDialog';
import { CopyInput } from '@shared/webComponents/TextInput/CopyInput';

import './PlanSuccessDialog.scss';

interface Props {
    team: Team;
    planName: string;
    header?: React.ReactNode;
    content?: React.ReactNode;
}

export const PlanSuccessDialog = ({ team, planName, header: propsHeader, content: propsContent }: Props) => {
    const header = useMemo(() => {
        if (propsHeader) {
            return propsHeader;
        }

        return (
            <>
                {team.displayName}
                <br />
                is now on the {planName} plan.
            </>
        );
    }, [propsHeader, team, planName]);

    const content = useMemo(() => {
        if (propsContent) {
            return propsContent;
        }

        return (
            <>
                <div>Next, invite members from your team to Unblocked with the shareable invite link below:</div>
                <CopyInput value={team.links.inviteUrl} fullWidth />
            </>
        );
    }, [propsContent, team]);

    return (
        <SuccessDialog
            className="plan_success_dialog"
            title={header}
            description={content}
            buttons={{ close: true }}
            size="medium"
            preventClickOutside
        />
    );
};
