import { SuccessCheckIcon } from '@shared/webComponents/SuccessCheckIcon/SuccessCheckIcon';

import './PaymentInviteSuccess.scss';

export const PaymentInviteSuccess = ({ teamName, planName }: { teamName?: string; planName?: string }) => {
    return (
        <div className="payment_invite_success">
            <SuccessCheckIcon size={{ width: 140, height: 120 }} />
            <div>
                <h1>Thank you for your payment!</h1>

                {teamName && planName ? (
                    <div className="payment_invite_success__info">
                        {teamName} is now on the <b>{planName} plan.</b>
                    </div>
                ) : null}
            </div>
            <div className="payment_invite_footer">
                <div>You may close this window.</div>
            </div>
        </div>
    );
};
