import { PlanTemplate, Team } from '@shared/api/models';

import { Dialog } from '@web/components/Dialog/Dialog';

interface Props {
    team: Team;
    currentPlan: PlanTemplate;
    onContactSupport: () => void;
}

export const DowngradeSupportDialog = ({ team, currentPlan, onContactSupport }: Props) => {
    return (
        <Dialog
            className="downgrade_support_dialog"
            title={<div>Contact Support to Downgrade</div>}
            deprecatedButtons={{
                primary: {
                    children: <>Contact Support</>,
                    onClick: onContactSupport,
                },
            }}
        >
            <div className="downgrade_support_dialog__content">
                <div>
                    &ldquo;{team.displayName}&rdquo; is on the {currentPlan.displayName} plan, which is a customized
                    plan. To downgrade, please contact our support team for assistance.
                </div>
            </div>
        </Dialog>
    );
};
