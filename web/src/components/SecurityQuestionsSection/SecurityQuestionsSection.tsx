import classNames from 'classnames';

import { Icon } from '@shared/webComponents/Icon/Icon';

import securityShieldIcon from './security-shield-icon.svg';

import './SecurityQuestionsSection.scss';

const securityPage = 'https://getunblocked.com/security';

export const SecurityQuestionsSection = ({ topMargin }: { topMargin?: boolean }) => {
    const classnames = classNames('security_questions_section', {
        security_questions_section__top_margin: topMargin,
    });

    return (
        <div className={classnames}>
            <Icon icon={securityShieldIcon} size="large" />
            <b>Security Questions?</b>
            <div>
                Check out our{' '}
                <a href={securityPage} target="_blank" rel="noreferrer">
                    Security Page
                </a>{' '}
                to review our security documentation and download our SOC 2 report.
            </div>
        </div>
    );
};
