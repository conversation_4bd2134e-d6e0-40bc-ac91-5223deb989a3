import classNames from 'classnames';
import { ReactNode, useCallback, useMemo } from 'react';

import { SetUtils } from '@shared/webUtils';

import { faChevronDown } from '@fortawesome/pro-regular-svg-icons/faChevronDown';

import { Input } from '../Input/Input';
import { Dropdown, DropdownItem } from './Dropdown';
import { SelectableListDropdownHeader } from './SelectableListDropdownHeader';

import './SelectableListDropdown.scss';

interface SelectableListDropdownProps<T> {
    items: T[];
    selectedItems: T[];
    onItemChange: (items: T[]) => void;
    headerLabel: (item: T[]) => string;
    itemLabel: (item: T) => ReactNode;
    identifier: (item: T) => string;
    closeOnSelect?: boolean;
    className?: string;
    itemsClassName?: string;
    itemClassName?: string;
}

export const SelectableListDropdown = <T,>({
    items,
    selectedItems,
    onItemChange,
    headerLabel,
    itemLabel,
    identifier,
    closeOnSelect = false,
    className,
    itemsClassName,
    itemClassName,
}: SelectableListDropdownProps<T>) => {
    const classes = classNames(className, {
        selectable_list_dropdown: true,
        selectable_list_dropdown__modified: selectedItems.length > 0,
    });
    const selectedItemIdentifiers = useMemo(() => selectedItems?.map(identifier) ?? [], [selectedItems, identifier]);

    const toggleItem = useCallback(
        (item: T | undefined) => {
            if (!item) {
                onItemChange([]);
                return;
            }
            const toggledItems = [...SetUtils.toggle(item, new Set(selectedItems))];
            onItemChange(toggledItems);
        },
        [selectedItems, onItemChange]
    );

    return (
        <Dropdown
            alignment="left"
            className={classes}
            withCaret={selectedItems.length === 0}
            caretIcon={faChevronDown}
            caretSize="xSmall"
            header={
                <SelectableListDropdownHeader
                    onClear={selectedItems.length > 0 ? () => toggleItem(undefined) : undefined}
                >
                    {headerLabel(selectedItems)}
                </SelectableListDropdownHeader>
            }
            dropdownItemsClasses={itemsClassName}
        >
            <>
                {items.map((item) => {
                    const currentId = identifier(item);
                    const isSelected = selectedItems && selectedItemIdentifiers.some((v) => v === currentId);
                    return (
                        <DropdownItem
                            key={currentId}
                            onClick={() => {
                                toggleItem(item);
                            }}
                            closeOnSelect={closeOnSelect}
                            className={itemClassName}
                        >
                            <div
                                className={classNames({
                                    selectable_list_dropdown__row: true,
                                    'selectable_list_dropdown_row--selected': isSelected,
                                })}
                            >
                                <Input
                                    key={isSelected ? `filter-${currentId}-selected` : `filter-${currentId}-unselected`}
                                    type="checkbox"
                                    checked={isSelected}
                                    readOnly
                                />

                                <span>{itemLabel(item)}</span>
                            </div>
                        </DropdownItem>
                    );
                })}
            </>
        </Dropdown>
    );
};
