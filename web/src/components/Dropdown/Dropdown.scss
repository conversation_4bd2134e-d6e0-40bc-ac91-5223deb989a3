@use 'layout' as *;
@use 'flex' as *;
@use 'misc' as *;
@use 'fonts' as *;
@use 'theme' as *;

@use '../Popover/Popover' as *;

.dropdown__items {
    @include popover-canvas-themed;

    .dropdown__item {
        font-size: $font-size-14;
        padding: $spacer-10 $spacer-16;
        transition: background 0.1s ease-in-out;
        color: themed($text);
        border-radius: $border-radius-6;

        &:hover,
        &.dropdown__item__active {
            background-color: themed($dropdown-bg-hover);
        }

        &.dropdown__item__danger {
            color: themed($danger);
        }
    }

    .dropdown__divider {
        border-top-color: themed($dropdown-divider);
        margin: $spacer-4 0;
    }
}

.dropdown__group {
    .dropdown__group__header {
        font-weight: $font-weight-bold;
        color: themed($text-secondary);
    }
    .dropdown__group__content {
        padding: $spacer-4;
    }
}

.dropdown {
    &.dropdown__select {
        border-radius: $border-radius-6;

        .dropdown__header {
            border-color: themed($border);
            background-color: themed($dropdown-bg);
            border-radius: $border-radius-6 $border-radius-6;
            padding: $spacer-12;
        }

        .dropdown__items {
            width: 100%;
            box-sizing: border-box;
            border-radius: $border-radius-6 $border-radius-6;
            margin-top: $spacer-2;
            border: $border-style $border-width themed($border);
        }
    }

    .dropdown__search__container {
        background-color: themed($dropdown-bg);
        padding: $spacer-6 $spacer-6 0 $spacer-6;
        @include flex-column;

        justify-content: center;
    }

    .dropdown__loading {
        color: themed($text-secondary);
        background: themed($dropdown-bg);
        border-top: $border-width $border-style themed($border);
    }
}
