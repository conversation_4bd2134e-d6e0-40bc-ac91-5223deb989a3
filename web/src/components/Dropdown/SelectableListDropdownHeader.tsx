import { faCircleXmark } from '@fortawesome/pro-solid-svg-icons/faCircleXmark';

import { Button } from '../Button/Button';

export const SelectableListDropdownHeader = ({
    onClear,
    children,
}: {
    onClear?: () => void;
    children: React.ReactNode;
}) => {
    return (
        <span className="selectable_list_dropdown__header">
            {children}
            {onClear ? (
                <Button
                    as="icon"
                    icon={faCircleXmark}
                    iconSize="xSmall"
                    className="selectable_list_dropdown__header_clear"
                    onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        onClear();
                    }}
                />
            ) : null}
        </span>
    );
};
