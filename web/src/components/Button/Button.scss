@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'button-mixin' as *;
@use 'flex' as *;
@use 'layout' as *;
@use 'misc' as *;
@use 'theme' as *;

@mixin local-button-color-styles {
    &.button__primary {
        @include primary-button;
    }

    &.button__secondary {
        @include secondary-button;
    }

    &.button__tertiary {
        @include tertiary-button;
    }

    &.button__outline {
        @include outline-button;
    }

    &.button__destructive {
        @include destructive-button;
    }

    &.button__destructive-secondary {
        @include destructive-secondary-button;
    }

    &.button__destructive-outline {
        @include destructive-outline-button;
    }
}

.button {
    border-radius: $border-radius-4;
    line-height: $lh-default;
    border: none;
    transition: background-color 0.1s ease-in;

    &.button__large {
        padding: $spacer-12 $spacer-16;
    }

    &.button__standard {
        padding: $spacer-10 $spacer-12;
    }

    &.button__small {
        padding: $spacer-6 $spacer-12;
    }

    &.button__xs {
        padding: $spacer-3 $spacer-12;
    }

    &.button__tight {
        padding: $spacer-1 $spacer-8;
        @include detail;
    }

    &:disabled {
        pointer-events: none;
    }

    &.button__as_icon {
        padding: 0;
        font-size: 0;

        &:hover {
            color: themed($link);
        }
    }

    &.button__as_link {
        color: themed($link);
        padding: 0;
    }
    @include local-button-color-styles;
}

.button__options_dropdown {
    @include flex-center-center;
    @include local-button-color-styles;

    padding: $spacer-4;
    border-top-right-radius: $border-radius-4;
    border-bottom-right-radius: $border-radius-4;
}

.button__container > .button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.button__options {
    padding: 0 $spacer-4;
    background: themed($button-primary-bg);
    border: $border-width $border-style themed($button-secondary-bg);
    height: max-content;

    .button__option {
        padding: $spacer-4 $spacer-24 $spacer-4 0;
        color: themed($button-secondary-fg);
        text-align: left;
        display: grid;
        grid-template-columns: $spacer-12 auto;
        grid-column-gap: $spacer-4;

        &:not(:last-of-type) {
            border-bottom: $border-width $border-style themed($button-secondary-fg);
        }
    }
}

.copy_icon_button {
    transition: color 0.2s ease-in-out;
    &.fa-copy {
        color: themed($text-tertiary);

        &:hover {
            color: themed($link-hover);
        }
    }

    &.fa-check {
        color: themed($link);
    }
}

.add_button {
    color: themed($button-primary-fg);

    .icon .fa-secondary {
        color: themed($button-primary-bg);
    }

    &:hover {
        color: themed($button-primary-fg);

        .icon .fa-secondary {
            color: themed($button-primary-hover-bg);
        }
    }
}

.delete_button {
    color: themed($button-destructive-bg);
    &:hover {
        color: themed($button-destructive-hover-bg);
    }
}
