import { forwardRef, Ref } from 'react';

import { Button, Props as ButtonProps } from '@shared/webComponents/Button/Button';

import './OpaqueButton.scss';

// wrapper class that has a built-in $bg background div ensuring the button renders opaquely
const OpaqueButtonComponent = (props: ButtonProps, ref: Ref<HTMLDivElement>) => {
    return (
        <div className="opaque_button" ref={ref}>
            <Button {...props} />
        </div>
    );
};

export const OpaqueButton = forwardRef(OpaqueButtonComponent);
