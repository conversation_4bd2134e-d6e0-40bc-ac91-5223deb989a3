import type { Meta, StoryObj } from '@storybook/react';

import { StatusIcon } from './StatusIcon';
import { UserIcon } from './UserIcon';
import { UserIconStack } from './UserIconStack';

export default {
    title: 'Web/User Icon',
    component: UserIcon,
    argTypes: {
        backgroundColor: { control: 'color' },
    },
    parameters: {
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof UserIcon>;

type UserIconStory = StoryObj<typeof UserIcon>;
type UserIconStackStory = StoryObj<typeof UserIconStack>;
type StatusIconStory = StoryObj<typeof StatusIcon>;

export const SingleUserIcon: UserIconStory = {
    args: {
        user: { name: '<PERSON> <PERSON>', iconUrl: 'https://static.independent.co.uk/2021/06/16/08/newFile-4.jpg' },
    },
};

export const InitialsIcon: UserIconStory = {
    args: {
        user: { name: '<PERSON>', iconUrl: '' },
    },
};

export const IconStack: UserIconStackStory = {
    render: (args) => (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: '1rem' }}>
            <UserIconStack {...args} />
        </div>
    ),
    args: {
        users: [
            { name: 'Kay Cheng', iconUrl: 'https://static.independent.co.uk/2021/06/16/08/newFile-4.jpg' },
            { name: 'Ben Ng', iconUrl: '' },
            {
                name: 'Matt Adam',
                iconUrl: 'https://vetmed.tamu.edu/news/wp-content/uploads/sites/9/2019/10/CatGrassPetTalk.png',
            },
        ],
    },
};

export const OverflowIconStack: UserIconStackStory = {
    render: (args) => (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: '1rem' }}>
            <UserIconStack {...args} />
        </div>
    ),
    args: {
        users: [
            { name: 'Kay Cheng', iconUrl: 'https://static.independent.co.uk/2021/06/16/08/newFile-4.jpg' },
            { name: 'Ben Ng', iconUrl: '' },
            {
                name: 'Matt Adam',
                iconUrl: 'https://vetmed.tamu.edu/news/wp-content/uploads/sites/9/2019/10/CatGrassPetTalk.png',
            },
            {
                name: 'Jeff Ng',
                iconUrl: '',
            },
            {
                name: 'David Lam',
                iconUrl: '',
            },
        ],
    },
};

export const WithAvailableStatus: StatusIconStory = {
    render: (args) => <StatusIcon {...args} size="large" />,
    args: {
        user: { name: 'Kay Cheng' },
        status: 'available',
    },
};

export const WithAvailableMobileStatus: StatusIconStory = {
    render: (args) => <StatusIcon {...args} size="large" />,
    args: {
        user: { name: 'Kay Cheng' },
        status: 'available',
        size: 'large',
    },
};

export const WithAwayStatus: StatusIconStory = {
    render: (args) => <StatusIcon {...args} size="large" />,
    args: {
        user: { name: 'Kay Cheng' },
        status: 'away',
    },
};

export const WithDNDStatus: StatusIconStory = {
    render: (args) => <StatusIcon {...args} size="large" />,
    args: {
        user: { name: 'Kay Cheng' },
        status: 'doNotDisturb',
    },
};

export const WithOfflineStatus: StatusIconStory = {
    render: (args) => <StatusIcon {...args} size="large" />,
    args: {
        user: { name: 'Kay Cheng' },
        status: 'offline',
    },
};
