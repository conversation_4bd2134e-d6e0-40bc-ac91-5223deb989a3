@use 'misc' as *;
@use 'theme' as *;
@use 'fonts' as *;
@use 'layout' as *;

* {
    .user_icon {
        color: themed($icon-text);
        background-color: themed($icon-bg);
        border-color: themed($icon-text);

        &.user_icon__no_style {
            background-color: transparent;
            border-color: transparent;
        }
    }

    .stack_icon.stack_icon__more {
        color: themed($icon-text);
        background-color: themed($icon-bg-secondary);
        border-color: themed($icon-text);

        & > span {
            font-size: $font-size-13;

            // offset the + so that the number is aligned closer to the middle
            margin-right: $spacer-2;
        }

        &.icon--medium {
            & > span {
                font-size: $font-size-10;
            }
        }
    }

    .provider_icon {
        &.provider_icon__web {
            color: themed($link);
        }
    }
}
