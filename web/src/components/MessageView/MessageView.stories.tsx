import { useEffect, useState } from 'react';

import { MessageContent, Provider } from '@shared/api/generatedApi';

import { MessageTransformer, PromiseUtils } from '@shared/webUtils';
import { createMockMessage, createMockTeamMember } from '@shared-mocks';
import type { Meta, StoryObj } from '@storybook/react';

import {
    Block,
    InlineElement,
    ListBlock_Style,
} from '../../../../common/build/generated/source/proto/main/ts_proto/Message';
import { MessageView } from './MessageView';

export default {
    title: 'Web/MessageView',
    component: MessageView,
} as Meta<typeof MessageView>;

import MockDate from 'mockdate';
MockDate.reset();

const paragraph = (elements: InlineElement[]): Block => ({
    content: { $case: 'paragraph', paragraph: { elements } },
});

const text = (text: string, isBold?: boolean): InlineElement => ({
    content: { $case: 'text', text: { text, isBold } },
});

const link = (title: string, url: string): InlineElement => ({
    content: { $case: 'link', link: { url, textItems: [{ text: title }] } },
});

const list = (items: InlineElement[][]): Block => ({
    content: {
        $case: 'list',
        list: {
            style: ListBlock_Style.ORDERED,
            items: items.map((item) => ({
                blocks: [paragraph(item)],
            })),
        },
    },
});

const code = (text: string): Block => ({
    content: {
        $case: 'code',
        code: {
            text,
        },
    },
});

const message = (blocks: Block[]) => ({
    version: '1',
    content: MessageTransformer.fromMessageToBytes({
        blocks,
        version: '1',
    }),
});

type MessageViewStory = StoryObj<typeof MessageView>;

const mockMessage = createMockMessage({});
const mockTeamMember = createMockTeamMember({});

export const Primary: MessageViewStory = {
    args: {
        teamId: 'abc',
        author: mockTeamMember,
        createdAt: new Date('2021-12-31T03:24:00'),
        links: { dashboardUrl: mockMessage.links.dashboardUrl },
        messageContent: mockMessage.messageContent,
        isAuthorCurrentPerson: true,
        messageReferences: mockMessage.references,
    },
};

export const Pinned: MessageViewStory = {
    args: {
        teamId: 'abc',
        author: mockTeamMember,
        createdAt: new Date('2022-01-01T11:24:00'),
        links: { dashboardUrl: mockMessage.links.dashboardUrl },
        messageContent: mockMessage.messageContent,
        pinned: true,
        isAuthorCurrentPerson: true,
    },
};

const loadingMessage = message([paragraph([text('Unbot is thinking...')])]);

export const Loading: MessageViewStory = {
    args: {
        teamId: 'abc',
        author: mockTeamMember,
        createdAt: new Date('2022-01-01T11:24:00'),
        links: { dashboardUrl: mockMessage.links.dashboardUrl },
        messageContent: loadingMessage,
        messageState: 'loading',
        pinned: true,
        isAuthorCurrentPerson: true,
    },
};

const interpolatedMessage1 = message([
    paragraph([text('This is a first text element.  '), text('This is a second text', true)]),
]);

const interpolatedMessage2 = message([
    paragraph([
        text('This is a first text element.  '),
        text('This is a second text element.  ', true),
        text('This is a third text element'),
    ]),
]);

const interpolatedMessage3 = message([
    paragraph([
        text('This is a first text element.  '),
        text('This is a second text element.  ', true),
        text('This is a third text element'),
    ]),
    list([[text('This is a first list item')], [text('This is a second list item')]]),
    code(`// This is some code.
const q = 1;
const q2 = q;
console.log('hello');
`),
    paragraph([text('This is a final paragraph')]),
]);

interface MsgData {
    message: MessageContent;
    delay: number;
}

function InterpolatedView({ messages }: { messages: MsgData[] }) {
    const [message, setMessage] = useState<MessageContent>();
    const [isStreaming, setIsStreaming] = useState(true);

    useEffect(() => {
        const asyncFn = async () => {
            for (const msgData of messages) {
                await PromiseUtils.wait(msgData.delay);
                setMessage(msgData.message);
            }

            setIsStreaming(false);
        };

        asyncFn();
    }, [messages]);

    if (!message) {
        return <></>;
    }

    return (
        // Mock client config flag to allow interpolated rendering
        <MessageView
            teamId="abc"
            id="abc"
            repoId={undefined}
            index={0}
            provider={Provider.Github}
            onDeleteMessage={() => {}}
            isAuthorCurrentPerson={true}
            isAnchor={false}
            messageContent={message}
            author={mockTeamMember}
            createdAt={new Date('2022-01-01T11:24:00')}
            links={{ dashboardUrl: mockMessage.links.dashboardUrl }}
            isStreaming={isStreaming}
        />
    );
}

type InterpolatedViewStory = StoryObj<typeof InterpolatedView>;

export const Interpolated: InterpolatedViewStory = {
    render: (args) => <InterpolatedView {...args} />,
    args: {
        messages: [
            { message: interpolatedMessage1, delay: 0 },
            { message: interpolatedMessage2, delay: 100 },
            { message: interpolatedMessage3, delay: 1000 },
        ],
    },
};

const interpolatedMessage10 = message([
    paragraph([text('This is a first text element.')]),
    paragraph([text('This is a second text element.  It has a pretty long string to', true)]),
]);

const interpolatedMessage11 = message([
    paragraph([text('This is a first text element.')]),
    list([
        [
            text(
                'This is a second text element.  It has a pretty long string to introduce a long delay to we can see it change'
            ),
        ],
        [text('And a third one')],
    ]),
]);

export const Interpolated2: InterpolatedViewStory = {
    render: (args) => <InterpolatedView {...args} />,
    args: {
        messages: [
            { message: interpolatedMessage10, delay: 0 },
            { message: interpolatedMessage11, delay: 1000 },
        ],
    },
};

const interpolatedMessage20 = message([paragraph([text(`The SourceMark engine is a key component of Next Chapter `)])]);

const interpolatedMessage21 = message([
    paragraph([
        text(
            `The SourceMark engine is a key component of Next Chapter Software Inc's system. It provides APIs to obtain sourcemarks that are up-to-date with respect to the Git workspace. The engine is primarily defined in the ISourceMarkProvider`
        ),
    ]),
]);

const interpolatedMessage22 = message([
    paragraph([
        text(
            `The SourceMark engine is a key component of Next Chapter Software Inc's system. It provides APIs to obtain sourcemarks that are up-to-date with respect to the Git workspace. The engine is primarily defined in the ISourceMarkProvider interface, which includes methods such as getSourceMarkLatestPoint, getSourceMarkStreamForFile, addActiveFiles, and recalculateRepo.`
        ),
    ]),
]);

export const Interpolated3: InterpolatedViewStory = {
    render: (args) => <InterpolatedView {...args} />,
    args: {
        messages: [
            { message: interpolatedMessage20, delay: 0 },
            { message: interpolatedMessage21, delay: 2000 },
            { message: interpolatedMessage22, delay: 1000 },
        ],
    },
};

const interpolatedMessage40 = message([
    paragraph([
        text(
            `The Sourcemark Engine is a key component of Next Chapter Software's system. Reference: [A fake link that has a really long title](https://somereallylongurl.com/this/has/a/really/long/path/wow/super/long/long/path/super/long)`
        ),
    ]),
]);

const interpolatedMessage41 = message([
    paragraph([
        text(`The Sourcemark Engine is a key component of Next Chapter Software's system. Reference: `),
        link('A link goes here', 'https://whocares'),
        text(' some additional text'),
    ]),
    paragraph([
        text(
            `The engine is designed to be efficient and performant. For example, it prunes the set of marks passed to the engine to only those that can possibly affect the current file, which includes all uncommitted files if the current file is edited. It also maintains a cache from markID-to-point at the given tree SHA for each tree SHA (SourceMarkCalculator).`
        ),
    ]),
]);

export const Interpolated4: InterpolatedViewStory = {
    render: (args) => <InterpolatedView {...args} />,
    args: {
        messages: [
            { message: interpolatedMessage40, delay: 0 },
            { message: interpolatedMessage41, delay: 3000 },
        ],
    },
};
