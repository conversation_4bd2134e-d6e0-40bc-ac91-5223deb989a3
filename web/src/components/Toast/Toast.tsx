import { Transition } from '@headlessui/react';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';

import { useTimeout } from '@shared/hooks/useTimeout';
import { Banner, BannerVariant, Props as BannerProps } from '@shared/webComponents/Banner/Banner';

import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons/faCircleCheck';

import { useToastContext } from './ToastManagerContext';

import './Toast.scss';

interface Props {
    children: React.ReactNode;
    persist?: boolean;
}

/** Get the latency for the toast based on the variant */
const getToastLatency = (variant?: BannerVariant): number => {
    if (!variant) {
        return 0;
    }

    switch (variant) {
        case 'success':
        case 'confirm':
            return 2500;
        case 'alert':
        case 'warning':
            return 10000;
        case 'info':
        case 'unblocked':
        case 'private':
            return 5000;
    }
};

export const Toast = ({
    children,
    className,
    onClose: propsOnClose,
    persist = false,
    ...bannerProps
}: Props & BannerProps) => {
    const { close } = useToastContext();
    const [show, setShow] = useState(true);
    const { trigger: hide } = useTimeout(() => setShow(false), getToastLatency(bannerProps.variant));
    const { trigger: onClose } = useTimeout(() => {
        propsOnClose?.();
        close();
    }, 500);

    useEffect(() => {
        if (!show) {
            onClose();
        }
    }, [show, onClose]);

    useEffect(() => {
        if (persist) {
            return;
        }

        hide();
    }, [persist, hide]);

    const icon = useMemo(() => {
        if (bannerProps.icon) {
            return bannerProps.icon;
        }

        if (bannerProps.variant === 'info') {
            return faCircleCheck;
        }

        return undefined;
    }, [bannerProps]);

    const classes = classNames({
        toast: true,
        [`${className}`]: !!className,
    });

    return (
        <Transition
            appear={false}
            unmount={false}
            show={show}
            as="div"
            className="toast_container transition-div"
            enter="transition"
            enterFrom="transition-right"
            enterTo="transition-base"
            leave="transition"
            leaveFrom="transition-base"
            leaveTo="transition-right"
        >
            <Banner
                {...bannerProps}
                className={classes}
                onClose={() => setShow(false)}
                icon={icon}
                closePlacement="top"
                floating
            >
                {children}
            </Banner>
        </Transition>
    );
};
