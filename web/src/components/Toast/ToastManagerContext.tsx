import { createContext, useCallback, useContext, useRef, useState } from 'react';
import { v4 as uuid } from 'uuid';

import { EmptyFn } from '@shared/webUtils/TypeUtils';

type ToastsMap = Map<string, React.ReactNode>;
const ToastContext = createContext<{
    close: () => void;
}>({
    close: EmptyFn,
});

interface ToastManagerContextProps {
    toasts: ToastsMap;
    registerToast: (toast: React.ReactNode) => void;
    closeToast: (id: string) => void;
    closeAll: () => void;
}

const ToastManagerContext = createContext<ToastManagerContextProps>({
    toasts: new Map(),
    registerToast: EmptyFn,
    closeToast: EmptyFn,
    closeAll: EmptyFn,
});

// provider wrapper that wraps the app to render toasts
export const ToastManagerContextProvider = ({ children }: { children: React.ReactNode }) => {
    const [toastsMap, setToastsMap] = useState<ToastsMap>(new Map());
    const toastsMapRef = useRef<ToastsMap>(new Map());

    const setToasts = useCallback((map: ToastsMap) => {
        const newMap = new Map([...map]);
        setToastsMap(newMap);
    }, []);

    const addToast = useCallback(
        (toast: React.ReactNode) => {
            const toastsMap = toastsMapRef.current;
            const copy = new Map(toastsMap);
            copy.set(uuid(), toast);
            toastsMapRef.current = copy;
            setToasts(copy);
        },
        [setToasts]
    );

    const removeToast = useCallback(
        (id: string) => {
            const toastsMap = toastsMapRef.current;
            const copy = new Map(toastsMap);
            copy.delete(id);
            setToasts(copy);
            toastsMapRef.current = copy;
        },
        [setToasts]
    );

    const removeAllToasts = useCallback(() => {
        const toastsMap = toastsMapRef.current;
        const copy = new Map(toastsMap);
        copy.clear();
        setToasts(copy);
        toastsMapRef.current = copy;
    }, [setToasts]);

    return (
        <ToastManagerContext.Provider
            value={{
                toasts: toastsMap,
                registerToast: addToast,
                closeToast: removeToast,
                closeAll: removeAllToasts,
            }}
        >
            {children}
            <ToastRenderer />
        </ToastManagerContext.Provider>
    );
};

const ToastRenderer = () => {
    const { toasts, closeToast } = useContext(ToastManagerContext);

    return (
        <>
            {[...toasts].map(([id, toast]) => (
                // each toast has its own ToastContext
                <ToastContext.Provider
                    key={id}
                    value={{
                        close: () => closeToast(id),
                    }}
                >
                    {toast}
                </ToastContext.Provider>
            ))}
        </>
    );
};

// used for macro-level toast management
export function useToastManagerContext(): ToastManagerContextProps {
    return useContext(ToastManagerContext);
}

// used inside individual toast
export function useToastContext() {
    return useContext(ToastContext);
}
