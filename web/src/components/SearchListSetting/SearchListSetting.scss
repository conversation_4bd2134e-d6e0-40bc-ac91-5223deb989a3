@use 'theme' as *;
@use 'misc' as *;
@use 'fonts' as *;
@use 'flex' as *;
@use 'layout' as *;
@use 'layout-mixin' as *;

.search_list_setting {
    background: themed($thread-summary-bg);

    &,
    .search_list_setting_section__title_section,
    .search_list_setting_section__item_row,
    .search_list_setting__search {
        border-color: themed($border);
    }

    .search_list_setting_section__title_section {
        color: themed($text-tertiary);
        background-color: themed($row-section-bg);
        font-size: $font-size-12;
        line-height: $line-height-18;

        .table_header {
            background: none;
        }

        .search_list_setting_section__title {
            &:first-of-type {
                text-align: left;
            }

            &:last-of-type {
                &:not(:first-of-type) {
                    text-align: right;
                }
            }
        }
    }

    .search_list_setting_section__item_row {
        &:hover {
            background: themed($context-row-hover-bg);
        }

        .search_list_setting__remove__icon {
            color: themed($danger);
        }
    }

    .search_list_setting__empty,
    .search_list_setting__return,
    .search_list_setting_section__added,
    .search_list_setting__suggestions--loading,
    .search_list_setting__suggestions--empty {
        color: themed($text-tertiary);
    }

    .search_list_setting__suggestions--loading,
    .search_list_setting__suggestions--empty {
        font-size: $font-size-12;
    }

    .search_list_setting__error {
        color: themed($danger);
        padding: $spacer-6 $spacer-4 0;
        font-size: $font-size-12;
    }

    .search_list_setting_section__title,
    .search_list_setting_section__item_row,
    .search_list_setting_section__item_row .context_row__content {
        @include fill-available;
    }

    .search_list_setting_section__empty {
        display: flex;
        flex-direction: column;

        .search_list_setting_section__empty_section {
            margin-top: $spacer-16;
            @include flex-column-center;
        }
    }
}
