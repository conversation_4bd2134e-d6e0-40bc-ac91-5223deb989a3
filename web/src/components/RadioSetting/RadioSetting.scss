@use 'layout' as *;
@use 'misc' as *;
@use 'fonts' as *;
@use 'theme' as *;
@use 'flex' as *;

$radio-setting-body-min-height: 80px;

.radio_setting {
    .radio_setting__header {
        text-align: left;
        display: grid;
        grid-template-columns: auto 1fr;
        align-items: center;
        grid-column-gap: $spacer-12;
        padding-bottom: $spacer-8;
        cursor: pointer;
    }

    input[type='radio'] {
        accent-color: themed($border-active);
    }

    &.radio_setting__checked {
        display: flex;
        flex-direction: column;
        min-height: 0;

        .radio_setting__header {
            grid-template-columns: auto 1fr auto;
        }
        .radio_setting__body {
            flex: 1;
        }
    }

    &.radio_setting__disabled {
        .radio_setting__header {
            cursor: auto;
        }
    }

    .radio_setting__body {
        min-height: $radio-setting-body-min-height;
        margin-top: $spacer-4;
        @include flex-column;
    }
}
