@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'fonts' as *;

.text_input {
    padding: $spacer-11;
    border: $border-width $border-style themed($input-border);
    border-radius: $border-radius-6;

    // to prevent input auto-zoom
    font-size: $font-size-16;

    @include breakpoint(md) {
        font-size: $font-size-14;
    }

    &:focus-within {
        border-color: themed($input-border-focus);
        box-shadow: 0 0 0 $border-width-2 themed($input-border-outline);
    }

    input {
        background-color: transparent;
        color: inherit;

        &::placeholder {
            color: themed($input-placeholder);
            opacity: 1;
        }
    }

    &.text_input__primary {
        background-color: themed($input-background);
        color: themed($text);
    }

    &.text_input__outline {
        border-color: themed($input-placeholder);
        color: themed($input-outline-fg);
    }

    &.text_input__large {
        padding: $spacer-12;
    }

    &.text_input__small {
        padding: $spacer-6 $spacer-12;
    }

    &.text_input__read_only {
        background-color: themed($input-background-inactive);
        opacity: 0.6;
    }

    &.save_input {
        padding: $spacer-8;
    }
}

.copy_input {
    & > .text_input_icon > .copy_input__icon {
        color: themed($link);
    }
}

.email_input {
    .email_input__error_icon {
        color: themed($danger);
    }
}

.search_text_input {
    .search_text_input__leading_icon {
        color: themed($input-placeholder);
    }

    .search_text_input__close_button {
        transition: opacity 0.1s ease-in-out;

        .fa-primary {
            color: themed($button-tertiary-fg);
            transition: color 0.1s ease-in-out;
        }

        .fa-secondary {
            color: themed($button-tertiary-bg);
            transition: color 0.1s ease-in-out;
            opacity: 1;
        }

        &:hover {
            .fa-primary {
                color: themed($button-tertiary-hover-fg);
            }

            .fa-secondary {
                color: themed($button-tertiary-hover-bg);
            }
        }
    }
}
