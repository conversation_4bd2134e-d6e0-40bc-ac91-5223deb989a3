import { Icon } from '@shared/webComponents/Icon/Icon';
import type { Meta, StoryObj } from '@storybook/react';

import { faFileCode } from '@fortawesome/pro-regular-svg-icons/faFileCode';
import { faPlus } from '@fortawesome/pro-regular-svg-icons/faPlus';

import { ContextRow } from './ContextRow';

// More on default export: https://storybook.js.org/docs/react/writing-stories/introduction#default-export
export default {
    title: 'Web/ContextRow',
    component: ContextRow,
    // More on argTypes: https://storybook.js.org/docs/react/api/argtypes
    argTypes: {
        backgroundColor: { control: 'color' },
        onClick: { defaultValue: undefined, action: 'onClick' },
    },
    // enables snapshotting for the component
    parameters: {
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof ContextRow>;

type Story = StoryObj<typeof ContextRow>;

const noOpFn = () => {
    /* */
};

const icon = <Icon icon={faFileCode} size="medium" />;
const rightContent = <Icon icon={faPlus} size="medium" />;

export const Bold: Story = {
    render: () => (
        <ContextRow icon={icon} onClick={noOpFn} variant="bold">
            Context Row
        </ContextRow>
    ),
};

export const WithRightContent: Story = {
    render: () => (
        <ContextRow icon={icon} onClick={noOpFn} rightContent={rightContent}>
            Context Row
        </ContextRow>
    ),
};

export const Regular: Story = {
    render: () => (
        <ContextRow icon={icon} onClick={noOpFn} rightContent={rightContent}>
            Context Row
        </ContextRow>
    ),
};

export const NotClickable: Story = {
    render: () => <ContextRow icon={icon}>Context Row</ContextRow>,
};
