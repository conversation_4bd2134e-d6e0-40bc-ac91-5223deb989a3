import { useEffect } from 'react';

import { useLayoutContext } from '@web/components/Layout/LayoutContext';

export const useLockScroll = (shouldLock: boolean) => {
    const { setLockScroll } = useLayoutContext();

    useEffect(() => {
        if (shouldLock) {
            // if is open, override scrolling on navigator element
            setLockScroll(true);
        } else {
            // remove override, will be styled by CSS
            setLockScroll(false);
        }
    }, [shouldLock, setLockScroll]);
};
