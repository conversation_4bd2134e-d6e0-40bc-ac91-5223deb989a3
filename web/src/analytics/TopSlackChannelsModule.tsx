import { NavigateFunction, useNavigate } from 'react-router-dom';

import { Provider } from '@shared/api/models';

import { AnalyticsStore } from '@shared/stores/AnalyticsStore';
import { AnswersByChannelState } from '@shared/stores/AnalyticsStoreTypes';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { HoverTooltip } from '@shared/webComponents/Tooltip/HoverTooltip';
import { BrandIcons } from '@shared/webUtils/BrandIcons';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';
import { Button } from '@web/components/Button/Button';

import { faCircleInfo } from '@fortawesome/pro-duotone-svg-icons/faCircleInfo';

import { AnalyticsModule } from './AnalyticsModule';
import { HorizontalBarGraph } from './HorizontalBarGraph';
import { TimeAgoDropdown } from './TimeAgoDropdown';
import { TopSlackChannelsDialog } from './TopSlackChannelsDialog';

import './TopSlackChannelsModule.scss';

const SlackChannelsBarChart = ({ state }: { state: AnswersByChannelState }) => {
    return (
        <HorizontalBarGraph
            items={state.channels}
            itemValue={(item) => item.answers}
            itemContent={(item) => `#${item.channel}`}
        />
    );
};

const SlackChannelsDisconnected = ({ teamId, navigate }: { teamId: string; navigate: NavigateFunction }) => (
    <div className="analytics_module__info_panel">
        <div className="analytics_module__info_panel__title">
            Connecting Slack shows your most active channels here.
        </div>
        <p>
            Unblocked uses historical discussions from channels you add to answer questions. You can also ask questions
            directly in Slack.
        </p>
        <Button
            variant="outline"
            size="small"
            icon={BrandIcons.slack}
            onClick={() => navigate(DashboardUrls.providerSettings(teamId, Provider.Slack))}
        >
            Connect Slack
        </Button>
    </div>
);

export const TopSlackChannelsModule = ({
    teamId,
    state,
    store,
}: {
    teamId: string;
    state: AnswersByChannelState;
    store: AnalyticsStore;
}) => {
    const navigate = useNavigate();
    const { openModal } = useModalContext();

    return (
        <AnalyticsModule
            className="slack_channels_module"
            title={
                <div className="slack_channels_module__header">
                    <span>Most Active Channels</span>
                    <HoverTooltip
                        header={({ ref }) => <Button ref={ref} as="icon" icon={faCircleInfo} />}
                        placement="top"
                        containerClassName="slack_channels_module__header_tooltip__container"
                        className="slack_channels_module__header_tooltip"
                    >
                        The Slack channels with the most questions asked within the selected time range.
                    </HoverTooltip>
                </div>
            }
            trailingAction={
                <TimeAgoDropdown
                    duration={state.duration}
                    onDurationChange={(dataDuration) => store.answersByChannel.updateProps({ dataDuration })}
                />
            }
            footer={
                state.isSlackConnected && (
                    <div className="slack_channels_module__footer">
                        <Button
                            as="link"
                            onClick={() =>
                                navigate(DashboardUrls.providerSettings(teamId, Provider.Slack, { addNewItems: true }))
                            }
                        >
                            Add Channels
                        </Button>
                        {state.hasMoreChannels && (
                            <>
                                <span className="slack_channels_module__footer__spacer">|</span>
                                <Button
                                    as="link"
                                    onClick={() =>
                                        openModal(
                                            <TopSlackChannelsDialog store={store} defaultDuration={state.duration} />
                                        )
                                    }
                                >
                                    View More
                                </Button>
                            </>
                        )}
                    </div>
                )
            }
            contentPadding="tight"
        >
            {state.isSlackConnected ? (
                <SlackChannelsBarChart state={state} />
            ) : (
                <SlackChannelsDisconnected teamId={teamId} navigate={navigate} />
            )}
        </AnalyticsModule>
    );
};
