@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'theme' as *;

.search_insights_view {
    .insight_feed_header__icon {
        color: themed($topic);
    }

    .search_insights_view__filters {
        display: none;

        @include breakpoint(md) {
            @include flex-center($spacer-12);
        }
    }

    .search_insights_view__actions {
        @include flex-center($spacer-16);

        @include breakpoint(md) {
            display: none;
        }
    }
}
