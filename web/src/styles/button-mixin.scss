@use './colors' as *;
@use './layout' as *;
@use './misc' as *;
@use './fonts' as *;
@use './theme' as *;

@mixin primary-button {
    background-color: themed($button-primary-bg);
    color: themed($button-primary-fg);
    border: $border-style $border-width transparent;

    &:hover {
        background-color: themed($button-primary-hover-bg);
    }

    &:disabled {
        background-color: themed($button-primary-disabled-bg);
        color: themed($button-primary-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-focus-outline);
    }
}

@mixin secondary-button {
    background-color: themed($button-secondary-bg);
    color: themed($button-secondary-fg);
    border: $border-style transparent;

    &:hover {
        background-color: themed($button-secondary-hover-bg);
        color: themed($button-secondary-hover-fg);
    }

    &:active {
        background-color: themed($button-secondary-active-bg);
        color: themed($button-secondary-active-fg);
    }

    &:disabled {
        background-color: themed($button-secondary-disabled-bg);
        color: themed($button-secondary-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-focus-outline);
    }
}

@mixin tertiary-button {
    background-color: themed($button-tertiary-bg);
    color: themed($button-tertiary-fg);
    border: $border-style $border-width transparent;

    &:hover {
        background-color: themed($button-tertiary-hover-bg);
        color: themed($button-tertiary-hover-fg);
    }

    &:disabled {
        background-color: themed($button-tertiary-disabled-bg);
        color: themed($button-tertiary-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-focus-outline);
    }
}

@mixin outline-button {
    background-color: transparent;
    color: themed($button-outline-fg);
    border: $border-style $border-width themed($button-outline-border);

    &:hover {
        background-color: themed($button-outline-hover-bg);
        border-color: themed($button-outline-hover-border);
        color: themed($button-outline-hover-fg);
    }

    &:active {
        background-color: themed($button-outline-active-bg);
        color: themed($button-outline-active-fg);
    }

    &:disabled {
        background-color: transparent;
        border-color: themed($button-outline-disabled-border);
        color: themed($button-outline-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-focus-outline);
    }
}

@mixin destructive-button {
    background-color: themed($button-destructive-bg);
    color: themed($button-destructive-fg);
    border: $border-style $border-width transparent;

    &:hover {
        background-color: themed($button-destructive-hover-bg);
    }

    &:disabled {
        background-color: themed($button-destructive-disabled-bg);
        color: themed($button-destructive-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-destructive-focus-outline);
    }
}

@mixin destructive-secondary-button {
    background-color: themed($button-destructive-secondary-bg);
    color: themed($button-destructive-secondary-fg);
    border: $border-style $border-width transparent;

    &:hover {
        background-color: themed($button-destructive-secondary-hover-bg);
    }

    &:active {
        background-color: themed($button-destructive-secondary-active-bg);
        color: themed($button-destructive-secondary-active-fg);
    }

    &:disabled {
        background-color: themed($button-destructive-secondary-hover-bg);
        color: themed($button-destructive-disabled-bg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-destructive-focus-outline);
    }
}

@mixin destructive-outline-button {
    background-color: transparent;
    color: themed($button-destructive-outline-fg);
    border: $border-style $border-width themed($button-destructive-outline-border);

    &:hover {
        background-color: themed($button-destructive-outline-hover-bg);
        border-color: themed($button-destructive-outline-hover-fg);
        color: themed($button-destructive-outline-hover-fg);
    }

    &:active {
        background-color: themed($button-destructive-outline-active-bg);
        color: themed($button-destructive-outline-active-fg);
    }

    &:disabled {
        background-color: transparent;
        border-color: themed($button-destructive-outline-disabled-border);
        color: themed($button-destructive-outline-disabled-fg);
    }

    &:focus {
        outline: $outline-width $outline-style themed($button-destructive-focus-outline);
    }
}
