import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

import { useStorageContext } from '@components';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { StorageKeys } from '@shared-web-utils';

import { HubFallbackDialog, HubFallbackStorage } from './DiscussionThreadHubFallbackDialog';
export const FALLBACK_PARAM = 'fallback';

export const DiscussionThreadHubFallback = () => {
    const [searchParams, setSearchParams] = useSearchParams();
    const { openModal } = useModalContext();
    const { getItem, setItem } = useStorageContext();

    useEffect(() => {
        const isFallback = searchParams.get(FALLBACK_PARAM);
        const ignoreFallback = HubFallbackStorage.shouldIgnoreDialog();
        const ignoreBanner = getItem(StorageKeys.showExtensionBanner) === 'false';
        const isExtensionInstalled = getItem(StorageKeys.isExtensionInstalled) === 'true';

        if (!isFallback) {
            return;
        }

        // if extension isn't installed:
        if (!isExtensionInstalled) {
            // show fallback dialog if not dismissed
            if (!ignoreFallback) {
                openModal(<HubFallbackDialog />);
            }
            // show extension banner if not dismissed
            if (!ignoreBanner) {
                setItem(StorageKeys.showExtensionBanner, 'true');
            }
        }

        searchParams.delete(FALLBACK_PARAM);
        setSearchParams(searchParams, { replace: true });

        // Explicitly keeping openModal out of deps array
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [searchParams, setSearchParams, getItem, setItem]);

    return <></>;
};
