import { Provider, ScmAccount } from '@shared/api/generatedApi';

import { RepoPartialData } from '@web/settings/Scm/ScmSettingsTypes';

import { SelectDefaultRepos } from './DefaultRepoSelection';
import { SkipSelectRepoCheck } from './SkipSelectRepoCheck';

const SelectedDefaultRepos = Symbol('SelectedDefaultRepos');

// Mock the SelectDefaultRepos function
jest.mock('./DefaultRepoSelection', () => ({
    SelectDefaultRepos: jest.fn(() => SelectedDefaultRepos),
}));

const generateRepos = (count: number, isFork: boolean = false): RepoPartialData[] =>
    Array.from({ length: count }, (_, index) => ({
        id: `repo${index}`,
        displayName: `Repository ${index}`,
        ownerName: `Owner ${index}`,
        isEmpty: false,
        isFork: isFork,
        isSelected: false,
        isSavedSelected: false,
        hasCompletedProcessing: true,
    }));

const defaultAccount: ScmAccount = {
    avatarUrl: '',
    displayName: '',
    fullPath: '',
    htmlUrl: '',
    isPersonalAccount: false,
    provider: Provider.Github,
    allReposAccessible: false,
    externalId: 'not-used',
    externalInstallationId: 'not-used',
    needsCiScopes: false,
    isCiEligible: true,
};

const glAccount: ScmAccount = {
    avatarUrl: '',
    displayName: '',
    fullPath: '',
    htmlUrl: '',
    isPersonalAccount: false,
    provider: Provider.Gitlab,
    allReposAccessible: false,
    externalId: 'not-used',
    externalInstallationId: 'not-used',
    needsCiScopes: false,
    isCiEligible: true,
};

describe('SkipSelectRepoCheck', () => {
    it('shows repo selection for a personal account with more than 10 repos, no forks', () => {
        const installation = { ...defaultAccount, isPersonalAccount: true, allReposAccessible: true };
        const repos = generateRepos(11);
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });

    it('does not show repo selection for a non-personal account with 20 or fewer repos, no forks', () => {
        const installation = { ...defaultAccount, allReposAccessible: true };
        const repos = generateRepos(20);
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    it('shows repo selection when any repo is a fork', () => {
        const installation = { ...defaultAccount, allReposAccessible: true };
        const repos = generateRepos(5).concat(generateRepos(1, true));
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });

    it('skips repo selection when all repos are accessible and no forks are present', () => {
        const installation = { ...defaultAccount, allReposAccessible: true };
        const repos = generateRepos(5);
        const { reposToSelect, shouldShowRepoSelection } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(SelectDefaultRepos).not.toHaveBeenCalledWith(repos);
        expect(reposToSelect).toEqual(repos);
    });

    it('directly returns provided repos when not all are accessible', () => {
        const installation = { ...defaultAccount, allReposAccessible: false };
        const repos = generateRepos(5);
        const { reposToSelect, shouldShowRepoSelection } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    it('skips selection when specific repos selected, and some are forks', () => {
        const installation = { ...defaultAccount, allReposAccessible: false };
        const repos = generateRepos(3, true); // All forks
        const { reposToSelect, shouldShowRepoSelection } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    it('shows repo selection for non-personal account with more than 20 repos, including forks', () => {
        const installation = { ...defaultAccount, isPersonalAccount: false, allReposAccessible: true };
        const repos = generateRepos(15).concat(generateRepos(6, true)); // Total 21 repos with 6 forks
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });

    it('show repo selection for personal account with less than 10 repos and includes forks', () => {
        const installation = { ...defaultAccount, isPersonalAccount: true, allReposAccessible: true };
        const repos = generateRepos(5).concat(generateRepos(3, true)); // 8 repos, some are forks
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });

    it('returns provided repos when allReposAccessible is false and no forks', () => {
        const installation = { ...defaultAccount, allReposAccessible: false };
        const repos = generateRepos(10); // No forks
        const { reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(reposToSelect).toEqual(repos);
        expect(reposToSelect.every((r) => !r.isFork)).toBeTruthy();
    });

    it('shows selection for all forks when not all repos are accessible and all are forks', () => {
        const installation = { ...defaultAccount, allReposAccessible: false };
        const repos = generateRepos(5, true); // All forks
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    it('does not show selection when repo list is empty', () => {
        const installation = { ...defaultAccount, allReposAccessible: true };
        const repos: RepoPartialData[] = [];
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    it('skips selection when repo list is empty but allReposAccessible is false', () => {
        const installation = { ...defaultAccount, allReposAccessible: false };
        const repos: RepoPartialData[] = [];
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeFalsy();
        expect(reposToSelect).toEqual(repos);
    });

    // Test for selecting default repos when all are accessible and there are mixed repos (forks and non-forks)
    it('uses SelectDefaultRepos for mixed repos when all are accessible', () => {
        const installation = { ...defaultAccount, allReposAccessible: true };
        const repos = generateRepos(3).concat(generateRepos(2, true)); // Some forks, some not
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });

    it('shows selection for non-GH providers', () => {
        const installation = { ...glAccount, allReposAccessible: true };
        const repos = generateRepos(3);
        const { shouldShowRepoSelection, reposToSelect } = SkipSelectRepoCheck(installation, repos);
        expect(shouldShowRepoSelection).toBeTruthy();
        expect(reposToSelect).toEqual(SelectedDefaultRepos);
    });
});
