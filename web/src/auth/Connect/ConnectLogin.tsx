import { Icon } from '@shared/webComponents/Icon/Icon';
import { LoginList } from '@shared/webComponents/Login/LoginList';
import { DashboardUrls } from '@shared/webUtils';

import unblocked from '@clientAssets/unblockedColor.svg';

import './ConnectLogin.scss';
export const ConnectLogin = () => {
    return (
        <div className="connect_login">
            <div className="connect_login__body">
                <Icon className="unblocked_icon" icon={unblocked} size="medium" />
                <h1>Create your Unblocked team</h1>
                <p>Get started by selecting your source code system.</p>
                <LoginList
                    ignoreCache
                    ignoreSSO
                    scmOnly
                    completionUrl={DashboardUrls.onboardingCreateTeam()}
                    separatorTitle="More source code systems"
                />
            </div>
        </div>
    );
};
