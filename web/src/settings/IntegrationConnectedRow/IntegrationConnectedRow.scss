@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.integration_connected_row {
    background-color: themed($nav-bg);
    border-radius: $border-radius-6;
    padding: $spacer-12 $spacer-16;

    .integration_connected_row__icon {
        border-radius: $border-radius-6;
        margin-right: $spacer-6;

        &.integration_connected_row__default_icon {
            background: themed($nav-bg);
            color: themed($text-secondary);
            padding: $spacer-4;
        }
    }

    .integration_connected_row__name {
        font-weight: $font-weight-bold;
    }

    .integration_connected_row__description {
        @include detail;

        color: themed($text-tertiary);
    }

    .integration_row__connected_icon {
        color: themed($success);
    }
}
