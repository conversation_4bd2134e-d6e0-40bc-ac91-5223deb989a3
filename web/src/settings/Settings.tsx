import { useEffect, useState } from 'react';
import { Outlet } from 'react-router';

import { TabHeadersRendererProps } from '@shared/webComponents/Tabs/Tabs';
import { EmptyFn } from '@shared/webUtils/TypeUtils';
import { DetailLayoutView, ViewVariant, ViewWidth } from '@web/components/Layout/DetailLayoutView';

import { SettingsOutletContext } from './SettingsOutletContext';

import './Settings.scss';

export const SettingsLabel = ({
    label,
    labelBadge,
    description,
    onClick,
}: {
    label: React.ReactNode;
    labelBadge?: React.ReactNode;
    description?: React.ReactNode;
    onClick?: () => void;
}) => {
    return (
        <div className="settings_label" onClick={onClick}>
            <div className="settings_label__title">
                <h3>{label}</h3>
                {labelBadge}
            </div>
            {description ? <span className="settings_label__description">{description}</span> : null}
        </div>
    );
};

export const Settings = () => {
    // top level scoped banner
    const [banner, setBanner] = useState<React.ReactNode>(null);
    // child view scoped banner
    const [viewBanner, setViewBanner] = useState<React.ReactNode>(null);
    const [nav, setNav] = useState<React.ReactNode>();
    const [header, setHeader] = useState<React.ReactNode>(null);
    const [footer, setFooter] = useState<React.ReactNode>(null);
    const [indent, setIndent] = useState<boolean>(true);
    const [variant, setVariant] = useState<ViewVariant>('primary');
    const [width, setWidth] = useState<ViewWidth>('standard');
    const [onClose, setOnClose] = useState<() => () => void>();
    const [disabled, setDisabled] = useState<boolean>();
    const [tabs, setTabs] = useState<TabHeadersRendererProps | null>(null);

    // pinned content; goes directly under tas
    const [pinnedContent, setPinnedContent] = useState<React.ReactNode>(null);

    useEffect(() => {
        return () => {
            setBanner(null);
            setDisabled(false);
        };
    }, []);

    return (
        <div className="settings">
            <DetailLayoutView
                banner={viewBanner ?? banner}
                header={header ? <div className="settings__header">{header}</div> : null}
                onClose={onClose}
                indentContent={indent}
                footer={footer}
                disabled={disabled}
                variant={variant}
                width={width}
                tabs={tabs}
                pinnedContent={pinnedContent}
                nav={nav}
            >
                <SettingsOutletContext.Provider
                    value={{
                        setPrimaryBanner: setBanner,
                        setSettingsBanner: setViewBanner,
                        setSettingsHeader: setHeader,
                        setSettingsFooter: setFooter,
                        setSettingsIndent: setIndent,
                        setSettingsViewVariant: setVariant,
                        setSettingsViewWidth: setWidth,
                        setSettingsRightColumn: EmptyFn,
                        setSettingsOnClose: (callback) => setOnClose(callback ? () => callback : undefined),
                        setSettingsDisabled: setDisabled,
                        setSettingsTabs: setTabs,
                        setSettingsPinnedContent: setPinnedContent,
                        setSettingsNav: setNav,
                    }}
                >
                    <Outlet />
                </SettingsOutletContext.Provider>
            </DetailLayoutView>
        </div>
    );
};
