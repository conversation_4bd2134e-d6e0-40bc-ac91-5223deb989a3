import { SuccessDialog } from '@shared/webComponents/Modal/IconDialog';
import { TwoEqualButtons } from '@shared/webComponents/Modal/ModalDialogButtons/DialogButtons';

import './AsanaLinkedAccountsDialog.scss';

interface Props {
    onSelectAll: () => void;
}

export const AsanaLinkedAccountsDialog = ({ onSelectAll }: Props) => {
    return (
        <SuccessDialog
            title="Your Asana account has been successfully linked!"
            description="Unblocked now has access to your projects. Would you like to select all of them?"
            buttons={
                <TwoEqualButtons
                    primary={{
                        children: 'Select All',
                        onClick: onSelectAll,
                    }}
                    secondary={{
                        children: 'Choose Specific Projects',
                    }}
                />
            }
            size={412}
        />
    );
};
