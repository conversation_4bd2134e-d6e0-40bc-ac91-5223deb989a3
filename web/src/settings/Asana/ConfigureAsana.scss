@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.configure_asana {
    margin: $spacer-24 0;
    @include flex-column;

    gap: $spacer-16;

    .configure_asana__description {
        color: themed($text-secondary);
    }
}

.configure_asana__empty_connection_prompt {
    @include flex-column-center;

    gap: $spacer-4;
    max-width: 450px;
    font-weight: $font-weight-normal;

    h5 {
        color: themed($text);
        font-weight: $font-weight-bold;
    }

    .button {
        margin-top: $spacer-8;
    }
}
