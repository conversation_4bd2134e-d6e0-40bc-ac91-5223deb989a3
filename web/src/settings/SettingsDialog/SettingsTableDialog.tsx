import { ReactNode } from 'react';

import { Identifiable } from '@shared/stores/LocalCache';
import { Props as TableProps, StoreTable } from '@shared/webComponents/Table/StoreTable';
import { TableDataStore } from '@shared/webComponents/Table/TableTypes';
import { Dialog } from '@web/components';

import { SettingsTableDataStore } from './SettingsTableDataStore';

import './SettingsTableDialog.scss';

type Props<T extends Identifiable, StoreT extends TableDataStore<T, SortT>, SortT = void> = TableProps<
    T,
    StoreT,
    SortT
> & {
    title: ReactNode;
    description?: ReactNode;
    action?: ReactNode;
};

export const SettingsTableDialog = <
    T extends Identifiable,
    StoreT extends SettingsTableDataStore<T, SortT>,
    SortT = void,
>(
    props: Props<T, StoreT, SortT>
) => {
    const { title, description, action, ...remaining } = props;
    return (
        <Dialog
            className="settings_table_dialog"
            title={title}
            description={description}
            size="x-wide"
            padding="compact"
            action={action}
            buttons={{ close: true }}
        >
            <StoreTable {...remaining} />
        </Dialog>
    );
};
