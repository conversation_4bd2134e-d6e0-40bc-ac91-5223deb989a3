import { useNavigate, useParams } from 'react-router-dom';

import { Provider } from '@shared/api/models';

import { useQuery } from '@shared/hooks/useQuery';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';
import { getProviderFromAppRoute } from '@shared/webUtils/ProviderUtils';
import { Loading } from '@web/components/Loading/Loading';
import { useTeamContext } from '@web/components/Team/TeamContext';

import { ConnectAsana } from '../Asana/ConnectAsana';
import { ConnectBuildkite } from '../CI/Buildkite/ConnectBuildkite';
import { ConnectCircleCI } from '../CI/CircleCI/ConnectCircleCI';
import { ConnectGitHubActions } from '../CI/GithubActions/ConnectGithubActions';
import { ConnectCoda } from '../Coda/ConnectCoda';
import { ConnectConfluenceDataCenter } from '../Confluence/ConnectConfluenceDataCenter';
import { CreateCustomIntegration } from '../CustomIntegrations/CreateCustomIntegration';
import { ConnectGoogleDriveWorkspace } from '../GoogleDriveWorkspace/ConnectGoogleDriveWorkspace';
import { ConnectJira } from '../Jira/ConnectJira';
import { ConnectJiraDataCenter } from '../Jira/ConnectJiraDataCenter';
import { ConnectScm } from '../Scm/ConnectScm';
import { EnterpriseConnectScm } from '../Scm/EnterpriseConnectScm';
import { ConnectStackOverflowTeams } from '../StackOverflowTeams/ConnectStackOverflowTeams';
import { WebIngestionSettings } from '../WebIngestion/WebIngestionSettings';
import { ConnectProvider } from './ConnectProvider';

const InstallationConnectionRedirect = ({
    provider,
    teamId,
    connectingIdentityId,
    externalInstallationId,
    enterpriseProviderId,
    forceInstall,
    initialConnection,
}: {
    provider: Provider;
    teamId: string;
    connectingIdentityId: string | undefined;
    externalInstallationId: string | undefined;
    enterpriseProviderId: string | undefined;
    forceInstall?: boolean;
    initialConnection?: boolean;
}) => {
    const navigate = useNavigate();

    switch (provider) {
        case Provider.GithubEnterprise:
        case Provider.BitbucketDataCenter:
        case Provider.GitlabSelfHosted:
            return (
                <EnterpriseConnectScm
                    provider={provider}
                    teamId={teamId}
                    connectingIdentityId={connectingIdentityId}
                    externalInstallationId={externalInstallationId}
                    enterpriseProviderId={enterpriseProviderId}
                    forceInstall={forceInstall}
                    initialConnection={initialConnection}
                />
            );
        case Provider.AzureDevOps:
        case Provider.Github:
        case Provider.Bitbucket:
        case Provider.Gitlab:
            return (
                <ConnectScm
                    provider={provider}
                    teamId={teamId}
                    connectingIdentityId={connectingIdentityId}
                    externalInstallationId={externalInstallationId}
                    forceInstall={forceInstall}
                />
            );

        case Provider.GithubActions:
            return <ConnectGitHubActions />;

        case Provider.BuildKite:
            return <ConnectBuildkite />;

        case Provider.Circleci:
            return <ConnectCircleCI />;

        case Provider.ConfluenceDataCenter:
            return <ConnectConfluenceDataCenter />;

        case Provider.Jira:
            return <ConnectJira />;

        case Provider.JiraDataCenter:
            return <ConnectJiraDataCenter />;

        case Provider.GoogleDriveWorkspace:
            return (
                <ConnectGoogleDriveWorkspace
                    onConnect={(installationId) =>
                        navigate(DashboardUrls.providerInstallation(teamId, provider, installationId))
                    }
                />
            );

        case Provider.StackOverflowTeams:
            return <ConnectStackOverflowTeams />;

        case Provider.Web:
            return <WebIngestionSettings />;

        case Provider.Coda:
            return <ConnectCoda />;

        case Provider.Asana:
            return <ConnectAsana />;

        case Provider.CustomIntegration:
            return <CreateCustomIntegration />;

        default:
            return <ConnectProvider provider={provider} />;
    }
};

export const InstallationConnection = () => {
    const { provider: paramProvider } = useParams();
    const { currentTeamId } = useTeamContext();
    const query = useQuery();
    const connectingIdentityId = query.get('connectingIdentityId') ?? undefined;
    const enterpriseProviderId = query.get('enterpriseProviderId') ?? undefined;
    const externalInstallationId = query.get('externalInstallationId') ?? undefined;
    const forceInstall = query.get('forceInstall') === 'true';
    const initialConnection = query.get('initialConnection') === 'true';

    if (!paramProvider) {
        return <Loading />;
    }

    const provider = getProviderFromAppRoute(paramProvider);

    if (!provider) {
        return <Loading />;
    }

    return (
        <InstallationConnectionRedirect
            provider={provider}
            teamId={currentTeamId}
            connectingIdentityId={connectingIdentityId}
            externalInstallationId={externalInstallationId}
            enterpriseProviderId={enterpriseProviderId}
            forceInstall={forceInstall}
            initialConnection={initialConnection}
        />
    );
};
