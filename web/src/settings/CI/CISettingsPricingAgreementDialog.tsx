import { Provider } from '@shared/api/generatedApi';

import { ModalContextApi } from '@shared/webComponents/Modal/ModalContext';
import { TwoEqualButtons } from '@shared/webComponents/Modal/ModalDialogButtons/DialogButtons';
import { DocsUrl } from '@shared/webUtils/DocsUrl';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { Dialog } from '@web/components';

import './CISettingsPricingAgreementDialog.scss';

export async function CISettingsPricingAgreementDialog(
    modalContext: ModalContextApi,
    provider: Provider
): Promise<boolean> {
    const { openModal } = modalContext;

    return new Promise<boolean>((resolve) => {
        const providerTraits = ProviderTraitsUtil.get(provider);
        const dialog = (
            <Dialog
                title="Member Licensing for Continuous Integration"
                className="ci_settings_pricing_agreement_dialog"
                size="medium"
                buttons={
                    <TwoEqualButtons
                        primary={{
                            children: 'Accept and Continue',
                            onClick: () => resolve(true),
                        }}
                        secondary={{
                            children: 'Cancel',
                            onClick: () => resolve(false),
                        }}
                    />
                }
            >
                <p>
                    Once {providerTraits.displayName} is connected, developers will automatically receive CI reports
                    from Unblocked when their builds fail.
                </p>
                <p>Developers will be assigned an Unblocked license after receiving 5 reports.</p>
                <p>
                    Developers can opt out of receiving CI reports.&nbsp;
                    <a href={DocsUrl.ci()} target="_blank" rel="noopener noreferrer">
                        Learn more.
                    </a>
                </p>
            </Dialog>
        );

        openModal(dialog);
    });
}
