import { jwtDecode, JwtPayload } from 'jwt-decode';
export type CIContextJwtPayload = JwtPayload & {
    orgs?: string[];
    scmAvatar: string;
    scmProvider: string;
    scmRepo: string;
    scmUsername: string;
};

export const getCIContextJwtPayload = (token: string): CIContextJwtPayload | undefined => {
    try {
        return jwtDecode<CIContextJwtPayload>(token);
    } catch {
        return undefined;
    }
};
