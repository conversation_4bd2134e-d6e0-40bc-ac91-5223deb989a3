import { Provider } from '@shared/api/generatedApi';

import { CISettings } from '../CISettings';
import { CircleCISteps } from './CircleCISteps';

interface Props {
    installationId: string;
    initialConnection?: boolean;
}

export const CircleCISettings = ({ installationId, initialConnection }: Props) => {
    return (
        <CISettings
            provider={Provider.Circleci}
            installationId={installationId}
            onboardingSteps={CircleCISteps}
            initialConnection={initialConnection}
        />
    );
};
