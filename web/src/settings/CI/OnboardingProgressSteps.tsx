import { ProgressSteps } from '@shared/webComponents/ProgressSteps/ProgressSteps';
import { TitleStep } from '@shared/webComponents/ProgressSteps/Step';

interface Props {
    steps: string[];
    currentStep: number;
}
export const CIOnboardingProgressSteps = ({ steps, currentStep }: Props) => {
    return (
        <div className="ci_onboarding_progress_steps">
            <ProgressSteps current={currentStep}>
                {steps.map((step) => (
                    <TitleStep title={step} key={step} />
                ))}
            </ProgressSteps>
        </div>
    );
};
