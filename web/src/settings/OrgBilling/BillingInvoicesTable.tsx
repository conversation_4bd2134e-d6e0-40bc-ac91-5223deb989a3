import dayjs from 'dayjs';
import { useMemo } from 'react';

import { BillingInvoice, Team } from '@shared/api/models';

import { Icon } from '@shared/webComponents/Icon/Icon';
import { TableHeader } from '@shared/webComponents/Table/TableHeader';
import DateTime from '@shared/webUtils/DateTime';
import { NumberUtils } from '@shared/webUtils/NumberUtils';
import { Breakpoints } from '@web/components/Breakpoint/Breakpoints';
import { UnstyledTable } from '@web/components/Table/UnstyledTable';

import { faCircleDown } from '@fortawesome/pro-solid-svg-icons/faCircleDown';

import { PaymentMethod } from './BillingPaymentDetails';

import './BillingInvoicesTable.scss';

const InvoiceRow = ({ teamName, invoice }: { teamName: string; invoice: BillingInvoice }) => {
    const fileName = `${teamName}-UnblockedInvoice${dayjs(invoice.billedAt).format('MMDDYYYY')}.pdf`.replace(/\s/g, '');

    return (
        <div className="invoice_row">
            <div className="invoice_row__billing_date">
                <Breakpoints
                    xs={DateTime.numberPretty(invoice.billedAt)}
                    md={DateTime.shortPretty(invoice.billedAt, true)}
                />
            </div>
            <div className="invoice_row__payment_info">
                <PaymentMethod paymentInfo={invoice.paymentInfo} />
            </div>
            <div className="invoice_row__amount_charged">
                {NumberUtils.dollarValue(invoice.amountCharged, { inCents: true, currency: 'USD' })}
            </div>
            <div className="invoice_row__download">
                <a href={invoice.downloadUrl} download={fileName}>
                    <Icon icon={faCircleDown} />
                </a>
            </div>
        </div>
    );
};

interface Props {
    header?: React.ReactNode;
    team: Team;
    invoices: BillingInvoice[];
}

export const BillingInvoicesTable = ({ team, header, invoices }: Props) => {
    const tableHeader = useMemo(() => {
        return (
            <TableHeader className="billing_invoices_table_header">
                <div className="billing_invoices_table__billing_date">Date</div>
                <div className="billing_invoices_table__payment_info">Payment</div>
                <div className="billing_invoices_table__amount_charged">Amount</div>
                <div className="billing_invoices_table__download">Download</div>
            </TableHeader>
        );
    }, []);

    return (
        <>
            {header}
            <UnstyledTable className="billing_invoices_table">
                {tableHeader}
                {invoices.map((invoice, idx) => (
                    <InvoiceRow key={idx} teamName={team.displayName} invoice={invoice} />
                ))}
            </UnstyledTable>
        </>
    );
};
