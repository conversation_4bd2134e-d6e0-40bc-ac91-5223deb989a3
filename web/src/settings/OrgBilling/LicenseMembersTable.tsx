import { useCallback, useMemo } from 'react';

import { Plan, Team, TeamMember } from '@shared/api/generatedApi';

import { ClientConfigKeys } from '@shared/stores/ClientConfigStore';
import { useStream } from '@shared/stores/DataCacheStream';
import { FeatureSettingsStoreTraits } from '@shared/stores/FeatureSettingsStoreType';
import { PersonStoreTraits } from '@shared/stores/PersonStoreTypes';
import { PlanStore } from '@shared/stores/PlanStore';
import { TeamMemberListStore } from '@shared/stores/TeamMemberListStore';
import { useStore } from '@shared/stores/useStore';
import { useCapabilityFromServer } from '@shared/webComponents/CapabilitiesContext';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { ArrayUtils } from '@shared/webUtils';
import { findPersonTeamMember } from '@shared/webUtils/TeamMemberUtils';
import { Dropdown, DropdownItem } from '@web/components';
import { useMakeToast } from '@web/components/Toast/useMakeToast';

import { faEllipsis } from '@fortawesome/pro-regular-svg-icons/faEllipsis';

import { RemoveMemberLicenseDialog } from '../ConfigureOrgMembers/RemoveMemberLicenseDialog';
import {
    CITriageReportsColumn,
    createActionColumn,
    LastActivityColumn,
    MembersTable,
    NameColumn,
    QuestionsAskedColumn,
} from '../MembersTable';

interface Props {
    team: Team;
    currentPlan: Plan;
    borderless?: boolean;
}

export const LicenseMembersTable = ({ team, currentPlan, borderless }: Props) => {
    const { makeToast } = useMakeToast();
    const { openModal } = useModalContext();
    const personStore = useStore(PersonStoreTraits, {});
    const person = useStream(() => personStore.person, [personStore]);
    const currentTeamMember = findPersonTeamMember(person, team.id);
    const featureSettingsStore = useStore(FeatureSettingsStoreTraits, { teamId: team.id });
    const featureSettings = useStream(() => featureSettingsStore.stream, [featureSettingsStore]);
    const isCIEnabled = useCapabilityFromServer(ClientConfigKeys.FeatureConfigureCI);

    const membersStore = useMemo(() => new TeamMemberListStore(team.id, { hasLicense: true }), [team.id]);

    const removeLicense = useCallback(
        async (member: TeamMember) => {
            if (member.id === currentTeamMember?.id) {
                return;
            }
            try {
                await membersStore.removeTeamMemberLicense(team.id, member.id);
                PlanStore.get(team.id).refresh();
                makeToast({
                    type: 'confirm',
                    message: (
                        <>The license for {member.identity.displayName} has been removed and is available to claim.</>
                    ),
                });
            } catch {
                makeToast({
                    type: 'alert',
                    message: `There was an issue removing the license for ${member.identity.displayName}.`,
                });
            }
        },
        [team.id, makeToast, currentTeamMember, membersStore]
    );

    const canRemoveMemberLicense = useCallback(
        (teamMember: TeamMember) => {
            if (!featureSettings || featureSettings.$case !== 'ready') {
                return false;
            }

            return (
                featureSettings.isAllowedToUpdate &&
                teamMember.id !== currentTeamMember?.id &&
                !!teamMember.hasLicense &&
                !!currentPlan.seats
            );
        },
        [featureSettings, currentTeamMember, currentPlan]
    );

    const removeLicenseDialogConfirmation = useCallback(
        async (member: TeamMember) => {
            openModal(
                <RemoveMemberLicenseDialog teamMember={member} team={team} onDelete={() => removeLicense(member)} />
            );
        },
        [removeLicense, openModal, team]
    );

    const columns = ArrayUtils.compact([
        NameColumn,
        isCIEnabled ? CITriageReportsColumn : undefined,
        QuestionsAskedColumn,
        LastActivityColumn,
        createActionColumn(
            (member) => {
                if (!canRemoveMemberLicense(member)) {
                    return <></>;
                }
                return (
                    <Dropdown portal header={<Icon icon={faEllipsis} size="medium" />} withCaret={false}>
                        <DropdownItem onClick={() => removeLicenseDialogConfirmation(member)}>
                            Remove License
                        </DropdownItem>
                    </Dropdown>
                );
            },
            '50px',
            'secondary',
            true
        ),
    ]);

    return (
        <MembersTable
            membersStore={membersStore}
            team={team}
            columns={columns}
            hasLicense={true}
            searchPlaceholder="Find a team member"
            borderless={borderless}
        />
    );
};
