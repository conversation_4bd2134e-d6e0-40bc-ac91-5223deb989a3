import { forwardRef, Ref } from 'react';

import { JiraSite } from '@shared/api/models';

import { ConfigureIntegrationProps } from '../ConfigureIntegration/ConfigureIntegration';
import { ConfigureSettingForwardedRef } from '../SettingsTypes';
import { ConfigureJira } from './ConfigureJira';

import './ConfigureJiraDataCenter.scss';

const ConfigureJiraDataCenterComponent = (
    {
        installationId,
        ...props
    }: ConfigureIntegrationProps & {
        onFetchJiraSite?: (site: JiraSite) => void;
        tokenError?: string;
        onConnected?: (id: string) => void;
        onTokenValuesChanged?: (args: { hostUrl?: string; pat?: string }) => void;
    },
    ref: Ref<ConfigureSettingForwardedRef>
) => {
    return <ConfigureJira {...props} installationId={installationId} ref={ref} isDataCenter />;
};

export const ConfigureJiraDataCenter = forwardRef(ConfigureJiraDataCenterComponent);
