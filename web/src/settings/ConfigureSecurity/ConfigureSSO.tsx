import { useMemo } from 'react';

import { Team } from '@shared/api/generatedApi';

import { SSOSettingsUpdate, useSSOSettings } from '@shared/hooks/useSSOSettings';
import { SSOSettingState } from '@shared/stores/SSOSettingsStore';
import { ArrayUtils } from '@shared/webUtils';
import { getProviderDisplayName, isSignInCapable } from '@shared/webUtils/ProviderUtils';
import { UpgradePlanChip } from '@web/components/IntegrationTile/UpgradePlanChip';

import { useSettingsContext } from '../SettingsContext';
import { ConfigureScimRow } from './ConfigureScimRow';
import { EnforceSSORow } from './EnforceSSORow';
import { SAMLConfigurationRow } from './SAMLConfigurationRow';
import { VerifyDomainsRow } from './VerifyDomainsRow';

import './ConfigureSSO.scss';

export const ConfigureSSO = ({
    team,
    disabled,
    needsPlanUpgrade,
}: {
    team: Team;
    disabled: boolean;
    needsPlanUpgrade: boolean;
}) => {
    const ssoState = useSSOSettings(team.id);

    switch (ssoState.$case) {
        case 'loading':
            return null;
        case 'ready':
            return (
                <div className="configure_sso">
                    <div className="configure_sso__title">
                        <h3>Single Sign-On</h3>
                        {needsPlanUpgrade && <UpgradePlanChip />}
                    </div>
                    <div className="configure_sso__body">
                        <LoadedConfigureSSOBody
                            state={ssoState.value}
                            update={ssoState}
                            team={team}
                            disabled={disabled}
                        />
                    </div>
                </div>
            );
    }
};

const LoadedConfigureSSOBody = (props: {
    disabled: boolean;
    state: SSOSettingState;
    update: SSOSettingsUpdate;
    team: Team;
}) => {
    const { teamIntegrations } = useSettingsContext();
    const signInCapableProviders = useMemo(() => {
        const signInCapableProviders = [...teamIntegrations.installations.keys()].filter((p) => isSignInCapable(p));
        return ArrayUtils.distinct(signInCapableProviders.map((p) => getProviderDisplayName(p))).sort();
    }, [teamIntegrations.installations]);

    const hasSetup = !!props.state.identityProviderData && !!props.team.sso;
    return (
        <>
            <SAMLConfigurationRow {...props} signInCapableProviders={signInCapableProviders} />
            {hasSetup && <EnforceSSORow {...props} signInCapableProviders={signInCapableProviders} />}
            {hasSetup && <ConfigureScimRow {...props} />}
            {hasSetup && <VerifyDomainsRow {...props} />}
        </>
    );
};
