import classNames from 'classnames';
import { useCallback, useMemo } from 'react';

import { Provider, Team } from '@shared/api/generatedApi';

import { useStream } from '@shared/stores/DataCacheStream';
import { SSOSettingState } from '@shared/stores/SSOSettingsStore';
import { ScimTokenStoreTraits } from '@shared/stores/TokenStoreTraits';
import { useStore } from '@shared/stores/useStore';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { DateTime } from '@shared/webUtils';
import { Button } from '@web/components';

import { ConfigureScimDialog } from './ConfigureScimDialog';

interface Props {
    team: Team;
    state: SSOSettingState;
    disabled: boolean;
}

export const ConfigureScimRow = ({ team, state }: Props) => {
    const { openModal } = useModalContext();
    const scimMetadata = state.scimMetadata;

    const store = useStore(ScimTokenStoreTraits, { teamId: team.id });
    const tokenState = useStream(() => store.stream, [store]);

    const triggerDialog = useCallback(() => {
        openModal(
            <ConfigureScimDialog
                metadata={scimMetadata}
                teamId={team.id}
                provider={state.SSOProvider?.provider ?? Provider.Saml}
            />
        );
    }, [openModal, scimMetadata, team.id, state.SSOProvider?.provider]);

    const classes = classNames({
        configure_sso__row: true,
        configure_scim__row: true,
    });

    const description = useMemo(() => {
        if (tokenState?.$case === 'ready' && tokenState.value.lastUpdatedDate) {
            return `Last Synced ${DateTime.shortTimePretty(tokenState.value.lastUpdatedDate)}.`;
        }

        return 'Automate user onboarding and offboarding, and import groups with SCIM.';
    }, [tokenState]);

    return (
        <div className={classes}>
            <div className="configure_sso__row_description">
                <h5>SCIM User and Group Provisioning</h5>
                <p>{description}</p>
            </div>
            <div className="configure_sso__row_action">
                <Button
                    variant="primary"
                    size="small"
                    onClick={() => {
                        triggerDialog();
                    }}
                >
                    {tokenState?.$case === 'ready' && tokenState.value.metadata.length > 0
                        ? 'View Configuration'
                        : 'Configure…'}
                </Button>
            </div>
        </div>
    );
};
