import classNames from 'classnames';
import { ReactNode, useCallback, useMemo, useState } from 'react';

import { Provider, SamlIdentityProviderData, SamlServiceProviderData, Team } from '@shared/api/generatedApi';

import { useDialogContext, useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { Button } from '@web/components';
import { Dialog } from '@web/components/Dialog/Dialog';

import { ConfirmDeleteSamlDialog } from './ConfirmDeleteSamlDialog';

import './ConfigureSamlDialog.scss';

interface ChildProps {
    canSave: boolean;
    isEdit: boolean;
    serviceProviderData: SamlServiceProviderData;
    entityId: string | undefined;
    setEntityId: (value: string) => void;
    testUrl: string | undefined;
    setTestUrl: (value: string) => void;
    cert: string | undefined;
    setCert: (value: string) => void;
}

interface Props {
    serviceProviderData: SamlServiceProviderData;
    identityProviderData?: SamlIdentityProviderData;
    team: Team;
    provider?: Provider;
    signInCapableProviders: string[];
    onSave: (data: SamlIdentityProviderData) => Promise<void>;
    onDelete: () => void;
    title: string;
    docsUrl: string;
    isEdit: boolean;
    children: (args: ChildProps) => ReactNode;
}

export const BaseConfigureSamlDialog = ({
    serviceProviderData,
    identityProviderData,
    team,
    provider,
    signInCapableProviders,
    onSave,
    onDelete,
    docsUrl,
    title,
    isEdit,
    children,
}: Props) => {
    const { closeModal } = useDialogContext();
    const { openModal } = useModalContext();
    const [entityId, setEntityId] = useState(identityProviderData?.entityId);
    const [testUrl, setTestUrl] = useState(identityProviderData?.testUrl);
    const [cert, setCert] = useState(identityProviderData?.x509Certificate);

    const canSave = useMemo(() => {
        return !!entityId && !!testUrl && !!cert;
    }, [entityId, testUrl, cert]);

    const triggerSave = useCallback(async () => {
        if (!entityId || !testUrl || !cert) {
            return;
        }
        await onSave({
            entityId: entityId,
            testUrl: testUrl,
            x509Certificate: cert,
        });
    }, [entityId, testUrl, cert, onSave]);

    const triggerDelete = useCallback(async () => {
        openModal(
            <ConfirmDeleteSamlDialog
                teamName={team.displayName}
                provider={provider}
                onDelete={async () => {
                    closeModal();
                    onDelete();
                }}
                signInCapableProviders={signInCapableProviders}
            />
        );
    }, [openModal, onDelete, closeModal, team, provider, signInCapableProviders]);

    const classes = classNames({
        configure_saml_dialog: true,
    });

    return (
        <Dialog
            className={classes}
            title={title}
            action={
                <Button
                    size="small"
                    variant="tertiary"
                    onClick={() => {
                        window.open(docsUrl, '_blank');
                    }}
                >
                    View our Docs
                </Button>
            }
            padding="compact"
            description={
                <span>
                    <a href={docsUrl} rel="noreferrer" target="_blank">
                        View our docs
                    </a>
                    &nbsp;for a detailed, step-by-step guide.
                </span>
            }
            size="x-wide"
            deprecatedButtons={{
                primary: {
                    children: 'Save',
                    disabled: !canSave,
                    onClick: () => {
                        triggerSave();
                        closeModal();
                    },
                },
                secondary: {
                    children: 'Cancel',
                    variant: 'outline',
                    onClick: () => {
                        closeModal();
                    },
                },
                tertiary: isEdit
                    ? {
                          children: 'Delete',
                          variant: 'destructive',
                          overrideClose: true,
                          onClick: () => {
                              triggerDelete();
                          },
                      }
                    : undefined,
            }}
        >
            {children({
                canSave,
                isEdit,
                serviceProviderData,
                entityId,
                setEntityId,
                testUrl,
                setTestUrl,
                cert,
                setCert,
            })}
        </Dialog>
    );
};
