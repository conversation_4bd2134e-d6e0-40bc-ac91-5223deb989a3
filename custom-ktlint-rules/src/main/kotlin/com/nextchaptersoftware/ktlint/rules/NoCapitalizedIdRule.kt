package com.nextchaptersoftware.ktlint.rules

import org.jetbrains.kotlin.KtNodeTypes
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtParameter
import org.jetbrains.kotlin.psi.KtProperty

class NoCapitalizedIdRule : CustomRule("no-capitalized-ids") {

    override fun beforeVisitChildNodes(
        node: ASTNode,
        emit: Emitter,
    ) {
        when (node.elementType) {
            KtNodeTypes.PROPERTY -> (node.psi as KtProperty).name
            KtNodeTypes.VALUE_PARAMETER -> (node.psi as KtParameter).name
            else -> null
        }?.let {
            if (it.matches(".*[^A-Z_]+ID".toRegex())) {
                val errorMessage = """
                Properties and value parameters representing IDs should end in lowercase d: $it
                """.trimIndent()
                emit(node.startOffset, errorMessage, false)
            }
        }
    }
}
