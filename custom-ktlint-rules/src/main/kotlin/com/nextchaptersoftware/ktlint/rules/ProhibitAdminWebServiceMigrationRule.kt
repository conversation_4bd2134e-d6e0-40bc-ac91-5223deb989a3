package com.nextchaptersoftware.ktlint.rules

import com.nextchaptersoftware.ktlint.utils.FileValidator
import org.jetbrains.kotlin.com.intellij.lang.ASTNode

/**
 * **ProhibitAdminWebserviceMigrationRule**
 *
 * Any Kotlin source under
 * `projects/services/adminwebservice/src/main/kotlin/com/nextchaptersoftware/adminwebservice/migration`
 * will fail ktlint with the message below.
 *
 * Rationale  →  All database migrations must now be **Flyway** migrations and
 * belong in
 * `projects/models/src/main/kotlin/com/nextchaptersoftware/db/migration/java`.
 * See the engineering guidelines:
 * https://www.notion.so/nextchaptersoftware/Versioned-Database-Migrations-1f6b8003557580558db1f88d322c1457#200b8003557580e4b20ecf59419d0159
 */
class ProhibitAdminWebServiceMigrationRule :
    CustomRule("prohibit-adminwebservice-migration-rule") {

    // Matches absolute or project-relative paths that include the forbidden folder.
    private val forbiddenPath = Regex(
        """.*/src/main/.*/adminwebservice/migration/.*""",
    )

    private val fileValidator = FileValidator(
        inclusionPatterns = listOf(forbiddenPath),
    )

    // Emit only once per file.
    private var emitted = false

    override fun beforeVisitChildNodes(node: ASTNode, emit: Emitter) {
        if (!emitted && fileValidator.validate(node)) {
            emit(
                0,
                """
                Database migrations in *adminwebservice* are forbidden.

                • Author migrations in *models* *only*, using **Flyway**:
                  ├─ SQL DDL scripts → projects/models/src/main/resources/db/migration
                  └─ Java/Kotlin code → projects/models/src/main/kotlin/com/nextchaptersoftware/db/migration/java

                👉  Engineering guidelines:
                https://www.notion.so/nextchaptersoftware/Versioned-Database-Migrations-1f6b8003557580558db1f88d322c1457#200b8003557580e4b20ecf59419d0159
                """.trimIndent(),
                false,
            )
            emitted = true
        }
    }
}
