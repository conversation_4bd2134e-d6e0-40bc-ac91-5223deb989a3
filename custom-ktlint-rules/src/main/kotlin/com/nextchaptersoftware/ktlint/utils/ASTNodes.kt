package com.nextchaptersoftware.ktlint.utils

import org.jetbrains.kotlin.KtNodeTypes
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtImportDirective
import org.jetbrains.kotlin.psi.KtImportList
import org.jetbrains.kotlin.psi.psiUtil.getChildrenOfType

object ASTNodes {

    fun ASTNode.findImportDirectives(): List<KtImportDirective>? {
        return treeParent.getChildren(null)
            .filter { it.elementType == KtNodeTypes.IMPORT_LIST }
            .flatMap {
                (it.psi as KtImportList)
                    .getChildrenOfType<KtImportDirective>()
                    .toList()
            }
            .ifEmpty { return null }
    }
}
